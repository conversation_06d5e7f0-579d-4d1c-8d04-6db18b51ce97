{"ast": null, "code": "export { A as ActionCodeOperation, aj as ActionCodeURL, M as AuthCredential, J as AuthErrorCodes, N as EmailAuthCredential, W as EmailAuthProvider, X as FacebookAuthProvider, F as FactorId, Z as GithubAuthProvider, Y as GoogleAuthProvider, Q as OAuthCredential, _ as OAuthProvider, O as OperationType, U as PhoneAuthCredential, P as PhoneAuthProvider, n as PhoneMultiFactorGenerator, q as ProviderId, R as RecaptchaVerifier, $ as SAMLAuthProvider, S as SignInMethod, T as TotpMultiFactorGenerator, o as TotpSecret, a0 as TwitterAuthProvider, a8 as applyActionCode, y as beforeAuthStateChanged, a as browserCookiePersistence, b as browserLocalPersistence, m as browserPopupRedirectResolver, c as browserSessionPersistence, a9 as checkActionCode, a7 as confirmPasswordReset, L as connectAuthEmulator, ab as createUserWithEmailAndPassword, H as debugErrorMap, G as deleteUser, ag as fetchSignInMethodsForEmail, ar as getAdditionalUserInfo, p as getAuth, ao as getIdToken, ap as getIdTokenResult, at as getMultiFactorResolver, k as getRedirectResult, V as inMemoryPersistence, i as indexedDBLocalPersistence, K as initializeAuth, v as initializeRecaptchaConfig, ae as isSignInWithEmailLink, a3 as linkWithCredential, l as linkWithPhoneNumber, e as linkWithPopup, h as linkWithRedirect, au as multiFactor, z as onAuthStateChanged, x as onIdTokenChanged, ak as parseActionCodeURL, I as prodErrorMap, a4 as reauthenticateWithCredential, r as reauthenticateWithPhoneNumber, f as reauthenticateWithPopup, j as reauthenticateWithRedirect, as as reload, E as revokeAccessToken, ah as sendEmailVerification, a6 as sendPasswordResetEmail, ad as sendSignInLinkToEmail, t as setPersistence, a1 as signInAnonymously, a2 as signInWithCredential, a5 as signInWithCustomToken, ac as signInWithEmailAndPassword, af as signInWithEmailLink, s as signInWithPhoneNumber, d as signInWithPopup, g as signInWithRedirect, D as signOut, aq as unlink, C as updateCurrentUser, am as updateEmail, an as updatePassword, u as updatePhoneNumber, al as updateProfile, B as useDeviceLanguage, w as validatePassword, ai as verifyBeforeUpdateEmail, aa as verifyPasswordResetCode } from './index-8bd0c73f.js';\nimport '@firebase/app';\nimport '@firebase/util';\nimport '@firebase/logger';\nimport 'tslib';\nimport '@firebase/component';", "map": {"version": 3, "names": ["A", "ActionCodeOperation", "aj", "ActionCodeURL", "M", "AuthCredential", "J", "AuthErrorCodes", "N", "EmailAuthCredential", "W", "EmailAuthProvider", "X", "FacebookAuthProvider", "F", "FactorId", "Z", "GithubAuth<PERSON>rovider", "Y", "GoogleAuthProvider", "Q", "OAuthCredential", "_", "OAuth<PERSON><PERSON><PERSON>", "O", "OperationType", "U", "PhoneAuthCredential", "P", "PhoneAuthProvider", "n", "PhoneMultiFactorGenerator", "q", "ProviderId", "R", "RecaptchaVerifier", "$", "SAMLAuthProvider", "S", "SignInMethod", "T", "TotpMultiFactorGenerator", "o", "TotpSecret", "a0", "TwitterAuthProvider", "a8", "applyActionCode", "y", "beforeAuthStateChanged", "a", "browserCookiePersistence", "b", "browserLocalPersistence", "m", "browserPopupRedirectResolver", "c", "browserSessionPersistence", "a9", "checkActionCode", "a7", "confirmPasswordReset", "L", "connectAuthEmulator", "ab", "createUserWithEmailAndPassword", "H", "debugErrorMap", "G", "deleteUser", "ag", "fetchSignInMethodsForEmail", "ar", "getAdditionalUserInfo", "p", "getAuth", "ao", "getIdToken", "ap", "getIdTokenResult", "at", "getMultiFactorResolver", "k", "getRedirectResult", "V", "inMemoryPersistence", "i", "indexedDBLocalPersistence", "K", "initializeAuth", "v", "initializeRecaptchaConfig", "ae", "isSignInWithEmailLink", "a3", "linkWithCredential", "l", "linkWithPhoneNumber", "e", "linkWithPopup", "h", "linkWithRedirect", "au", "multiFactor", "z", "onAuthStateChanged", "x", "onIdTokenChanged", "ak", "parseActionCodeURL", "I", "prodErrorMap", "a4", "reauthenticateWithCredential", "r", "reauthenticateWithPhoneNumber", "f", "reauthenticateWithPopup", "j", "reauthenticateWithRedirect", "as", "reload", "E", "revokeAccessToken", "ah", "sendEmailVerification", "a6", "sendPasswordResetEmail", "ad", "sendSignInLinkToEmail", "t", "setPersistence", "a1", "signInAnonymously", "a2", "signInWithCredential", "a5", "signInWithCustomToken", "ac", "signInWithEmailAndPassword", "af", "signInWithEmailLink", "s", "signInWithPhoneNumber", "d", "signInWithPopup", "g", "signInWithRedirect", "D", "signOut", "aq", "unlink", "C", "updateCurrentUser", "am", "updateEmail", "an", "updatePassword", "u", "updatePhoneNumber", "al", "updateProfile", "B", "useDeviceLanguage", "w", "validatePassword", "ai", "verifyBeforeUpdateEmail", "aa", "verifyPasswordResetCode"], "sources": ["D:/ProjetPfa/pfa/pfa/node_modules/@firebase/auth/dist/esm2017/index.js"], "sourcesContent": ["export { A as ActionCodeOperation, aj as ActionCodeURL, M as AuthCredential, J as AuthErrorCodes, N as EmailAuthCredential, W as EmailAuthProvider, X as FacebookAuthProvider, F as FactorId, Z as GithubAuthProvider, Y as GoogleAuthProvider, Q as OAuthCredential, _ as OAuthProvider, O as OperationType, U as PhoneAuthCredential, P as PhoneAuthProvider, n as PhoneMultiFactorGenerator, q as ProviderId, R as RecaptchaVerifier, $ as SAMLAuthProvider, S as SignInMethod, T as TotpMultiFactorGenerator, o as TotpSecret, a0 as TwitterAuthProvider, a8 as applyActionCode, y as beforeAuthStateChanged, a as browserCookiePersistence, b as browserLocalPersistence, m as browserPopupRedirectResolver, c as browserSessionPersistence, a9 as checkActionCode, a7 as confirmPasswordReset, L as connectAuthEmulator, ab as createUserWithEmailAndPassword, H as debugErrorMap, G as deleteUser, ag as fetchSignInMethodsForEmail, ar as getAdditionalUserInfo, p as getAuth, ao as getIdToken, ap as getIdTokenResult, at as getMultiFactorResolver, k as getRedirectResult, V as inMemoryPersistence, i as indexedDBLocalPersistence, K as initializeAuth, v as initializeRecaptchaConfig, ae as isSignInWithEmailLink, a3 as linkWithCredential, l as linkWithPhoneNumber, e as linkWithPopup, h as linkWithRedirect, au as multiFactor, z as onAuthStateChanged, x as onIdTokenChanged, ak as parseActionCodeURL, I as prodErrorMap, a4 as reauthenticateWithCredential, r as reauthenticateWithPhoneNumber, f as reauthenticateWithPopup, j as reauthenticateWithRedirect, as as reload, E as revokeAccessToken, ah as sendEmailVerification, a6 as sendPasswordResetEmail, ad as sendSignInLinkToEmail, t as setPersistence, a1 as signInAnonymously, a2 as signInWithCredential, a5 as signInWithCustomToken, ac as signInWithEmailAndPassword, af as signInWithEmailLink, s as signInWithPhoneNumber, d as signInWithPopup, g as signInWithRedirect, D as signOut, aq as unlink, C as updateCurrentUser, am as updateEmail, an as updatePassword, u as updatePhoneNumber, al as updateProfile, B as useDeviceLanguage, w as validatePassword, ai as verifyBeforeUpdateEmail, aa as verifyPasswordResetCode } from './index-8bd0c73f.js';\nimport '@firebase/app';\nimport '@firebase/util';\nimport '@firebase/logger';\nimport 'tslib';\nimport '@firebase/component';\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,mBAAmB,EAAEC,EAAE,IAAIC,aAAa,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,EAAE,IAAIC,mBAAmB,EAAEC,EAAE,IAAIC,eAAe,EAAEC,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,EAAE,IAAIC,eAAe,EAAEC,EAAE,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,EAAE,IAAIC,8BAA8B,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,UAAU,EAAEC,EAAE,IAAIC,0BAA0B,EAAEC,EAAE,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,OAAO,EAAEC,EAAE,IAAIC,UAAU,EAAEC,EAAE,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,EAAE,IAAIC,qBAAqB,EAAEC,EAAE,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,WAAW,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,YAAY,EAAEC,EAAE,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,6BAA6B,EAAEC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,0BAA0B,EAAEC,EAAE,IAAIC,MAAM,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,EAAE,IAAIC,qBAAqB,EAAEC,EAAE,IAAIC,sBAAsB,EAAEC,EAAE,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,EAAE,IAAIC,iBAAiB,EAAEC,EAAE,IAAIC,oBAAoB,EAAEC,EAAE,IAAIC,qBAAqB,EAAEC,EAAE,IAAIC,0BAA0B,EAAEC,EAAE,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,OAAO,EAAEC,EAAE,IAAIC,MAAM,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,EAAE,IAAIC,WAAW,EAAEC,EAAE,IAAIC,cAAc,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,EAAE,IAAIC,aAAa,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,uBAAuB,EAAEC,EAAE,IAAIC,uBAAuB,QAAQ,qBAAqB;AACxnE,OAAO,eAAe;AACtB,OAAO,gBAAgB;AACvB,OAAO,kBAAkB;AACzB,OAAO,OAAO;AACd,OAAO,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}