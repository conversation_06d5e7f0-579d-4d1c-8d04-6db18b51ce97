{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjetPfa/pfa/pfa/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { getDefaultsFromPostinstall } from './postinstall.mjs';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.\n */\nconst CONSTANTS = {\n  /**\n   * @define {boolean} Whether this is the client Node.js SDK.\n   */\n  NODE_CLIENT: false,\n  /**\n   * @define {boolean} Whether this is the Admin Node.js SDK.\n   */\n  NODE_ADMIN: false,\n  /**\n   * Firebase SDK Version\n   */\n  SDK_VERSION: '${JSCORE_VERSION}'\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Throws an error if the provided assertion is falsy\n */\nconst assert = function (assertion, message) {\n  if (!assertion) {\n    throw assertionError(message);\n  }\n};\n/**\n * Returns an Error object suitable for throwing.\n */\nconst assertionError = function (message) {\n  return new Error('Firebase Database (' + CONSTANTS.SDK_VERSION + ') INTERNAL ASSERT FAILED: ' + message);\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst stringToByteArray$1 = function (str) {\n  // TODO(user): Use native implementations if/when available\n  const out = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = c >> 6 | 192;\n      out[p++] = c & 63 | 128;\n    } else if ((c & 0xfc00) === 0xd800 && i + 1 < str.length && (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {\n      // Surrogate Pair\n      c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n      out[p++] = c >> 18 | 240;\n      out[p++] = c >> 12 & 63 | 128;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    } else {\n      out[p++] = c >> 12 | 224;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    }\n  }\n  return out;\n};\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes) {\n  // TODO(user): Use native implementations if/when available\n  const out = [];\n  let pos = 0,\n    c = 0;\n  while (pos < bytes.length) {\n    const c1 = bytes[pos++];\n    if (c1 < 128) {\n      out[c++] = String.fromCharCode(c1);\n    } else if (c1 > 191 && c1 < 224) {\n      const c2 = bytes[pos++];\n      out[c++] = String.fromCharCode((c1 & 31) << 6 | c2 & 63);\n    } else if (c1 > 239 && c1 < 365) {\n      // Surrogate Pair\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      const c4 = bytes[pos++];\n      const u = ((c1 & 7) << 18 | (c2 & 63) << 12 | (c3 & 63) << 6 | c4 & 63) - 0x10000;\n      out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n      out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n    } else {\n      const c2 = bytes[pos++];\n      const c3 = bytes[pos++];\n      out[c++] = String.fromCharCode((c1 & 15) << 12 | (c2 & 63) << 6 | c3 & 63);\n    }\n  }\n  return out.join('');\n};\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nconst base64 = {\n  /**\n   * Maps bytes to characters.\n   */\n  byteToCharMap_: null,\n  /**\n   * Maps characters to bytes.\n   */\n  charToByteMap_: null,\n  /**\n   * Maps bytes to websafe characters.\n   * @private\n   */\n  byteToCharMapWebSafe_: null,\n  /**\n   * Maps websafe characters to bytes.\n   * @private\n   */\n  charToByteMapWebSafe_: null,\n  /**\n   * Our default alphabet, shared between\n   * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n   */\n  ENCODED_VALS_BASE: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n  /**\n   * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n   */\n  get ENCODED_VALS() {\n    return this.ENCODED_VALS_BASE + '+/=';\n  },\n  /**\n   * Our websafe alphabet.\n   */\n  get ENCODED_VALS_WEBSAFE() {\n    return this.ENCODED_VALS_BASE + '-_.';\n  },\n  /**\n   * Whether this browser supports the atob and btoa functions. This extension\n   * started at Mozilla but is now implemented by many browsers. We use the\n   * ASSUME_* variables to avoid pulling in the full useragent detection library\n   * but still allowing the standard per-browser compilations.\n   *\n   */\n  HAS_NATIVE_SUPPORT: typeof atob === 'function',\n  /**\n   * Base64-encode an array of bytes.\n   *\n   * @param input An array of bytes (numbers with\n   *     value in [0, 255]) to encode.\n   * @param webSafe Boolean indicating we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeByteArray(input, webSafe) {\n    if (!Array.isArray(input)) {\n      throw Error('encodeByteArray takes an array as a parameter');\n    }\n    this.init_();\n    const byteToCharMap = webSafe ? this.byteToCharMapWebSafe_ : this.byteToCharMap_;\n    const output = [];\n    for (let i = 0; i < input.length; i += 3) {\n      const byte1 = input[i];\n      const haveByte2 = i + 1 < input.length;\n      const byte2 = haveByte2 ? input[i + 1] : 0;\n      const haveByte3 = i + 2 < input.length;\n      const byte3 = haveByte3 ? input[i + 2] : 0;\n      const outByte1 = byte1 >> 2;\n      const outByte2 = (byte1 & 0x03) << 4 | byte2 >> 4;\n      let outByte3 = (byte2 & 0x0f) << 2 | byte3 >> 6;\n      let outByte4 = byte3 & 0x3f;\n      if (!haveByte3) {\n        outByte4 = 64;\n        if (!haveByte2) {\n          outByte3 = 64;\n        }\n      }\n      output.push(byteToCharMap[outByte1], byteToCharMap[outByte2], byteToCharMap[outByte3], byteToCharMap[outByte4]);\n    }\n    return output.join('');\n  },\n  /**\n   * Base64-encode a string.\n   *\n   * @param input A string to encode.\n   * @param webSafe If true, we should use the\n   *     alternative alphabet.\n   * @return The base64 encoded string.\n   */\n  encodeString(input, webSafe) {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return btoa(input);\n    }\n    return this.encodeByteArray(stringToByteArray$1(input), webSafe);\n  },\n  /**\n   * Base64-decode a string.\n   *\n   * @param input to decode.\n   * @param webSafe True if we should use the\n   *     alternative alphabet.\n   * @return string representing the decoded value.\n   */\n  decodeString(input, webSafe) {\n    // Shortcut for Mozilla browsers that implement\n    // a native base64 encoder in the form of \"btoa/atob\"\n    if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n      return atob(input);\n    }\n    return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n  },\n  /**\n   * Base64-decode a string.\n   *\n   * In base-64 decoding, groups of four characters are converted into three\n   * bytes.  If the encoder did not apply padding, the input length may not\n   * be a multiple of 4.\n   *\n   * In this case, the last group will have fewer than 4 characters, and\n   * padding will be inferred.  If the group has one or two characters, it decodes\n   * to one byte.  If the group has three characters, it decodes to two bytes.\n   *\n   * @param input Input to decode.\n   * @param webSafe True if we should use the web-safe alphabet.\n   * @return bytes representing the decoded value.\n   */\n  decodeStringToByteArray(input, webSafe) {\n    this.init_();\n    const charToByteMap = webSafe ? this.charToByteMapWebSafe_ : this.charToByteMap_;\n    const output = [];\n    for (let i = 0; i < input.length;) {\n      const byte1 = charToByteMap[input.charAt(i++)];\n      const haveByte2 = i < input.length;\n      const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n      ++i;\n      const haveByte3 = i < input.length;\n      const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n      const haveByte4 = i < input.length;\n      const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n      ++i;\n      if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n        throw new DecodeBase64StringError();\n      }\n      const outByte1 = byte1 << 2 | byte2 >> 4;\n      output.push(outByte1);\n      if (byte3 !== 64) {\n        const outByte2 = byte2 << 4 & 0xf0 | byte3 >> 2;\n        output.push(outByte2);\n        if (byte4 !== 64) {\n          const outByte3 = byte3 << 6 & 0xc0 | byte4;\n          output.push(outByte3);\n        }\n      }\n    }\n    return output;\n  },\n  /**\n   * Lazy static initialization function. Called before\n   * accessing any of the static map variables.\n   * @private\n   */\n  init_() {\n    if (!this.byteToCharMap_) {\n      this.byteToCharMap_ = {};\n      this.charToByteMap_ = {};\n      this.byteToCharMapWebSafe_ = {};\n      this.charToByteMapWebSafe_ = {};\n      // We want quick mappings back and forth, so we precompute two maps.\n      for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n        this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n        this.charToByteMap_[this.byteToCharMap_[i]] = i;\n        this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n        this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n        // Be forgiving when decoding and correctly decode both encodings.\n        if (i >= this.ENCODED_VALS_BASE.length) {\n          this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n          this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n        }\n      }\n    }\n  }\n};\n/**\n * An error encountered while decoding base64 string.\n */\nclass DecodeBase64StringError extends Error {\n  constructor() {\n    super(...arguments);\n    this.name = 'DecodeBase64StringError';\n  }\n}\n/**\n * URL-safe base64 encoding\n */\nconst base64Encode = function (str) {\n  const utf8Bytes = stringToByteArray$1(str);\n  return base64.encodeByteArray(utf8Bytes, true);\n};\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nconst base64urlEncodeWithoutPadding = function (str) {\n  // Use base64url encoding and remove padding in the end (dot characters).\n  return base64Encode(str).replace(/\\./g, '');\n};\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nconst base64Decode = function (str) {\n  try {\n    return base64.decodeString(str, true);\n  } catch (e) {\n    console.error('base64Decode failed: ', e);\n  }\n  return null;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nfunction deepCopy(value) {\n  return deepExtend(undefined, value);\n}\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nfunction deepExtend(target, source) {\n  if (!(source instanceof Object)) {\n    return source;\n  }\n  switch (source.constructor) {\n    case Date:\n      // Treat Dates like scalars; if the target date object had any child\n      // properties - they will be lost!\n      const dateValue = source;\n      return new Date(dateValue.getTime());\n    case Object:\n      if (target === undefined) {\n        target = {};\n      }\n      break;\n    case Array:\n      // Always copy the array source and overwrite the target.\n      target = [];\n      break;\n    default:\n      // Not a plain Object - treat it as a scalar.\n      return source;\n  }\n  for (const prop in source) {\n    // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n    if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n      continue;\n    }\n    target[prop] = deepExtend(target[prop], source[prop]);\n  }\n  return target;\n}\nfunction isValidKey(key) {\n  return key !== '__proto__';\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nfunction getGlobal() {\n  if (typeof self !== 'undefined') {\n    return self;\n  }\n  if (typeof window !== 'undefined') {\n    return window;\n  }\n  if (typeof global !== 'undefined') {\n    return global;\n  }\n  throw new Error('Unable to locate global object.');\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst getDefaultsFromGlobal = () => getGlobal().__FIREBASE_DEFAULTS__;\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = () => {\n  if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n    return;\n  }\n  const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n  if (defaultsJsonString) {\n    return JSON.parse(defaultsJsonString);\n  }\n};\nconst getDefaultsFromCookie = () => {\n  if (typeof document === 'undefined') {\n    return;\n  }\n  let match;\n  try {\n    match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n  } catch (e) {\n    // Some environments such as Angular Universal SSR have a\n    // `document` object but error on accessing `document.cookie`.\n    return;\n  }\n  const decoded = match && base64Decode(match[1]);\n  return decoded && JSON.parse(decoded);\n};\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nconst getDefaults = () => {\n  try {\n    return getDefaultsFromPostinstall() || getDefaultsFromGlobal() || getDefaultsFromEnvVariable() || getDefaultsFromCookie();\n  } catch (e) {\n    /**\n     * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n     * to any environment case we have not accounted for. Log to\n     * info instead of swallowing so we can find these unknown cases\n     * and add paths for them if needed.\n     */\n    console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n    return;\n  }\n};\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nconst getDefaultEmulatorHost = productName => {\n  var _a, _b;\n  return (_b = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.emulatorHosts) === null || _b === void 0 ? void 0 : _b[productName];\n};\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nconst getDefaultEmulatorHostnameAndPort = productName => {\n  const host = getDefaultEmulatorHost(productName);\n  if (!host) {\n    return undefined;\n  }\n  const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n  if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n    throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n  }\n  // eslint-disable-next-line no-restricted-globals\n  const port = parseInt(host.substring(separatorIndex + 1), 10);\n  if (host[0] === '[') {\n    // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n    return [host.substring(1, separatorIndex - 1), port];\n  } else {\n    return [host.substring(0, separatorIndex), port];\n  }\n};\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nconst getDefaultAppConfig = () => {\n  var _a;\n  return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.config;\n};\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nconst getExperimentalSetting = name => {\n  var _a;\n  return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a[`_${name}`];\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Deferred {\n  constructor() {\n    this.reject = () => {};\n    this.resolve = () => {};\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = resolve;\n      this.reject = reject;\n    });\n  }\n  /**\n   * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n   * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n   * and returns a node-style callback which will resolve or reject the Deferred's promise.\n   */\n  wrapCallback(callback) {\n    return (error, value) => {\n      if (error) {\n        this.reject(error);\n      } else {\n        this.resolve(value);\n      }\n      if (typeof callback === 'function') {\n        // Attaching noop handler just in case developer wasn't expecting\n        // promises\n        this.promise.catch(() => {});\n        // Some of our callbacks don't expect a value and our own tests\n        // assert that the parameter length is 1\n        if (callback.length === 1) {\n          callback(error);\n        } else {\n          callback(error, value);\n        }\n      }\n    };\n  }\n}\n\n/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nfunction isCloudWorkstation(host) {\n  return host.endsWith('.cloudworkstations.dev');\n}\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nfunction pingServer(_x) {\n  return _pingServer.apply(this, arguments);\n}\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction _pingServer() {\n  _pingServer = _asyncToGenerator(function* (endpoint) {\n    const result = yield fetch(endpoint, {\n      credentials: 'include'\n    });\n    return result.ok;\n  });\n  return _pingServer.apply(this, arguments);\n}\nfunction createMockUserToken(token, projectId) {\n  if (token.uid) {\n    throw new Error('The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.');\n  }\n  // Unsecured JWTs use \"none\" as the algorithm.\n  const header = {\n    alg: 'none',\n    type: 'JWT'\n  };\n  const project = projectId || 'demo-project';\n  const iat = token.iat || 0;\n  const sub = token.sub || token.user_id;\n  if (!sub) {\n    throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n  }\n  const payload = Object.assign({\n    // Set all required fields to decent defaults\n    iss: `https://securetoken.google.com/${project}`,\n    aud: project,\n    iat,\n    exp: iat + 3600,\n    auth_time: iat,\n    sub,\n    user_id: sub,\n    firebase: {\n      sign_in_provider: 'custom',\n      identities: {}\n    }\n  }, token);\n  // Unsecured JWTs use the empty string as a signature.\n  const signature = '';\n  return [base64urlEncodeWithoutPadding(JSON.stringify(header)), base64urlEncodeWithoutPadding(JSON.stringify(payload)), signature].join('.');\n}\nconst emulatorStatus = {};\n// Checks whether any products are running on an emulator\nfunction getEmulatorSummary() {\n  const summary = {\n    prod: [],\n    emulator: []\n  };\n  for (const key of Object.keys(emulatorStatus)) {\n    if (emulatorStatus[key]) {\n      summary.emulator.push(key);\n    } else {\n      summary.prod.push(key);\n    }\n  }\n  return summary;\n}\nfunction getOrCreateEl(id) {\n  let parentDiv = document.getElementById(id);\n  let created = false;\n  if (!parentDiv) {\n    parentDiv = document.createElement('div');\n    parentDiv.setAttribute('id', id);\n    created = true;\n  }\n  return {\n    created,\n    element: parentDiv\n  };\n}\nlet previouslyDismissed = false;\n/**\n * Updates Emulator Banner. Primarily used for Firebase Studio\n * @param name\n * @param isRunningEmulator\n * @public\n */\nfunction updateEmulatorBanner(name, isRunningEmulator) {\n  if (typeof window === 'undefined' || typeof document === 'undefined' || !isCloudWorkstation(window.location.host) || emulatorStatus[name] === isRunningEmulator || emulatorStatus[name] ||\n  // If already set to use emulator, can't go back to prod.\n  previouslyDismissed) {\n    return;\n  }\n  emulatorStatus[name] = isRunningEmulator;\n  function prefixedId(id) {\n    return `__firebase__banner__${id}`;\n  }\n  const bannerId = '__firebase__banner';\n  const summary = getEmulatorSummary();\n  const showError = summary.prod.length > 0;\n  function tearDown() {\n    const element = document.getElementById(bannerId);\n    if (element) {\n      element.remove();\n    }\n  }\n  function setupBannerStyles(bannerEl) {\n    bannerEl.style.display = 'flex';\n    bannerEl.style.background = '#7faaf0';\n    bannerEl.style.position = 'fixed';\n    bannerEl.style.bottom = '5px';\n    bannerEl.style.left = '5px';\n    bannerEl.style.padding = '.5em';\n    bannerEl.style.borderRadius = '5px';\n    bannerEl.style.alignItems = 'center';\n  }\n  function setupIconStyles(prependIcon, iconId) {\n    prependIcon.setAttribute('width', '24');\n    prependIcon.setAttribute('id', iconId);\n    prependIcon.setAttribute('height', '24');\n    prependIcon.setAttribute('viewBox', '0 0 24 24');\n    prependIcon.setAttribute('fill', 'none');\n    prependIcon.style.marginLeft = '-6px';\n  }\n  function setupCloseBtn() {\n    const closeBtn = document.createElement('span');\n    closeBtn.style.cursor = 'pointer';\n    closeBtn.style.marginLeft = '16px';\n    closeBtn.style.fontSize = '24px';\n    closeBtn.innerHTML = ' &times;';\n    closeBtn.onclick = () => {\n      previouslyDismissed = true;\n      tearDown();\n    };\n    return closeBtn;\n  }\n  function setupLinkStyles(learnMoreLink, learnMoreId) {\n    learnMoreLink.setAttribute('id', learnMoreId);\n    learnMoreLink.innerText = 'Learn more';\n    learnMoreLink.href = 'https://firebase.google.com/docs/studio/preview-apps#preview-backend';\n    learnMoreLink.setAttribute('target', '__blank');\n    learnMoreLink.style.paddingLeft = '5px';\n    learnMoreLink.style.textDecoration = 'underline';\n  }\n  function setupDom() {\n    const banner = getOrCreateEl(bannerId);\n    const firebaseTextId = prefixedId('text');\n    const firebaseText = document.getElementById(firebaseTextId) || document.createElement('span');\n    const learnMoreId = prefixedId('learnmore');\n    const learnMoreLink = document.getElementById(learnMoreId) || document.createElement('a');\n    const prependIconId = prefixedId('preprendIcon');\n    const prependIcon = document.getElementById(prependIconId) || document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    if (banner.created) {\n      // update styles\n      const bannerEl = banner.element;\n      setupBannerStyles(bannerEl);\n      setupLinkStyles(learnMoreLink, learnMoreId);\n      const closeBtn = setupCloseBtn();\n      setupIconStyles(prependIcon, prependIconId);\n      bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);\n      document.body.appendChild(bannerEl);\n    }\n    if (showError) {\n      firebaseText.innerText = `Preview backend disconnected.`;\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6013_33858)\">\n<path d=\"M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6013_33858\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n    } else {\n      prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6083_34804)\">\n<path d=\"M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6083_34804\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n      firebaseText.innerText = 'Preview backend running in this workspace.';\n    }\n    firebaseText.setAttribute('id', firebaseTextId);\n  }\n  if (document.readyState === 'loading') {\n    window.addEventListener('DOMContentLoaded', setupDom);\n  } else {\n    setupDom();\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nfunction getUA() {\n  if (typeof navigator !== 'undefined' && typeof navigator['userAgent'] === 'string') {\n    return navigator['userAgent'];\n  } else {\n    return '';\n  }\n}\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nfunction isMobileCordova() {\n  return typeof window !== 'undefined' &&\n  // @ts-ignore Setting up an broadly applicable index signature for Window\n  // just to deal with this case would probably be a bad idea.\n  !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) && /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA());\n}\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nfunction isNode() {\n  var _a;\n  const forceEnvironment = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.forceEnvironment;\n  if (forceEnvironment === 'node') {\n    return true;\n  } else if (forceEnvironment === 'browser') {\n    return false;\n  }\n  try {\n    return Object.prototype.toString.call(global.process) === '[object process]';\n  } catch (e) {\n    return false;\n  }\n}\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nfunction isBrowser() {\n  return typeof window !== 'undefined' || isWebWorker();\n}\n/**\n * Detect Web Worker context.\n */\nfunction isWebWorker() {\n  return typeof WorkerGlobalScope !== 'undefined' && typeof self !== 'undefined' && self instanceof WorkerGlobalScope;\n}\n/**\n * Detect Cloudflare Worker context.\n */\nfunction isCloudflareWorker() {\n  return typeof navigator !== 'undefined' && navigator.userAgent === 'Cloudflare-Workers';\n}\nfunction isBrowserExtension() {\n  const runtime = typeof chrome === 'object' ? chrome.runtime : typeof browser === 'object' ? browser.runtime : undefined;\n  return typeof runtime === 'object' && runtime.id !== undefined;\n}\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nfunction isReactNative() {\n  return typeof navigator === 'object' && navigator['product'] === 'ReactNative';\n}\n/** Detects Electron apps. */\nfunction isElectron() {\n  return getUA().indexOf('Electron/') >= 0;\n}\n/** Detects Internet Explorer. */\nfunction isIE() {\n  const ua = getUA();\n  return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n/** Detects Universal Windows Platform apps. */\nfunction isUWP() {\n  return getUA().indexOf('MSAppHost/') >= 0;\n}\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nfunction isNodeSdk() {\n  return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n/** Returns true if we are running in Safari. */\nfunction isSafari() {\n  return !isNode() && !!navigator.userAgent && navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome');\n}\n/** Returns true if we are running in Safari or WebKit */\nfunction isSafariOrWebkit() {\n  return !isNode() && !!navigator.userAgent && (navigator.userAgent.includes('Safari') || navigator.userAgent.includes('WebKit')) && !navigator.userAgent.includes('Chrome');\n}\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nfunction isIndexedDBAvailable() {\n  try {\n    return typeof indexedDB === 'object';\n  } catch (e) {\n    return false;\n  }\n}\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nfunction validateIndexedDBOpenable() {\n  return new Promise((resolve, reject) => {\n    try {\n      let preExist = true;\n      const DB_CHECK_NAME = 'validate-browser-context-for-indexeddb-analytics-module';\n      const request = self.indexedDB.open(DB_CHECK_NAME);\n      request.onsuccess = () => {\n        request.result.close();\n        // delete database only when it doesn't pre-exist\n        if (!preExist) {\n          self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n        }\n        resolve(true);\n      };\n      request.onupgradeneeded = () => {\n        preExist = false;\n      };\n      request.onerror = () => {\n        var _a;\n        reject(((_a = request.error) === null || _a === void 0 ? void 0 : _a.message) || '');\n      };\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nfunction areCookiesEnabled() {\n  if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n    return false;\n  }\n  return true;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\nconst ERROR_NAME = 'FirebaseError';\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nclass FirebaseError extends Error {\n  constructor( /** The error code for this error. */\n  code, message, /** Custom data for this error. */\n  customData) {\n    super(message);\n    this.code = code;\n    this.customData = customData;\n    /** The custom name for all FirebaseErrors. */\n    this.name = ERROR_NAME;\n    // Fix For ES5\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, FirebaseError.prototype);\n    // Maintains proper stack trace for where our error was thrown.\n    // Only available on V8.\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, ErrorFactory.prototype.create);\n    }\n  }\n}\nclass ErrorFactory {\n  constructor(service, serviceName, errors) {\n    this.service = service;\n    this.serviceName = serviceName;\n    this.errors = errors;\n  }\n  create(code, ...data) {\n    const customData = data[0] || {};\n    const fullCode = `${this.service}/${code}`;\n    const template = this.errors[code];\n    const message = template ? replaceTemplate(template, customData) : 'Error';\n    // Service Name: Error message (service/code).\n    const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n    const error = new FirebaseError(fullCode, fullMessage, customData);\n    return error;\n  }\n}\nfunction replaceTemplate(template, data) {\n  return template.replace(PATTERN, (_, key) => {\n    const value = data[key];\n    return value != null ? String(value) : `<${key}?>`;\n  });\n}\nconst PATTERN = /\\{\\$([^}]+)}/g;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Evaluates a JSON string into a javascript object.\n *\n * @param {string} str A string containing JSON.\n * @return {*} The javascript object representing the specified JSON.\n */\nfunction jsonEval(str) {\n  return JSON.parse(str);\n}\n/**\n * Returns JSON representing a javascript object.\n * @param {*} data JavaScript object to be stringified.\n * @return {string} The JSON contents of the object.\n */\nfunction stringify(data) {\n  return JSON.stringify(data);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Decodes a Firebase auth. token into constituent parts.\n *\n * Notes:\n * - May return with invalid / incomplete claims if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst decode = function (token) {\n  let header = {},\n    claims = {},\n    data = {},\n    signature = '';\n  try {\n    const parts = token.split('.');\n    header = jsonEval(base64Decode(parts[0]) || '');\n    claims = jsonEval(base64Decode(parts[1]) || '');\n    signature = parts[2];\n    data = claims['d'] || {};\n    delete claims['d'];\n  } catch (e) {}\n  return {\n    header,\n    claims,\n    data,\n    signature\n  };\n};\n/**\n * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the\n * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isValidTimestamp = function (token) {\n  const claims = decode(token).claims;\n  const now = Math.floor(new Date().getTime() / 1000);\n  let validSince = 0,\n    validUntil = 0;\n  if (typeof claims === 'object') {\n    if (claims.hasOwnProperty('nbf')) {\n      validSince = claims['nbf'];\n    } else if (claims.hasOwnProperty('iat')) {\n      validSince = claims['iat'];\n    }\n    if (claims.hasOwnProperty('exp')) {\n      validUntil = claims['exp'];\n    } else {\n      // token will expire after 24h by default\n      validUntil = validSince + 86400;\n    }\n  }\n  return !!now && !!validSince && !!validUntil && now >= validSince && now <= validUntil;\n};\n/**\n * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.\n *\n * Notes:\n * - May return null if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst issuedAtTime = function (token) {\n  const claims = decode(token).claims;\n  if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {\n    return claims['iat'];\n  }\n  return null;\n};\n/**\n * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isValidFormat = function (token) {\n  const decoded = decode(token),\n    claims = decoded.claims;\n  return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');\n};\n/**\n * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isAdmin = function (token) {\n  const claims = decode(token).claims;\n  return typeof claims === 'object' && claims['admin'] === true;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction contains(obj, key) {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction safeGet(obj, key) {\n  if (Object.prototype.hasOwnProperty.call(obj, key)) {\n    return obj[key];\n  } else {\n    return undefined;\n  }\n}\nfunction isEmpty(obj) {\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction map(obj, fn, contextObj) {\n  const res = {};\n  for (const key in obj) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n      res[key] = fn.call(contextObj, obj[key], key, obj);\n    }\n  }\n  return res;\n}\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  const aKeys = Object.keys(a);\n  const bKeys = Object.keys(b);\n  for (const k of aKeys) {\n    if (!bKeys.includes(k)) {\n      return false;\n    }\n    const aProp = a[k];\n    const bProp = b[k];\n    if (isObject(aProp) && isObject(bProp)) {\n      if (!deepEqual(aProp, bProp)) {\n        return false;\n      }\n    } else if (aProp !== bProp) {\n      return false;\n    }\n  }\n  for (const k of bKeys) {\n    if (!aKeys.includes(k)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isObject(thing) {\n  return thing !== null && typeof thing === 'object';\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Rejects if the given promise doesn't resolve in timeInMS milliseconds.\n * @internal\n */\nfunction promiseWithTimeout(promise, timeInMS = 2000) {\n  const deferredPromise = new Deferred();\n  setTimeout(() => deferredPromise.reject('timeout!'), timeInMS);\n  promise.then(deferredPromise.resolve, deferredPromise.reject);\n  return deferredPromise.promise;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a\n * params object (e.g. {arg: 'val', arg2: 'val2'})\n * Note: You must prepend it with ? when adding it to a URL.\n */\nfunction querystring(querystringParams) {\n  const params = [];\n  for (const [key, value] of Object.entries(querystringParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(arrayVal => {\n        params.push(encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal));\n      });\n    } else {\n      params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n    }\n  }\n  return params.length ? '&' + params.join('&') : '';\n}\n/**\n * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object\n * (e.g. {arg: 'val', arg2: 'val2'})\n */\nfunction querystringDecode(querystring) {\n  const obj = {};\n  const tokens = querystring.replace(/^\\?/, '').split('&');\n  tokens.forEach(token => {\n    if (token) {\n      const [key, value] = token.split('=');\n      obj[decodeURIComponent(key)] = decodeURIComponent(value);\n    }\n  });\n  return obj;\n}\n/**\n * Extract the query string part of a URL, including the leading question mark (if present).\n */\nfunction extractQuerystring(url) {\n  const queryStart = url.indexOf('?');\n  if (!queryStart) {\n    return '';\n  }\n  const fragmentStart = url.indexOf('#', queryStart);\n  return url.substring(queryStart, fragmentStart > 0 ? fragmentStart : undefined);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview SHA-1 cryptographic hash.\n * Variable names follow the notation in FIPS PUB 180-3:\n * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.\n *\n * Usage:\n *   var sha1 = new sha1();\n *   sha1.update(bytes);\n *   var hash = sha1.digest();\n *\n * Performance:\n *   Chrome 23:   ~400 Mbit/s\n *   Firefox 16:  ~250 Mbit/s\n *\n */\n/**\n * SHA-1 cryptographic hash constructor.\n *\n * The properties declared here are discussed in the above algorithm document.\n * @constructor\n * @final\n * @struct\n */\nclass Sha1 {\n  constructor() {\n    /**\n     * Holds the previous values of accumulated variables a-e in the compress_\n     * function.\n     * @private\n     */\n    this.chain_ = [];\n    /**\n     * A buffer holding the partially computed hash result.\n     * @private\n     */\n    this.buf_ = [];\n    /**\n     * An array of 80 bytes, each a part of the message to be hashed.  Referred to\n     * as the message schedule in the docs.\n     * @private\n     */\n    this.W_ = [];\n    /**\n     * Contains data needed to pad messages less than 64 bytes.\n     * @private\n     */\n    this.pad_ = [];\n    /**\n     * @private {number}\n     */\n    this.inbuf_ = 0;\n    /**\n     * @private {number}\n     */\n    this.total_ = 0;\n    this.blockSize = 512 / 8;\n    this.pad_[0] = 128;\n    for (let i = 1; i < this.blockSize; ++i) {\n      this.pad_[i] = 0;\n    }\n    this.reset();\n  }\n  reset() {\n    this.chain_[0] = 0x67452301;\n    this.chain_[1] = 0xefcdab89;\n    this.chain_[2] = 0x98badcfe;\n    this.chain_[3] = 0x10325476;\n    this.chain_[4] = 0xc3d2e1f0;\n    this.inbuf_ = 0;\n    this.total_ = 0;\n  }\n  /**\n   * Internal compress helper function.\n   * @param buf Block to compress.\n   * @param offset Offset of the block in the buffer.\n   * @private\n   */\n  compress_(buf, offset) {\n    if (!offset) {\n      offset = 0;\n    }\n    const W = this.W_;\n    // get 16 big endian words\n    if (typeof buf === 'string') {\n      for (let i = 0; i < 16; i++) {\n        // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS\n        // have a bug that turns the post-increment ++ operator into pre-increment\n        // during JIT compilation.  We have code that depends heavily on SHA-1 for\n        // correctness and which is affected by this bug, so I've removed all uses\n        // of post-increment ++ in which the result value is used.  We can revert\n        // this change once the Safari bug\n        // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and\n        // most clients have been updated.\n        W[i] = buf.charCodeAt(offset) << 24 | buf.charCodeAt(offset + 1) << 16 | buf.charCodeAt(offset + 2) << 8 | buf.charCodeAt(offset + 3);\n        offset += 4;\n      }\n    } else {\n      for (let i = 0; i < 16; i++) {\n        W[i] = buf[offset] << 24 | buf[offset + 1] << 16 | buf[offset + 2] << 8 | buf[offset + 3];\n        offset += 4;\n      }\n    }\n    // expand to 80 words\n    for (let i = 16; i < 80; i++) {\n      const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n      W[i] = (t << 1 | t >>> 31) & 0xffffffff;\n    }\n    let a = this.chain_[0];\n    let b = this.chain_[1];\n    let c = this.chain_[2];\n    let d = this.chain_[3];\n    let e = this.chain_[4];\n    let f, k;\n    // TODO(user): Try to unroll this loop to speed up the computation.\n    for (let i = 0; i < 80; i++) {\n      if (i < 40) {\n        if (i < 20) {\n          f = d ^ b & (c ^ d);\n          k = 0x5a827999;\n        } else {\n          f = b ^ c ^ d;\n          k = 0x6ed9eba1;\n        }\n      } else {\n        if (i < 60) {\n          f = b & c | d & (b | c);\n          k = 0x8f1bbcdc;\n        } else {\n          f = b ^ c ^ d;\n          k = 0xca62c1d6;\n        }\n      }\n      const t = (a << 5 | a >>> 27) + f + e + k + W[i] & 0xffffffff;\n      e = d;\n      d = c;\n      c = (b << 30 | b >>> 2) & 0xffffffff;\n      b = a;\n      a = t;\n    }\n    this.chain_[0] = this.chain_[0] + a & 0xffffffff;\n    this.chain_[1] = this.chain_[1] + b & 0xffffffff;\n    this.chain_[2] = this.chain_[2] + c & 0xffffffff;\n    this.chain_[3] = this.chain_[3] + d & 0xffffffff;\n    this.chain_[4] = this.chain_[4] + e & 0xffffffff;\n  }\n  update(bytes, length) {\n    // TODO(johnlenz): tighten the function signature and remove this check\n    if (bytes == null) {\n      return;\n    }\n    if (length === undefined) {\n      length = bytes.length;\n    }\n    const lengthMinusBlock = length - this.blockSize;\n    let n = 0;\n    // Using local instead of member variables gives ~5% speedup on Firefox 16.\n    const buf = this.buf_;\n    let inbuf = this.inbuf_;\n    // The outer while loop should execute at most twice.\n    while (n < length) {\n      // When we have no data in the block to top up, we can directly process the\n      // input buffer (assuming it contains sufficient data). This gives ~25%\n      // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that\n      // the data is provided in large chunks (or in multiples of 64 bytes).\n      if (inbuf === 0) {\n        while (n <= lengthMinusBlock) {\n          this.compress_(bytes, n);\n          n += this.blockSize;\n        }\n      }\n      if (typeof bytes === 'string') {\n        while (n < length) {\n          buf[inbuf] = bytes.charCodeAt(n);\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      } else {\n        while (n < length) {\n          buf[inbuf] = bytes[n];\n          ++inbuf;\n          ++n;\n          if (inbuf === this.blockSize) {\n            this.compress_(buf);\n            inbuf = 0;\n            // Jump to the outer loop so we use the full-block optimization.\n            break;\n          }\n        }\n      }\n    }\n    this.inbuf_ = inbuf;\n    this.total_ += length;\n  }\n  /** @override */\n  digest() {\n    const digest = [];\n    let totalBits = this.total_ * 8;\n    // Add pad 0x80 0x00*.\n    if (this.inbuf_ < 56) {\n      this.update(this.pad_, 56 - this.inbuf_);\n    } else {\n      this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));\n    }\n    // Add # bits.\n    for (let i = this.blockSize - 1; i >= 56; i--) {\n      this.buf_[i] = totalBits & 255;\n      totalBits /= 256; // Don't use bit-shifting here!\n    }\n\n    this.compress_(this.buf_);\n    let n = 0;\n    for (let i = 0; i < 5; i++) {\n      for (let j = 24; j >= 0; j -= 8) {\n        digest[n] = this.chain_[i] >> j & 255;\n        ++n;\n      }\n    }\n    return digest;\n  }\n}\n\n/**\n * Helper to make a Subscribe function (just like Promise helps make a\n * Thenable).\n *\n * @param executor Function which can make calls to a single Observer\n *     as a proxy.\n * @param onNoObservers Callback when count of Observers goes to zero.\n */\nfunction createSubscribe(executor, onNoObservers) {\n  const proxy = new ObserverProxy(executor, onNoObservers);\n  return proxy.subscribe.bind(proxy);\n}\n/**\n * Implement fan-out for any number of Observers attached via a subscribe\n * function.\n */\nclass ObserverProxy {\n  /**\n   * @param executor Function which can make calls to a single Observer\n   *     as a proxy.\n   * @param onNoObservers Callback when count of Observers goes to zero.\n   */\n  constructor(executor, onNoObservers) {\n    this.observers = [];\n    this.unsubscribes = [];\n    this.observerCount = 0;\n    // Micro-task scheduling by calling task.then().\n    this.task = Promise.resolve();\n    this.finalized = false;\n    this.onNoObservers = onNoObservers;\n    // Call the executor asynchronously so subscribers that are called\n    // synchronously after the creation of the subscribe function\n    // can still receive the very first value generated in the executor.\n    this.task.then(() => {\n      executor(this);\n    }).catch(e => {\n      this.error(e);\n    });\n  }\n  next(value) {\n    this.forEachObserver(observer => {\n      observer.next(value);\n    });\n  }\n  error(error) {\n    this.forEachObserver(observer => {\n      observer.error(error);\n    });\n    this.close(error);\n  }\n  complete() {\n    this.forEachObserver(observer => {\n      observer.complete();\n    });\n    this.close();\n  }\n  /**\n   * Subscribe function that can be used to add an Observer to the fan-out list.\n   *\n   * - We require that no event is sent to a subscriber synchronously to their\n   *   call to subscribe().\n   */\n  subscribe(nextOrObserver, error, complete) {\n    let observer;\n    if (nextOrObserver === undefined && error === undefined && complete === undefined) {\n      throw new Error('Missing Observer.');\n    }\n    // Assemble an Observer object when passed as callback functions.\n    if (implementsAnyMethods(nextOrObserver, ['next', 'error', 'complete'])) {\n      observer = nextOrObserver;\n    } else {\n      observer = {\n        next: nextOrObserver,\n        error,\n        complete\n      };\n    }\n    if (observer.next === undefined) {\n      observer.next = noop;\n    }\n    if (observer.error === undefined) {\n      observer.error = noop;\n    }\n    if (observer.complete === undefined) {\n      observer.complete = noop;\n    }\n    const unsub = this.unsubscribeOne.bind(this, this.observers.length);\n    // Attempt to subscribe to a terminated Observable - we\n    // just respond to the Observer with the final error or complete\n    // event.\n    if (this.finalized) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this.task.then(() => {\n        try {\n          if (this.finalError) {\n            observer.error(this.finalError);\n          } else {\n            observer.complete();\n          }\n        } catch (e) {\n          // nothing\n        }\n        return;\n      });\n    }\n    this.observers.push(observer);\n    return unsub;\n  }\n  // Unsubscribe is synchronous - we guarantee that no events are sent to\n  // any unsubscribed Observer.\n  unsubscribeOne(i) {\n    if (this.observers === undefined || this.observers[i] === undefined) {\n      return;\n    }\n    delete this.observers[i];\n    this.observerCount -= 1;\n    if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n      this.onNoObservers(this);\n    }\n  }\n  forEachObserver(fn) {\n    if (this.finalized) {\n      // Already closed by previous event....just eat the additional values.\n      return;\n    }\n    // Since sendOne calls asynchronously - there is no chance that\n    // this.observers will become undefined.\n    for (let i = 0; i < this.observers.length; i++) {\n      this.sendOne(i, fn);\n    }\n  }\n  // Call the Observer via one of it's callback function. We are careful to\n  // confirm that the observe has not been unsubscribed since this asynchronous\n  // function had been queued.\n  sendOne(i, fn) {\n    // Execute the callback asynchronously\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      if (this.observers !== undefined && this.observers[i] !== undefined) {\n        try {\n          fn(this.observers[i]);\n        } catch (e) {\n          // Ignore exceptions raised in Observers or missing methods of an\n          // Observer.\n          // Log error to console. b/31404806\n          if (typeof console !== 'undefined' && console.error) {\n            console.error(e);\n          }\n        }\n      }\n    });\n  }\n  close(err) {\n    if (this.finalized) {\n      return;\n    }\n    this.finalized = true;\n    if (err !== undefined) {\n      this.finalError = err;\n    }\n    // Proxy is no longer needed - garbage collect references\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this.task.then(() => {\n      this.observers = undefined;\n      this.onNoObservers = undefined;\n    });\n  }\n}\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction async(fn, onError) {\n  return (...args) => {\n    Promise.resolve(true).then(() => {\n      fn(...args);\n    }).catch(error => {\n      if (onError) {\n        onError(error);\n      }\n    });\n  };\n}\n/**\n * Return true if the object passed in implements any of the named methods.\n */\nfunction implementsAnyMethods(obj, methods) {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n  for (const method of methods) {\n    if (method in obj && typeof obj[method] === 'function') {\n      return true;\n    }\n  }\n  return false;\n}\nfunction noop() {\n  // do nothing\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Check to make sure the appropriate number of arguments are provided for a public function.\n * Throws an error if it fails.\n *\n * @param fnName The function name\n * @param minCount The minimum number of arguments to allow for the function call\n * @param maxCount The maximum number of argument to allow for the function call\n * @param argCount The actual number of arguments provided.\n */\nconst validateArgCount = function (fnName, minCount, maxCount, argCount) {\n  let argError;\n  if (argCount < minCount) {\n    argError = 'at least ' + minCount;\n  } else if (argCount > maxCount) {\n    argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;\n  }\n  if (argError) {\n    const error = fnName + ' failed: Was called with ' + argCount + (argCount === 1 ? ' argument.' : ' arguments.') + ' Expects ' + argError + '.';\n    throw new Error(error);\n  }\n};\n/**\n * Generates a string to prefix an error message about failed argument validation\n *\n * @param fnName The function name\n * @param argName The name of the argument\n * @return The prefix to add to the error thrown for validation.\n */\nfunction errorPrefix(fnName, argName) {\n  return `${fnName} failed: ${argName} argument `;\n}\n/**\n * @param fnName\n * @param argumentNumber\n * @param namespace\n * @param optional\n */\nfunction validateNamespace(fnName, namespace, optional) {\n  if (optional && !namespace) {\n    return;\n  }\n  if (typeof namespace !== 'string') {\n    //TODO: I should do more validation here. We only allow certain chars in namespaces.\n    throw new Error(errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.');\n  }\n}\nfunction validateCallback(fnName, argumentName,\n// eslint-disable-next-line @typescript-eslint/ban-types\ncallback, optional) {\n  if (optional && !callback) {\n    return;\n  }\n  if (typeof callback !== 'function') {\n    throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid function.');\n  }\n}\nfunction validateContextObject(fnName, argumentName, context, optional) {\n  if (optional && !context) {\n    return;\n  }\n  if (typeof context !== 'object' || context === null) {\n    throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid context object.');\n  }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they\n// automatically replaced '\\r\\n' with '\\n', and they didn't handle surrogate pairs,\n// so it's been modified.\n// Note that not all Unicode characters appear as single characters in JavaScript strings.\n// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters\n// use 2 characters in JavaScript.  All 4-byte UTF-8 characters begin with a first\n// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate\n// pair).\n// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3\n/**\n * @param {string} str\n * @return {Array}\n */\nconst stringToByteArray = function (str) {\n  const out = [];\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    let c = str.charCodeAt(i);\n    // Is this the lead surrogate in a surrogate pair?\n    if (c >= 0xd800 && c <= 0xdbff) {\n      const high = c - 0xd800; // the high 10 bits.\n      i++;\n      assert(i < str.length, 'Surrogate pair missing trail surrogate.');\n      const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.\n      c = 0x10000 + (high << 10) + low;\n    }\n    if (c < 128) {\n      out[p++] = c;\n    } else if (c < 2048) {\n      out[p++] = c >> 6 | 192;\n      out[p++] = c & 63 | 128;\n    } else if (c < 65536) {\n      out[p++] = c >> 12 | 224;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    } else {\n      out[p++] = c >> 18 | 240;\n      out[p++] = c >> 12 & 63 | 128;\n      out[p++] = c >> 6 & 63 | 128;\n      out[p++] = c & 63 | 128;\n    }\n  }\n  return out;\n};\n/**\n * Calculate length without actually converting; useful for doing cheaper validation.\n * @param {string} str\n * @return {number}\n */\nconst stringLength = function (str) {\n  let p = 0;\n  for (let i = 0; i < str.length; i++) {\n    const c = str.charCodeAt(i);\n    if (c < 128) {\n      p++;\n    } else if (c < 2048) {\n      p += 2;\n    } else if (c >= 0xd800 && c <= 0xdbff) {\n      // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.\n      p += 4;\n      i++; // skip trail surrogate.\n    } else {\n      p += 3;\n    }\n  }\n  return p;\n};\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nconst MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nconst RANDOM_FACTOR = 0.5;\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nfunction calculateBackoffMillis(backoffCount, intervalMillis = DEFAULT_INTERVAL_MILLIS, backoffFactor = DEFAULT_BACKOFF_FACTOR) {\n  // Calculates an exponentially increasing value.\n  // Deviation: calculates value from count and a constant interval, so we only need to save value\n  // and count to restore state.\n  const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n  // A random \"fuzz\" to avoid waves of retries.\n  // Deviation: randomFactor is required.\n  const randomWait = Math.round(\n  // A fraction of the backoff value to add/subtract.\n  // Deviation: changes multiplication order to improve readability.\n  RANDOM_FACTOR * currBaseValue * (\n  // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n  // if we add or subtract.\n  Math.random() - 0.5) * 2);\n  // Limits backoff to max to avoid effectively permanent backoff.\n  return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provide English ordinal letters after a number\n */\nfunction ordinal(i) {\n  if (!Number.isFinite(i)) {\n    return `${i}`;\n  }\n  return i + indicator(i);\n}\nfunction indicator(i) {\n  i = Math.abs(i);\n  const cent = i % 100;\n  if (cent >= 10 && cent <= 20) {\n    return 'th';\n  }\n  const dec = i % 10;\n  if (dec === 1) {\n    return 'st';\n  }\n  if (dec === 2) {\n    return 'nd';\n  }\n  if (dec === 3) {\n    return 'rd';\n  }\n  return 'th';\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction getModularInstance(service) {\n  if (service && service._delegate) {\n    return service._delegate;\n  } else {\n    return service;\n  }\n}\nexport { CONSTANTS, DecodeBase64StringError, Deferred, ErrorFactory, FirebaseError, MAX_VALUE_MILLIS, RANDOM_FACTOR, Sha1, areCookiesEnabled, assert, assertionError, async, base64, base64Decode, base64Encode, base64urlEncodeWithoutPadding, calculateBackoffMillis, contains, createMockUserToken, createSubscribe, decode, deepCopy, deepEqual, deepExtend, errorPrefix, extractQuerystring, getDefaultAppConfig, getDefaultEmulatorHost, getDefaultEmulatorHostnameAndPort, getDefaults, getExperimentalSetting, getGlobal, getModularInstance, getUA, isAdmin, isBrowser, isBrowserExtension, isCloudWorkstation, isCloudflareWorker, isElectron, isEmpty, isIE, isIndexedDBAvailable, isMobileCordova, isNode, isNodeSdk, isReactNative, isSafari, isSafariOrWebkit, isUWP, isValidFormat, isValidTimestamp, isWebWorker, issuedAtTime, jsonEval, map, ordinal, pingServer, promiseWithTimeout, querystring, querystringDecode, safeGet, stringLength, stringToByteArray, stringify, updateEmulatorBanner, validateArgCount, validateCallback, validateContextObject, validateIndexedDBOpenable, validateNamespace };", "map": {"version": 3, "names": ["getDefaultsFromPostinstall", "CONSTANTS", "NODE_CLIENT", "NODE_ADMIN", "SDK_VERSION", "assert", "assertion", "message", "assertionError", "Error", "stringToByteArray$1", "str", "out", "p", "i", "length", "c", "charCodeAt", "byteArrayToString", "bytes", "pos", "c1", "String", "fromCharCode", "c2", "c3", "c4", "u", "join", "base64", "byteToCharMap_", "charToByteMap_", "byteToCharMapWebSafe_", "charToByteMapWebSafe_", "ENCODED_VALS_BASE", "ENCODED_VALS", "ENCODED_VALS_WEBSAFE", "HAS_NATIVE_SUPPORT", "atob", "encodeByteArray", "input", "webSafe", "Array", "isArray", "init_", "byteToCharMap", "output", "byte1", "haveByte2", "byte2", "haveByte3", "byte3", "outByte1", "outByte2", "outByte3", "outByte4", "push", "encodeString", "btoa", "decodeString", "decodeStringToByteArray", "charToByteMap", "char<PERSON>t", "haveByte4", "byte4", "DecodeBase64StringError", "constructor", "arguments", "name", "base64Encode", "utf8Bytes", "base64urlEncodeWithoutPadding", "replace", "base64Decode", "e", "console", "error", "deepCopy", "value", "deepExtend", "undefined", "target", "source", "Object", "Date", "dateValue", "getTime", "prop", "hasOwnProperty", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "getGlobal", "self", "window", "global", "getDefaultsFromGlobal", "__FIREBASE_DEFAULTS__", "getDefaultsFromEnvVariable", "process", "env", "defaultsJsonString", "JSON", "parse", "getDefaultsFromCookie", "document", "match", "cookie", "decoded", "getDefaults", "info", "getDefaultEmulatorHost", "productName", "_a", "_b", "emulatorHosts", "getDefaultEmulatorHostnameAndPort", "host", "separatorIndex", "lastIndexOf", "port", "parseInt", "substring", "getDefaultAppConfig", "config", "getExperimentalSetting", "Deferred", "reject", "resolve", "promise", "Promise", "wrapCallback", "callback", "catch", "isCloudWorkstation", "endsWith", "pingServer", "_x", "_pingServer", "apply", "_asyncToGenerator", "endpoint", "result", "fetch", "credentials", "ok", "createMockUserToken", "token", "projectId", "uid", "header", "alg", "type", "project", "iat", "sub", "user_id", "payload", "assign", "iss", "aud", "exp", "auth_time", "firebase", "sign_in_provider", "identities", "signature", "stringify", "emulatorStatus", "getEmulatorSummary", "summary", "prod", "emulator", "keys", "getOrCreateEl", "id", "parentDiv", "getElementById", "created", "createElement", "setAttribute", "element", "previouslyDismissed", "updateEmulatorBanner", "isRunningEmulator", "location", "prefixedId", "bannerId", "showError", "tearDown", "remove", "setupBannerStyles", "bannerEl", "style", "display", "background", "position", "bottom", "left", "padding", "borderRadius", "alignItems", "setupIconStyles", "prependIcon", "iconId", "marginLeft", "setupCloseBtn", "closeBtn", "cursor", "fontSize", "innerHTML", "onclick", "setupLinkStyles", "learnMoreLink", "learnMoreId", "innerText", "href", "paddingLeft", "textDecoration", "setupDom", "banner", "firebaseTextId", "firebaseText", "prependIconId", "createElementNS", "append", "body", "append<PERSON><PERSON><PERSON>", "readyState", "addEventListener", "getUA", "navigator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "test", "isNode", "forceEnvironment", "prototype", "toString", "call", "<PERSON><PERSON><PERSON><PERSON>", "isWebWorker", "WorkerGlobalScope", "isCloudflareWorker", "userAgent", "isBrowserExtension", "runtime", "chrome", "browser", "isReactNative", "isElectron", "indexOf", "isIE", "ua", "isUWP", "isNodeSdk", "<PERSON><PERSON><PERSON><PERSON>", "includes", "isSafariOrWebkit", "isIndexedDBAvailable", "indexedDB", "validateIndexedDBOpenable", "preExist", "DB_CHECK_NAME", "request", "open", "onsuccess", "close", "deleteDatabase", "onupgradeneeded", "onerror", "areCookiesEnabled", "cookieEnabled", "ERROR_NAME", "FirebaseError", "code", "customData", "setPrototypeOf", "captureStackTrace", "ErrorFactory", "create", "service", "serviceName", "errors", "data", "fullCode", "template", "replaceTemplate", "fullMessage", "PATTERN", "_", "jsonEval", "decode", "claims", "parts", "split", "isValidTimestamp", "now", "Math", "floor", "validSince", "validUntil", "issuedAtTime", "isValidFormat", "isAdmin", "contains", "obj", "safeGet", "isEmpty", "map", "fn", "contextObj", "res", "deepEqual", "a", "b", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "k", "aProp", "bProp", "isObject", "thing", "promiseWithTimeout", "timeInMS", "deferred<PERSON><PERSON><PERSON>", "setTimeout", "then", "querystring", "querystringParams", "params", "entries", "for<PERSON>ach", "arrayVal", "encodeURIComponent", "querystringDecode", "tokens", "decodeURIComponent", "extractQuerystring", "url", "queryStart", "fragmentStart", "Sha1", "chain_", "buf_", "W_", "pad_", "inbuf_", "total_", "blockSize", "reset", "compress_", "buf", "offset", "W", "t", "d", "f", "update", "lengthMinusBlock", "n", "inbuf", "digest", "totalBits", "j", "createSubscribe", "executor", "onNoObservers", "proxy", "ObserverProxy", "subscribe", "bind", "observers", "unsubscribes", "observerCount", "task", "finalized", "next", "forEachObserver", "observer", "complete", "nextOrObserver", "implementsAnyMethods", "noop", "unsub", "unsubscribeOne", "finalError", "sendOne", "err", "async", "onError", "args", "methods", "method", "validateArgCount", "fnName", "minCount", "maxCount", "argCount", "arg<PERSON><PERSON>r", "errorPrefix", "argName", "validateNamespace", "namespace", "optional", "validate<PERSON><PERSON>back", "argumentName", "validateContextObject", "context", "stringToByteArray", "high", "low", "stringLength", "DEFAULT_INTERVAL_MILLIS", "DEFAULT_BACKOFF_FACTOR", "MAX_VALUE_MILLIS", "RANDOM_FACTOR", "calculateBackoffMillis", "backoffCount", "<PERSON><PERSON><PERSON><PERSON>", "backoffFactor", "currBaseValue", "pow", "randomWait", "round", "random", "min", "ordinal", "Number", "isFinite", "indicator", "abs", "cent", "dec", "getModularInstance", "_delegate"], "sources": ["D:/ProjetPfa/pfa/pfa/node_modules/@firebase/util/dist/index.esm2017.js"], "sourcesContent": ["import { getDefaultsFromPostinstall } from './postinstall.mjs';\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Firebase constants.  Some of these (@defines) can be overridden at compile-time.\n */\nconst CONSTANTS = {\n    /**\n     * @define {boolean} Whether this is the client Node.js SDK.\n     */\n    NODE_CLIENT: false,\n    /**\n     * @define {boolean} Whether this is the Admin Node.js SDK.\n     */\n    NODE_ADMIN: false,\n    /**\n     * Firebase SDK Version\n     */\n    SDK_VERSION: '${JSCORE_VERSION}'\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Throws an error if the provided assertion is falsy\n */\nconst assert = function (assertion, message) {\n    if (!assertion) {\n        throw assertionError(message);\n    }\n};\n/**\n * Returns an Error object suitable for throwing.\n */\nconst assertionError = function (message) {\n    return new Error('Firebase Database (' +\n        CONSTANTS.SDK_VERSION +\n        ') INTERNAL ASSERT FAILED: ' +\n        message);\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst stringToByteArray$1 = function (str) {\n    // TODO(user): Use native implementations if/when available\n    const out = [];\n    let p = 0;\n    for (let i = 0; i < str.length; i++) {\n        let c = str.charCodeAt(i);\n        if (c < 128) {\n            out[p++] = c;\n        }\n        else if (c < 2048) {\n            out[p++] = (c >> 6) | 192;\n            out[p++] = (c & 63) | 128;\n        }\n        else if ((c & 0xfc00) === 0xd800 &&\n            i + 1 < str.length &&\n            (str.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {\n            // Surrogate Pair\n            c = 0x10000 + ((c & 0x03ff) << 10) + (str.charCodeAt(++i) & 0x03ff);\n            out[p++] = (c >> 18) | 240;\n            out[p++] = ((c >> 12) & 63) | 128;\n            out[p++] = ((c >> 6) & 63) | 128;\n            out[p++] = (c & 63) | 128;\n        }\n        else {\n            out[p++] = (c >> 12) | 224;\n            out[p++] = ((c >> 6) & 63) | 128;\n            out[p++] = (c & 63) | 128;\n        }\n    }\n    return out;\n};\n/**\n * Turns an array of numbers into the string given by the concatenation of the\n * characters to which the numbers correspond.\n * @param bytes Array of numbers representing characters.\n * @return Stringification of the array.\n */\nconst byteArrayToString = function (bytes) {\n    // TODO(user): Use native implementations if/when available\n    const out = [];\n    let pos = 0, c = 0;\n    while (pos < bytes.length) {\n        const c1 = bytes[pos++];\n        if (c1 < 128) {\n            out[c++] = String.fromCharCode(c1);\n        }\n        else if (c1 > 191 && c1 < 224) {\n            const c2 = bytes[pos++];\n            out[c++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));\n        }\n        else if (c1 > 239 && c1 < 365) {\n            // Surrogate Pair\n            const c2 = bytes[pos++];\n            const c3 = bytes[pos++];\n            const c4 = bytes[pos++];\n            const u = (((c1 & 7) << 18) | ((c2 & 63) << 12) | ((c3 & 63) << 6) | (c4 & 63)) -\n                0x10000;\n            out[c++] = String.fromCharCode(0xd800 + (u >> 10));\n            out[c++] = String.fromCharCode(0xdc00 + (u & 1023));\n        }\n        else {\n            const c2 = bytes[pos++];\n            const c3 = bytes[pos++];\n            out[c++] = String.fromCharCode(((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));\n        }\n    }\n    return out.join('');\n};\n// We define it as an object literal instead of a class because a class compiled down to es5 can't\n// be treeshaked. https://github.com/rollup/rollup/issues/1691\n// Static lookup maps, lazily populated by init_()\n// TODO(dlarocque): Define this as a class, since we no longer target ES5.\nconst base64 = {\n    /**\n     * Maps bytes to characters.\n     */\n    byteToCharMap_: null,\n    /**\n     * Maps characters to bytes.\n     */\n    charToByteMap_: null,\n    /**\n     * Maps bytes to websafe characters.\n     * @private\n     */\n    byteToCharMapWebSafe_: null,\n    /**\n     * Maps websafe characters to bytes.\n     * @private\n     */\n    charToByteMapWebSafe_: null,\n    /**\n     * Our default alphabet, shared between\n     * ENCODED_VALS and ENCODED_VALS_WEBSAFE\n     */\n    ENCODED_VALS_BASE: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' + 'abcdefghijklmnopqrstuvwxyz' + '0123456789',\n    /**\n     * Our default alphabet. Value 64 (=) is special; it means \"nothing.\"\n     */\n    get ENCODED_VALS() {\n        return this.ENCODED_VALS_BASE + '+/=';\n    },\n    /**\n     * Our websafe alphabet.\n     */\n    get ENCODED_VALS_WEBSAFE() {\n        return this.ENCODED_VALS_BASE + '-_.';\n    },\n    /**\n     * Whether this browser supports the atob and btoa functions. This extension\n     * started at Mozilla but is now implemented by many browsers. We use the\n     * ASSUME_* variables to avoid pulling in the full useragent detection library\n     * but still allowing the standard per-browser compilations.\n     *\n     */\n    HAS_NATIVE_SUPPORT: typeof atob === 'function',\n    /**\n     * Base64-encode an array of bytes.\n     *\n     * @param input An array of bytes (numbers with\n     *     value in [0, 255]) to encode.\n     * @param webSafe Boolean indicating we should use the\n     *     alternative alphabet.\n     * @return The base64 encoded string.\n     */\n    encodeByteArray(input, webSafe) {\n        if (!Array.isArray(input)) {\n            throw Error('encodeByteArray takes an array as a parameter');\n        }\n        this.init_();\n        const byteToCharMap = webSafe\n            ? this.byteToCharMapWebSafe_\n            : this.byteToCharMap_;\n        const output = [];\n        for (let i = 0; i < input.length; i += 3) {\n            const byte1 = input[i];\n            const haveByte2 = i + 1 < input.length;\n            const byte2 = haveByte2 ? input[i + 1] : 0;\n            const haveByte3 = i + 2 < input.length;\n            const byte3 = haveByte3 ? input[i + 2] : 0;\n            const outByte1 = byte1 >> 2;\n            const outByte2 = ((byte1 & 0x03) << 4) | (byte2 >> 4);\n            let outByte3 = ((byte2 & 0x0f) << 2) | (byte3 >> 6);\n            let outByte4 = byte3 & 0x3f;\n            if (!haveByte3) {\n                outByte4 = 64;\n                if (!haveByte2) {\n                    outByte3 = 64;\n                }\n            }\n            output.push(byteToCharMap[outByte1], byteToCharMap[outByte2], byteToCharMap[outByte3], byteToCharMap[outByte4]);\n        }\n        return output.join('');\n    },\n    /**\n     * Base64-encode a string.\n     *\n     * @param input A string to encode.\n     * @param webSafe If true, we should use the\n     *     alternative alphabet.\n     * @return The base64 encoded string.\n     */\n    encodeString(input, webSafe) {\n        // Shortcut for Mozilla browsers that implement\n        // a native base64 encoder in the form of \"btoa/atob\"\n        if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n            return btoa(input);\n        }\n        return this.encodeByteArray(stringToByteArray$1(input), webSafe);\n    },\n    /**\n     * Base64-decode a string.\n     *\n     * @param input to decode.\n     * @param webSafe True if we should use the\n     *     alternative alphabet.\n     * @return string representing the decoded value.\n     */\n    decodeString(input, webSafe) {\n        // Shortcut for Mozilla browsers that implement\n        // a native base64 encoder in the form of \"btoa/atob\"\n        if (this.HAS_NATIVE_SUPPORT && !webSafe) {\n            return atob(input);\n        }\n        return byteArrayToString(this.decodeStringToByteArray(input, webSafe));\n    },\n    /**\n     * Base64-decode a string.\n     *\n     * In base-64 decoding, groups of four characters are converted into three\n     * bytes.  If the encoder did not apply padding, the input length may not\n     * be a multiple of 4.\n     *\n     * In this case, the last group will have fewer than 4 characters, and\n     * padding will be inferred.  If the group has one or two characters, it decodes\n     * to one byte.  If the group has three characters, it decodes to two bytes.\n     *\n     * @param input Input to decode.\n     * @param webSafe True if we should use the web-safe alphabet.\n     * @return bytes representing the decoded value.\n     */\n    decodeStringToByteArray(input, webSafe) {\n        this.init_();\n        const charToByteMap = webSafe\n            ? this.charToByteMapWebSafe_\n            : this.charToByteMap_;\n        const output = [];\n        for (let i = 0; i < input.length;) {\n            const byte1 = charToByteMap[input.charAt(i++)];\n            const haveByte2 = i < input.length;\n            const byte2 = haveByte2 ? charToByteMap[input.charAt(i)] : 0;\n            ++i;\n            const haveByte3 = i < input.length;\n            const byte3 = haveByte3 ? charToByteMap[input.charAt(i)] : 64;\n            ++i;\n            const haveByte4 = i < input.length;\n            const byte4 = haveByte4 ? charToByteMap[input.charAt(i)] : 64;\n            ++i;\n            if (byte1 == null || byte2 == null || byte3 == null || byte4 == null) {\n                throw new DecodeBase64StringError();\n            }\n            const outByte1 = (byte1 << 2) | (byte2 >> 4);\n            output.push(outByte1);\n            if (byte3 !== 64) {\n                const outByte2 = ((byte2 << 4) & 0xf0) | (byte3 >> 2);\n                output.push(outByte2);\n                if (byte4 !== 64) {\n                    const outByte3 = ((byte3 << 6) & 0xc0) | byte4;\n                    output.push(outByte3);\n                }\n            }\n        }\n        return output;\n    },\n    /**\n     * Lazy static initialization function. Called before\n     * accessing any of the static map variables.\n     * @private\n     */\n    init_() {\n        if (!this.byteToCharMap_) {\n            this.byteToCharMap_ = {};\n            this.charToByteMap_ = {};\n            this.byteToCharMapWebSafe_ = {};\n            this.charToByteMapWebSafe_ = {};\n            // We want quick mappings back and forth, so we precompute two maps.\n            for (let i = 0; i < this.ENCODED_VALS.length; i++) {\n                this.byteToCharMap_[i] = this.ENCODED_VALS.charAt(i);\n                this.charToByteMap_[this.byteToCharMap_[i]] = i;\n                this.byteToCharMapWebSafe_[i] = this.ENCODED_VALS_WEBSAFE.charAt(i);\n                this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[i]] = i;\n                // Be forgiving when decoding and correctly decode both encodings.\n                if (i >= this.ENCODED_VALS_BASE.length) {\n                    this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(i)] = i;\n                    this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(i)] = i;\n                }\n            }\n        }\n    }\n};\n/**\n * An error encountered while decoding base64 string.\n */\nclass DecodeBase64StringError extends Error {\n    constructor() {\n        super(...arguments);\n        this.name = 'DecodeBase64StringError';\n    }\n}\n/**\n * URL-safe base64 encoding\n */\nconst base64Encode = function (str) {\n    const utf8Bytes = stringToByteArray$1(str);\n    return base64.encodeByteArray(utf8Bytes, true);\n};\n/**\n * URL-safe base64 encoding (without \".\" padding in the end).\n * e.g. Used in JSON Web Token (JWT) parts.\n */\nconst base64urlEncodeWithoutPadding = function (str) {\n    // Use base64url encoding and remove padding in the end (dot characters).\n    return base64Encode(str).replace(/\\./g, '');\n};\n/**\n * URL-safe base64 decoding\n *\n * NOTE: DO NOT use the global atob() function - it does NOT support the\n * base64Url variant encoding.\n *\n * @param str To be decoded\n * @return Decoded result, if possible\n */\nconst base64Decode = function (str) {\n    try {\n        return base64.decodeString(str, true);\n    }\n    catch (e) {\n        console.error('base64Decode failed: ', e);\n    }\n    return null;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Do a deep-copy of basic JavaScript Objects or Arrays.\n */\nfunction deepCopy(value) {\n    return deepExtend(undefined, value);\n}\n/**\n * Copy properties from source to target (recursively allows extension\n * of Objects and Arrays).  Scalar values in the target are over-written.\n * If target is undefined, an object of the appropriate type will be created\n * (and returned).\n *\n * We recursively copy all child properties of plain Objects in the source- so\n * that namespace- like dictionaries are merged.\n *\n * Note that the target can be a function, in which case the properties in\n * the source Object are copied onto it as static properties of the Function.\n *\n * Note: we don't merge __proto__ to prevent prototype pollution\n */\nfunction deepExtend(target, source) {\n    if (!(source instanceof Object)) {\n        return source;\n    }\n    switch (source.constructor) {\n        case Date:\n            // Treat Dates like scalars; if the target date object had any child\n            // properties - they will be lost!\n            const dateValue = source;\n            return new Date(dateValue.getTime());\n        case Object:\n            if (target === undefined) {\n                target = {};\n            }\n            break;\n        case Array:\n            // Always copy the array source and overwrite the target.\n            target = [];\n            break;\n        default:\n            // Not a plain Object - treat it as a scalar.\n            return source;\n    }\n    for (const prop in source) {\n        // use isValidKey to guard against prototype pollution. See https://snyk.io/vuln/SNYK-JS-LODASH-450202\n        if (!source.hasOwnProperty(prop) || !isValidKey(prop)) {\n            continue;\n        }\n        target[prop] = deepExtend(target[prop], source[prop]);\n    }\n    return target;\n}\nfunction isValidKey(key) {\n    return key !== '__proto__';\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Polyfill for `globalThis` object.\n * @returns the `globalThis` object for the given environment.\n * @public\n */\nfunction getGlobal() {\n    if (typeof self !== 'undefined') {\n        return self;\n    }\n    if (typeof window !== 'undefined') {\n        return window;\n    }\n    if (typeof global !== 'undefined') {\n        return global;\n    }\n    throw new Error('Unable to locate global object.');\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst getDefaultsFromGlobal = () => getGlobal().__FIREBASE_DEFAULTS__;\n/**\n * Attempt to read defaults from a JSON string provided to\n * process(.)env(.)__FIREBASE_DEFAULTS__ or a JSON file whose path is in\n * process(.)env(.)__FIREBASE_DEFAULTS_PATH__\n * The dots are in parens because certain compilers (Vite?) cannot\n * handle seeing that variable in comments.\n * See https://github.com/firebase/firebase-js-sdk/issues/6838\n */\nconst getDefaultsFromEnvVariable = () => {\n    if (typeof process === 'undefined' || typeof process.env === 'undefined') {\n        return;\n    }\n    const defaultsJsonString = process.env.__FIREBASE_DEFAULTS__;\n    if (defaultsJsonString) {\n        return JSON.parse(defaultsJsonString);\n    }\n};\nconst getDefaultsFromCookie = () => {\n    if (typeof document === 'undefined') {\n        return;\n    }\n    let match;\n    try {\n        match = document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/);\n    }\n    catch (e) {\n        // Some environments such as Angular Universal SSR have a\n        // `document` object but error on accessing `document.cookie`.\n        return;\n    }\n    const decoded = match && base64Decode(match[1]);\n    return decoded && JSON.parse(decoded);\n};\n/**\n * Get the __FIREBASE_DEFAULTS__ object. It checks in order:\n * (1) if such an object exists as a property of `globalThis`\n * (2) if such an object was provided on a shell environment variable\n * (3) if such an object exists in a cookie\n * @public\n */\nconst getDefaults = () => {\n    try {\n        return (getDefaultsFromPostinstall() ||\n            getDefaultsFromGlobal() ||\n            getDefaultsFromEnvVariable() ||\n            getDefaultsFromCookie());\n    }\n    catch (e) {\n        /**\n         * Catch-all for being unable to get __FIREBASE_DEFAULTS__ due\n         * to any environment case we have not accounted for. Log to\n         * info instead of swallowing so we can find these unknown cases\n         * and add paths for them if needed.\n         */\n        console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);\n        return;\n    }\n};\n/**\n * Returns emulator host stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a URL host formatted like `127.0.0.1:9999` or `[::1]:4000` if available\n * @public\n */\nconst getDefaultEmulatorHost = (productName) => { var _a, _b; return (_b = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.emulatorHosts) === null || _b === void 0 ? void 0 : _b[productName]; };\n/**\n * Returns emulator hostname and port stored in the __FIREBASE_DEFAULTS__ object\n * for the given product.\n * @returns a pair of hostname and port like `[\"::1\", 4000]` if available\n * @public\n */\nconst getDefaultEmulatorHostnameAndPort = (productName) => {\n    const host = getDefaultEmulatorHost(productName);\n    if (!host) {\n        return undefined;\n    }\n    const separatorIndex = host.lastIndexOf(':'); // Finding the last since IPv6 addr also has colons.\n    if (separatorIndex <= 0 || separatorIndex + 1 === host.length) {\n        throw new Error(`Invalid host ${host} with no separate hostname and port!`);\n    }\n    // eslint-disable-next-line no-restricted-globals\n    const port = parseInt(host.substring(separatorIndex + 1), 10);\n    if (host[0] === '[') {\n        // Bracket-quoted `[ipv6addr]:port` => return \"ipv6addr\" (without brackets).\n        return [host.substring(1, separatorIndex - 1), port];\n    }\n    else {\n        return [host.substring(0, separatorIndex), port];\n    }\n};\n/**\n * Returns Firebase app config stored in the __FIREBASE_DEFAULTS__ object.\n * @public\n */\nconst getDefaultAppConfig = () => { var _a; return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.config; };\n/**\n * Returns an experimental setting on the __FIREBASE_DEFAULTS__ object (properties\n * prefixed by \"_\")\n * @public\n */\nconst getExperimentalSetting = (name) => { var _a; return (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a[`_${name}`]; };\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass Deferred {\n    constructor() {\n        this.reject = () => { };\n        this.resolve = () => { };\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n    /**\n     * Our API internals are not promisified and cannot because our callback APIs have subtle expectations around\n     * invoking promises inline, which Promises are forbidden to do. This method accepts an optional node-style callback\n     * and returns a node-style callback which will resolve or reject the Deferred's promise.\n     */\n    wrapCallback(callback) {\n        return (error, value) => {\n            if (error) {\n                this.reject(error);\n            }\n            else {\n                this.resolve(value);\n            }\n            if (typeof callback === 'function') {\n                // Attaching noop handler just in case developer wasn't expecting\n                // promises\n                this.promise.catch(() => { });\n                // Some of our callbacks don't expect a value and our own tests\n                // assert that the parameter length is 1\n                if (callback.length === 1) {\n                    callback(error);\n                }\n                else {\n                    callback(error, value);\n                }\n            }\n        };\n    }\n}\n\n/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Checks whether host is a cloud workstation or not.\n * @public\n */\nfunction isCloudWorkstation(host) {\n    return host.endsWith('.cloudworkstations.dev');\n}\n/**\n * Makes a fetch request to the given server.\n * Mostly used for forwarding cookies in Firebase Studio.\n * @public\n */\nasync function pingServer(endpoint) {\n    const result = await fetch(endpoint, {\n        credentials: 'include'\n    });\n    return result.ok;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction createMockUserToken(token, projectId) {\n    if (token.uid) {\n        throw new Error('The \"uid\" field is no longer supported by mockUserToken. Please use \"sub\" instead for Firebase Auth User ID.');\n    }\n    // Unsecured JWTs use \"none\" as the algorithm.\n    const header = {\n        alg: 'none',\n        type: 'JWT'\n    };\n    const project = projectId || 'demo-project';\n    const iat = token.iat || 0;\n    const sub = token.sub || token.user_id;\n    if (!sub) {\n        throw new Error(\"mockUserToken must contain 'sub' or 'user_id' field!\");\n    }\n    const payload = Object.assign({ \n        // Set all required fields to decent defaults\n        iss: `https://securetoken.google.com/${project}`, aud: project, iat, exp: iat + 3600, auth_time: iat, sub, user_id: sub, firebase: {\n            sign_in_provider: 'custom',\n            identities: {}\n        } }, token);\n    // Unsecured JWTs use the empty string as a signature.\n    const signature = '';\n    return [\n        base64urlEncodeWithoutPadding(JSON.stringify(header)),\n        base64urlEncodeWithoutPadding(JSON.stringify(payload)),\n        signature\n    ].join('.');\n}\nconst emulatorStatus = {};\n// Checks whether any products are running on an emulator\nfunction getEmulatorSummary() {\n    const summary = {\n        prod: [],\n        emulator: []\n    };\n    for (const key of Object.keys(emulatorStatus)) {\n        if (emulatorStatus[key]) {\n            summary.emulator.push(key);\n        }\n        else {\n            summary.prod.push(key);\n        }\n    }\n    return summary;\n}\nfunction getOrCreateEl(id) {\n    let parentDiv = document.getElementById(id);\n    let created = false;\n    if (!parentDiv) {\n        parentDiv = document.createElement('div');\n        parentDiv.setAttribute('id', id);\n        created = true;\n    }\n    return { created, element: parentDiv };\n}\nlet previouslyDismissed = false;\n/**\n * Updates Emulator Banner. Primarily used for Firebase Studio\n * @param name\n * @param isRunningEmulator\n * @public\n */\nfunction updateEmulatorBanner(name, isRunningEmulator) {\n    if (typeof window === 'undefined' ||\n        typeof document === 'undefined' ||\n        !isCloudWorkstation(window.location.host) ||\n        emulatorStatus[name] === isRunningEmulator ||\n        emulatorStatus[name] || // If already set to use emulator, can't go back to prod.\n        previouslyDismissed) {\n        return;\n    }\n    emulatorStatus[name] = isRunningEmulator;\n    function prefixedId(id) {\n        return `__firebase__banner__${id}`;\n    }\n    const bannerId = '__firebase__banner';\n    const summary = getEmulatorSummary();\n    const showError = summary.prod.length > 0;\n    function tearDown() {\n        const element = document.getElementById(bannerId);\n        if (element) {\n            element.remove();\n        }\n    }\n    function setupBannerStyles(bannerEl) {\n        bannerEl.style.display = 'flex';\n        bannerEl.style.background = '#7faaf0';\n        bannerEl.style.position = 'fixed';\n        bannerEl.style.bottom = '5px';\n        bannerEl.style.left = '5px';\n        bannerEl.style.padding = '.5em';\n        bannerEl.style.borderRadius = '5px';\n        bannerEl.style.alignItems = 'center';\n    }\n    function setupIconStyles(prependIcon, iconId) {\n        prependIcon.setAttribute('width', '24');\n        prependIcon.setAttribute('id', iconId);\n        prependIcon.setAttribute('height', '24');\n        prependIcon.setAttribute('viewBox', '0 0 24 24');\n        prependIcon.setAttribute('fill', 'none');\n        prependIcon.style.marginLeft = '-6px';\n    }\n    function setupCloseBtn() {\n        const closeBtn = document.createElement('span');\n        closeBtn.style.cursor = 'pointer';\n        closeBtn.style.marginLeft = '16px';\n        closeBtn.style.fontSize = '24px';\n        closeBtn.innerHTML = ' &times;';\n        closeBtn.onclick = () => {\n            previouslyDismissed = true;\n            tearDown();\n        };\n        return closeBtn;\n    }\n    function setupLinkStyles(learnMoreLink, learnMoreId) {\n        learnMoreLink.setAttribute('id', learnMoreId);\n        learnMoreLink.innerText = 'Learn more';\n        learnMoreLink.href =\n            'https://firebase.google.com/docs/studio/preview-apps#preview-backend';\n        learnMoreLink.setAttribute('target', '__blank');\n        learnMoreLink.style.paddingLeft = '5px';\n        learnMoreLink.style.textDecoration = 'underline';\n    }\n    function setupDom() {\n        const banner = getOrCreateEl(bannerId);\n        const firebaseTextId = prefixedId('text');\n        const firebaseText = document.getElementById(firebaseTextId) || document.createElement('span');\n        const learnMoreId = prefixedId('learnmore');\n        const learnMoreLink = document.getElementById(learnMoreId) ||\n            document.createElement('a');\n        const prependIconId = prefixedId('preprendIcon');\n        const prependIcon = document.getElementById(prependIconId) ||\n            document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n        if (banner.created) {\n            // update styles\n            const bannerEl = banner.element;\n            setupBannerStyles(bannerEl);\n            setupLinkStyles(learnMoreLink, learnMoreId);\n            const closeBtn = setupCloseBtn();\n            setupIconStyles(prependIcon, prependIconId);\n            bannerEl.append(prependIcon, firebaseText, learnMoreLink, closeBtn);\n            document.body.appendChild(bannerEl);\n        }\n        if (showError) {\n            firebaseText.innerText = `Preview backend disconnected.`;\n            prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6013_33858)\">\n<path d=\"M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6013_33858\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n        }\n        else {\n            prependIcon.innerHTML = `<g clip-path=\"url(#clip0_6083_34804)\">\n<path d=\"M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z\" fill=\"#212121\"/>\n</g>\n<defs>\n<clipPath id=\"clip0_6083_34804\">\n<rect width=\"24\" height=\"24\" fill=\"white\"/>\n</clipPath>\n</defs>`;\n            firebaseText.innerText = 'Preview backend running in this workspace.';\n        }\n        firebaseText.setAttribute('id', firebaseTextId);\n    }\n    if (document.readyState === 'loading') {\n        window.addEventListener('DOMContentLoaded', setupDom);\n    }\n    else {\n        setupDom();\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns navigator.userAgent string or '' if it's not defined.\n * @return user agent string\n */\nfunction getUA() {\n    if (typeof navigator !== 'undefined' &&\n        typeof navigator['userAgent'] === 'string') {\n        return navigator['userAgent'];\n    }\n    else {\n        return '';\n    }\n}\n/**\n * Detect Cordova / PhoneGap / Ionic frameworks on a mobile device.\n *\n * Deliberately does not rely on checking `file://` URLs (as this fails PhoneGap\n * in the Ripple emulator) nor Cordova `onDeviceReady`, which would normally\n * wait for a callback.\n */\nfunction isMobileCordova() {\n    return (typeof window !== 'undefined' &&\n        // @ts-ignore Setting up an broadly applicable index signature for Window\n        // just to deal with this case would probably be a bad idea.\n        !!(window['cordova'] || window['phonegap'] || window['PhoneGap']) &&\n        /ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA()));\n}\n/**\n * Detect Node.js.\n *\n * @return true if Node.js environment is detected or specified.\n */\n// Node detection logic from: https://github.com/iliakan/detect-node/\nfunction isNode() {\n    var _a;\n    const forceEnvironment = (_a = getDefaults()) === null || _a === void 0 ? void 0 : _a.forceEnvironment;\n    if (forceEnvironment === 'node') {\n        return true;\n    }\n    else if (forceEnvironment === 'browser') {\n        return false;\n    }\n    try {\n        return (Object.prototype.toString.call(global.process) === '[object process]');\n    }\n    catch (e) {\n        return false;\n    }\n}\n/**\n * Detect Browser Environment.\n * Note: This will return true for certain test frameworks that are incompletely\n * mimicking a browser, and should not lead to assuming all browser APIs are\n * available.\n */\nfunction isBrowser() {\n    return typeof window !== 'undefined' || isWebWorker();\n}\n/**\n * Detect Web Worker context.\n */\nfunction isWebWorker() {\n    return (typeof WorkerGlobalScope !== 'undefined' &&\n        typeof self !== 'undefined' &&\n        self instanceof WorkerGlobalScope);\n}\n/**\n * Detect Cloudflare Worker context.\n */\nfunction isCloudflareWorker() {\n    return (typeof navigator !== 'undefined' &&\n        navigator.userAgent === 'Cloudflare-Workers');\n}\nfunction isBrowserExtension() {\n    const runtime = typeof chrome === 'object'\n        ? chrome.runtime\n        : typeof browser === 'object'\n            ? browser.runtime\n            : undefined;\n    return typeof runtime === 'object' && runtime.id !== undefined;\n}\n/**\n * Detect React Native.\n *\n * @return true if ReactNative environment is detected.\n */\nfunction isReactNative() {\n    return (typeof navigator === 'object' && navigator['product'] === 'ReactNative');\n}\n/** Detects Electron apps. */\nfunction isElectron() {\n    return getUA().indexOf('Electron/') >= 0;\n}\n/** Detects Internet Explorer. */\nfunction isIE() {\n    const ua = getUA();\n    return ua.indexOf('MSIE ') >= 0 || ua.indexOf('Trident/') >= 0;\n}\n/** Detects Universal Windows Platform apps. */\nfunction isUWP() {\n    return getUA().indexOf('MSAppHost/') >= 0;\n}\n/**\n * Detect whether the current SDK build is the Node version.\n *\n * @return true if it's the Node SDK build.\n */\nfunction isNodeSdk() {\n    return CONSTANTS.NODE_CLIENT === true || CONSTANTS.NODE_ADMIN === true;\n}\n/** Returns true if we are running in Safari. */\nfunction isSafari() {\n    return (!isNode() &&\n        !!navigator.userAgent &&\n        navigator.userAgent.includes('Safari') &&\n        !navigator.userAgent.includes('Chrome'));\n}\n/** Returns true if we are running in Safari or WebKit */\nfunction isSafariOrWebkit() {\n    return (!isNode() &&\n        !!navigator.userAgent &&\n        (navigator.userAgent.includes('Safari') ||\n            navigator.userAgent.includes('WebKit')) &&\n        !navigator.userAgent.includes('Chrome'));\n}\n/**\n * This method checks if indexedDB is supported by current browser/service worker context\n * @return true if indexedDB is supported by current browser/service worker context\n */\nfunction isIndexedDBAvailable() {\n    try {\n        return typeof indexedDB === 'object';\n    }\n    catch (e) {\n        return false;\n    }\n}\n/**\n * This method validates browser/sw context for indexedDB by opening a dummy indexedDB database and reject\n * if errors occur during the database open operation.\n *\n * @throws exception if current browser/sw context can't run idb.open (ex: Safari iframe, Firefox\n * private browsing)\n */\nfunction validateIndexedDBOpenable() {\n    return new Promise((resolve, reject) => {\n        try {\n            let preExist = true;\n            const DB_CHECK_NAME = 'validate-browser-context-for-indexeddb-analytics-module';\n            const request = self.indexedDB.open(DB_CHECK_NAME);\n            request.onsuccess = () => {\n                request.result.close();\n                // delete database only when it doesn't pre-exist\n                if (!preExist) {\n                    self.indexedDB.deleteDatabase(DB_CHECK_NAME);\n                }\n                resolve(true);\n            };\n            request.onupgradeneeded = () => {\n                preExist = false;\n            };\n            request.onerror = () => {\n                var _a;\n                reject(((_a = request.error) === null || _a === void 0 ? void 0 : _a.message) || '');\n            };\n        }\n        catch (error) {\n            reject(error);\n        }\n    });\n}\n/**\n *\n * This method checks whether cookie is enabled within current browser\n * @return true if cookie is enabled within current browser\n */\nfunction areCookiesEnabled() {\n    if (typeof navigator === 'undefined' || !navigator.cookieEnabled) {\n        return false;\n    }\n    return true;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview Standardized Firebase Error.\n *\n * Usage:\n *\n *   // TypeScript string literals for type-safe codes\n *   type Err =\n *     'unknown' |\n *     'object-not-found'\n *     ;\n *\n *   // Closure enum for type-safe error codes\n *   // at-enum {string}\n *   var Err = {\n *     UNKNOWN: 'unknown',\n *     OBJECT_NOT_FOUND: 'object-not-found',\n *   }\n *\n *   let errors: Map<Err, string> = {\n *     'generic-error': \"Unknown error\",\n *     'file-not-found': \"Could not find file: {$file}\",\n *   };\n *\n *   // Type-safe function - must pass a valid error code as param.\n *   let error = new ErrorFactory<Err>('service', 'Service', errors);\n *\n *   ...\n *   throw error.create(Err.GENERIC);\n *   ...\n *   throw error.create(Err.FILE_NOT_FOUND, {'file': fileName});\n *   ...\n *   // Service: Could not file file: foo.txt (service/file-not-found).\n *\n *   catch (e) {\n *     assert(e.message === \"Could not find file: foo.txt.\");\n *     if ((e as FirebaseError)?.code === 'service/file-not-found') {\n *       console.log(\"Could not read file: \" + e['file']);\n *     }\n *   }\n */\nconst ERROR_NAME = 'FirebaseError';\n// Based on code from:\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error#Custom_Error_Types\nclass FirebaseError extends Error {\n    constructor(\n    /** The error code for this error. */\n    code, message, \n    /** Custom data for this error. */\n    customData) {\n        super(message);\n        this.code = code;\n        this.customData = customData;\n        /** The custom name for all FirebaseErrors. */\n        this.name = ERROR_NAME;\n        // Fix For ES5\n        // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n        // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n        //                   which we can now use since we no longer target ES5.\n        Object.setPrototypeOf(this, FirebaseError.prototype);\n        // Maintains proper stack trace for where our error was thrown.\n        // Only available on V8.\n        if (Error.captureStackTrace) {\n            Error.captureStackTrace(this, ErrorFactory.prototype.create);\n        }\n    }\n}\nclass ErrorFactory {\n    constructor(service, serviceName, errors) {\n        this.service = service;\n        this.serviceName = serviceName;\n        this.errors = errors;\n    }\n    create(code, ...data) {\n        const customData = data[0] || {};\n        const fullCode = `${this.service}/${code}`;\n        const template = this.errors[code];\n        const message = template ? replaceTemplate(template, customData) : 'Error';\n        // Service Name: Error message (service/code).\n        const fullMessage = `${this.serviceName}: ${message} (${fullCode}).`;\n        const error = new FirebaseError(fullCode, fullMessage, customData);\n        return error;\n    }\n}\nfunction replaceTemplate(template, data) {\n    return template.replace(PATTERN, (_, key) => {\n        const value = data[key];\n        return value != null ? String(value) : `<${key}?>`;\n    });\n}\nconst PATTERN = /\\{\\$([^}]+)}/g;\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Evaluates a JSON string into a javascript object.\n *\n * @param {string} str A string containing JSON.\n * @return {*} The javascript object representing the specified JSON.\n */\nfunction jsonEval(str) {\n    return JSON.parse(str);\n}\n/**\n * Returns JSON representing a javascript object.\n * @param {*} data JavaScript object to be stringified.\n * @return {string} The JSON contents of the object.\n */\nfunction stringify(data) {\n    return JSON.stringify(data);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Decodes a Firebase auth. token into constituent parts.\n *\n * Notes:\n * - May return with invalid / incomplete claims if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst decode = function (token) {\n    let header = {}, claims = {}, data = {}, signature = '';\n    try {\n        const parts = token.split('.');\n        header = jsonEval(base64Decode(parts[0]) || '');\n        claims = jsonEval(base64Decode(parts[1]) || '');\n        signature = parts[2];\n        data = claims['d'] || {};\n        delete claims['d'];\n    }\n    catch (e) { }\n    return {\n        header,\n        claims,\n        data,\n        signature\n    };\n};\n/**\n * Decodes a Firebase auth. token and checks the validity of its time-based claims. Will return true if the\n * token is within the time window authorized by the 'nbf' (not-before) and 'iat' (issued-at) claims.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isValidTimestamp = function (token) {\n    const claims = decode(token).claims;\n    const now = Math.floor(new Date().getTime() / 1000);\n    let validSince = 0, validUntil = 0;\n    if (typeof claims === 'object') {\n        if (claims.hasOwnProperty('nbf')) {\n            validSince = claims['nbf'];\n        }\n        else if (claims.hasOwnProperty('iat')) {\n            validSince = claims['iat'];\n        }\n        if (claims.hasOwnProperty('exp')) {\n            validUntil = claims['exp'];\n        }\n        else {\n            // token will expire after 24h by default\n            validUntil = validSince + 86400;\n        }\n    }\n    return (!!now &&\n        !!validSince &&\n        !!validUntil &&\n        now >= validSince &&\n        now <= validUntil);\n};\n/**\n * Decodes a Firebase auth. token and returns its issued at time if valid, null otherwise.\n *\n * Notes:\n * - May return null if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst issuedAtTime = function (token) {\n    const claims = decode(token).claims;\n    if (typeof claims === 'object' && claims.hasOwnProperty('iat')) {\n        return claims['iat'];\n    }\n    return null;\n};\n/**\n * Decodes a Firebase auth. token and checks the validity of its format. Expects a valid issued-at time.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isValidFormat = function (token) {\n    const decoded = decode(token), claims = decoded.claims;\n    return !!claims && typeof claims === 'object' && claims.hasOwnProperty('iat');\n};\n/**\n * Attempts to peer into an auth token and determine if it's an admin auth token by looking at the claims portion.\n *\n * Notes:\n * - May return a false negative if there's no native base64 decoding support.\n * - Doesn't check if the token is actually valid.\n */\nconst isAdmin = function (token) {\n    const claims = decode(token).claims;\n    return typeof claims === 'object' && claims['admin'] === true;\n};\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction contains(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\nfunction safeGet(obj, key) {\n    if (Object.prototype.hasOwnProperty.call(obj, key)) {\n        return obj[key];\n    }\n    else {\n        return undefined;\n    }\n}\nfunction isEmpty(obj) {\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction map(obj, fn, contextObj) {\n    const res = {};\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            res[key] = fn.call(contextObj, obj[key], key, obj);\n        }\n    }\n    return res;\n}\n/**\n * Deep equal two objects. Support Arrays and Objects.\n */\nfunction deepEqual(a, b) {\n    if (a === b) {\n        return true;\n    }\n    const aKeys = Object.keys(a);\n    const bKeys = Object.keys(b);\n    for (const k of aKeys) {\n        if (!bKeys.includes(k)) {\n            return false;\n        }\n        const aProp = a[k];\n        const bProp = b[k];\n        if (isObject(aProp) && isObject(bProp)) {\n            if (!deepEqual(aProp, bProp)) {\n                return false;\n            }\n        }\n        else if (aProp !== bProp) {\n            return false;\n        }\n    }\n    for (const k of bKeys) {\n        if (!aKeys.includes(k)) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction isObject(thing) {\n    return thing !== null && typeof thing === 'object';\n}\n\n/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Rejects if the given promise doesn't resolve in timeInMS milliseconds.\n * @internal\n */\nfunction promiseWithTimeout(promise, timeInMS = 2000) {\n    const deferredPromise = new Deferred();\n    setTimeout(() => deferredPromise.reject('timeout!'), timeInMS);\n    promise.then(deferredPromise.resolve, deferredPromise.reject);\n    return deferredPromise.promise;\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Returns a querystring-formatted string (e.g. &arg=val&arg2=val2) from a\n * params object (e.g. {arg: 'val', arg2: 'val2'})\n * Note: You must prepend it with ? when adding it to a URL.\n */\nfunction querystring(querystringParams) {\n    const params = [];\n    for (const [key, value] of Object.entries(querystringParams)) {\n        if (Array.isArray(value)) {\n            value.forEach(arrayVal => {\n                params.push(encodeURIComponent(key) + '=' + encodeURIComponent(arrayVal));\n            });\n        }\n        else {\n            params.push(encodeURIComponent(key) + '=' + encodeURIComponent(value));\n        }\n    }\n    return params.length ? '&' + params.join('&') : '';\n}\n/**\n * Decodes a querystring (e.g. ?arg=val&arg2=val2) into a params object\n * (e.g. {arg: 'val', arg2: 'val2'})\n */\nfunction querystringDecode(querystring) {\n    const obj = {};\n    const tokens = querystring.replace(/^\\?/, '').split('&');\n    tokens.forEach(token => {\n        if (token) {\n            const [key, value] = token.split('=');\n            obj[decodeURIComponent(key)] = decodeURIComponent(value);\n        }\n    });\n    return obj;\n}\n/**\n * Extract the query string part of a URL, including the leading question mark (if present).\n */\nfunction extractQuerystring(url) {\n    const queryStart = url.indexOf('?');\n    if (!queryStart) {\n        return '';\n    }\n    const fragmentStart = url.indexOf('#', queryStart);\n    return url.substring(queryStart, fragmentStart > 0 ? fragmentStart : undefined);\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @fileoverview SHA-1 cryptographic hash.\n * Variable names follow the notation in FIPS PUB 180-3:\n * http://csrc.nist.gov/publications/fips/fips180-3/fips180-3_final.pdf.\n *\n * Usage:\n *   var sha1 = new sha1();\n *   sha1.update(bytes);\n *   var hash = sha1.digest();\n *\n * Performance:\n *   Chrome 23:   ~400 Mbit/s\n *   Firefox 16:  ~250 Mbit/s\n *\n */\n/**\n * SHA-1 cryptographic hash constructor.\n *\n * The properties declared here are discussed in the above algorithm document.\n * @constructor\n * @final\n * @struct\n */\nclass Sha1 {\n    constructor() {\n        /**\n         * Holds the previous values of accumulated variables a-e in the compress_\n         * function.\n         * @private\n         */\n        this.chain_ = [];\n        /**\n         * A buffer holding the partially computed hash result.\n         * @private\n         */\n        this.buf_ = [];\n        /**\n         * An array of 80 bytes, each a part of the message to be hashed.  Referred to\n         * as the message schedule in the docs.\n         * @private\n         */\n        this.W_ = [];\n        /**\n         * Contains data needed to pad messages less than 64 bytes.\n         * @private\n         */\n        this.pad_ = [];\n        /**\n         * @private {number}\n         */\n        this.inbuf_ = 0;\n        /**\n         * @private {number}\n         */\n        this.total_ = 0;\n        this.blockSize = 512 / 8;\n        this.pad_[0] = 128;\n        for (let i = 1; i < this.blockSize; ++i) {\n            this.pad_[i] = 0;\n        }\n        this.reset();\n    }\n    reset() {\n        this.chain_[0] = 0x67452301;\n        this.chain_[1] = 0xefcdab89;\n        this.chain_[2] = 0x98badcfe;\n        this.chain_[3] = 0x10325476;\n        this.chain_[4] = 0xc3d2e1f0;\n        this.inbuf_ = 0;\n        this.total_ = 0;\n    }\n    /**\n     * Internal compress helper function.\n     * @param buf Block to compress.\n     * @param offset Offset of the block in the buffer.\n     * @private\n     */\n    compress_(buf, offset) {\n        if (!offset) {\n            offset = 0;\n        }\n        const W = this.W_;\n        // get 16 big endian words\n        if (typeof buf === 'string') {\n            for (let i = 0; i < 16; i++) {\n                // TODO(user): [bug 8140122] Recent versions of Safari for Mac OS and iOS\n                // have a bug that turns the post-increment ++ operator into pre-increment\n                // during JIT compilation.  We have code that depends heavily on SHA-1 for\n                // correctness and which is affected by this bug, so I've removed all uses\n                // of post-increment ++ in which the result value is used.  We can revert\n                // this change once the Safari bug\n                // (https://bugs.webkit.org/show_bug.cgi?id=109036) has been fixed and\n                // most clients have been updated.\n                W[i] =\n                    (buf.charCodeAt(offset) << 24) |\n                        (buf.charCodeAt(offset + 1) << 16) |\n                        (buf.charCodeAt(offset + 2) << 8) |\n                        buf.charCodeAt(offset + 3);\n                offset += 4;\n            }\n        }\n        else {\n            for (let i = 0; i < 16; i++) {\n                W[i] =\n                    (buf[offset] << 24) |\n                        (buf[offset + 1] << 16) |\n                        (buf[offset + 2] << 8) |\n                        buf[offset + 3];\n                offset += 4;\n            }\n        }\n        // expand to 80 words\n        for (let i = 16; i < 80; i++) {\n            const t = W[i - 3] ^ W[i - 8] ^ W[i - 14] ^ W[i - 16];\n            W[i] = ((t << 1) | (t >>> 31)) & 0xffffffff;\n        }\n        let a = this.chain_[0];\n        let b = this.chain_[1];\n        let c = this.chain_[2];\n        let d = this.chain_[3];\n        let e = this.chain_[4];\n        let f, k;\n        // TODO(user): Try to unroll this loop to speed up the computation.\n        for (let i = 0; i < 80; i++) {\n            if (i < 40) {\n                if (i < 20) {\n                    f = d ^ (b & (c ^ d));\n                    k = 0x5a827999;\n                }\n                else {\n                    f = b ^ c ^ d;\n                    k = 0x6ed9eba1;\n                }\n            }\n            else {\n                if (i < 60) {\n                    f = (b & c) | (d & (b | c));\n                    k = 0x8f1bbcdc;\n                }\n                else {\n                    f = b ^ c ^ d;\n                    k = 0xca62c1d6;\n                }\n            }\n            const t = (((a << 5) | (a >>> 27)) + f + e + k + W[i]) & 0xffffffff;\n            e = d;\n            d = c;\n            c = ((b << 30) | (b >>> 2)) & 0xffffffff;\n            b = a;\n            a = t;\n        }\n        this.chain_[0] = (this.chain_[0] + a) & 0xffffffff;\n        this.chain_[1] = (this.chain_[1] + b) & 0xffffffff;\n        this.chain_[2] = (this.chain_[2] + c) & 0xffffffff;\n        this.chain_[3] = (this.chain_[3] + d) & 0xffffffff;\n        this.chain_[4] = (this.chain_[4] + e) & 0xffffffff;\n    }\n    update(bytes, length) {\n        // TODO(johnlenz): tighten the function signature and remove this check\n        if (bytes == null) {\n            return;\n        }\n        if (length === undefined) {\n            length = bytes.length;\n        }\n        const lengthMinusBlock = length - this.blockSize;\n        let n = 0;\n        // Using local instead of member variables gives ~5% speedup on Firefox 16.\n        const buf = this.buf_;\n        let inbuf = this.inbuf_;\n        // The outer while loop should execute at most twice.\n        while (n < length) {\n            // When we have no data in the block to top up, we can directly process the\n            // input buffer (assuming it contains sufficient data). This gives ~25%\n            // speedup on Chrome 23 and ~15% speedup on Firefox 16, but requires that\n            // the data is provided in large chunks (or in multiples of 64 bytes).\n            if (inbuf === 0) {\n                while (n <= lengthMinusBlock) {\n                    this.compress_(bytes, n);\n                    n += this.blockSize;\n                }\n            }\n            if (typeof bytes === 'string') {\n                while (n < length) {\n                    buf[inbuf] = bytes.charCodeAt(n);\n                    ++inbuf;\n                    ++n;\n                    if (inbuf === this.blockSize) {\n                        this.compress_(buf);\n                        inbuf = 0;\n                        // Jump to the outer loop so we use the full-block optimization.\n                        break;\n                    }\n                }\n            }\n            else {\n                while (n < length) {\n                    buf[inbuf] = bytes[n];\n                    ++inbuf;\n                    ++n;\n                    if (inbuf === this.blockSize) {\n                        this.compress_(buf);\n                        inbuf = 0;\n                        // Jump to the outer loop so we use the full-block optimization.\n                        break;\n                    }\n                }\n            }\n        }\n        this.inbuf_ = inbuf;\n        this.total_ += length;\n    }\n    /** @override */\n    digest() {\n        const digest = [];\n        let totalBits = this.total_ * 8;\n        // Add pad 0x80 0x00*.\n        if (this.inbuf_ < 56) {\n            this.update(this.pad_, 56 - this.inbuf_);\n        }\n        else {\n            this.update(this.pad_, this.blockSize - (this.inbuf_ - 56));\n        }\n        // Add # bits.\n        for (let i = this.blockSize - 1; i >= 56; i--) {\n            this.buf_[i] = totalBits & 255;\n            totalBits /= 256; // Don't use bit-shifting here!\n        }\n        this.compress_(this.buf_);\n        let n = 0;\n        for (let i = 0; i < 5; i++) {\n            for (let j = 24; j >= 0; j -= 8) {\n                digest[n] = (this.chain_[i] >> j) & 255;\n                ++n;\n            }\n        }\n        return digest;\n    }\n}\n\n/**\n * Helper to make a Subscribe function (just like Promise helps make a\n * Thenable).\n *\n * @param executor Function which can make calls to a single Observer\n *     as a proxy.\n * @param onNoObservers Callback when count of Observers goes to zero.\n */\nfunction createSubscribe(executor, onNoObservers) {\n    const proxy = new ObserverProxy(executor, onNoObservers);\n    return proxy.subscribe.bind(proxy);\n}\n/**\n * Implement fan-out for any number of Observers attached via a subscribe\n * function.\n */\nclass ObserverProxy {\n    /**\n     * @param executor Function which can make calls to a single Observer\n     *     as a proxy.\n     * @param onNoObservers Callback when count of Observers goes to zero.\n     */\n    constructor(executor, onNoObservers) {\n        this.observers = [];\n        this.unsubscribes = [];\n        this.observerCount = 0;\n        // Micro-task scheduling by calling task.then().\n        this.task = Promise.resolve();\n        this.finalized = false;\n        this.onNoObservers = onNoObservers;\n        // Call the executor asynchronously so subscribers that are called\n        // synchronously after the creation of the subscribe function\n        // can still receive the very first value generated in the executor.\n        this.task\n            .then(() => {\n            executor(this);\n        })\n            .catch(e => {\n            this.error(e);\n        });\n    }\n    next(value) {\n        this.forEachObserver((observer) => {\n            observer.next(value);\n        });\n    }\n    error(error) {\n        this.forEachObserver((observer) => {\n            observer.error(error);\n        });\n        this.close(error);\n    }\n    complete() {\n        this.forEachObserver((observer) => {\n            observer.complete();\n        });\n        this.close();\n    }\n    /**\n     * Subscribe function that can be used to add an Observer to the fan-out list.\n     *\n     * - We require that no event is sent to a subscriber synchronously to their\n     *   call to subscribe().\n     */\n    subscribe(nextOrObserver, error, complete) {\n        let observer;\n        if (nextOrObserver === undefined &&\n            error === undefined &&\n            complete === undefined) {\n            throw new Error('Missing Observer.');\n        }\n        // Assemble an Observer object when passed as callback functions.\n        if (implementsAnyMethods(nextOrObserver, [\n            'next',\n            'error',\n            'complete'\n        ])) {\n            observer = nextOrObserver;\n        }\n        else {\n            observer = {\n                next: nextOrObserver,\n                error,\n                complete\n            };\n        }\n        if (observer.next === undefined) {\n            observer.next = noop;\n        }\n        if (observer.error === undefined) {\n            observer.error = noop;\n        }\n        if (observer.complete === undefined) {\n            observer.complete = noop;\n        }\n        const unsub = this.unsubscribeOne.bind(this, this.observers.length);\n        // Attempt to subscribe to a terminated Observable - we\n        // just respond to the Observer with the final error or complete\n        // event.\n        if (this.finalized) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.task.then(() => {\n                try {\n                    if (this.finalError) {\n                        observer.error(this.finalError);\n                    }\n                    else {\n                        observer.complete();\n                    }\n                }\n                catch (e) {\n                    // nothing\n                }\n                return;\n            });\n        }\n        this.observers.push(observer);\n        return unsub;\n    }\n    // Unsubscribe is synchronous - we guarantee that no events are sent to\n    // any unsubscribed Observer.\n    unsubscribeOne(i) {\n        if (this.observers === undefined || this.observers[i] === undefined) {\n            return;\n        }\n        delete this.observers[i];\n        this.observerCount -= 1;\n        if (this.observerCount === 0 && this.onNoObservers !== undefined) {\n            this.onNoObservers(this);\n        }\n    }\n    forEachObserver(fn) {\n        if (this.finalized) {\n            // Already closed by previous event....just eat the additional values.\n            return;\n        }\n        // Since sendOne calls asynchronously - there is no chance that\n        // this.observers will become undefined.\n        for (let i = 0; i < this.observers.length; i++) {\n            this.sendOne(i, fn);\n        }\n    }\n    // Call the Observer via one of it's callback function. We are careful to\n    // confirm that the observe has not been unsubscribed since this asynchronous\n    // function had been queued.\n    sendOne(i, fn) {\n        // Execute the callback asynchronously\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        this.task.then(() => {\n            if (this.observers !== undefined && this.observers[i] !== undefined) {\n                try {\n                    fn(this.observers[i]);\n                }\n                catch (e) {\n                    // Ignore exceptions raised in Observers or missing methods of an\n                    // Observer.\n                    // Log error to console. b/31404806\n                    if (typeof console !== 'undefined' && console.error) {\n                        console.error(e);\n                    }\n                }\n            }\n        });\n    }\n    close(err) {\n        if (this.finalized) {\n            return;\n        }\n        this.finalized = true;\n        if (err !== undefined) {\n            this.finalError = err;\n        }\n        // Proxy is no longer needed - garbage collect references\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        this.task.then(() => {\n            this.observers = undefined;\n            this.onNoObservers = undefined;\n        });\n    }\n}\n/** Turn synchronous function into one called asynchronously. */\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction async(fn, onError) {\n    return (...args) => {\n        Promise.resolve(true)\n            .then(() => {\n            fn(...args);\n        })\n            .catch((error) => {\n            if (onError) {\n                onError(error);\n            }\n        });\n    };\n}\n/**\n * Return true if the object passed in implements any of the named methods.\n */\nfunction implementsAnyMethods(obj, methods) {\n    if (typeof obj !== 'object' || obj === null) {\n        return false;\n    }\n    for (const method of methods) {\n        if (method in obj && typeof obj[method] === 'function') {\n            return true;\n        }\n    }\n    return false;\n}\nfunction noop() {\n    // do nothing\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Check to make sure the appropriate number of arguments are provided for a public function.\n * Throws an error if it fails.\n *\n * @param fnName The function name\n * @param minCount The minimum number of arguments to allow for the function call\n * @param maxCount The maximum number of argument to allow for the function call\n * @param argCount The actual number of arguments provided.\n */\nconst validateArgCount = function (fnName, minCount, maxCount, argCount) {\n    let argError;\n    if (argCount < minCount) {\n        argError = 'at least ' + minCount;\n    }\n    else if (argCount > maxCount) {\n        argError = maxCount === 0 ? 'none' : 'no more than ' + maxCount;\n    }\n    if (argError) {\n        const error = fnName +\n            ' failed: Was called with ' +\n            argCount +\n            (argCount === 1 ? ' argument.' : ' arguments.') +\n            ' Expects ' +\n            argError +\n            '.';\n        throw new Error(error);\n    }\n};\n/**\n * Generates a string to prefix an error message about failed argument validation\n *\n * @param fnName The function name\n * @param argName The name of the argument\n * @return The prefix to add to the error thrown for validation.\n */\nfunction errorPrefix(fnName, argName) {\n    return `${fnName} failed: ${argName} argument `;\n}\n/**\n * @param fnName\n * @param argumentNumber\n * @param namespace\n * @param optional\n */\nfunction validateNamespace(fnName, namespace, optional) {\n    if (optional && !namespace) {\n        return;\n    }\n    if (typeof namespace !== 'string') {\n        //TODO: I should do more validation here. We only allow certain chars in namespaces.\n        throw new Error(errorPrefix(fnName, 'namespace') + 'must be a valid firebase namespace.');\n    }\n}\nfunction validateCallback(fnName, argumentName, \n// eslint-disable-next-line @typescript-eslint/ban-types\ncallback, optional) {\n    if (optional && !callback) {\n        return;\n    }\n    if (typeof callback !== 'function') {\n        throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid function.');\n    }\n}\nfunction validateContextObject(fnName, argumentName, context, optional) {\n    if (optional && !context) {\n        return;\n    }\n    if (typeof context !== 'object' || context === null) {\n        throw new Error(errorPrefix(fnName, argumentName) + 'must be a valid context object.');\n    }\n}\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Code originally came from goog.crypt.stringToUtf8ByteArray, but for some reason they\n// automatically replaced '\\r\\n' with '\\n', and they didn't handle surrogate pairs,\n// so it's been modified.\n// Note that not all Unicode characters appear as single characters in JavaScript strings.\n// fromCharCode returns the UTF-16 encoding of a character - so some Unicode characters\n// use 2 characters in JavaScript.  All 4-byte UTF-8 characters begin with a first\n// character in the range 0xD800 - 0xDBFF (the first character of a so-called surrogate\n// pair).\n// See http://www.ecma-international.org/ecma-262/5.1/#sec-15.1.3\n/**\n * @param {string} str\n * @return {Array}\n */\nconst stringToByteArray = function (str) {\n    const out = [];\n    let p = 0;\n    for (let i = 0; i < str.length; i++) {\n        let c = str.charCodeAt(i);\n        // Is this the lead surrogate in a surrogate pair?\n        if (c >= 0xd800 && c <= 0xdbff) {\n            const high = c - 0xd800; // the high 10 bits.\n            i++;\n            assert(i < str.length, 'Surrogate pair missing trail surrogate.');\n            const low = str.charCodeAt(i) - 0xdc00; // the low 10 bits.\n            c = 0x10000 + (high << 10) + low;\n        }\n        if (c < 128) {\n            out[p++] = c;\n        }\n        else if (c < 2048) {\n            out[p++] = (c >> 6) | 192;\n            out[p++] = (c & 63) | 128;\n        }\n        else if (c < 65536) {\n            out[p++] = (c >> 12) | 224;\n            out[p++] = ((c >> 6) & 63) | 128;\n            out[p++] = (c & 63) | 128;\n        }\n        else {\n            out[p++] = (c >> 18) | 240;\n            out[p++] = ((c >> 12) & 63) | 128;\n            out[p++] = ((c >> 6) & 63) | 128;\n            out[p++] = (c & 63) | 128;\n        }\n    }\n    return out;\n};\n/**\n * Calculate length without actually converting; useful for doing cheaper validation.\n * @param {string} str\n * @return {number}\n */\nconst stringLength = function (str) {\n    let p = 0;\n    for (let i = 0; i < str.length; i++) {\n        const c = str.charCodeAt(i);\n        if (c < 128) {\n            p++;\n        }\n        else if (c < 2048) {\n            p += 2;\n        }\n        else if (c >= 0xd800 && c <= 0xdbff) {\n            // Lead surrogate of a surrogate pair.  The pair together will take 4 bytes to represent.\n            p += 4;\n            i++; // skip trail surrogate.\n        }\n        else {\n            p += 3;\n        }\n    }\n    return p;\n};\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The amount of milliseconds to exponentially increase.\n */\nconst DEFAULT_INTERVAL_MILLIS = 1000;\n/**\n * The factor to backoff by.\n * Should be a number greater than 1.\n */\nconst DEFAULT_BACKOFF_FACTOR = 2;\n/**\n * The maximum milliseconds to increase to.\n *\n * <p>Visible for testing\n */\nconst MAX_VALUE_MILLIS = 4 * 60 * 60 * 1000; // Four hours, like iOS and Android.\n/**\n * The percentage of backoff time to randomize by.\n * See\n * http://go/safe-client-behavior#step-1-determine-the-appropriate-retry-interval-to-handle-spike-traffic\n * for context.\n *\n * <p>Visible for testing\n */\nconst RANDOM_FACTOR = 0.5;\n/**\n * Based on the backoff method from\n * https://github.com/google/closure-library/blob/master/closure/goog/math/exponentialbackoff.js.\n * Extracted here so we don't need to pass metadata and a stateful ExponentialBackoff object around.\n */\nfunction calculateBackoffMillis(backoffCount, intervalMillis = DEFAULT_INTERVAL_MILLIS, backoffFactor = DEFAULT_BACKOFF_FACTOR) {\n    // Calculates an exponentially increasing value.\n    // Deviation: calculates value from count and a constant interval, so we only need to save value\n    // and count to restore state.\n    const currBaseValue = intervalMillis * Math.pow(backoffFactor, backoffCount);\n    // A random \"fuzz\" to avoid waves of retries.\n    // Deviation: randomFactor is required.\n    const randomWait = Math.round(\n    // A fraction of the backoff value to add/subtract.\n    // Deviation: changes multiplication order to improve readability.\n    RANDOM_FACTOR *\n        currBaseValue *\n        // A random float (rounded to int by Math.round above) in the range [-1, 1]. Determines\n        // if we add or subtract.\n        (Math.random() - 0.5) *\n        2);\n    // Limits backoff to max to avoid effectively permanent backoff.\n    return Math.min(MAX_VALUE_MILLIS, currBaseValue + randomWait);\n}\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Provide English ordinal letters after a number\n */\nfunction ordinal(i) {\n    if (!Number.isFinite(i)) {\n        return `${i}`;\n    }\n    return i + indicator(i);\n}\nfunction indicator(i) {\n    i = Math.abs(i);\n    const cent = i % 100;\n    if (cent >= 10 && cent <= 20) {\n        return 'th';\n    }\n    const dec = i % 10;\n    if (dec === 1) {\n        return 'st';\n    }\n    if (dec === 2) {\n        return 'nd';\n    }\n    if (dec === 3) {\n        return 'rd';\n    }\n    return 'th';\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction getModularInstance(service) {\n    if (service && service._delegate) {\n        return service._delegate;\n    }\n    else {\n        return service;\n    }\n}\n\nexport { CONSTANTS, DecodeBase64StringError, Deferred, ErrorFactory, FirebaseError, MAX_VALUE_MILLIS, RANDOM_FACTOR, Sha1, areCookiesEnabled, assert, assertionError, async, base64, base64Decode, base64Encode, base64urlEncodeWithoutPadding, calculateBackoffMillis, contains, createMockUserToken, createSubscribe, decode, deepCopy, deepEqual, deepExtend, errorPrefix, extractQuerystring, getDefaultAppConfig, getDefaultEmulatorHost, getDefaultEmulatorHostnameAndPort, getDefaults, getExperimentalSetting, getGlobal, getModularInstance, getUA, isAdmin, isBrowser, isBrowserExtension, isCloudWorkstation, isCloudflareWorker, isElectron, isEmpty, isIE, isIndexedDBAvailable, isMobileCordova, isNode, isNodeSdk, isReactNative, isSafari, isSafariOrWebkit, isUWP, isValidFormat, isValidTimestamp, isWebWorker, issuedAtTime, jsonEval, map, ordinal, pingServer, promiseWithTimeout, querystring, querystringDecode, safeGet, stringLength, stringToByteArray, stringify, updateEmulatorBanner, validateArgCount, validateCallback, validateContextObject, validateIndexedDBOpenable, validateNamespace };\n"], "mappings": ";AAAA,SAASA,0BAA0B,QAAQ,mBAAmB;;AAE9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG;EACd;AACJ;AACA;EACIC,WAAW,EAAE,KAAK;EAClB;AACJ;AACA;EACIC,UAAU,EAAE,KAAK;EACjB;AACJ;AACA;EACIC,WAAW,EAAE;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,SAAAA,CAAUC,SAAS,EAAEC,OAAO,EAAE;EACzC,IAAI,CAACD,SAAS,EAAE;IACZ,MAAME,cAAc,CAACD,OAAO,CAAC;EACjC;AACJ,CAAC;AACD;AACA;AACA;AACA,MAAMC,cAAc,GAAG,SAAAA,CAAUD,OAAO,EAAE;EACtC,OAAO,IAAIE,KAAK,CAAC,qBAAqB,GAClCR,SAAS,CAACG,WAAW,GACrB,4BAA4B,GAC5BG,OAAO,CAAC;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,mBAAmB,GAAG,SAAAA,CAAUC,GAAG,EAAE;EACvC;EACA,MAAMC,GAAG,GAAG,EAAE;EACd,IAAIC,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,IAAIE,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;IACzB,IAAIE,CAAC,GAAG,GAAG,EAAE;MACTJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAGG,CAAC;IAChB,CAAC,MACI,IAAIA,CAAC,GAAG,IAAI,EAAE;MACfJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,CAAC,GAAI,GAAG;MACzBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B,CAAC,MACI,IAAI,CAACA,CAAC,GAAG,MAAM,MAAM,MAAM,IAC5BF,CAAC,GAAG,CAAC,GAAGH,GAAG,CAACI,MAAM,IAClB,CAACJ,GAAG,CAACM,UAAU,CAACH,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,MAAM,MAAM,EAAE;MAC7C;MACAE,CAAC,GAAG,OAAO,IAAI,CAACA,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,IAAIL,GAAG,CAACM,UAAU,CAAC,EAAEH,CAAC,CAAC,GAAG,MAAM,CAAC;MACnEF,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,EAAE,GAAI,EAAE,GAAI,GAAG;MACjCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B,CAAC,MACI;MACDJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B;EACJ;EACA,OAAOJ,GAAG;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,iBAAiB,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACvC;EACA,MAAMP,GAAG,GAAG,EAAE;EACd,IAAIQ,GAAG,GAAG,CAAC;IAAEJ,CAAC,GAAG,CAAC;EAClB,OAAOI,GAAG,GAAGD,KAAK,CAACJ,MAAM,EAAE;IACvB,MAAMM,EAAE,GAAGF,KAAK,CAACC,GAAG,EAAE,CAAC;IACvB,IAAIC,EAAE,GAAG,GAAG,EAAE;MACVT,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAACF,EAAE,CAAC;IACtC,CAAC,MACI,IAAIA,EAAE,GAAG,GAAG,IAAIA,EAAE,GAAG,GAAG,EAAE;MAC3B,MAAMG,EAAE,GAAGL,KAAK,CAACC,GAAG,EAAE,CAAC;MACvBR,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAE,CAACF,EAAE,GAAG,EAAE,KAAK,CAAC,GAAKG,EAAE,GAAG,EAAG,CAAC;IAChE,CAAC,MACI,IAAIH,EAAE,GAAG,GAAG,IAAIA,EAAE,GAAG,GAAG,EAAE;MAC3B;MACA,MAAMG,EAAE,GAAGL,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMK,EAAE,GAAGN,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMM,EAAE,GAAGP,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMO,CAAC,GAAG,CAAE,CAACN,EAAE,GAAG,CAAC,KAAK,EAAE,GAAK,CAACG,EAAE,GAAG,EAAE,KAAK,EAAG,GAAI,CAACC,EAAE,GAAG,EAAE,KAAK,CAAE,GAAIC,EAAE,GAAG,EAAG,IAC1E,OAAO;MACXd,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAC,MAAM,IAAII,CAAC,IAAI,EAAE,CAAC,CAAC;MAClDf,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAC,MAAM,IAAII,CAAC,GAAG,IAAI,CAAC,CAAC;IACvD,CAAC,MACI;MACD,MAAMH,EAAE,GAAGL,KAAK,CAACC,GAAG,EAAE,CAAC;MACvB,MAAMK,EAAE,GAAGN,KAAK,CAACC,GAAG,EAAE,CAAC;MACvBR,GAAG,CAACI,CAAC,EAAE,CAAC,GAAGM,MAAM,CAACC,YAAY,CAAE,CAACF,EAAE,GAAG,EAAE,KAAK,EAAE,GAAK,CAACG,EAAE,GAAG,EAAE,KAAK,CAAE,GAAIC,EAAE,GAAG,EAAG,CAAC;IACpF;EACJ;EACA,OAAOb,GAAG,CAACgB,IAAI,CAAC,EAAE,CAAC;AACvB,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG;EACX;AACJ;AACA;EACIC,cAAc,EAAE,IAAI;EACpB;AACJ;AACA;EACIC,cAAc,EAAE,IAAI;EACpB;AACJ;AACA;AACA;EACIC,qBAAqB,EAAE,IAAI;EAC3B;AACJ;AACA;AACA;EACIC,qBAAqB,EAAE,IAAI;EAC3B;AACJ;AACA;AACA;EACIC,iBAAiB,EAAE,4BAA4B,GAAG,4BAA4B,GAAG,YAAY;EAC7F;AACJ;AACA;EACI,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACD,iBAAiB,GAAG,KAAK;EACzC,CAAC;EACD;AACJ;AACA;EACI,IAAIE,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACF,iBAAiB,GAAG,KAAK;EACzC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,kBAAkB,EAAE,OAAOC,IAAI,KAAK,UAAU;EAC9C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACC,KAAK,EAAEC,OAAO,EAAE;IAC5B,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACvB,MAAM/B,KAAK,CAAC,+CAA+C,CAAC;IAChE;IACA,IAAI,CAACmC,KAAK,CAAC,CAAC;IACZ,MAAMC,aAAa,GAAGJ,OAAO,GACvB,IAAI,CAACT,qBAAqB,GAC1B,IAAI,CAACF,cAAc;IACzB,MAAMgB,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,KAAK,CAACzB,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACtC,MAAMiC,KAAK,GAAGP,KAAK,CAAC1B,CAAC,CAAC;MACtB,MAAMkC,SAAS,GAAGlC,CAAC,GAAG,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MACtC,MAAMkC,KAAK,GAAGD,SAAS,GAAGR,KAAK,CAAC1B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1C,MAAMoC,SAAS,GAAGpC,CAAC,GAAG,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MACtC,MAAMoC,KAAK,GAAGD,SAAS,GAAGV,KAAK,CAAC1B,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC1C,MAAMsC,QAAQ,GAAGL,KAAK,IAAI,CAAC;MAC3B,MAAMM,QAAQ,GAAI,CAACN,KAAK,GAAG,IAAI,KAAK,CAAC,GAAKE,KAAK,IAAI,CAAE;MACrD,IAAIK,QAAQ,GAAI,CAACL,KAAK,GAAG,IAAI,KAAK,CAAC,GAAKE,KAAK,IAAI,CAAE;MACnD,IAAII,QAAQ,GAAGJ,KAAK,GAAG,IAAI;MAC3B,IAAI,CAACD,SAAS,EAAE;QACZK,QAAQ,GAAG,EAAE;QACb,IAAI,CAACP,SAAS,EAAE;UACZM,QAAQ,GAAG,EAAE;QACjB;MACJ;MACAR,MAAM,CAACU,IAAI,CAACX,aAAa,CAACO,QAAQ,CAAC,EAAEP,aAAa,CAACQ,QAAQ,CAAC,EAAER,aAAa,CAACS,QAAQ,CAAC,EAAET,aAAa,CAACU,QAAQ,CAAC,CAAC;IACnH;IACA,OAAOT,MAAM,CAAClB,IAAI,CAAC,EAAE,CAAC;EAC1B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI6B,YAAYA,CAACjB,KAAK,EAAEC,OAAO,EAAE;IACzB;IACA;IACA,IAAI,IAAI,CAACJ,kBAAkB,IAAI,CAACI,OAAO,EAAE;MACrC,OAAOiB,IAAI,CAAClB,KAAK,CAAC;IACtB;IACA,OAAO,IAAI,CAACD,eAAe,CAAC7B,mBAAmB,CAAC8B,KAAK,CAAC,EAAEC,OAAO,CAAC;EACpE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIkB,YAAYA,CAACnB,KAAK,EAAEC,OAAO,EAAE;IACzB;IACA;IACA,IAAI,IAAI,CAACJ,kBAAkB,IAAI,CAACI,OAAO,EAAE;MACrC,OAAOH,IAAI,CAACE,KAAK,CAAC;IACtB;IACA,OAAOtB,iBAAiB,CAAC,IAAI,CAAC0C,uBAAuB,CAACpB,KAAK,EAAEC,OAAO,CAAC,CAAC;EAC1E,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImB,uBAAuBA,CAACpB,KAAK,EAAEC,OAAO,EAAE;IACpC,IAAI,CAACG,KAAK,CAAC,CAAC;IACZ,MAAMiB,aAAa,GAAGpB,OAAO,GACvB,IAAI,CAACR,qBAAqB,GAC1B,IAAI,CAACF,cAAc;IACzB,MAAMe,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,KAAK,CAACzB,MAAM,GAAG;MAC/B,MAAMgC,KAAK,GAAGc,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,EAAE,CAAC,CAAC;MAC9C,MAAMkC,SAAS,GAAGlC,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MAClC,MAAMkC,KAAK,GAAGD,SAAS,GAAGa,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAG,CAAC;MAC5D,EAAEA,CAAC;MACH,MAAMoC,SAAS,GAAGpC,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MAClC,MAAMoC,KAAK,GAAGD,SAAS,GAAGW,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAG,EAAE;MAC7D,EAAEA,CAAC;MACH,MAAMiD,SAAS,GAAGjD,CAAC,GAAG0B,KAAK,CAACzB,MAAM;MAClC,MAAMiD,KAAK,GAAGD,SAAS,GAAGF,aAAa,CAACrB,KAAK,CAACsB,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAG,EAAE;MAC7D,EAAEA,CAAC;MACH,IAAIiC,KAAK,IAAI,IAAI,IAAIE,KAAK,IAAI,IAAI,IAAIE,KAAK,IAAI,IAAI,IAAIa,KAAK,IAAI,IAAI,EAAE;QAClE,MAAM,IAAIC,uBAAuB,CAAC,CAAC;MACvC;MACA,MAAMb,QAAQ,GAAIL,KAAK,IAAI,CAAC,GAAKE,KAAK,IAAI,CAAE;MAC5CH,MAAM,CAACU,IAAI,CAACJ,QAAQ,CAAC;MACrB,IAAID,KAAK,KAAK,EAAE,EAAE;QACd,MAAME,QAAQ,GAAKJ,KAAK,IAAI,CAAC,GAAI,IAAI,GAAKE,KAAK,IAAI,CAAE;QACrDL,MAAM,CAACU,IAAI,CAACH,QAAQ,CAAC;QACrB,IAAIW,KAAK,KAAK,EAAE,EAAE;UACd,MAAMV,QAAQ,GAAKH,KAAK,IAAI,CAAC,GAAI,IAAI,GAAIa,KAAK;UAC9ClB,MAAM,CAACU,IAAI,CAACF,QAAQ,CAAC;QACzB;MACJ;IACJ;IACA,OAAOR,MAAM;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIF,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACd,cAAc,EAAE;MACtB,IAAI,CAACA,cAAc,GAAG,CAAC,CAAC;MACxB,IAAI,CAACC,cAAc,GAAG,CAAC,CAAC;MACxB,IAAI,CAACC,qBAAqB,GAAG,CAAC,CAAC;MAC/B,IAAI,CAACC,qBAAqB,GAAG,CAAC,CAAC;MAC/B;MACA,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACqB,YAAY,CAACpB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/C,IAAI,CAACgB,cAAc,CAAChB,CAAC,CAAC,GAAG,IAAI,CAACqB,YAAY,CAAC2B,MAAM,CAAChD,CAAC,CAAC;QACpD,IAAI,CAACiB,cAAc,CAAC,IAAI,CAACD,cAAc,CAAChB,CAAC,CAAC,CAAC,GAAGA,CAAC;QAC/C,IAAI,CAACkB,qBAAqB,CAAClB,CAAC,CAAC,GAAG,IAAI,CAACsB,oBAAoB,CAAC0B,MAAM,CAAChD,CAAC,CAAC;QACnE,IAAI,CAACmB,qBAAqB,CAAC,IAAI,CAACD,qBAAqB,CAAClB,CAAC,CAAC,CAAC,GAAGA,CAAC;QAC7D;QACA,IAAIA,CAAC,IAAI,IAAI,CAACoB,iBAAiB,CAACnB,MAAM,EAAE;UACpC,IAAI,CAACgB,cAAc,CAAC,IAAI,CAACK,oBAAoB,CAAC0B,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAGA,CAAC;UAC5D,IAAI,CAACmB,qBAAqB,CAAC,IAAI,CAACE,YAAY,CAAC2B,MAAM,CAAChD,CAAC,CAAC,CAAC,GAAGA,CAAC;QAC/D;MACJ;IACJ;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA,MAAMmD,uBAAuB,SAASxD,KAAK,CAAC;EACxCyD,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,IAAI,GAAG,yBAAyB;EACzC;AACJ;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,SAAAA,CAAU1D,GAAG,EAAE;EAChC,MAAM2D,SAAS,GAAG5D,mBAAmB,CAACC,GAAG,CAAC;EAC1C,OAAOkB,MAAM,CAACU,eAAe,CAAC+B,SAAS,EAAE,IAAI,CAAC;AAClD,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAG,SAAAA,CAAU5D,GAAG,EAAE;EACjD;EACA,OAAO0D,YAAY,CAAC1D,GAAG,CAAC,CAAC6D,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAC/C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,SAAAA,CAAU9D,GAAG,EAAE;EAChC,IAAI;IACA,OAAOkB,MAAM,CAAC8B,YAAY,CAAChD,GAAG,EAAE,IAAI,CAAC;EACzC,CAAC,CACD,OAAO+D,CAAC,EAAE;IACNC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,CAAC,CAAC;EAC7C;EACA,OAAO,IAAI;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAOC,UAAU,CAACC,SAAS,EAAEF,KAAK,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACE,MAAM,EAAEC,MAAM,EAAE;EAChC,IAAI,EAAEA,MAAM,YAAYC,MAAM,CAAC,EAAE;IAC7B,OAAOD,MAAM;EACjB;EACA,QAAQA,MAAM,CAAChB,WAAW;IACtB,KAAKkB,IAAI;MACL;MACA;MACA,MAAMC,SAAS,GAAGH,MAAM;MACxB,OAAO,IAAIE,IAAI,CAACC,SAAS,CAACC,OAAO,CAAC,CAAC,CAAC;IACxC,KAAKH,MAAM;MACP,IAAIF,MAAM,KAAKD,SAAS,EAAE;QACtBC,MAAM,GAAG,CAAC,CAAC;MACf;MACA;IACJ,KAAKvC,KAAK;MACN;MACAuC,MAAM,GAAG,EAAE;MACX;IACJ;MACI;MACA,OAAOC,MAAM;EACrB;EACA,KAAK,MAAMK,IAAI,IAAIL,MAAM,EAAE;IACvB;IACA,IAAI,CAACA,MAAM,CAACM,cAAc,CAACD,IAAI,CAAC,IAAI,CAACE,UAAU,CAACF,IAAI,CAAC,EAAE;MACnD;IACJ;IACAN,MAAM,CAACM,IAAI,CAAC,GAAGR,UAAU,CAACE,MAAM,CAACM,IAAI,CAAC,EAAEL,MAAM,CAACK,IAAI,CAAC,CAAC;EACzD;EACA,OAAON,MAAM;AACjB;AACA,SAASQ,UAAUA,CAACC,GAAG,EAAE;EACrB,OAAOA,GAAG,KAAK,WAAW;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAA,EAAG;EACjB,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAOA,IAAI;EACf;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOA,MAAM;EACjB;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOA,MAAM;EACjB;EACA,MAAM,IAAIrF,KAAK,CAAC,iCAAiC,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsF,qBAAqB,GAAGA,CAAA,KAAMJ,SAAS,CAAC,CAAC,CAACK,qBAAqB;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAGA,CAAA,KAAM;EACrC,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACC,GAAG,KAAK,WAAW,EAAE;IACtE;EACJ;EACA,MAAMC,kBAAkB,GAAGF,OAAO,CAACC,GAAG,CAACH,qBAAqB;EAC5D,IAAII,kBAAkB,EAAE;IACpB,OAAOC,IAAI,CAACC,KAAK,CAACF,kBAAkB,CAAC;EACzC;AACJ,CAAC;AACD,MAAMG,qBAAqB,GAAGA,CAAA,KAAM;EAChC,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACjC;EACJ;EACA,IAAIC,KAAK;EACT,IAAI;IACAA,KAAK,GAAGD,QAAQ,CAACE,MAAM,CAACD,KAAK,CAAC,+BAA+B,CAAC;EAClE,CAAC,CACD,OAAO/B,CAAC,EAAE;IACN;IACA;IACA;EACJ;EACA,MAAMiC,OAAO,GAAGF,KAAK,IAAIhC,YAAY,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/C,OAAOE,OAAO,IAAIN,IAAI,CAACC,KAAK,CAACK,OAAO,CAAC;AACzC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACtB,IAAI;IACA,OAAQ5G,0BAA0B,CAAC,CAAC,IAChC+F,qBAAqB,CAAC,CAAC,IACvBE,0BAA0B,CAAC,CAAC,IAC5BM,qBAAqB,CAAC,CAAC;EAC/B,CAAC,CACD,OAAO7B,CAAC,EAAE;IACN;AACR;AACA;AACA;AACA;AACA;IACQC,OAAO,CAACkC,IAAI,CAAE,+CAA8CnC,CAAE,EAAC,CAAC;IAChE;EACJ;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,sBAAsB,GAAIC,WAAW,IAAK;EAAE,IAAIC,EAAE,EAAEC,EAAE;EAAE,OAAO,CAACA,EAAE,GAAG,CAACD,EAAE,GAAGJ,WAAW,CAAC,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,aAAa,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACF,WAAW,CAAC;AAAE,CAAC;AAC9M;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,iCAAiC,GAAIJ,WAAW,IAAK;EACvD,MAAMK,IAAI,GAAGN,sBAAsB,CAACC,WAAW,CAAC;EAChD,IAAI,CAACK,IAAI,EAAE;IACP,OAAOpC,SAAS;EACpB;EACA,MAAMqC,cAAc,GAAGD,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9C,IAAID,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAG,CAAC,KAAKD,IAAI,CAACrG,MAAM,EAAE;IAC3D,MAAM,IAAIN,KAAK,CAAE,gBAAe2G,IAAK,sCAAqC,CAAC;EAC/E;EACA;EACA,MAAMG,IAAI,GAAGC,QAAQ,CAACJ,IAAI,CAACK,SAAS,CAACJ,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;EAC7D,IAAID,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACjB;IACA,OAAO,CAACA,IAAI,CAACK,SAAS,CAAC,CAAC,EAAEJ,cAAc,GAAG,CAAC,CAAC,EAAEE,IAAI,CAAC;EACxD,CAAC,MACI;IACD,OAAO,CAACH,IAAI,CAACK,SAAS,CAAC,CAAC,EAAEJ,cAAc,CAAC,EAAEE,IAAI,CAAC;EACpD;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;EAAE,IAAIV,EAAE;EAAE,OAAO,CAACA,EAAE,GAAGJ,WAAW,CAAC,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,MAAM;AAAE,CAAC;AACzH;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAIxD,IAAI,IAAK;EAAE,IAAI4C,EAAE;EAAE,OAAO,CAACA,EAAE,GAAGJ,WAAW,CAAC,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAE,IAAG5C,IAAK,EAAC,CAAC;AAAE,CAAC;;AAErI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyD,QAAQ,CAAC;EACX3D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4D,MAAM,GAAG,MAAM,CAAE,CAAC;IACvB,IAAI,CAACC,OAAO,GAAG,MAAM,CAAE,CAAC;IACxB,IAAI,CAACC,OAAO,GAAG,IAAIC,OAAO,CAAC,CAACF,OAAO,EAAED,MAAM,KAAK;MAC5C,IAAI,CAACC,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACD,MAAM,GAAGA,MAAM;IACxB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACII,YAAYA,CAACC,QAAQ,EAAE;IACnB,OAAO,CAACvD,KAAK,EAAEE,KAAK,KAAK;MACrB,IAAIF,KAAK,EAAE;QACP,IAAI,CAACkD,MAAM,CAAClD,KAAK,CAAC;MACtB,CAAC,MACI;QACD,IAAI,CAACmD,OAAO,CAACjD,KAAK,CAAC;MACvB;MACA,IAAI,OAAOqD,QAAQ,KAAK,UAAU,EAAE;QAChC;QACA;QACA,IAAI,CAACH,OAAO,CAACI,KAAK,CAAC,MAAM,CAAE,CAAC,CAAC;QAC7B;QACA;QACA,IAAID,QAAQ,CAACpH,MAAM,KAAK,CAAC,EAAE;UACvBoH,QAAQ,CAACvD,KAAK,CAAC;QACnB,CAAC,MACI;UACDuD,QAAQ,CAACvD,KAAK,EAAEE,KAAK,CAAC;QAC1B;MACJ;IACJ,CAAC;EACL;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuD,kBAAkBA,CAACjB,IAAI,EAAE;EAC9B,OAAOA,IAAI,CAACkB,QAAQ,CAAC,wBAAwB,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AAJA,SAKeC,UAAUA,CAAAC,EAAA;EAAA,OAAAC,WAAA,CAAAC,KAAA,OAAAvE,SAAA;AAAA;AAOzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAAAsE,YAAA;EAAAA,WAAA,GAAAE,iBAAA,CAPA,WAA0BC,QAAQ,EAAE;IAChC,MAAMC,MAAM,SAASC,KAAK,CAACF,QAAQ,EAAE;MACjCG,WAAW,EAAE;IACjB,CAAC,CAAC;IACF,OAAOF,MAAM,CAACG,EAAE;EACpB,CAAC;EAAA,OAAAP,WAAA,CAAAC,KAAA,OAAAvE,SAAA;AAAA;AAkBD,SAAS8E,mBAAmBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC3C,IAAID,KAAK,CAACE,GAAG,EAAE;IACX,MAAM,IAAI3I,KAAK,CAAC,8GAA8G,CAAC;EACnI;EACA;EACA,MAAM4I,MAAM,GAAG;IACXC,GAAG,EAAE,MAAM;IACXC,IAAI,EAAE;EACV,CAAC;EACD,MAAMC,OAAO,GAAGL,SAAS,IAAI,cAAc;EAC3C,MAAMM,GAAG,GAAGP,KAAK,CAACO,GAAG,IAAI,CAAC;EAC1B,MAAMC,GAAG,GAAGR,KAAK,CAACQ,GAAG,IAAIR,KAAK,CAACS,OAAO;EACtC,IAAI,CAACD,GAAG,EAAE;IACN,MAAM,IAAIjJ,KAAK,CAAC,sDAAsD,CAAC;EAC3E;EACA,MAAMmJ,OAAO,GAAGzE,MAAM,CAAC0E,MAAM,CAAC;IAC1B;IACAC,GAAG,EAAG,kCAAiCN,OAAQ,EAAC;IAAEO,GAAG,EAAEP,OAAO;IAAEC,GAAG;IAAEO,GAAG,EAAEP,GAAG,GAAG,IAAI;IAAEQ,SAAS,EAAER,GAAG;IAAEC,GAAG;IAAEC,OAAO,EAAED,GAAG;IAAEQ,QAAQ,EAAE;MAC/HC,gBAAgB,EAAE,QAAQ;MAC1BC,UAAU,EAAE,CAAC;IACjB;EAAE,CAAC,EAAElB,KAAK,CAAC;EACf;EACA,MAAMmB,SAAS,GAAG,EAAE;EACpB,OAAO,CACH9F,6BAA6B,CAAC8B,IAAI,CAACiE,SAAS,CAACjB,MAAM,CAAC,CAAC,EACrD9E,6BAA6B,CAAC8B,IAAI,CAACiE,SAAS,CAACV,OAAO,CAAC,CAAC,EACtDS,SAAS,CACZ,CAACzI,IAAI,CAAC,GAAG,CAAC;AACf;AACA,MAAM2I,cAAc,GAAG,CAAC,CAAC;AACzB;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,MAAMC,OAAO,GAAG;IACZC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE;EACd,CAAC;EACD,KAAK,MAAMjF,GAAG,IAAIP,MAAM,CAACyF,IAAI,CAACL,cAAc,CAAC,EAAE;IAC3C,IAAIA,cAAc,CAAC7E,GAAG,CAAC,EAAE;MACrB+E,OAAO,CAACE,QAAQ,CAACnH,IAAI,CAACkC,GAAG,CAAC;IAC9B,CAAC,MACI;MACD+E,OAAO,CAACC,IAAI,CAAClH,IAAI,CAACkC,GAAG,CAAC;IAC1B;EACJ;EACA,OAAO+E,OAAO;AAClB;AACA,SAASI,aAAaA,CAACC,EAAE,EAAE;EACvB,IAAIC,SAAS,GAAGvE,QAAQ,CAACwE,cAAc,CAACF,EAAE,CAAC;EAC3C,IAAIG,OAAO,GAAG,KAAK;EACnB,IAAI,CAACF,SAAS,EAAE;IACZA,SAAS,GAAGvE,QAAQ,CAAC0E,aAAa,CAAC,KAAK,CAAC;IACzCH,SAAS,CAACI,YAAY,CAAC,IAAI,EAAEL,EAAE,CAAC;IAChCG,OAAO,GAAG,IAAI;EAClB;EACA,OAAO;IAAEA,OAAO;IAAEG,OAAO,EAAEL;EAAU,CAAC;AAC1C;AACA,IAAIM,mBAAmB,GAAG,KAAK;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAClH,IAAI,EAAEmH,iBAAiB,EAAE;EACnD,IAAI,OAAO1F,MAAM,KAAK,WAAW,IAC7B,OAAOW,QAAQ,KAAK,WAAW,IAC/B,CAAC6B,kBAAkB,CAACxC,MAAM,CAAC2F,QAAQ,CAACpE,IAAI,CAAC,IACzCmD,cAAc,CAACnG,IAAI,CAAC,KAAKmH,iBAAiB,IAC1ChB,cAAc,CAACnG,IAAI,CAAC;EAAI;EACxBiH,mBAAmB,EAAE;IACrB;EACJ;EACAd,cAAc,CAACnG,IAAI,CAAC,GAAGmH,iBAAiB;EACxC,SAASE,UAAUA,CAACX,EAAE,EAAE;IACpB,OAAQ,uBAAsBA,EAAG,EAAC;EACtC;EACA,MAAMY,QAAQ,GAAG,oBAAoB;EACrC,MAAMjB,OAAO,GAAGD,kBAAkB,CAAC,CAAC;EACpC,MAAMmB,SAAS,GAAGlB,OAAO,CAACC,IAAI,CAAC3J,MAAM,GAAG,CAAC;EACzC,SAAS6K,QAAQA,CAAA,EAAG;IAChB,MAAMR,OAAO,GAAG5E,QAAQ,CAACwE,cAAc,CAACU,QAAQ,CAAC;IACjD,IAAIN,OAAO,EAAE;MACTA,OAAO,CAACS,MAAM,CAAC,CAAC;IACpB;EACJ;EACA,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;IACjCA,QAAQ,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;IAC/BF,QAAQ,CAACC,KAAK,CAACE,UAAU,GAAG,SAAS;IACrCH,QAAQ,CAACC,KAAK,CAACG,QAAQ,GAAG,OAAO;IACjCJ,QAAQ,CAACC,KAAK,CAACI,MAAM,GAAG,KAAK;IAC7BL,QAAQ,CAACC,KAAK,CAACK,IAAI,GAAG,KAAK;IAC3BN,QAAQ,CAACC,KAAK,CAACM,OAAO,GAAG,MAAM;IAC/BP,QAAQ,CAACC,KAAK,CAACO,YAAY,GAAG,KAAK;IACnCR,QAAQ,CAACC,KAAK,CAACQ,UAAU,GAAG,QAAQ;EACxC;EACA,SAASC,eAAeA,CAACC,WAAW,EAAEC,MAAM,EAAE;IAC1CD,WAAW,CAACvB,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC;IACvCuB,WAAW,CAACvB,YAAY,CAAC,IAAI,EAAEwB,MAAM,CAAC;IACtCD,WAAW,CAACvB,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC;IACxCuB,WAAW,CAACvB,YAAY,CAAC,SAAS,EAAE,WAAW,CAAC;IAChDuB,WAAW,CAACvB,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;IACxCuB,WAAW,CAACV,KAAK,CAACY,UAAU,GAAG,MAAM;EACzC;EACA,SAASC,aAAaA,CAAA,EAAG;IACrB,MAAMC,QAAQ,GAAGtG,QAAQ,CAAC0E,aAAa,CAAC,MAAM,CAAC;IAC/C4B,QAAQ,CAACd,KAAK,CAACe,MAAM,GAAG,SAAS;IACjCD,QAAQ,CAACd,KAAK,CAACY,UAAU,GAAG,MAAM;IAClCE,QAAQ,CAACd,KAAK,CAACgB,QAAQ,GAAG,MAAM;IAChCF,QAAQ,CAACG,SAAS,GAAG,UAAU;IAC/BH,QAAQ,CAACI,OAAO,GAAG,MAAM;MACrB7B,mBAAmB,GAAG,IAAI;MAC1BO,QAAQ,CAAC,CAAC;IACd,CAAC;IACD,OAAOkB,QAAQ;EACnB;EACA,SAASK,eAAeA,CAACC,aAAa,EAAEC,WAAW,EAAE;IACjDD,aAAa,CAACjC,YAAY,CAAC,IAAI,EAAEkC,WAAW,CAAC;IAC7CD,aAAa,CAACE,SAAS,GAAG,YAAY;IACtCF,aAAa,CAACG,IAAI,GACd,sEAAsE;IAC1EH,aAAa,CAACjC,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAC;IAC/CiC,aAAa,CAACpB,KAAK,CAACwB,WAAW,GAAG,KAAK;IACvCJ,aAAa,CAACpB,KAAK,CAACyB,cAAc,GAAG,WAAW;EACpD;EACA,SAASC,QAAQA,CAAA,EAAG;IAChB,MAAMC,MAAM,GAAG9C,aAAa,CAACa,QAAQ,CAAC;IACtC,MAAMkC,cAAc,GAAGnC,UAAU,CAAC,MAAM,CAAC;IACzC,MAAMoC,YAAY,GAAGrH,QAAQ,CAACwE,cAAc,CAAC4C,cAAc,CAAC,IAAIpH,QAAQ,CAAC0E,aAAa,CAAC,MAAM,CAAC;IAC9F,MAAMmC,WAAW,GAAG5B,UAAU,CAAC,WAAW,CAAC;IAC3C,MAAM2B,aAAa,GAAG5G,QAAQ,CAACwE,cAAc,CAACqC,WAAW,CAAC,IACtD7G,QAAQ,CAAC0E,aAAa,CAAC,GAAG,CAAC;IAC/B,MAAM4C,aAAa,GAAGrC,UAAU,CAAC,cAAc,CAAC;IAChD,MAAMiB,WAAW,GAAGlG,QAAQ,CAACwE,cAAc,CAAC8C,aAAa,CAAC,IACtDtH,QAAQ,CAACuH,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;IACjE,IAAIJ,MAAM,CAAC1C,OAAO,EAAE;MAChB;MACA,MAAMc,QAAQ,GAAG4B,MAAM,CAACvC,OAAO;MAC/BU,iBAAiB,CAACC,QAAQ,CAAC;MAC3BoB,eAAe,CAACC,aAAa,EAAEC,WAAW,CAAC;MAC3C,MAAMP,QAAQ,GAAGD,aAAa,CAAC,CAAC;MAChCJ,eAAe,CAACC,WAAW,EAAEoB,aAAa,CAAC;MAC3C/B,QAAQ,CAACiC,MAAM,CAACtB,WAAW,EAAEmB,YAAY,EAAET,aAAa,EAAEN,QAAQ,CAAC;MACnEtG,QAAQ,CAACyH,IAAI,CAACC,WAAW,CAACnC,QAAQ,CAAC;IACvC;IACA,IAAIJ,SAAS,EAAE;MACXkC,YAAY,CAACP,SAAS,GAAI,+BAA8B;MACxDZ,WAAW,CAACO,SAAS,GAAI;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;IACA,CAAC,MACI;MACDP,WAAW,CAACO,SAAS,GAAI;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;MACIY,YAAY,CAACP,SAAS,GAAG,4CAA4C;IACzE;IACAO,YAAY,CAAC1C,YAAY,CAAC,IAAI,EAAEyC,cAAc,CAAC;EACnD;EACA,IAAIpH,QAAQ,CAAC2H,UAAU,KAAK,SAAS,EAAE;IACnCtI,MAAM,CAACuI,gBAAgB,CAAC,kBAAkB,EAAEV,QAAQ,CAAC;EACzD,CAAC,MACI;IACDA,QAAQ,CAAC,CAAC;EACd;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,KAAKA,CAAA,EAAG;EACb,IAAI,OAAOC,SAAS,KAAK,WAAW,IAChC,OAAOA,SAAS,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;IAC5C,OAAOA,SAAS,CAAC,WAAW,CAAC;EACjC,CAAC,MACI;IACD,OAAO,EAAE;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAAA,EAAG;EACvB,OAAQ,OAAO1I,MAAM,KAAK,WAAW;EACjC;EACA;EACA,CAAC,EAAEA,MAAM,CAAC,SAAS,CAAC,IAAIA,MAAM,CAAC,UAAU,CAAC,IAAIA,MAAM,CAAC,UAAU,CAAC,CAAC,IACjE,mDAAmD,CAAC2I,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,MAAMA,CAAA,EAAG;EACd,IAAIzH,EAAE;EACN,MAAM0H,gBAAgB,GAAG,CAAC1H,EAAE,GAAGJ,WAAW,CAAC,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC0H,gBAAgB;EACtG,IAAIA,gBAAgB,KAAK,MAAM,EAAE;IAC7B,OAAO,IAAI;EACf,CAAC,MACI,IAAIA,gBAAgB,KAAK,SAAS,EAAE;IACrC,OAAO,KAAK;EAChB;EACA,IAAI;IACA,OAAQvJ,MAAM,CAACwJ,SAAS,CAACC,QAAQ,CAACC,IAAI,CAAC/I,MAAM,CAACI,OAAO,CAAC,KAAK,kBAAkB;EACjF,CAAC,CACD,OAAOxB,CAAC,EAAE;IACN,OAAO,KAAK;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoK,SAASA,CAAA,EAAG;EACjB,OAAO,OAAOjJ,MAAM,KAAK,WAAW,IAAIkJ,WAAW,CAAC,CAAC;AACzD;AACA;AACA;AACA;AACA,SAASA,WAAWA,CAAA,EAAG;EACnB,OAAQ,OAAOC,iBAAiB,KAAK,WAAW,IAC5C,OAAOpJ,IAAI,KAAK,WAAW,IAC3BA,IAAI,YAAYoJ,iBAAiB;AACzC;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,OAAQ,OAAOX,SAAS,KAAK,WAAW,IACpCA,SAAS,CAACY,SAAS,KAAK,oBAAoB;AACpD;AACA,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,MAAMC,OAAO,GAAG,OAAOC,MAAM,KAAK,QAAQ,GACpCA,MAAM,CAACD,OAAO,GACd,OAAOE,OAAO,KAAK,QAAQ,GACvBA,OAAO,CAACF,OAAO,GACfpK,SAAS;EACnB,OAAO,OAAOoK,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACtE,EAAE,KAAK9F,SAAS;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,SAASuK,aAAaA,CAAA,EAAG;EACrB,OAAQ,OAAOjB,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAAC,SAAS,CAAC,KAAK,aAAa;AACnF;AACA;AACA,SAASkB,UAAUA,CAAA,EAAG;EAClB,OAAOnB,KAAK,CAAC,CAAC,CAACoB,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;AAC5C;AACA;AACA,SAASC,IAAIA,CAAA,EAAG;EACZ,MAAMC,EAAE,GAAGtB,KAAK,CAAC,CAAC;EAClB,OAAOsB,EAAE,CAACF,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,IAAIE,EAAE,CAACF,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;AAClE;AACA;AACA,SAASG,KAAKA,CAAA,EAAG;EACb,OAAOvB,KAAK,CAAC,CAAC,CAACoB,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,SAASA,CAAA,EAAG;EACjB,OAAO5P,SAAS,CAACC,WAAW,KAAK,IAAI,IAAID,SAAS,CAACE,UAAU,KAAK,IAAI;AAC1E;AACA;AACA,SAAS2P,QAAQA,CAAA,EAAG;EAChB,OAAQ,CAACrB,MAAM,CAAC,CAAC,IACb,CAAC,CAACH,SAAS,CAACY,SAAS,IACrBZ,SAAS,CAACY,SAAS,CAACa,QAAQ,CAAC,QAAQ,CAAC,IACtC,CAACzB,SAAS,CAACY,SAAS,CAACa,QAAQ,CAAC,QAAQ,CAAC;AAC/C;AACA;AACA,SAASC,gBAAgBA,CAAA,EAAG;EACxB,OAAQ,CAACvB,MAAM,CAAC,CAAC,IACb,CAAC,CAACH,SAAS,CAACY,SAAS,KACpBZ,SAAS,CAACY,SAAS,CAACa,QAAQ,CAAC,QAAQ,CAAC,IACnCzB,SAAS,CAACY,SAAS,CAACa,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAC3C,CAACzB,SAAS,CAACY,SAAS,CAACa,QAAQ,CAAC,QAAQ,CAAC;AAC/C;AACA;AACA;AACA;AACA;AACA,SAASE,oBAAoBA,CAAA,EAAG;EAC5B,IAAI;IACA,OAAO,OAAOC,SAAS,KAAK,QAAQ;EACxC,CAAC,CACD,OAAOxL,CAAC,EAAE;IACN,OAAO,KAAK;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyL,yBAAyBA,CAAA,EAAG;EACjC,OAAO,IAAIlI,OAAO,CAAC,CAACF,OAAO,EAAED,MAAM,KAAK;IACpC,IAAI;MACA,IAAIsI,QAAQ,GAAG,IAAI;MACnB,MAAMC,aAAa,GAAG,yDAAyD;MAC/E,MAAMC,OAAO,GAAG1K,IAAI,CAACsK,SAAS,CAACK,IAAI,CAACF,aAAa,CAAC;MAClDC,OAAO,CAACE,SAAS,GAAG,MAAM;QACtBF,OAAO,CAACzH,MAAM,CAAC4H,KAAK,CAAC,CAAC;QACtB;QACA,IAAI,CAACL,QAAQ,EAAE;UACXxK,IAAI,CAACsK,SAAS,CAACQ,cAAc,CAACL,aAAa,CAAC;QAChD;QACAtI,OAAO,CAAC,IAAI,CAAC;MACjB,CAAC;MACDuI,OAAO,CAACK,eAAe,GAAG,MAAM;QAC5BP,QAAQ,GAAG,KAAK;MACpB,CAAC;MACDE,OAAO,CAACM,OAAO,GAAG,MAAM;QACpB,IAAI5J,EAAE;QACNc,MAAM,CAAC,CAAC,CAACd,EAAE,GAAGsJ,OAAO,CAAC1L,KAAK,MAAM,IAAI,IAAIoC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACzG,OAAO,KAAK,EAAE,CAAC;MACxF,CAAC;IACL,CAAC,CACD,OAAOqE,KAAK,EAAE;MACVkD,MAAM,CAAClD,KAAK,CAAC;IACjB;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA,SAASiM,iBAAiBA,CAAA,EAAG;EACzB,IAAI,OAAOvC,SAAS,KAAK,WAAW,IAAI,CAACA,SAAS,CAACwC,aAAa,EAAE;IAC9D,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,eAAe;AAClC;AACA;AACA,MAAMC,aAAa,SAASvQ,KAAK,CAAC;EAC9ByD,WAAWA,CAAA,CACX;EACA+M,IAAI,EAAE1Q,OAAO,EACb;EACA2Q,UAAU,EAAE;IACR,KAAK,CAAC3Q,OAAO,CAAC;IACd,IAAI,CAAC0Q,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B;IACA,IAAI,CAAC9M,IAAI,GAAG2M,UAAU;IACtB;IACA;IACA;IACA;IACA5L,MAAM,CAACgM,cAAc,CAAC,IAAI,EAAEH,aAAa,CAACrC,SAAS,CAAC;IACpD;IACA;IACA,IAAIlO,KAAK,CAAC2Q,iBAAiB,EAAE;MACzB3Q,KAAK,CAAC2Q,iBAAiB,CAAC,IAAI,EAAEC,YAAY,CAAC1C,SAAS,CAAC2C,MAAM,CAAC;IAChE;EACJ;AACJ;AACA,MAAMD,YAAY,CAAC;EACfnN,WAAWA,CAACqN,OAAO,EAAEC,WAAW,EAAEC,MAAM,EAAE;IACtC,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAH,MAAMA,CAACL,IAAI,EAAE,GAAGS,IAAI,EAAE;IAClB,MAAMR,UAAU,GAAGQ,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAChC,MAAMC,QAAQ,GAAI,GAAE,IAAI,CAACJ,OAAQ,IAAGN,IAAK,EAAC;IAC1C,MAAMW,QAAQ,GAAG,IAAI,CAACH,MAAM,CAACR,IAAI,CAAC;IAClC,MAAM1Q,OAAO,GAAGqR,QAAQ,GAAGC,eAAe,CAACD,QAAQ,EAAEV,UAAU,CAAC,GAAG,OAAO;IAC1E;IACA,MAAMY,WAAW,GAAI,GAAE,IAAI,CAACN,WAAY,KAAIjR,OAAQ,KAAIoR,QAAS,IAAG;IACpE,MAAM/M,KAAK,GAAG,IAAIoM,aAAa,CAACW,QAAQ,EAAEG,WAAW,EAAEZ,UAAU,CAAC;IAClE,OAAOtM,KAAK;EAChB;AACJ;AACA,SAASiN,eAAeA,CAACD,QAAQ,EAAEF,IAAI,EAAE;EACrC,OAAOE,QAAQ,CAACpN,OAAO,CAACuN,OAAO,EAAE,CAACC,CAAC,EAAEtM,GAAG,KAAK;IACzC,MAAMZ,KAAK,GAAG4M,IAAI,CAAChM,GAAG,CAAC;IACvB,OAAOZ,KAAK,IAAI,IAAI,GAAGxD,MAAM,CAACwD,KAAK,CAAC,GAAI,IAAGY,GAAI,IAAG;EACtD,CAAC,CAAC;AACN;AACA,MAAMqM,OAAO,GAAG,eAAe;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,QAAQA,CAACtR,GAAG,EAAE;EACnB,OAAO0F,IAAI,CAACC,KAAK,CAAC3F,GAAG,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2J,SAASA,CAACoH,IAAI,EAAE;EACrB,OAAOrL,IAAI,CAACiE,SAAS,CAACoH,IAAI,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,MAAM,GAAG,SAAAA,CAAUhJ,KAAK,EAAE;EAC5B,IAAIG,MAAM,GAAG,CAAC,CAAC;IAAE8I,MAAM,GAAG,CAAC,CAAC;IAAET,IAAI,GAAG,CAAC,CAAC;IAAErH,SAAS,GAAG,EAAE;EACvD,IAAI;IACA,MAAM+H,KAAK,GAAGlJ,KAAK,CAACmJ,KAAK,CAAC,GAAG,CAAC;IAC9BhJ,MAAM,GAAG4I,QAAQ,CAACxN,YAAY,CAAC2N,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/CD,MAAM,GAAGF,QAAQ,CAACxN,YAAY,CAAC2N,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IAC/C/H,SAAS,GAAG+H,KAAK,CAAC,CAAC,CAAC;IACpBV,IAAI,GAAGS,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxB,OAAOA,MAAM,CAAC,GAAG,CAAC;EACtB,CAAC,CACD,OAAOzN,CAAC,EAAE,CAAE;EACZ,OAAO;IACH2E,MAAM;IACN8I,MAAM;IACNT,IAAI;IACJrH;EACJ,CAAC;AACL,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiI,gBAAgB,GAAG,SAAAA,CAAUpJ,KAAK,EAAE;EACtC,MAAMiJ,MAAM,GAAGD,MAAM,CAAChJ,KAAK,CAAC,CAACiJ,MAAM;EACnC,MAAMI,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAIrN,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC;EACnD,IAAIoN,UAAU,GAAG,CAAC;IAAEC,UAAU,GAAG,CAAC;EAClC,IAAI,OAAOR,MAAM,KAAK,QAAQ,EAAE;IAC5B,IAAIA,MAAM,CAAC3M,cAAc,CAAC,KAAK,CAAC,EAAE;MAC9BkN,UAAU,GAAGP,MAAM,CAAC,KAAK,CAAC;IAC9B,CAAC,MACI,IAAIA,MAAM,CAAC3M,cAAc,CAAC,KAAK,CAAC,EAAE;MACnCkN,UAAU,GAAGP,MAAM,CAAC,KAAK,CAAC;IAC9B;IACA,IAAIA,MAAM,CAAC3M,cAAc,CAAC,KAAK,CAAC,EAAE;MAC9BmN,UAAU,GAAGR,MAAM,CAAC,KAAK,CAAC;IAC9B,CAAC,MACI;MACD;MACAQ,UAAU,GAAGD,UAAU,GAAG,KAAK;IACnC;EACJ;EACA,OAAQ,CAAC,CAACH,GAAG,IACT,CAAC,CAACG,UAAU,IACZ,CAAC,CAACC,UAAU,IACZJ,GAAG,IAAIG,UAAU,IACjBH,GAAG,IAAII,UAAU;AACzB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,SAAAA,CAAU1J,KAAK,EAAE;EAClC,MAAMiJ,MAAM,GAAGD,MAAM,CAAChJ,KAAK,CAAC,CAACiJ,MAAM;EACnC,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAAC3M,cAAc,CAAC,KAAK,CAAC,EAAE;IAC5D,OAAO2M,MAAM,CAAC,KAAK,CAAC;EACxB;EACA,OAAO,IAAI;AACf,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMU,aAAa,GAAG,SAAAA,CAAU3J,KAAK,EAAE;EACnC,MAAMvC,OAAO,GAAGuL,MAAM,CAAChJ,KAAK,CAAC;IAAEiJ,MAAM,GAAGxL,OAAO,CAACwL,MAAM;EACtD,OAAO,CAAC,CAACA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAAC3M,cAAc,CAAC,KAAK,CAAC;AACjF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsN,OAAO,GAAG,SAAAA,CAAU5J,KAAK,EAAE;EAC7B,MAAMiJ,MAAM,GAAGD,MAAM,CAAChJ,KAAK,CAAC,CAACiJ,MAAM;EACnC,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI;AACjE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASY,QAAQA,CAACC,GAAG,EAAEtN,GAAG,EAAE;EACxB,OAAOP,MAAM,CAACwJ,SAAS,CAACnJ,cAAc,CAACqJ,IAAI,CAACmE,GAAG,EAAEtN,GAAG,CAAC;AACzD;AACA,SAASuN,OAAOA,CAACD,GAAG,EAAEtN,GAAG,EAAE;EACvB,IAAIP,MAAM,CAACwJ,SAAS,CAACnJ,cAAc,CAACqJ,IAAI,CAACmE,GAAG,EAAEtN,GAAG,CAAC,EAAE;IAChD,OAAOsN,GAAG,CAACtN,GAAG,CAAC;EACnB,CAAC,MACI;IACD,OAAOV,SAAS;EACpB;AACJ;AACA,SAASkO,OAAOA,CAACF,GAAG,EAAE;EAClB,KAAK,MAAMtN,GAAG,IAAIsN,GAAG,EAAE;IACnB,IAAI7N,MAAM,CAACwJ,SAAS,CAACnJ,cAAc,CAACqJ,IAAI,CAACmE,GAAG,EAAEtN,GAAG,CAAC,EAAE;MAChD,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASyN,GAAGA,CAACH,GAAG,EAAEI,EAAE,EAAEC,UAAU,EAAE;EAC9B,MAAMC,GAAG,GAAG,CAAC,CAAC;EACd,KAAK,MAAM5N,GAAG,IAAIsN,GAAG,EAAE;IACnB,IAAI7N,MAAM,CAACwJ,SAAS,CAACnJ,cAAc,CAACqJ,IAAI,CAACmE,GAAG,EAAEtN,GAAG,CAAC,EAAE;MAChD4N,GAAG,CAAC5N,GAAG,CAAC,GAAG0N,EAAE,CAACvE,IAAI,CAACwE,UAAU,EAAEL,GAAG,CAACtN,GAAG,CAAC,EAAEA,GAAG,EAAEsN,GAAG,CAAC;IACtD;EACJ;EACA,OAAOM,GAAG;AACd;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACrB,IAAID,CAAC,KAAKC,CAAC,EAAE;IACT,OAAO,IAAI;EACf;EACA,MAAMC,KAAK,GAAGvO,MAAM,CAACyF,IAAI,CAAC4I,CAAC,CAAC;EAC5B,MAAMG,KAAK,GAAGxO,MAAM,CAACyF,IAAI,CAAC6I,CAAC,CAAC;EAC5B,KAAK,MAAMG,CAAC,IAAIF,KAAK,EAAE;IACnB,IAAI,CAACC,KAAK,CAAC5D,QAAQ,CAAC6D,CAAC,CAAC,EAAE;MACpB,OAAO,KAAK;IAChB;IACA,MAAMC,KAAK,GAAGL,CAAC,CAACI,CAAC,CAAC;IAClB,MAAME,KAAK,GAAGL,CAAC,CAACG,CAAC,CAAC;IAClB,IAAIG,QAAQ,CAACF,KAAK,CAAC,IAAIE,QAAQ,CAACD,KAAK,CAAC,EAAE;MACpC,IAAI,CAACP,SAAS,CAACM,KAAK,EAAEC,KAAK,CAAC,EAAE;QAC1B,OAAO,KAAK;MAChB;IACJ,CAAC,MACI,IAAID,KAAK,KAAKC,KAAK,EAAE;MACtB,OAAO,KAAK;IAChB;EACJ;EACA,KAAK,MAAMF,CAAC,IAAID,KAAK,EAAE;IACnB,IAAI,CAACD,KAAK,CAAC3D,QAAQ,CAAC6D,CAAC,CAAC,EAAE;MACpB,OAAO,KAAK;IAChB;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASG,QAAQA,CAACC,KAAK,EAAE;EACrB,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ;AACtD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACjM,OAAO,EAAEkM,QAAQ,GAAG,IAAI,EAAE;EAClD,MAAMC,eAAe,GAAG,IAAItM,QAAQ,CAAC,CAAC;EACtCuM,UAAU,CAAC,MAAMD,eAAe,CAACrM,MAAM,CAAC,UAAU,CAAC,EAAEoM,QAAQ,CAAC;EAC9DlM,OAAO,CAACqM,IAAI,CAACF,eAAe,CAACpM,OAAO,EAAEoM,eAAe,CAACrM,MAAM,CAAC;EAC7D,OAAOqM,eAAe,CAACnM,OAAO;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsM,WAAWA,CAACC,iBAAiB,EAAE;EACpC,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,MAAM,CAAC9O,GAAG,EAAEZ,KAAK,CAAC,IAAIK,MAAM,CAACsP,OAAO,CAACF,iBAAiB,CAAC,EAAE;IAC1D,IAAI7R,KAAK,CAACC,OAAO,CAACmC,KAAK,CAAC,EAAE;MACtBA,KAAK,CAAC4P,OAAO,CAACC,QAAQ,IAAI;QACtBH,MAAM,CAAChR,IAAI,CAACoR,kBAAkB,CAAClP,GAAG,CAAC,GAAG,GAAG,GAAGkP,kBAAkB,CAACD,QAAQ,CAAC,CAAC;MAC7E,CAAC,CAAC;IACN,CAAC,MACI;MACDH,MAAM,CAAChR,IAAI,CAACoR,kBAAkB,CAAClP,GAAG,CAAC,GAAG,GAAG,GAAGkP,kBAAkB,CAAC9P,KAAK,CAAC,CAAC;IAC1E;EACJ;EACA,OAAO0P,MAAM,CAACzT,MAAM,GAAG,GAAG,GAAGyT,MAAM,CAAC5S,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;AACtD;AACA;AACA;AACA;AACA;AACA,SAASiT,iBAAiBA,CAACP,WAAW,EAAE;EACpC,MAAMtB,GAAG,GAAG,CAAC,CAAC;EACd,MAAM8B,MAAM,GAAGR,WAAW,CAAC9P,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC6N,KAAK,CAAC,GAAG,CAAC;EACxDyC,MAAM,CAACJ,OAAO,CAACxL,KAAK,IAAI;IACpB,IAAIA,KAAK,EAAE;MACP,MAAM,CAACxD,GAAG,EAAEZ,KAAK,CAAC,GAAGoE,KAAK,CAACmJ,KAAK,CAAC,GAAG,CAAC;MACrCW,GAAG,CAAC+B,kBAAkB,CAACrP,GAAG,CAAC,CAAC,GAAGqP,kBAAkB,CAACjQ,KAAK,CAAC;IAC5D;EACJ,CAAC,CAAC;EACF,OAAOkO,GAAG;AACd;AACA;AACA;AACA;AACA,SAASgC,kBAAkBA,CAACC,GAAG,EAAE;EAC7B,MAAMC,UAAU,GAAGD,GAAG,CAACxF,OAAO,CAAC,GAAG,CAAC;EACnC,IAAI,CAACyF,UAAU,EAAE;IACb,OAAO,EAAE;EACb;EACA,MAAMC,aAAa,GAAGF,GAAG,CAACxF,OAAO,CAAC,GAAG,EAAEyF,UAAU,CAAC;EAClD,OAAOD,GAAG,CAACxN,SAAS,CAACyN,UAAU,EAAEC,aAAa,GAAG,CAAC,GAAGA,aAAa,GAAGnQ,SAAS,CAAC;AACnF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoQ,IAAI,CAAC;EACPlR,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACmR,MAAM,GAAG,EAAE;IAChB;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,EAAE,GAAG,EAAE;IACZ;AACR;AACA;AACA;IACQ,IAAI,CAACC,IAAI,GAAG,EAAE;IACd;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC;IACf;AACR;AACA;IACQ,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,SAAS,GAAG,GAAG,GAAG,CAAC;IACxB,IAAI,CAACH,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG;IAClB,KAAK,IAAI1U,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC6U,SAAS,EAAE,EAAE7U,CAAC,EAAE;MACrC,IAAI,CAAC0U,IAAI,CAAC1U,CAAC,CAAC,GAAG,CAAC;IACpB;IACA,IAAI,CAAC8U,KAAK,CAAC,CAAC;EAChB;EACAA,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACP,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG,UAAU;IAC3B,IAAI,CAACI,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,CAAC;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,SAASA,CAACC,GAAG,EAAEC,MAAM,EAAE;IACnB,IAAI,CAACA,MAAM,EAAE;MACTA,MAAM,GAAG,CAAC;IACd;IACA,MAAMC,CAAC,GAAG,IAAI,CAACT,EAAE;IACjB;IACA,IAAI,OAAOO,GAAG,KAAK,QAAQ,EAAE;MACzB,KAAK,IAAIhV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAkV,CAAC,CAAClV,CAAC,CAAC,GACCgV,GAAG,CAAC7U,UAAU,CAAC8U,MAAM,CAAC,IAAI,EAAE,GACxBD,GAAG,CAAC7U,UAAU,CAAC8U,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,GACjCD,GAAG,CAAC7U,UAAU,CAAC8U,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACjCD,GAAG,CAAC7U,UAAU,CAAC8U,MAAM,GAAG,CAAC,CAAC;QAClCA,MAAM,IAAI,CAAC;MACf;IACJ,CAAC,MACI;MACD,KAAK,IAAIjV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACzBkV,CAAC,CAAClV,CAAC,CAAC,GACCgV,GAAG,CAACC,MAAM,CAAC,IAAI,EAAE,GACbD,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAG,GACtBD,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAE,GACtBD,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;QACvBA,MAAM,IAAI,CAAC;MACf;IACJ;IACA;IACA,KAAK,IAAIjV,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC1B,MAAMmV,CAAC,GAAGD,CAAC,CAAClV,CAAC,GAAG,CAAC,CAAC,GAAGkV,CAAC,CAAClV,CAAC,GAAG,CAAC,CAAC,GAAGkV,CAAC,CAAClV,CAAC,GAAG,EAAE,CAAC,GAAGkV,CAAC,CAAClV,CAAC,GAAG,EAAE,CAAC;MACrDkV,CAAC,CAAClV,CAAC,CAAC,GAAG,CAAEmV,CAAC,IAAI,CAAC,GAAKA,CAAC,KAAK,EAAG,IAAI,UAAU;IAC/C;IACA,IAAIzC,CAAC,GAAG,IAAI,CAAC6B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI5B,CAAC,GAAG,IAAI,CAAC4B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAIrU,CAAC,GAAG,IAAI,CAACqU,MAAM,CAAC,CAAC,CAAC;IACtB,IAAIa,CAAC,GAAG,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI3Q,CAAC,GAAG,IAAI,CAAC2Q,MAAM,CAAC,CAAC,CAAC;IACtB,IAAIc,CAAC,EAAEvC,CAAC;IACR;IACA,KAAK,IAAI9S,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzB,IAAIA,CAAC,GAAG,EAAE,EAAE;QACR,IAAIA,CAAC,GAAG,EAAE,EAAE;UACRqV,CAAC,GAAGD,CAAC,GAAIzC,CAAC,IAAIzS,CAAC,GAAGkV,CAAC,CAAE;UACrBtC,CAAC,GAAG,UAAU;QAClB,CAAC,MACI;UACDuC,CAAC,GAAG1C,CAAC,GAAGzS,CAAC,GAAGkV,CAAC;UACbtC,CAAC,GAAG,UAAU;QAClB;MACJ,CAAC,MACI;QACD,IAAI9S,CAAC,GAAG,EAAE,EAAE;UACRqV,CAAC,GAAI1C,CAAC,GAAGzS,CAAC,GAAKkV,CAAC,IAAIzC,CAAC,GAAGzS,CAAC,CAAE;UAC3B4S,CAAC,GAAG,UAAU;QAClB,CAAC,MACI;UACDuC,CAAC,GAAG1C,CAAC,GAAGzS,CAAC,GAAGkV,CAAC;UACbtC,CAAC,GAAG,UAAU;QAClB;MACJ;MACA,MAAMqC,CAAC,GAAI,CAAEzC,CAAC,IAAI,CAAC,GAAKA,CAAC,KAAK,EAAG,IAAI2C,CAAC,GAAGzR,CAAC,GAAGkP,CAAC,GAAGoC,CAAC,CAAClV,CAAC,CAAC,GAAI,UAAU;MACnE4D,CAAC,GAAGwR,CAAC;MACLA,CAAC,GAAGlV,CAAC;MACLA,CAAC,GAAG,CAAEyS,CAAC,IAAI,EAAE,GAAKA,CAAC,KAAK,CAAE,IAAI,UAAU;MACxCA,CAAC,GAAGD,CAAC;MACLA,CAAC,GAAGyC,CAAC;IACT;IACA,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG7B,CAAC,GAAI,UAAU;IAClD,IAAI,CAAC6B,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG5B,CAAC,GAAI,UAAU;IAClD,IAAI,CAAC4B,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGrU,CAAC,GAAI,UAAU;IAClD,IAAI,CAACqU,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAGa,CAAC,GAAI,UAAU;IAClD,IAAI,CAACb,MAAM,CAAC,CAAC,CAAC,GAAI,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG3Q,CAAC,GAAI,UAAU;EACtD;EACA0R,MAAMA,CAACjV,KAAK,EAAEJ,MAAM,EAAE;IAClB;IACA,IAAII,KAAK,IAAI,IAAI,EAAE;MACf;IACJ;IACA,IAAIJ,MAAM,KAAKiE,SAAS,EAAE;MACtBjE,MAAM,GAAGI,KAAK,CAACJ,MAAM;IACzB;IACA,MAAMsV,gBAAgB,GAAGtV,MAAM,GAAG,IAAI,CAAC4U,SAAS;IAChD,IAAIW,CAAC,GAAG,CAAC;IACT;IACA,MAAMR,GAAG,GAAG,IAAI,CAACR,IAAI;IACrB,IAAIiB,KAAK,GAAG,IAAI,CAACd,MAAM;IACvB;IACA,OAAOa,CAAC,GAAGvV,MAAM,EAAE;MACf;MACA;MACA;MACA;MACA,IAAIwV,KAAK,KAAK,CAAC,EAAE;QACb,OAAOD,CAAC,IAAID,gBAAgB,EAAE;UAC1B,IAAI,CAACR,SAAS,CAAC1U,KAAK,EAAEmV,CAAC,CAAC;UACxBA,CAAC,IAAI,IAAI,CAACX,SAAS;QACvB;MACJ;MACA,IAAI,OAAOxU,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAOmV,CAAC,GAAGvV,MAAM,EAAE;UACf+U,GAAG,CAACS,KAAK,CAAC,GAAGpV,KAAK,CAACF,UAAU,CAACqV,CAAC,CAAC;UAChC,EAAEC,KAAK;UACP,EAAED,CAAC;UACH,IAAIC,KAAK,KAAK,IAAI,CAACZ,SAAS,EAAE;YAC1B,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC;YACnBS,KAAK,GAAG,CAAC;YACT;YACA;UACJ;QACJ;MACJ,CAAC,MACI;QACD,OAAOD,CAAC,GAAGvV,MAAM,EAAE;UACf+U,GAAG,CAACS,KAAK,CAAC,GAAGpV,KAAK,CAACmV,CAAC,CAAC;UACrB,EAAEC,KAAK;UACP,EAAED,CAAC;UACH,IAAIC,KAAK,KAAK,IAAI,CAACZ,SAAS,EAAE;YAC1B,IAAI,CAACE,SAAS,CAACC,GAAG,CAAC;YACnBS,KAAK,GAAG,CAAC;YACT;YACA;UACJ;QACJ;MACJ;IACJ;IACA,IAAI,CAACd,MAAM,GAAGc,KAAK;IACnB,IAAI,CAACb,MAAM,IAAI3U,MAAM;EACzB;EACA;EACAyV,MAAMA,CAAA,EAAG;IACL,MAAMA,MAAM,GAAG,EAAE;IACjB,IAAIC,SAAS,GAAG,IAAI,CAACf,MAAM,GAAG,CAAC;IAC/B;IACA,IAAI,IAAI,CAACD,MAAM,GAAG,EAAE,EAAE;MAClB,IAAI,CAACW,MAAM,CAAC,IAAI,CAACZ,IAAI,EAAE,EAAE,GAAG,IAAI,CAACC,MAAM,CAAC;IAC5C,CAAC,MACI;MACD,IAAI,CAACW,MAAM,CAAC,IAAI,CAACZ,IAAI,EAAE,IAAI,CAACG,SAAS,IAAI,IAAI,CAACF,MAAM,GAAG,EAAE,CAAC,CAAC;IAC/D;IACA;IACA,KAAK,IAAI3U,CAAC,GAAG,IAAI,CAAC6U,SAAS,GAAG,CAAC,EAAE7U,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3C,IAAI,CAACwU,IAAI,CAACxU,CAAC,CAAC,GAAG2V,SAAS,GAAG,GAAG;MAC9BA,SAAS,IAAI,GAAG,CAAC,CAAC;IACtB;;IACA,IAAI,CAACZ,SAAS,CAAC,IAAI,CAACP,IAAI,CAAC;IACzB,IAAIgB,CAAC,GAAG,CAAC;IACT,KAAK,IAAIxV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,KAAK,IAAI4V,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;QAC7BF,MAAM,CAACF,CAAC,CAAC,GAAI,IAAI,CAACjB,MAAM,CAACvU,CAAC,CAAC,IAAI4V,CAAC,GAAI,GAAG;QACvC,EAAEJ,CAAC;MACP;IACJ;IACA,OAAOE,MAAM;EACjB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,eAAeA,CAACC,QAAQ,EAAEC,aAAa,EAAE;EAC9C,MAAMC,KAAK,GAAG,IAAIC,aAAa,CAACH,QAAQ,EAAEC,aAAa,CAAC;EACxD,OAAOC,KAAK,CAACE,SAAS,CAACC,IAAI,CAACH,KAAK,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB;AACJ;AACA;AACA;AACA;EACI7S,WAAWA,CAAC0S,QAAQ,EAAEC,aAAa,EAAE;IACjC,IAAI,CAACK,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB;IACA,IAAI,CAACC,IAAI,GAAGpP,OAAO,CAACF,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACuP,SAAS,GAAG,KAAK;IACtB,IAAI,CAACT,aAAa,GAAGA,aAAa;IAClC;IACA;IACA;IACA,IAAI,CAACQ,IAAI,CACJhD,IAAI,CAAC,MAAM;MACZuC,QAAQ,CAAC,IAAI,CAAC;IAClB,CAAC,CAAC,CACGxO,KAAK,CAAC1D,CAAC,IAAI;MACZ,IAAI,CAACE,KAAK,CAACF,CAAC,CAAC;IACjB,CAAC,CAAC;EACN;EACA6S,IAAIA,CAACzS,KAAK,EAAE;IACR,IAAI,CAAC0S,eAAe,CAAEC,QAAQ,IAAK;MAC/BA,QAAQ,CAACF,IAAI,CAACzS,KAAK,CAAC;IACxB,CAAC,CAAC;EACN;EACAF,KAAKA,CAACA,KAAK,EAAE;IACT,IAAI,CAAC4S,eAAe,CAAEC,QAAQ,IAAK;MAC/BA,QAAQ,CAAC7S,KAAK,CAACA,KAAK,CAAC;IACzB,CAAC,CAAC;IACF,IAAI,CAAC6L,KAAK,CAAC7L,KAAK,CAAC;EACrB;EACA8S,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,eAAe,CAAEC,QAAQ,IAAK;MAC/BA,QAAQ,CAACC,QAAQ,CAAC,CAAC;IACvB,CAAC,CAAC;IACF,IAAI,CAACjH,KAAK,CAAC,CAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuG,SAASA,CAACW,cAAc,EAAE/S,KAAK,EAAE8S,QAAQ,EAAE;IACvC,IAAID,QAAQ;IACZ,IAAIE,cAAc,KAAK3S,SAAS,IAC5BJ,KAAK,KAAKI,SAAS,IACnB0S,QAAQ,KAAK1S,SAAS,EAAE;MACxB,MAAM,IAAIvE,KAAK,CAAC,mBAAmB,CAAC;IACxC;IACA;IACA,IAAImX,oBAAoB,CAACD,cAAc,EAAE,CACrC,MAAM,EACN,OAAO,EACP,UAAU,CACb,CAAC,EAAE;MACAF,QAAQ,GAAGE,cAAc;IAC7B,CAAC,MACI;MACDF,QAAQ,GAAG;QACPF,IAAI,EAAEI,cAAc;QACpB/S,KAAK;QACL8S;MACJ,CAAC;IACL;IACA,IAAID,QAAQ,CAACF,IAAI,KAAKvS,SAAS,EAAE;MAC7ByS,QAAQ,CAACF,IAAI,GAAGM,IAAI;IACxB;IACA,IAAIJ,QAAQ,CAAC7S,KAAK,KAAKI,SAAS,EAAE;MAC9ByS,QAAQ,CAAC7S,KAAK,GAAGiT,IAAI;IACzB;IACA,IAAIJ,QAAQ,CAACC,QAAQ,KAAK1S,SAAS,EAAE;MACjCyS,QAAQ,CAACC,QAAQ,GAAGG,IAAI;IAC5B;IACA,MAAMC,KAAK,GAAG,IAAI,CAACC,cAAc,CAACd,IAAI,CAAC,IAAI,EAAE,IAAI,CAACC,SAAS,CAACnW,MAAM,CAAC;IACnE;IACA;IACA;IACA,IAAI,IAAI,CAACuW,SAAS,EAAE;MAChB;MACA,IAAI,CAACD,IAAI,CAAChD,IAAI,CAAC,MAAM;QACjB,IAAI;UACA,IAAI,IAAI,CAAC2D,UAAU,EAAE;YACjBP,QAAQ,CAAC7S,KAAK,CAAC,IAAI,CAACoT,UAAU,CAAC;UACnC,CAAC,MACI;YACDP,QAAQ,CAACC,QAAQ,CAAC,CAAC;UACvB;QACJ,CAAC,CACD,OAAOhT,CAAC,EAAE;UACN;QAAA;QAEJ;MACJ,CAAC,CAAC;IACN;IACA,IAAI,CAACwS,SAAS,CAAC1T,IAAI,CAACiU,QAAQ,CAAC;IAC7B,OAAOK,KAAK;EAChB;EACA;EACA;EACAC,cAAcA,CAACjX,CAAC,EAAE;IACd,IAAI,IAAI,CAACoW,SAAS,KAAKlS,SAAS,IAAI,IAAI,CAACkS,SAAS,CAACpW,CAAC,CAAC,KAAKkE,SAAS,EAAE;MACjE;IACJ;IACA,OAAO,IAAI,CAACkS,SAAS,CAACpW,CAAC,CAAC;IACxB,IAAI,CAACsW,aAAa,IAAI,CAAC;IACvB,IAAI,IAAI,CAACA,aAAa,KAAK,CAAC,IAAI,IAAI,CAACP,aAAa,KAAK7R,SAAS,EAAE;MAC9D,IAAI,CAAC6R,aAAa,CAAC,IAAI,CAAC;IAC5B;EACJ;EACAW,eAAeA,CAACpE,EAAE,EAAE;IAChB,IAAI,IAAI,CAACkE,SAAS,EAAE;MAChB;MACA;IACJ;IACA;IACA;IACA,KAAK,IAAIxW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACoW,SAAS,CAACnW,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAI,CAACmX,OAAO,CAACnX,CAAC,EAAEsS,EAAE,CAAC;IACvB;EACJ;EACA;EACA;EACA;EACA6E,OAAOA,CAACnX,CAAC,EAAEsS,EAAE,EAAE;IACX;IACA;IACA,IAAI,CAACiE,IAAI,CAAChD,IAAI,CAAC,MAAM;MACjB,IAAI,IAAI,CAAC6C,SAAS,KAAKlS,SAAS,IAAI,IAAI,CAACkS,SAAS,CAACpW,CAAC,CAAC,KAAKkE,SAAS,EAAE;QACjE,IAAI;UACAoO,EAAE,CAAC,IAAI,CAAC8D,SAAS,CAACpW,CAAC,CAAC,CAAC;QACzB,CAAC,CACD,OAAO4D,CAAC,EAAE;UACN;UACA;UACA;UACA,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,KAAK,EAAE;YACjDD,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;UACpB;QACJ;MACJ;IACJ,CAAC,CAAC;EACN;EACA+L,KAAKA,CAACyH,GAAG,EAAE;IACP,IAAI,IAAI,CAACZ,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACA,SAAS,GAAG,IAAI;IACrB,IAAIY,GAAG,KAAKlT,SAAS,EAAE;MACnB,IAAI,CAACgT,UAAU,GAAGE,GAAG;IACzB;IACA;IACA;IACA,IAAI,CAACb,IAAI,CAAChD,IAAI,CAAC,MAAM;MACjB,IAAI,CAAC6C,SAAS,GAAGlS,SAAS;MAC1B,IAAI,CAAC6R,aAAa,GAAG7R,SAAS;IAClC,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA,SAASmT,KAAKA,CAAC/E,EAAE,EAAEgF,OAAO,EAAE;EACxB,OAAO,CAAC,GAAGC,IAAI,KAAK;IAChBpQ,OAAO,CAACF,OAAO,CAAC,IAAI,CAAC,CAChBsM,IAAI,CAAC,MAAM;MACZjB,EAAE,CAAC,GAAGiF,IAAI,CAAC;IACf,CAAC,CAAC,CACGjQ,KAAK,CAAExD,KAAK,IAAK;MAClB,IAAIwT,OAAO,EAAE;QACTA,OAAO,CAACxT,KAAK,CAAC;MAClB;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AACA;AACA;AACA;AACA,SAASgT,oBAAoBA,CAAC5E,GAAG,EAAEsF,OAAO,EAAE;EACxC,IAAI,OAAOtF,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IACzC,OAAO,KAAK;EAChB;EACA,KAAK,MAAMuF,MAAM,IAAID,OAAO,EAAE;IAC1B,IAAIC,MAAM,IAAIvF,GAAG,IAAI,OAAOA,GAAG,CAACuF,MAAM,CAAC,KAAK,UAAU,EAAE;MACpD,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;AACA,SAASV,IAAIA,CAAA,EAAG;EACZ;AAAA;;AAGJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,gBAAgB,GAAG,SAAAA,CAAUC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EACrE,IAAIC,QAAQ;EACZ,IAAID,QAAQ,GAAGF,QAAQ,EAAE;IACrBG,QAAQ,GAAG,WAAW,GAAGH,QAAQ;EACrC,CAAC,MACI,IAAIE,QAAQ,GAAGD,QAAQ,EAAE;IAC1BE,QAAQ,GAAGF,QAAQ,KAAK,CAAC,GAAG,MAAM,GAAG,eAAe,GAAGA,QAAQ;EACnE;EACA,IAAIE,QAAQ,EAAE;IACV,MAAMjU,KAAK,GAAG6T,MAAM,GAChB,2BAA2B,GAC3BG,QAAQ,IACPA,QAAQ,KAAK,CAAC,GAAG,YAAY,GAAG,aAAa,CAAC,GAC/C,WAAW,GACXC,QAAQ,GACR,GAAG;IACP,MAAM,IAAIpY,KAAK,CAACmE,KAAK,CAAC;EAC1B;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkU,WAAWA,CAACL,MAAM,EAAEM,OAAO,EAAE;EAClC,OAAQ,GAAEN,MAAO,YAAWM,OAAQ,YAAW;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACP,MAAM,EAAEQ,SAAS,EAAEC,QAAQ,EAAE;EACpD,IAAIA,QAAQ,IAAI,CAACD,SAAS,EAAE;IACxB;EACJ;EACA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IAC/B;IACA,MAAM,IAAIxY,KAAK,CAACqY,WAAW,CAACL,MAAM,EAAE,WAAW,CAAC,GAAG,qCAAqC,CAAC;EAC7F;AACJ;AACA,SAASU,gBAAgBA,CAACV,MAAM,EAAEW,YAAY;AAC9C;AACAjR,QAAQ,EAAE+Q,QAAQ,EAAE;EAChB,IAAIA,QAAQ,IAAI,CAAC/Q,QAAQ,EAAE;IACvB;EACJ;EACA,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;IAChC,MAAM,IAAI1H,KAAK,CAACqY,WAAW,CAACL,MAAM,EAAEW,YAAY,CAAC,GAAG,2BAA2B,CAAC;EACpF;AACJ;AACA,SAASC,qBAAqBA,CAACZ,MAAM,EAAEW,YAAY,EAAEE,OAAO,EAAEJ,QAAQ,EAAE;EACpE,IAAIA,QAAQ,IAAI,CAACI,OAAO,EAAE;IACtB;EACJ;EACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE;IACjD,MAAM,IAAI7Y,KAAK,CAACqY,WAAW,CAACL,MAAM,EAAEW,YAAY,CAAC,GAAG,iCAAiC,CAAC;EAC1F;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,iBAAiB,GAAG,SAAAA,CAAU5Y,GAAG,EAAE;EACrC,MAAMC,GAAG,GAAG,EAAE;EACd,IAAIC,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,IAAIE,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;IACzB;IACA,IAAIE,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;MAC5B,MAAMwY,IAAI,GAAGxY,CAAC,GAAG,MAAM,CAAC,CAAC;MACzBF,CAAC,EAAE;MACHT,MAAM,CAACS,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAE,yCAAyC,CAAC;MACjE,MAAM0Y,GAAG,GAAG9Y,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;MACxCE,CAAC,GAAG,OAAO,IAAIwY,IAAI,IAAI,EAAE,CAAC,GAAGC,GAAG;IACpC;IACA,IAAIzY,CAAC,GAAG,GAAG,EAAE;MACTJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAGG,CAAC;IAChB,CAAC,MACI,IAAIA,CAAC,GAAG,IAAI,EAAE;MACfJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,CAAC,GAAI,GAAG;MACzBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B,CAAC,MACI,IAAIA,CAAC,GAAG,KAAK,EAAE;MAChBJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B,CAAC,MACI;MACDJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,IAAI,EAAE,GAAI,GAAG;MAC1BJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,EAAE,GAAI,EAAE,GAAI,GAAG;MACjCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAKG,CAAC,IAAI,CAAC,GAAI,EAAE,GAAI,GAAG;MAChCJ,GAAG,CAACC,CAAC,EAAE,CAAC,GAAIG,CAAC,GAAG,EAAE,GAAI,GAAG;IAC7B;EACJ;EACA,OAAOJ,GAAG;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAM8Y,YAAY,GAAG,SAAAA,CAAU/Y,GAAG,EAAE;EAChC,IAAIE,CAAC,GAAG,CAAC;EACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACjC,MAAME,CAAC,GAAGL,GAAG,CAACM,UAAU,CAACH,CAAC,CAAC;IAC3B,IAAIE,CAAC,GAAG,GAAG,EAAE;MACTH,CAAC,EAAE;IACP,CAAC,MACI,IAAIG,CAAC,GAAG,IAAI,EAAE;MACfH,CAAC,IAAI,CAAC;IACV,CAAC,MACI,IAAIG,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;MACjC;MACAH,CAAC,IAAI,CAAC;MACNC,CAAC,EAAE,CAAC,CAAC;IACT,CAAC,MACI;MACDD,CAAC,IAAI,CAAC;IACV;EACJ;EACA,OAAOA,CAAC;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8Y,uBAAuB,GAAG,IAAI;AACpC;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,GAAG;AACzB;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,YAAY,EAAEC,cAAc,GAAGN,uBAAuB,EAAEO,aAAa,GAAGN,sBAAsB,EAAE;EAC5H;EACA;EACA;EACA,MAAMO,aAAa,GAAGF,cAAc,GAAGzH,IAAI,CAAC4H,GAAG,CAACF,aAAa,EAAEF,YAAY,CAAC;EAC5E;EACA;EACA,MAAMK,UAAU,GAAG7H,IAAI,CAAC8H,KAAK;EAC7B;EACA;EACAR,aAAa,GACTK,aAAa;EACb;EACA;EACC3H,IAAI,CAAC+H,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GACrB,CAAC,CAAC;EACN;EACA,OAAO/H,IAAI,CAACgI,GAAG,CAACX,gBAAgB,EAAEM,aAAa,GAAGE,UAAU,CAAC;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,OAAOA,CAAC3Z,CAAC,EAAE;EAChB,IAAI,CAAC4Z,MAAM,CAACC,QAAQ,CAAC7Z,CAAC,CAAC,EAAE;IACrB,OAAQ,GAAEA,CAAE,EAAC;EACjB;EACA,OAAOA,CAAC,GAAG8Z,SAAS,CAAC9Z,CAAC,CAAC;AAC3B;AACA,SAAS8Z,SAASA,CAAC9Z,CAAC,EAAE;EAClBA,CAAC,GAAG0R,IAAI,CAACqI,GAAG,CAAC/Z,CAAC,CAAC;EACf,MAAMga,IAAI,GAAGha,CAAC,GAAG,GAAG;EACpB,IAAIga,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,EAAE;IAC1B,OAAO,IAAI;EACf;EACA,MAAMC,GAAG,GAAGja,CAAC,GAAG,EAAE;EAClB,IAAIia,GAAG,KAAK,CAAC,EAAE;IACX,OAAO,IAAI;EACf;EACA,IAAIA,GAAG,KAAK,CAAC,EAAE;IACX,OAAO,IAAI;EACf;EACA,IAAIA,GAAG,KAAK,CAAC,EAAE;IACX,OAAO,IAAI;EACf;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACzJ,OAAO,EAAE;EACjC,IAAIA,OAAO,IAAIA,OAAO,CAAC0J,SAAS,EAAE;IAC9B,OAAO1J,OAAO,CAAC0J,SAAS;EAC5B,CAAC,MACI;IACD,OAAO1J,OAAO;EAClB;AACJ;AAEA,SAAStR,SAAS,EAAEgE,uBAAuB,EAAE4D,QAAQ,EAAEwJ,YAAY,EAAEL,aAAa,EAAE6I,gBAAgB,EAAEC,aAAa,EAAE1E,IAAI,EAAEvE,iBAAiB,EAAExQ,MAAM,EAAEG,cAAc,EAAE2X,KAAK,EAAEtW,MAAM,EAAE4C,YAAY,EAAEJ,YAAY,EAAEE,6BAA6B,EAAEwV,sBAAsB,EAAEhH,QAAQ,EAAE9J,mBAAmB,EAAE0N,eAAe,EAAEzE,MAAM,EAAErN,QAAQ,EAAE0O,SAAS,EAAExO,UAAU,EAAE+T,WAAW,EAAE9D,kBAAkB,EAAEtN,mBAAmB,EAAEZ,sBAAsB,EAAEK,iCAAiC,EAAEP,WAAW,EAAEgB,sBAAsB,EAAEjC,SAAS,EAAEqV,kBAAkB,EAAE3M,KAAK,EAAEyE,OAAO,EAAEhE,SAAS,EAAEK,kBAAkB,EAAE9G,kBAAkB,EAAE4G,kBAAkB,EAAEO,UAAU,EAAE0D,OAAO,EAAExD,IAAI,EAAEO,oBAAoB,EAAE1B,eAAe,EAAEE,MAAM,EAAEoB,SAAS,EAAEN,aAAa,EAAEO,QAAQ,EAAEE,gBAAgB,EAAEJ,KAAK,EAAEiD,aAAa,EAAEP,gBAAgB,EAAEvD,WAAW,EAAE6D,YAAY,EAAEX,QAAQ,EAAEkB,GAAG,EAAEsH,OAAO,EAAElS,UAAU,EAAE0L,kBAAkB,EAAEK,WAAW,EAAEO,iBAAiB,EAAE5B,OAAO,EAAEyG,YAAY,EAAEH,iBAAiB,EAAEjP,SAAS,EAAEgB,oBAAoB,EAAEkN,gBAAgB,EAAEW,gBAAgB,EAAEE,qBAAqB,EAAElJ,yBAAyB,EAAE6I,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}