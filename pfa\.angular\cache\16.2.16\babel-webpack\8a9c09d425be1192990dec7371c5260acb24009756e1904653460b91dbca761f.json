{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nexport class SignupComponent {\n  constructor() {\n    this.user = {\n      name: '',\n      email: '',\n      password: '',\n      terms: false\n    };\n  }\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 53,\n      vars: 5,\n      consts: [[1, \"signup-container\"], [1, \"left-section\"], [1, \"graphic\"], [1, \"iris-collage\"], [\"src\", \"assets/iris-collage.png\", \"alt\", \"Iris Collage\", 1, \"iris-collage-img\"], [1, \"circle\", \"circle1\"], [1, \"circle\", \"circle2\"], [1, \"circle\", \"circle3\"], [1, \"slogan\"], [1, \"right-section\"], [1, \"form-container\"], [1, \"logo\"], [1, \"custom-logo\"], [1, \"google-btn\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google Icon\"], [1, \"divider\"], [3, \"ngSubmit\"], [\"signupForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \"Leslie Alexander\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"placeholder\", \"<EMAIL>\", \"required\", \"\", \"email\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"placeholder\", \"At least 8 characters\", \"required\", \"\", \"minlength\", \"8\", 3, \"ngModel\", \"ngModelChange\"], [1, \"checkbox-group\"], [\"type\", \"checkbox\", \"id\", \"terms\", \"name\", \"terms\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"terms\"], [\"href\", \"#\"], [\"type\", \"submit\", 1, \"signup-btn\", 3, \"disabled\"], [1, \"login-link\"], [\"routerLink\", \"/login\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 8);\n          i0.ɵɵtext(9, \" Each iris is a unique story written by nature, waiting to be decoded by technology \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"div\", 11)(13, \"div\", 12);\n          i0.ɵɵelement(14, \"span\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"h1\");\n          i0.ɵɵtext(16, \"Sign Up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\");\n          i0.ɵɵtext(18, \"Votre identit\\u00E9, red\\u00E9finie par la technologie. Cr\\u00E9ez votre compte.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 13);\n          i0.ɵɵelement(20, \"img\", 14);\n          i0.ɵɵtext(21, \" Continue with Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 15);\n          i0.ɵɵtext(23, \"or Sign in with Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"form\", 16, 17);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_24_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(26, \"div\", 18)(27, \"label\", 19);\n          i0.ɵɵtext(28, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_29_listener($event) {\n            return ctx.user.name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 18)(31, \"label\", 21);\n          i0.ɵɵtext(32, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_33_listener($event) {\n            return ctx.user.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 18)(35, \"label\", 23);\n          i0.ɵɵtext(36, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"input\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_37_listener($event) {\n            return ctx.user.password = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 25)(39, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_39_listener($event) {\n            return ctx.user.terms = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"label\", 27);\n          i0.ɵɵtext(41, \"I agree with \");\n          i0.ɵɵelementStart(42, \"a\", 28);\n          i0.ɵɵtext(43, \"Terms\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \" and \");\n          i0.ɵɵelementStart(45, \"a\", 28);\n          i0.ɵɵtext(46, \"Privacy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"button\", 29);\n          i0.ɵɵtext(48, \" Sign Up \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"p\", 30);\n          i0.ɵɵtext(50, \" Already have an account? \");\n          i0.ɵɵelementStart(51, \"a\", 31);\n          i0.ɵɵtext(52, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(25);\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.terms);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", !_r0.valid);\n        }\n      },\n      dependencies: [i1.RouterLink, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MinLengthValidator, i2.CheckboxRequiredValidator, i2.EmailValidator, i2.NgModel, i2.NgForm],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.left-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, #e6e6fa, #f5f5ff);\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.graphic[_ngcontent-%COMP%] {\\n  position: relative;\\n  text-align: center;\\n}\\n\\n\\n\\n.abstract-design[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 450px; \\n\\n  height: 300px; \\n\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n\\n\\n.abstract-design[_ngcontent-%COMP%]::before, .abstract-design[_ngcontent-%COMP%]::after, .abstract-design[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before, .abstract-design[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  border-radius: 50%;\\n  background: radial-gradient(circle, rgba(106, 90, 205, 0.3), rgba(106, 90, 205, 0.1));\\n  box-shadow: 0 0 20px rgba(106, 90, 205, 0.3);\\n  filter: blur(10px);\\n}\\n\\n\\n\\n.abstract-design[_ngcontent-%COMP%]::before {\\n  width: 150px;\\n  height: 150px;\\n  top: 20px;\\n  left: 50px;\\n  background: radial-gradient(circle, rgba(106, 90, 205, 0.5), rgba(106, 90, 205, 0.2));\\n}\\n\\n\\n\\n.abstract-design[_ngcontent-%COMP%]::after {\\n  width: 150px;\\n  height: 150px;\\n  top: 20px;\\n  right: 50px;\\n  background: radial-gradient(circle, rgba(255, 165, 0, 0.5), rgba(255, 165, 0, 0.2));\\n}\\n\\n\\n\\n.abstract-design[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before {\\n  width: 150px;\\n  height: 150px;\\n  bottom: 20px;\\n  left: 100px;\\n  background: radial-gradient(circle, rgba(106, 90, 205, 0.5), rgba(106, 90, 205, 0.2));\\n}\\n\\n\\n\\n.abstract-design[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  width: 150px;\\n  height: 150px;\\n  bottom: 20px;\\n  right: 100px;\\n  background: radial-gradient(circle, rgba(255, 165, 0, 0.5), rgba(255, 165, 0, 0.2));\\n}\\n\\n\\n\\n.circle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border-radius: 50%;\\n  background: rgba(255, 182, 193, 0.3);\\n}\\n\\n.circle1[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  top: 20px;\\n  left: 50px;\\n}\\n\\n.circle2[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  bottom: 50px;\\n  right: 30px;\\n}\\n\\n.circle3[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  top: 80px;\\n  right: 80px;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%]::before, .left-section[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  border-radius: 50%;\\n  background: #ff4040;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before {\\n  width: 5px;\\n  height: 5px;\\n  top: 10%;\\n  left: 20%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::after {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 15%;\\n  right: 10%;\\n}\\n\\n.slogan[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #6a5acd;\\n  text-align: center;\\n  margin-top: 20px;\\n  font-style: italic;\\n  font-family: \\\"Dancing Script\\\", cursive;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2lnbnVwL3NpZ251cC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxnQkFBZ0I7QUFBaEI7RUFDRSxPQUFBO0VBQ0EscURBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQUVGOztBQUNBO0VBQ0Usa0JBQUE7RUFDQSxrQkFBQTtBQUVGOztBQUNBLDJDQUFBO0FBQ0E7RUFDRSxrQkFBQTtFQUNBLFlBQUEsRUFBQSx1Q0FBQTtFQUNBLGFBQUEsRUFBQSw4QkFBQTtFQUNBLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0FBRUY7O0FBQ0EsMkNBQUE7QUFDQTs7OztFQUlFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0EscUZBQUE7RUFDQSw0Q0FBQTtFQUNBLGtCQUFBO0FBRUY7O0FBQ0EsaUNBQUE7QUFDQTtFQUNFLFlBQUE7RUFDQSxhQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7RUFDQSxxRkFBQTtBQUVGOztBQUNBLG1DQUFBO0FBQ0E7RUFDRSxZQUFBO0VBQ0EsYUFBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EsbUZBQUE7QUFFRjs7QUFDQSxtQ0FBQTtBQUNBO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLHFGQUFBO0FBRUY7O0FBQ0Esb0NBQUE7QUFDQTtFQUNFLFlBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxtRkFBQTtBQUVGOztBQUNBLG1DQUFBO0FBQ0E7RUFDRSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0Esb0NBQUE7QUFFRjs7QUFDQTtFQUNFLFlBQUE7RUFDQSxhQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7QUFFRjs7QUFDQTtFQUNFLFlBQUE7RUFDQSxhQUFBO0VBQ0EsWUFBQTtFQUNBLFdBQUE7QUFFRjs7QUFDQTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7QUFFRjs7QUFDQSxrQ0FBQTtBQUNBOztFQUVFLFdBQUE7RUFDQSxrQkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7QUFFRjs7QUFDQTtFQUNFLFVBQUE7RUFDQSxXQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7QUFFRjs7QUFDQTtFQUNFLFVBQUE7RUFDQSxXQUFBO0VBQ0EsV0FBQTtFQUNBLFVBQUE7QUFFRjs7QUFDQTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLHNDQUFBO0FBRUYiLCJzb3VyY2VzQ29udGVudCI6WyIubGVmdC1zZWN0aW9uIHtcclxuICBmbGV4OiAxO1xyXG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNlNmU2ZmEsICNmNWY1ZmYpO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICBvdmVyZmxvdzogaGlkZGVuO1xyXG59XHJcblxyXG4uZ3JhcGhpYyB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxufVxyXG5cclxuLyogRGVzaWduIGFic3RyYWl0IHBvdXIgcmVtcGxhY2VyIGwnaW1hZ2UgKi9cclxuLmFic3RyYWN0LWRlc2lnbiB7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIHdpZHRoOiA0NTBweDsgLyogTcODwqptZSB0YWlsbGUgcXVlIGwnaW1hZ2UgcHLDg8KpY8ODwqlkZW50ZSAqL1xyXG4gIGhlaWdodDogMzAwcHg7IC8qIEFqdXN0ZXogc2Vsb24gdm9zIGJlc29pbnMgKi9cclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbn1cclxuXHJcbi8qIENyw4PCqWVyIGRlcyBjZXJjbGVzIGFic3RyYWl0cyBzdXBlcnBvc8ODwqlzICovXHJcbi5hYnN0cmFjdC1kZXNpZ246OmJlZm9yZSxcclxuLmFic3RyYWN0LWRlc2lnbjo6YWZ0ZXIsXHJcbi5hYnN0cmFjdC1kZXNpZ24gc3Bhbjo6YmVmb3JlLFxyXG4uYWJzdHJhY3QtZGVzaWduIHNwYW46OmFmdGVyIHtcclxuICBjb250ZW50OiAnJztcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gIGJhY2tncm91bmQ6IHJhZGlhbC1ncmFkaWVudChjaXJjbGUsIHJnYmEoMTA2LCA5MCwgMjA1LCAwLjMpLCByZ2JhKDEwNiwgOTAsIDIwNSwgMC4xKSk7XHJcbiAgYm94LXNoYWRvdzogMCAwIDIwcHggcmdiYSgxMDYsIDkwLCAyMDUsIDAuMyk7XHJcbiAgZmlsdGVyOiBibHVyKDEwcHgpO1xyXG59XHJcblxyXG4vKiBQcmVtaWVyIGNlcmNsZSAoYmxldS12aW9sZXQpICovXHJcbi5hYnN0cmFjdC1kZXNpZ246OmJlZm9yZSB7XHJcbiAgd2lkdGg6IDE1MHB4O1xyXG4gIGhlaWdodDogMTUwcHg7XHJcbiAgdG9wOiAyMHB4O1xyXG4gIGxlZnQ6IDUwcHg7XHJcbiAgYmFja2dyb3VuZDogcmFkaWFsLWdyYWRpZW50KGNpcmNsZSwgcmdiYSgxMDYsIDkwLCAyMDUsIDAuNSksIHJnYmEoMTA2LCA5MCwgMjA1LCAwLjIpKTtcclxufVxyXG5cclxuLyogRGV1eGnDg8KobWUgY2VyY2xlIChqYXVuZS1vcmFuZ8ODwqkpICovXHJcbi5hYnN0cmFjdC1kZXNpZ246OmFmdGVyIHtcclxuICB3aWR0aDogMTUwcHg7XHJcbiAgaGVpZ2h0OiAxNTBweDtcclxuICB0b3A6IDIwcHg7XHJcbiAgcmlnaHQ6IDUwcHg7XHJcbiAgYmFja2dyb3VuZDogcmFkaWFsLWdyYWRpZW50KGNpcmNsZSwgcmdiYSgyNTUsIDE2NSwgMCwgMC41KSwgcmdiYSgyNTUsIDE2NSwgMCwgMC4yKSk7XHJcbn1cclxuXHJcbi8qIFRyb2lzacODwqhtZSBjZXJjbGUgKGJsZXUtdmlvbGV0KSAqL1xyXG4uYWJzdHJhY3QtZGVzaWduIHNwYW46OmJlZm9yZSB7XHJcbiAgd2lkdGg6IDE1MHB4O1xyXG4gIGhlaWdodDogMTUwcHg7XHJcbiAgYm90dG9tOiAyMHB4O1xyXG4gIGxlZnQ6IDEwMHB4O1xyXG4gIGJhY2tncm91bmQ6IHJhZGlhbC1ncmFkaWVudChjaXJjbGUsIHJnYmEoMTA2LCA5MCwgMjA1LCAwLjUpLCByZ2JhKDEwNiwgOTAsIDIwNSwgMC4yKSk7XHJcbn1cclxuXHJcbi8qIFF1YXRyacODwqhtZSBjZXJjbGUgKGphdW5lLW9yYW5nw4PCqSkgKi9cclxuLmFic3RyYWN0LWRlc2lnbiBzcGFuOjphZnRlciB7XHJcbiAgd2lkdGg6IDE1MHB4O1xyXG4gIGhlaWdodDogMTUwcHg7XHJcbiAgYm90dG9tOiAyMHB4O1xyXG4gIHJpZ2h0OiAxMDBweDtcclxuICBiYWNrZ3JvdW5kOiByYWRpYWwtZ3JhZGllbnQoY2lyY2xlLCByZ2JhKDI1NSwgMTY1LCAwLCAwLjUpLCByZ2JhKDI1NSwgMTY1LCAwLCAwLjIpKTtcclxufVxyXG5cclxuLyogQ2VyY2xlcyBkw4PCqWNvcmF0aWZzIChpbmNoYW5nw4PCqXMpICovXHJcbi5jaXJjbGUge1xyXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDE4MiwgMTkzLCAwLjMpO1xyXG59XHJcblxyXG4uY2lyY2xlMSB7XHJcbiAgd2lkdGg6IDE1MHB4O1xyXG4gIGhlaWdodDogMTUwcHg7XHJcbiAgdG9wOiAyMHB4O1xyXG4gIGxlZnQ6IDUwcHg7XHJcbn1cclxuXHJcbi5jaXJjbGUyIHtcclxuICB3aWR0aDogMTAwcHg7XHJcbiAgaGVpZ2h0OiAxMDBweDtcclxuICBib3R0b206IDUwcHg7XHJcbiAgcmlnaHQ6IDMwcHg7XHJcbn1cclxuXHJcbi5jaXJjbGUzIHtcclxuICB3aWR0aDogNTBweDtcclxuICBoZWlnaHQ6IDUwcHg7XHJcbiAgdG9wOiA4MHB4O1xyXG4gIHJpZ2h0OiA4MHB4O1xyXG59XHJcblxyXG4vKiBQb2ludHMgZMODwqljb3JhdGlmcyAoaW5jaGFuZ8ODwqlzKSAqL1xyXG4ubGVmdC1zZWN0aW9uOjpiZWZvcmUsXHJcbi5sZWZ0LXNlY3Rpb246OmFmdGVyIHtcclxuICBjb250ZW50OiAnJztcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gIGJhY2tncm91bmQ6ICNmZjQwNDA7XHJcbn1cclxuXHJcbi5sZWZ0LXNlY3Rpb246OmJlZm9yZSB7XHJcbiAgd2lkdGg6IDVweDtcclxuICBoZWlnaHQ6IDVweDtcclxuICB0b3A6IDEwJTtcclxuICBsZWZ0OiAyMCU7XHJcbn1cclxuXHJcbi5sZWZ0LXNlY3Rpb246OmFmdGVyIHtcclxuICB3aWR0aDogM3B4O1xyXG4gIGhlaWdodDogM3B4O1xyXG4gIGJvdHRvbTogMTUlO1xyXG4gIHJpZ2h0OiAxMCU7XHJcbn1cclxuXHJcbi5zbG9nYW4ge1xyXG4gIGZvbnQtc2l6ZTogMS41cmVtO1xyXG4gIGNvbG9yOiAjNmE1YWNkO1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBtYXJnaW4tdG9wOiAyMHB4O1xyXG4gIGZvbnQtc3R5bGU6IGl0YWxpYztcclxuICBmb250LWZhbWlseTogJ0RhbmNpbmcgU2NyaXB0JywgY3Vyc2l2ZTtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SignupComponent", "constructor", "user", "name", "email", "password", "terms", "onSubmit", "console", "log", "alert", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_24_listener", "SignupComponent_Template_input_ngModelChange_29_listener", "$event", "SignupComponent_Template_input_ngModelChange_33_listener", "SignupComponent_Template_input_ngModelChange_37_listener", "SignupComponent_Template_input_ngModelChange_39_listener", "ɵɵadvance", "ɵɵproperty", "_r0", "valid"], "sources": ["C:\\pfa\\src\\app\\signup\\signup.component.ts", "C:\\pfa\\src\\app\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.scss']\n})\nexport class SignupComponent {\n  user = {\n    name: '',\n    email: '',\n    password: '',\n    terms: false\n  };\n\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n}", "<div class=\"signup-container\">\n    <!-- Section gauche : Graphique et texte -->\n    <div class=\"left-section\">\n      <div class=\"graphic\">\n        <!-- Afficher une seule image contenant les quatre iris -->\n        <div class=\"iris-collage\">\n          <img src=\"assets/iris-collage.png\" alt=\"Iris Collage\" class=\"iris-collage-img\" />\n        </div>\n        <!-- Cercles décoratifs -->\n        <div class=\"circle circle1\"></div>\n        <div class=\"circle circle2\"></div>\n        <div class=\"circle circle3\"></div>\n      </div>\n      <p class=\"slogan\">\n        Each iris is a unique story written by nature, waiting to be decoded by technology\n      </p>\n    </div>\n  \n    <!-- Section droite : Formulaire -->\n    <div class=\"right-section\">\n      <div class=\"form-container\">\n        <div class=\"logo\">\n          <!-- Logo en forme de croix -->\n          <div class=\"custom-logo\"><span></span></div>\n          <h1>Sign Up</h1>\n          <p>Votre identité, redéfinie par la technologie. Créez votre compte.</p>\n        </div>\n  \n        <!-- Bouton \"Continue with Google\" -->\n        <button class=\"google-btn\">\n          <img src=\"assets/google-icon.png\" alt=\"Google Icon\" /> Continue with Google\n        </button>\n  \n        <div class=\"divider\">or Sign in with Email</div>\n  \n        <!-- Formulaire -->\n        <form (ngSubmit)=\"onSubmit()\" #signupForm=\"ngForm\">\n          <div class=\"form-group\">\n            <label for=\"name\">Name</label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              placeholder=\"Leslie Alexander\"\n              [(ngModel)]=\"user.name\"\n              required\n            />\n          </div>\n  \n          <div class=\"form-group\">\n            <label for=\"email\">Email</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              placeholder=\"<EMAIL>\"\n              [(ngModel)]=\"user.email\"\n              required\n              email\n            />\n          </div>\n  \n          <div class=\"form-group\">\n            <label for=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              placeholder=\"At least 8 characters\"\n              [(ngModel)]=\"user.password\"\n              required\n              minlength=\"8\"\n            />\n          </div>\n  \n          <div class=\"checkbox-group\">\n            <input\n              type=\"checkbox\"\n              id=\"terms\"\n              name=\"terms\"\n              [(ngModel)]=\"user.terms\"\n              required\n            />\n            <label for=\"terms\">I agree with <a href=\"#\">Terms</a> and <a href=\"#\">Privacy</a></label>\n          </div>\n  \n          <button type=\"submit\" class=\"signup-btn\" [disabled]=\"!signupForm.valid\">\n            Sign Up\n          </button>\n        </form>\n  \n        <p class=\"login-link\">\n          Already have an account? <a routerLink=\"/login\">Log in</a>\n        </p>\n      </div>\n    </div>\n  </div>"], "mappings": ";;;AAOA,OAAM,MAAOA,eAAe;EAL5BC,YAAA;IAME,KAAAC,IAAI,GAAG;MACLC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;KACR;;EAEDC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,IAAI,CAACI,KAAK,EAAE;MACnBE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACP,IAAI,CAAC;MACzC;KACD,MAAM;MACLQ,KAAK,CAAC,+CAA+C,CAAC;;EAE1D;;;uBAfWV,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAW,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,aAA8B;UAMpBD,EAAA,CAAAE,SAAA,aAAiF;UACnFF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAE,SAAA,aAAkC;UAGpCF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,WAAkB;UAChBD,EAAA,CAAAI,MAAA,2FACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,cAA2B;UAIID,EAAA,CAAAE,SAAA,YAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,wFAAiE;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAI1EH,EAAA,CAAAC,cAAA,kBAA2B;UACzBD,EAAA,CAAAE,SAAA,eAAsD;UAACF,EAAA,CAAAI,MAAA,8BACzD;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAI,MAAA,6BAAqB;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAGhDH,EAAA,CAAAC,cAAA,oBAAmD;UAA7CD,EAAA,CAAAK,UAAA,sBAAAC,mDAAA;YAAA,OAAYP,GAAA,CAAAX,QAAA,EAAU;UAAA,EAAC;UAC3BY,EAAA,CAAAC,cAAA,eAAwB;UACJD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAC,cAAA,iBAOE;UAFAD,EAAA,CAAAK,UAAA,2BAAAE,yDAAAC,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAC,IAAA,GAAAwB,MAAA;UAAA,EAAuB;UALzBR,EAAA,CAAAG,YAAA,EAOE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAI,yDAAAD,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAE,KAAA,GAAAuB,MAAA;UAAA,EAAwB;UAL1BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAK,yDAAAF,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAG,QAAA,GAAAsB,MAAA;UAAA,EAA2B;UAL7BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAA4B;UAKxBD,EAAA,CAAAK,UAAA,2BAAAM,yDAAAH,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAI,KAAA,GAAAqB,MAAA;UAAA,EAAwB;UAJ1BR,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAAmB;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAI,MAAA,aAAI;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGnFH,EAAA,CAAAC,cAAA,kBAAwE;UACtED,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,aAAsB;UACpBD,EAAA,CAAAI,MAAA,kCAAyB;UAAAJ,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;UAhDtDH,EAAA,CAAAY,SAAA,IAAuB;UAAvBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAC,IAAA,CAAuB;UAYvBgB,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAE,KAAA,CAAwB;UAaxBe,EAAA,CAAAY,SAAA,GAA2B;UAA3BZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAG,QAAA,CAA2B;UAW3Bc,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAI,KAAA,CAAwB;UAMaa,EAAA,CAAAY,SAAA,GAA8B;UAA9BZ,EAAA,CAAAa,UAAA,cAAAC,GAAA,CAAAC,KAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}