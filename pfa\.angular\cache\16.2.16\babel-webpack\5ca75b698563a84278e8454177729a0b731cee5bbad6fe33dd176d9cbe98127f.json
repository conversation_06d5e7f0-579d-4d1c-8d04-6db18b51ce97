{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nexport class SignupComponent {\n  constructor() {\n    this.user = {\n      name: '',\n      email: '',\n      password: '',\n      terms: false\n    };\n  }\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 53,\n      vars: 5,\n      consts: [[1, \"signup-container\"], [1, \"left-section\"], [1, \"graphic\"], [1, \"iris-collage\"], [\"src\", \"assets/iris-collage.png\", \"alt\", \"Iris Collage\", 1, \"iris-collage-img\"], [1, \"dot\"], [1, \"slogan\"], [1, \"right-section\"], [1, \"form-container\"], [1, \"logo\"], [1, \"custom-logo\"], [1, \"google-btn\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google Icon\"], [1, \"divider\"], [3, \"ngSubmit\"], [\"signupForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \"Leslie Alexander\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"placeholder\", \"<EMAIL>\", \"required\", \"\", \"email\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"placeholder\", \"At least 8 characters\", \"required\", \"\", \"minlength\", \"8\", 3, \"ngModel\", \"ngModelChange\"], [1, \"checkbox-group\"], [\"type\", \"checkbox\", \"id\", \"terms\", \"name\", \"terms\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"terms\"], [\"href\", \"#\"], [\"type\", \"submit\", 1, \"signup-btn\", 3, \"disabled\"], [1, \"login-link\"], [\"routerLink\", \"/login\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"div\", 5)(6, \"div\", 5)(7, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9, \" Each iris is a unique story written by nature, waiting to be decoded by technology \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10);\n          i0.ɵɵelement(14, \"span\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"h1\");\n          i0.ɵɵtext(16, \"Sign Up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\");\n          i0.ɵɵtext(18, \"Votre identit\\u00E9, red\\u00E9finie par la technologie. Cr\\u00E9ez votre compte.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 11);\n          i0.ɵɵelement(20, \"img\", 12);\n          i0.ɵɵtext(21, \" Continue with Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 13);\n          i0.ɵɵtext(23, \"or Sign in with Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"form\", 14, 15);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_24_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(26, \"div\", 16)(27, \"label\", 17);\n          i0.ɵɵtext(28, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"input\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_29_listener($event) {\n            return ctx.user.name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 16)(31, \"label\", 19);\n          i0.ɵɵtext(32, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_33_listener($event) {\n            return ctx.user.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 16)(35, \"label\", 21);\n          i0.ɵɵtext(36, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_37_listener($event) {\n            return ctx.user.password = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 23)(39, \"input\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_39_listener($event) {\n            return ctx.user.terms = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"label\", 25);\n          i0.ɵɵtext(41, \"I agree with \");\n          i0.ɵɵelementStart(42, \"a\", 26);\n          i0.ɵɵtext(43, \"Terms\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \" and \");\n          i0.ɵɵelementStart(45, \"a\", 26);\n          i0.ɵɵtext(46, \"Privacy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"button\", 27);\n          i0.ɵɵtext(48, \" Sign Up \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"p\", 28);\n          i0.ɵɵtext(50, \" Already have an account? \");\n          i0.ɵɵelementStart(51, \"a\", 29);\n          i0.ɵɵtext(52, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(25);\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.terms);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", !_r0.valid);\n        }\n      },\n      dependencies: [i1.RouterLink, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MinLengthValidator, i2.CheckboxRequiredValidator, i2.EmailValidator, i2.NgModel, i2.NgForm],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.signup-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  font-family: \\\"Poppins\\\", sans-serif; \\n\\n  background: #f5f5ff; \\n\\n  overflow: hidden;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 600px; \\n\\n  margin: 0 auto; \\n\\n  background: linear-gradient(135deg, #18184e, #1a1a4e); \\n\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n  overflow: hidden;\\n  border-radius: 20px; \\n\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); \\n\\n  \\n\\n  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.05) 1px, transparent 1px);\\n  background-size: 10px 10px;\\n}\\n\\n.graphic[_ngcontent-%COMP%] {\\n  position: relative;\\n  text-align: center;\\n}\\n\\n.iris-collage[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.iris-collage-img[_ngcontent-%COMP%] {\\n  width: 300px; \\n\\n  height: auto;\\n  object-fit: contain;\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_fadeInScale 1.5s ease-in-out forwards; \\n\\n}\\n\\n.iris-collage-img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05); \\n\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before {\\n  width: 5px;\\n  height: 5px;\\n  top: 10%;\\n  left: 20%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::after {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 15%;\\n  right: 10%;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(1) {\\n  width: 4px;\\n  height: 4px;\\n  top: 5%;\\n  left: 30%;\\n  animation-delay: 0.5s;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(2) {\\n  width: 6px;\\n  height: 6px;\\n  top: 20%;\\n  right: 15%;\\n  animation-delay: 1s;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(3) {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 20%;\\n  left: 10%;\\n  animation-delay: 1.5s;\\n}\\n\\n.slogan[_ngcontent-%COMP%] {\\n  font-size: 1.5rem; \\n\\n  color: #6464ad; \\n\\n  text-align: center;\\n  margin-top: 40px; \\n\\n  font-style: italic;\\n  font-family: \\\"Dancing Script\\\", cursive;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); \\n\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_fadeIn 1.5s ease-in-out forwards; \\n\\n}\\n\\n\\n\\n.right-section[_ngcontent-%COMP%] {\\n  max-width: 600px; \\n\\n  margin: 0 auto; \\n\\n  display: flex;\\n  justify-content: none;\\n  align-items: center;\\n  background: linear-gradient(135deg, #f5f5ff, #ffffff); \\n\\n}\\n\\n.form-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 550px; \\n\\n  padding: 20px; \\n\\n  background: rgba(255, 255, 255, 0.9); \\n\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(106, 90, 205, 0.1); \\n\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px); \\n\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_slideIn 1s ease-in-out forwards; \\n\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.custom-logo[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  margin: 0 auto;\\n  position: relative;\\n  background: transparent;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite; \\n\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd; \\n\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  bottom: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd;\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  left: 5px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  right: 5px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.2rem; \\n\\n  color: #333;\\n  font-weight: 700;\\n  margin: 10px 0;\\n  letter-spacing: 1px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem; \\n\\n  margin-bottom: 20px;\\n  font-weight: 400;\\n}\\n\\n.google-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  padding: 10px; \\n\\n  background: #fff;\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 0.9rem; \\n\\n  color: #333;\\n  font-weight: 500;\\n  margin-bottom: 15px;\\n  transition: all 0.3s ease;\\n}\\n\\n.google-btn[_ngcontent-%COMP%]:hover {\\n  background: #e6e6fa;\\n  box-shadow: 0 0 15px rgba(106, 90, 205, 0.2);\\n  transform: translateY(-2px);\\n}\\n\\n.google-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 18px; \\n\\n  margin-right: 8px;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #666;\\n  font-size: 0.8rem; \\n\\n  margin: 15px 0;\\n  position: relative;\\n  font-weight: 500;\\n}\\n\\n.divider[_ngcontent-%COMP%]::before, .divider[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 40%;\\n  height: 1px;\\n  background: rgba(106, 90, 205, 0.2);\\n}\\n\\n.divider[_ngcontent-%COMP%]::before {\\n  left: -10px;\\n}\\n\\n.divider[_ngcontent-%COMP%]::after {\\n  right: 0;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 15px; \\n\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.8rem; \\n\\n  color: #333;\\n  margin-bottom: 6px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px; \\n\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  border-radius: 8px;\\n  font-size: 0.9rem; \\n\\n  color: #333;\\n  background: rgba(255, 255, 255, 0.5);\\n  transition: all 0.3s ease;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #6a5acd;\\n  box-shadow: 0 0 10px rgba(106, 90, 205, 0.2);\\n  background: #fff;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-weight: 400;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 15px; \\n\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem; \\n\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: #5a4bbd;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px; \\n\\n  background: linear-gradient(90deg, #6a5acd, #5a4bbd); \\n\\n  color: #5a4bbd;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 0.9rem; \\n\\n  font-weight: 600;\\n  cursor: pointer;\\n  text-transform: uppercase;\\n  transition: all 0.3s ease;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, #5a4bbd, #6a5acd);\\n  box-shadow: 0 0 15px rgba(106, 90, 205, 0.4);\\n  transform: translateY(-2px);\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n  box-shadow: none;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 15px; \\n\\n  font-size: 0.8rem; \\n\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: #5a4bbd;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInScale {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.8);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulseDot {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.5);\\n    opacity: 0.5;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(50px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SignupComponent", "constructor", "user", "name", "email", "password", "terms", "onSubmit", "console", "log", "alert", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_24_listener", "SignupComponent_Template_input_ngModelChange_29_listener", "$event", "SignupComponent_Template_input_ngModelChange_33_listener", "SignupComponent_Template_input_ngModelChange_37_listener", "SignupComponent_Template_input_ngModelChange_39_listener", "ɵɵadvance", "ɵɵproperty", "_r0", "valid"], "sources": ["C:\\pfa\\src\\app\\signup\\signup.component.ts", "C:\\pfa\\src\\app\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.scss']\n})\nexport class SignupComponent {\n  user = {\n    name: '',\n    email: '',\n    password: '',\n    terms: false\n  };\n\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n}", "<div class=\"signup-container\">\n    <!-- Section gauche : Graphique et texte -->\n    <div class=\"left-section\">\n      <div class=\"graphic\">\n        <!-- Conserver l'image des iris -->\n        <div class=\"iris-collage\">\n          <img src=\"assets/iris-collage.png\" alt=\"Iris Collage\" class=\"iris-collage-img\" />\n        </div>\n        <!-- Points décoratifs -->\n        <div class=\"dot\"></div>\n        <div class=\"dot\"></div>\n        <div class=\"dot\"></div>\n      </div>\n      <p class=\"slogan\">\n        Each iris is a unique story written by nature, waiting to be decoded by technology\n      </p>\n    </div>\n  \n    <!-- Section droite : Formulaire -->\n    <div class=\"right-section\">\n      <div class=\"form-container\">\n        <div class=\"logo\">\n          <!-- Logo en forme de croix -->\n          <div class=\"custom-logo\"><span></span></div>\n          <h1>Sign Up</h1>\n          <p>Votre identité, redéfinie par la technologie. Créez votre compte.</p>\n        </div>\n  \n        <!-- Bouton \"Continue with Google\" -->\n        <button class=\"google-btn\">\n          <img src=\"assets/google-icon.png\" alt=\"Google Icon\" /> Continue with Google\n        </button>\n  \n        <div class=\"divider\">or Sign in with Email</div>\n  \n        <!-- Formulaire -->\n        <form (ngSubmit)=\"onSubmit()\" #signupForm=\"ngForm\">\n          <div class=\"form-group\">\n            <label for=\"name\">Name</label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              placeholder=\"Leslie Alexander\"\n              [(ngModel)]=\"user.name\"\n              required\n            />\n          </div>\n  \n          <div class=\"form-group\">\n            <label for=\"email\">Email</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              placeholder=\"<EMAIL>\"\n              [(ngModel)]=\"user.email\"\n              required\n              email\n            />\n          </div>\n  \n          <div class=\"form-group\">\n            <label for=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              placeholder=\"At least 8 characters\"\n              [(ngModel)]=\"user.password\"\n              required\n              minlength=\"8\"\n            />\n          </div>\n  \n          <div class=\"checkbox-group\">\n            <input\n              type=\"checkbox\"\n              id=\"terms\"\n              name=\"terms\"\n              [(ngModel)]=\"user.terms\"\n              required\n            />\n            <label for=\"terms\">I agree with <a href=\"#\">Terms</a> and <a href=\"#\">Privacy</a></label>\n          </div>\n  \n          <button type=\"submit\" class=\"signup-btn\" [disabled]=\"!signupForm.valid\">\n            Sign Up\n          </button>\n        </form>\n  \n        <p class=\"login-link\">\n          Already have an account? <a routerLink=\"/login\">Log in</a>\n        </p>\n      </div>\n    </div>\n  </div>"], "mappings": ";;;AAOA,OAAM,MAAOA,eAAe;EAL5BC,YAAA;IAME,KAAAC,IAAI,GAAG;MACLC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;KACR;;EAEDC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,IAAI,CAACI,KAAK,EAAE;MACnBE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACP,IAAI,CAAC;MACzC;KACD,MAAM;MACLQ,KAAK,CAAC,+CAA+C,CAAC;;EAE1D;;;uBAfWV,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAW,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,aAA8B;UAMpBD,EAAA,CAAAE,SAAA,aAAiF;UACnFF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAE,SAAA,aAAuB;UAGzBF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,WAAkB;UAChBD,EAAA,CAAAI,MAAA,2FACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,cAA2B;UAIID,EAAA,CAAAE,SAAA,YAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,wFAAiE;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAI1EH,EAAA,CAAAC,cAAA,kBAA2B;UACzBD,EAAA,CAAAE,SAAA,eAAsD;UAACF,EAAA,CAAAI,MAAA,8BACzD;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAI,MAAA,6BAAqB;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAGhDH,EAAA,CAAAC,cAAA,oBAAmD;UAA7CD,EAAA,CAAAK,UAAA,sBAAAC,mDAAA;YAAA,OAAYP,GAAA,CAAAX,QAAA,EAAU;UAAA,EAAC;UAC3BY,EAAA,CAAAC,cAAA,eAAwB;UACJD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAC,cAAA,iBAOE;UAFAD,EAAA,CAAAK,UAAA,2BAAAE,yDAAAC,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAC,IAAA,GAAAwB,MAAA;UAAA,EAAuB;UALzBR,EAAA,CAAAG,YAAA,EAOE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAI,yDAAAD,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAE,KAAA,GAAAuB,MAAA;UAAA,EAAwB;UAL1BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAK,yDAAAF,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAG,QAAA,GAAAsB,MAAA;UAAA,EAA2B;UAL7BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAA4B;UAKxBD,EAAA,CAAAK,UAAA,2BAAAM,yDAAAH,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAI,KAAA,GAAAqB,MAAA;UAAA,EAAwB;UAJ1BR,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAAmB;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAI,MAAA,aAAI;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGnFH,EAAA,CAAAC,cAAA,kBAAwE;UACtED,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,aAAsB;UACpBD,EAAA,CAAAI,MAAA,kCAAyB;UAAAJ,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;UAhDtDH,EAAA,CAAAY,SAAA,IAAuB;UAAvBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAC,IAAA,CAAuB;UAYvBgB,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAE,KAAA,CAAwB;UAaxBe,EAAA,CAAAY,SAAA,GAA2B;UAA3BZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAG,QAAA,CAA2B;UAW3Bc,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAI,KAAA,CAAwB;UAMaa,EAAA,CAAAY,SAAA,GAA8B;UAA9BZ,EAAA,CAAAa,UAAA,cAAAC,GAAA,CAAAC,KAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}