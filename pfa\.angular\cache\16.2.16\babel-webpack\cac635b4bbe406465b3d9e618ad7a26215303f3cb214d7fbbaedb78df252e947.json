{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DashboardComponent {\n  constructor(router) {\n    this.router = router;\n    // Données utilisateur\n    this.userName = '<PERSON>';\n    this.lastScanTime = '10:30';\n    this.securityLevel = 'Élevé';\n    this.profileStatus = 'Vérifié';\n    // Données de l'iris\n    this.irisType = 'Crypte Dominant';\n    this.irisColor = 'Marron';\n    this.uniqueFeatures = 42;\n    this.confidenceScore = '98.7%';\n    // Activités récentes\n    this.recentActivities = [{\n      type: 'scan',\n      title: 'Scan d\\'iris complété',\n      time: 'Aujourd\\'hui à 10:30'\n    }, {\n      type: 'profile',\n      title: 'Profil mis à jour',\n      time: 'Hier à 14:15'\n    }, {\n      type: 'report',\n      title: 'Rapport généré',\n      time: 'Il y a 3 jours à 11:45'\n    }];\n    // Statuts de sécurité\n    this.securityStatuses = [{\n      type: 'data',\n      title: 'Protection des données',\n      description: 'Vos données biométriques sont cryptées'\n    }, {\n      type: 'biometric',\n      title: 'Authentification biométrique',\n      description: 'Activée pour une sécurité renforcée'\n    }, {\n      type: 'compliance',\n      title: 'Conformité RGPD',\n      description: 'Conforme aux réglementations de protection des données'\n    }];\n  }\n  ngOnInit() {\n    // Vérifier si l'utilisateur est connecté (à implémenter avec un service d'authentification)\n    // Si non connecté, rediriger vers la page de connexion\n    // this.checkAuthentication();\n  }\n  // Méthode pour naviguer vers la page de scan d'iris\n  startNewScan() {\n    this.router.navigate(['/scan-iris']);\n  }\n  // Méthode pour changer d'onglet dans le tableau de bord\n  changeTab(tab) {\n    console.log(`Changement vers l'onglet: ${tab}`);\n    // Implémenter la logique de changement d'onglet\n  }\n  // Méthode pour vérifier l'authentification (à implémenter)\n  checkAuthentication() {\n    const isAuthenticated = false; // À remplacer par la vérification réelle\n    if (!isAuthenticated) {\n      this.router.navigate(['/login']);\n    }\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 157,\n      vars: 1,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"welcome-card\"], [1, \"new-scan-btn\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"info-cards\"], [1, \"info-card\"], [1, \"card-icon\", \"security\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"card-content\"], [1, \"status\"], [1, \"card-icon\", \"verified\"], [1, \"fas\", \"fa-check-circle\"], [1, \"card-icon\", \"time\"], [1, \"fas\", \"fa-clock\"], [1, \"dashboard-nav\"], [1, \"nav-btn\", \"active\", 3, \"click\"], [1, \"nav-btn\", 3, \"click\"], [1, \"profile-overview\"], [1, \"section-header\"], [1, \"profile-content\"], [1, \"iris-image-container\"], [\"src\", \"assets/iris.png\", \"alt\", \"Iris Scan\", 1, \"iris-image\"], [1, \"verification-badge\"], [1, \"iris-id\"], [1, \"iris-details\"], [1, \"detail-item\"], [1, \"detail-label\"], [1, \"detail-bar\"], [1, \"progress-bar\"], [1, \"detail-value\"], [1, \"verification-info\"], [1, \"verification-label\"], [1, \"verification-time\"], [1, \"verification-status\", \"success\"], [1, \"bottom-sections\"], [1, \"activity-section\"], [1, \"activity-list\"], [1, \"activity-item\"], [1, \"activity-icon\", \"scan\"], [1, \"activity-details\"], [1, \"activity-icon\", \"profile\"], [1, \"fas\", \"fa-user-edit\"], [1, \"activity-icon\", \"report\"], [1, \"fas\", \"fa-file-alt\"], [1, \"security-section\"], [1, \"security-list\"], [1, \"security-item\"], [1, \"security-icon\", \"data\"], [1, \"fas\", \"fa-database\"], [1, \"security-details\"], [1, \"security-icon\", \"biometric\"], [1, \"fas\", \"fa-fingerprint\"], [1, \"security-icon\", \"compliance\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Votre espace personnel de d\\u00E9tection et profilage d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_7_listener() {\n            return ctx.startNewScan();\n          });\n          i0.ɵɵelement(8, \"i\", 4);\n          i0.ɵɵtext(9, \" Nouveau scan d'iris \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6)(12, \"div\", 7);\n          i0.ɵɵelement(13, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"h3\");\n          i0.ɵɵtext(16, \"Niveau de s\\u00E9curit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\", 10);\n          i0.ɵɵtext(18, \"\\u00C9lev\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 6)(20, \"div\", 11);\n          i0.ɵɵelement(21, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 9)(23, \"h3\");\n          i0.ɵɵtext(24, \"Statut du profil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 10);\n          i0.ɵɵtext(26, \"V\\u00E9rifi\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 6)(28, \"div\", 13);\n          i0.ɵɵelement(29, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"h3\");\n          i0.ɵɵtext(32, \"Dernier scan\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"p\", 10);\n          i0.ɵɵtext(34, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(35, \"div\", 15)(36, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_36_listener() {\n            return ctx.changeTab(\"overview\");\n          });\n          i0.ɵɵtext(37, \"Vue d'ensemble\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_38_listener() {\n            return ctx.changeTab(\"scan\");\n          });\n          i0.ɵɵtext(39, \"Scan d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_40_listener() {\n            return ctx.changeTab(\"profile\");\n          });\n          i0.ɵɵtext(41, \"Mon profil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_42_listener() {\n            return ctx.changeTab(\"history\");\n          });\n          i0.ɵɵtext(43, \"Historique\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 18)(45, \"div\", 19)(46, \"h2\");\n          i0.ɵɵtext(47, \"Aper\\u00E7u de votre profil d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"p\");\n          i0.ɵɵtext(49, \"Caract\\u00E9ristiques principales de votre iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 20)(51, \"div\", 21);\n          i0.ɵɵelement(52, \"img\", 22);\n          i0.ɵɵelementStart(53, \"div\", 23);\n          i0.ɵɵtext(54, \"V\\u00E9rifi\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 24);\n          i0.ɵɵtext(56, \"ID: #A12345678\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 25)(58, \"div\", 26)(59, \"div\", 27);\n          i0.ɵɵtext(60, \"Type de motif:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 28);\n          i0.ɵɵelement(62, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 30);\n          i0.ɵɵtext(64, \"Crypte Dominant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 26)(66, \"div\", 27);\n          i0.ɵɵtext(67, \"Couleur d'iris:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"div\", 28);\n          i0.ɵɵelement(69, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"div\", 30);\n          i0.ɵɵtext(71, \"Marron\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 26)(73, \"div\", 27);\n          i0.ɵɵtext(74, \"Caract\\u00E9ristiques uniques:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"div\", 28);\n          i0.ɵɵelement(76, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\", 30);\n          i0.ɵɵtext(78, \"42\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"div\", 26)(80, \"div\", 27);\n          i0.ɵɵtext(81, \"Score de confiance:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 28);\n          i0.ɵɵelement(83, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"div\", 30);\n          i0.ɵɵtext(85, \"98.7%\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 31)(87, \"div\", 32);\n          i0.ɵɵtext(88, \"Derni\\u00E8re v\\u00E9rification\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"div\", 33);\n          i0.ɵɵtext(90, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"div\", 34);\n          i0.ɵɵelement(92, \"i\", 12);\n          i0.ɵɵtext(93, \" R\\u00E9ussi \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(94, \"div\", 35)(95, \"div\", 36)(96, \"div\", 19)(97, \"h2\");\n          i0.ɵɵtext(98, \"Activit\\u00E9 r\\u00E9cente\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"p\");\n          i0.ɵɵtext(100, \"Votre activit\\u00E9 r\\u00E9cente de scan d'iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(101, \"div\", 37)(102, \"div\", 38)(103, \"div\", 39);\n          i0.ɵɵelement(104, \"i\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 40)(106, \"h4\");\n          i0.ɵɵtext(107, \"Scan d'iris compl\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"p\");\n          i0.ɵɵtext(109, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(110, \"div\", 38)(111, \"div\", 41);\n          i0.ɵɵelement(112, \"i\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"div\", 40)(114, \"h4\");\n          i0.ɵɵtext(115, \"Profil mis \\u00E0 jour\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"p\");\n          i0.ɵɵtext(117, \"Hier \\u00E0 14:15\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(118, \"div\", 38)(119, \"div\", 43);\n          i0.ɵɵelement(120, \"i\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"div\", 40)(122, \"h4\");\n          i0.ɵɵtext(123, \"Rapport g\\u00E9n\\u00E9r\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"p\");\n          i0.ɵɵtext(125, \"Il y a 3 jours \\u00E0 11:45\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(126, \"div\", 45)(127, \"div\", 19)(128, \"h2\");\n          i0.ɵɵtext(129, \"Statut de s\\u00E9curit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"p\");\n          i0.ɵɵtext(131, \"Aper\\u00E7u de la s\\u00E9curit\\u00E9 de votre compte\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(132, \"div\", 46)(133, \"div\", 47)(134, \"div\", 48);\n          i0.ɵɵelement(135, \"i\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"div\", 50)(137, \"h4\");\n          i0.ɵɵtext(138, \"Protection des donn\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(139, \"p\");\n          i0.ɵɵtext(140, \"Vos donn\\u00E9es biom\\u00E9triques sont crypt\\u00E9es\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(141, \"div\", 47)(142, \"div\", 51);\n          i0.ɵɵelement(143, \"i\", 52);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(144, \"div\", 50)(145, \"h4\");\n          i0.ɵɵtext(146, \"Authentification biom\\u00E9trique\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"p\");\n          i0.ɵɵtext(148, \"Activ\\u00E9e pour une s\\u00E9curit\\u00E9 renforc\\u00E9e\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(149, \"div\", 47)(150, \"div\", 53);\n          i0.ɵɵelement(151, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"div\", 50)(153, \"h4\");\n          i0.ɵɵtext(154, \"Conformit\\u00E9 RGPD\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"p\");\n          i0.ɵɵtext(156, \"Conforme aux r\\u00E9glementations de protection des donn\\u00E9es\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"Bienvenue, \", ctx.userName, \"\");\n        }\n      },\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n  background-color: #f9fbfd;\\n  padding: 20px;\\n  min-height: 100vh;\\n  color: #12263f;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2c7be5, #1a68d1);\\n  color: white;\\n  padding: 30px;\\n  border-radius: 8px;\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 600;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  opacity: 0.9;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%] {\\n  background-color: white;\\n  color: #2c7be5;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);\\n}\\n\\n.info-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 8px;\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\\n  padding: 20px;\\n  display: flex;\\n  align-items: center;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1.2rem;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.security[_ngcontent-%COMP%] {\\n  background-color: #4e54ff;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.verified[_ngcontent-%COMP%] {\\n  background-color: #00d97e;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.time[_ngcontent-%COMP%] {\\n  background-color: #ff9d00;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin: 0 0 5px 0;\\n  color: #95aac9;\\n  font-weight: 500;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.dashboard-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 30px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 15px 25px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  color: #95aac9;\\n  cursor: pointer;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -1px;\\n  left: 0;\\n  width: 0;\\n  height: 3px;\\n  background-color: #6e00ff;\\n  transition: width 0.3s ease;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover, .dashboard-nav[_ngcontent-%COMP%]   .nav-btn.active[_ngcontent-%COMP%] {\\n  color: #6e00ff;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn.active[_ngcontent-%COMP%]:after {\\n  width: 100%;\\n}\\n\\n.profile-overview[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 8px;\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\\n  padding: 25px;\\n  margin-bottom: 30px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 600;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #95aac9;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 30px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 220px;\\n  height: 220px;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  background-color: #f0f0f0;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .verification-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 40px;\\n  left: 10px;\\n  background-color: #00d97e;\\n  color: white;\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .iris-id[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 10px;\\n  left: 10px;\\n  color: white;\\n  font-size: 0.8rem;\\n  background-color: rgba(0, 0, 0, 0.6);\\n  padding: 3px 8px;\\n  border-radius: 4px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  width: 180px;\\n  font-size: 0.9rem;\\n  color: #95aac9;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 6px;\\n  background-color: #f0f0f0;\\n  border-radius: 3px;\\n  margin: 0 20px;\\n  overflow: hidden;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(135deg, #2c7be5, #1a68d1);\\n  width: 100%;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  width: 120px;\\n  text-align: right;\\n  font-weight: 600;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  padding-top: 20px;\\n  border-top: 1px solid #f0f0f0;\\n  display: flex;\\n  align-items: center;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-label[_ngcontent-%COMP%] {\\n  color: #95aac9;\\n  font-size: 0.9rem;\\n  margin-right: 15px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-time[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-right: 20px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status.success[_ngcontent-%COMP%] {\\n  color: #00d97e;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n}\\n\\n.bottom-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 30px;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 8px;\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\\n  padding: 25px;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 600;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #95aac9;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 15px 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.scan[_ngcontent-%COMP%] {\\n  background-color: #8a56ff;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.profile[_ngcontent-%COMP%] {\\n  background-color: #ff5c8d;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.report[_ngcontent-%COMP%] {\\n  background-color: #ffb74d;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #95aac9;\\n  font-size: 0.85rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 15px 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.data[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.biometric[_ngcontent-%COMP%] {\\n  background-color: #8a56ff;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.compliance[_ngcontent-%COMP%] {\\n  background-color: #4e54ff;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #95aac9;\\n  font-size: 0.85rem;\\n}\\n\\n@media (max-width: 1024px) {\\n  .profile-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%] {\\n    margin: 0 auto 20px;\\n  }\\n  .bottom-sections[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .info-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n    white-space: nowrap;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    padding: 15px 15px;\\n  }\\n  .detail-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    margin-bottom: 5px;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    margin: 5px 0 !important;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    text-align: left !important;\\n    margin-top: 5px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DashboardComponent", "constructor", "router", "userName", "lastScanTime", "securityLevel", "profileStatus", "irisType", "irisColor", "uniqueFeatures", "confidenceScore", "recentActivities", "type", "title", "time", "securityStatuses", "description", "ngOnInit", "startNewScan", "navigate", "changeTab", "tab", "console", "log", "checkAuthentication", "isAuthenticated", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DashboardComponent_Template_button_click_7_listener", "ɵɵelement", "DashboardComponent_Template_button_click_36_listener", "DashboardComponent_Template_button_click_38_listener", "DashboardComponent_Template_button_click_40_listener", "DashboardComponent_Template_button_click_42_listener", "ɵɵadvance", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  // Données utilisateur\n  userName: string = '<PERSON>';\n  lastScanTime: string = '10:30';\n  securityLevel: string = 'Élevé';\n  profileStatus: string = 'Vérifié';\n\n  // Données de l'iris\n  irisType: string = 'Crypte Dominant';\n  irisColor: string = 'Marron';\n  uniqueFeatures: number = 42;\n  confidenceScore: string = '98.7%';\n\n  // Activités récentes\n  recentActivities = [\n    { type: 'scan', title: 'Scan d\\'iris complété', time: 'Aujourd\\'hui à 10:30' },\n    { type: 'profile', title: 'Profil mis à jour', time: 'Hier à 14:15' },\n    { type: 'report', title: 'Rapport généré', time: 'Il y a 3 jours à 11:45' }\n  ];\n\n  // Statuts de sécurité\n  securityStatuses = [\n    { type: 'data', title: 'Protection des données', description: 'Vos données biométriques sont cryptées' },\n    { type: 'biometric', title: 'Authentification biométrique', description: 'Activée pour une sécurité renforcée' },\n    { type: 'compliance', title: 'Conformité RGPD', description: 'Conforme aux réglementations de protection des données' }\n  ];\n\n  constructor(private router: Router) { }\n\n  ngOnInit(): void {\n    // Vérifier si l'utilisateur est connecté (à implémenter avec un service d'authentification)\n    // Si non connecté, rediriger vers la page de connexion\n    // this.checkAuthentication();\n  }\n\n  // Méthode pour naviguer vers la page de scan d'iris\n  startNewScan(): void {\n    this.router.navigate(['/scan-iris']);\n  }\n\n  // Méthode pour changer d'onglet dans le tableau de bord\n  changeTab(tab: string): void {\n    console.log(`Changement vers l'onglet: ${tab}`);\n    // Implémenter la logique de changement d'onglet\n  }\n\n  // Méthode pour vérifier l'authentification (à implémenter)\n  private checkAuthentication(): void {\n    const isAuthenticated = false; // À remplacer par la vérification réelle\n\n    if (!isAuthenticated) {\n      this.router.navigate(['/login']);\n    }\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- En-tête du tableau de bord -->\n  <div class=\"dashboard-header\">\n    <div class=\"welcome-card\">\n      <h1>Bienvenue, {{ userName }}</h1>\n      <p>Votre espace personnel de détection et profilage d'iris</p>\n      <button class=\"new-scan-btn\" (click)=\"startNewScan()\">\n        <i class=\"fas fa-eye\"></i>\n        Nouveau scan d'iris\n      </button>\n    </div>\n  </div>\n\n  <!-- Cartes d'information -->\n  <div class=\"info-cards\">\n    <div class=\"info-card\">\n      <div class=\"card-icon security\">\n        <i class=\"fas fa-shield-alt\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Niveau de sécurité</h3>\n        <p class=\"status\">Élevé</p>\n      </div>\n    </div>\n\n    <div class=\"info-card\">\n      <div class=\"card-icon verified\">\n        <i class=\"fas fa-check-circle\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Statut du profil</h3>\n        <p class=\"status\">Vérifié</p>\n      </div>\n    </div>\n\n    <div class=\"info-card\">\n      <div class=\"card-icon time\">\n        <i class=\"fas fa-clock\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Dernier scan</h3>\n        <p class=\"status\">Aujourd'hui à 10:30</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- Navigation du tableau de bord -->\n  <div class=\"dashboard-nav\">\n    <button class=\"nav-btn active\" (click)=\"changeTab('overview')\">Vue d'ensemble</button>\n    <button class=\"nav-btn\" (click)=\"changeTab('scan')\">Scan d'iris</button>\n    <button class=\"nav-btn\" (click)=\"changeTab('profile')\">Mon profil</button>\n    <button class=\"nav-btn\" (click)=\"changeTab('history')\">Historique</button>\n  </div>\n\n  <!-- Aperçu du profil d'iris -->\n  <div class=\"profile-overview\">\n    <div class=\"section-header\">\n      <h2>Aperçu de votre profil d'iris</h2>\n      <p>Caractéristiques principales de votre iris</p>\n    </div>\n\n    <div class=\"profile-content\">\n      <div class=\"iris-image-container\">\n        <img src=\"assets/iris.png\" alt=\"Iris Scan\" class=\"iris-image\">\n        <div class=\"verification-badge\">Vérifié</div>\n        <div class=\"iris-id\">ID: #A12345678</div>\n      </div>\n\n      <div class=\"iris-details\">\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Type de motif:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">Crypte Dominant</div>\n        </div>\n\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Couleur d'iris:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">Marron</div>\n        </div>\n\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Caractéristiques uniques:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">42</div>\n        </div>\n\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Score de confiance:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">98.7%</div>\n        </div>\n\n        <div class=\"verification-info\">\n          <div class=\"verification-label\">Dernière vérification</div>\n          <div class=\"verification-time\">Aujourd'hui à 10:30</div>\n          <div class=\"verification-status success\">\n            <i class=\"fas fa-check-circle\"></i> Réussi\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Section inférieure avec deux colonnes -->\n  <div class=\"bottom-sections\">\n    <!-- Activité récente -->\n    <div class=\"activity-section\">\n      <div class=\"section-header\">\n        <h2>Activité récente</h2>\n        <p>Votre activité récente de scan d'iris</p>\n      </div>\n\n      <div class=\"activity-list\">\n        <div class=\"activity-item\">\n          <div class=\"activity-icon scan\">\n            <i class=\"fas fa-eye\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Scan d'iris complété</h4>\n            <p>Aujourd'hui à 10:30</p>\n          </div>\n        </div>\n\n        <div class=\"activity-item\">\n          <div class=\"activity-icon profile\">\n            <i class=\"fas fa-user-edit\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Profil mis à jour</h4>\n            <p>Hier à 14:15</p>\n          </div>\n        </div>\n\n        <div class=\"activity-item\">\n          <div class=\"activity-icon report\">\n            <i class=\"fas fa-file-alt\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Rapport généré</h4>\n            <p>Il y a 3 jours à 11:45</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statut de sécurité -->\n    <div class=\"security-section\">\n      <div class=\"section-header\">\n        <h2>Statut de sécurité</h2>\n        <p>Aperçu de la sécurité de votre compte</p>\n      </div>\n\n      <div class=\"security-list\">\n        <div class=\"security-item\">\n          <div class=\"security-icon data\">\n            <i class=\"fas fa-database\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Protection des données</h4>\n            <p>Vos données biométriques sont cryptées</p>\n          </div>\n        </div>\n\n        <div class=\"security-item\">\n          <div class=\"security-icon biometric\">\n            <i class=\"fas fa-fingerprint\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Authentification biométrique</h4>\n            <p>Activée pour une sécurité renforcée</p>\n          </div>\n        </div>\n\n        <div class=\"security-item\">\n          <div class=\"security-icon compliance\">\n            <i class=\"fas fa-shield-alt\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Conformité RGPD</h4>\n            <p>Conforme aux réglementations de protection des données</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAQA,OAAM,MAAOA,kBAAkB;EA2B7BC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IA1B1B;IACA,KAAAC,QAAQ,GAAW,aAAa;IAChC,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAC,aAAa,GAAW,OAAO;IAC/B,KAAAC,aAAa,GAAW,SAAS;IAEjC;IACA,KAAAC,QAAQ,GAAW,iBAAiB;IACpC,KAAAC,SAAS,GAAW,QAAQ;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,eAAe,GAAW,OAAO;IAEjC;IACA,KAAAC,gBAAgB,GAAG,CACjB;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,uBAAuB;MAAEC,IAAI,EAAE;IAAsB,CAAE,EAC9E;MAAEF,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,IAAI,EAAE;IAAc,CAAE,EACrE;MAAEF,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAwB,CAAE,CAC5E;IAED;IACA,KAAAC,gBAAgB,GAAG,CACjB;MAAEH,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,wBAAwB;MAAEG,WAAW,EAAE;IAAwC,CAAE,EACxG;MAAEJ,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,8BAA8B;MAAEG,WAAW,EAAE;IAAqC,CAAE,EAChH;MAAEJ,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,iBAAiB;MAAEG,WAAW,EAAE;IAAwD,CAAE,CACxH;EAEqC;EAEtCC,QAAQA,CAAA;IACN;IACA;IACA;EAAA;EAGF;EACAC,YAAYA,CAAA;IACV,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEA;EACAC,SAASA,CAACC,GAAW;IACnBC,OAAO,CAACC,GAAG,CAAC,6BAA6BF,GAAG,EAAE,CAAC;IAC/C;EACF;EAEA;EACQG,mBAAmBA,CAAA;IACzB,MAAMC,eAAe,GAAG,KAAK,CAAC,CAAC;IAE/B,IAAI,CAACA,eAAe,EAAE;MACpB,IAAI,CAACvB,MAAM,CAACiB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;EAEpC;;;uBArDWnB,kBAAkB,EAAA0B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlB7B,kBAAkB;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR/BV,EAAA,CAAAY,cAAA,aAAiC;UAIvBZ,EAAA,CAAAa,MAAA,GAAyB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAClCd,EAAA,CAAAY,cAAA,QAAG;UAAAZ,EAAA,CAAAa,MAAA,mEAAuD;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAC9Dd,EAAA,CAAAY,cAAA,gBAAsD;UAAzBZ,EAAA,CAAAe,UAAA,mBAAAC,oDAAA;YAAA,OAASL,GAAA,CAAAnB,YAAA,EAAc;UAAA,EAAC;UACnDQ,EAAA,CAAAiB,SAAA,WAA0B;UAC1BjB,EAAA,CAAAa,MAAA,4BACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAKbd,EAAA,CAAAY,cAAA,cAAwB;UAGlBZ,EAAA,CAAAiB,SAAA,YAAiC;UACnCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,cAA0B;UACpBZ,EAAA,CAAAa,MAAA,oCAAkB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC3Bd,EAAA,CAAAY,cAAA,aAAkB;UAAAZ,EAAA,CAAAa,MAAA,uBAAK;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI/Bd,EAAA,CAAAY,cAAA,cAAuB;UAEnBZ,EAAA,CAAAiB,SAAA,aAAmC;UACrCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,cAA0B;UACpBZ,EAAA,CAAAa,MAAA,wBAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACzBd,EAAA,CAAAY,cAAA,aAAkB;UAAAZ,EAAA,CAAAa,MAAA,yBAAO;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAIjCd,EAAA,CAAAY,cAAA,cAAuB;UAEnBZ,EAAA,CAAAiB,SAAA,aAA4B;UAC9BjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,cAA0B;UACpBZ,EAAA,CAAAa,MAAA,oBAAY;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrBd,EAAA,CAAAY,cAAA,aAAkB;UAAAZ,EAAA,CAAAa,MAAA,gCAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAM/Cd,EAAA,CAAAY,cAAA,eAA2B;UACMZ,EAAA,CAAAe,UAAA,mBAAAG,qDAAA;YAAA,OAASP,GAAA,CAAAjB,SAAA,CAAU,UAAU,CAAC;UAAA,EAAC;UAACM,EAAA,CAAAa,MAAA,sBAAc;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACtFd,EAAA,CAAAY,cAAA,kBAAoD;UAA5BZ,EAAA,CAAAe,UAAA,mBAAAI,qDAAA;YAAA,OAASR,GAAA,CAAAjB,SAAA,CAAU,MAAM,CAAC;UAAA,EAAC;UAACM,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACxEd,EAAA,CAAAY,cAAA,kBAAuD;UAA/BZ,EAAA,CAAAe,UAAA,mBAAAK,qDAAA;YAAA,OAAST,GAAA,CAAAjB,SAAA,CAAU,SAAS,CAAC;UAAA,EAAC;UAACM,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAC1Ed,EAAA,CAAAY,cAAA,kBAAuD;UAA/BZ,EAAA,CAAAe,UAAA,mBAAAM,qDAAA;YAAA,OAASV,GAAA,CAAAjB,SAAA,CAAU,SAAS,CAAC;UAAA,EAAC;UAACM,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAI5Ed,EAAA,CAAAY,cAAA,eAA8B;UAEtBZ,EAAA,CAAAa,MAAA,0CAA6B;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtCd,EAAA,CAAAY,cAAA,SAAG;UAAAZ,EAAA,CAAAa,MAAA,uDAA0C;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAGnDd,EAAA,CAAAY,cAAA,eAA6B;UAEzBZ,EAAA,CAAAiB,SAAA,eAA8D;UAC9DjB,EAAA,CAAAY,cAAA,eAAgC;UAAAZ,EAAA,CAAAa,MAAA,yBAAO;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC7Cd,EAAA,CAAAY,cAAA,eAAqB;UAAAZ,EAAA,CAAAa,MAAA,sBAAc;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAG3Cd,EAAA,CAAAY,cAAA,eAA0B;UAEIZ,EAAA,CAAAa,MAAA,sBAAc;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC9Cd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,uBAAe;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGjDd,EAAA,CAAAY,cAAA,eAAyB;UACGZ,EAAA,CAAAa,MAAA,uBAAe;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC/Cd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,cAAM;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGxCd,EAAA,CAAAY,cAAA,eAAyB;UACGZ,EAAA,CAAAa,MAAA,sCAAyB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACzDd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,UAAE;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGpCd,EAAA,CAAAY,cAAA,eAAyB;UACGZ,EAAA,CAAAa,MAAA,2BAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACnDd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGvCd,EAAA,CAAAY,cAAA,eAA+B;UACGZ,EAAA,CAAAa,MAAA,uCAAqB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC3Dd,EAAA,CAAAY,cAAA,eAA+B;UAAAZ,EAAA,CAAAa,MAAA,gCAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACxDd,EAAA,CAAAY,cAAA,eAAyC;UACvCZ,EAAA,CAAAiB,SAAA,aAAmC;UAACjB,EAAA,CAAAa,MAAA,qBACtC;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAOdd,EAAA,CAAAY,cAAA,eAA6B;UAInBZ,EAAA,CAAAa,MAAA,kCAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACzBd,EAAA,CAAAY,cAAA,SAAG;UAAAZ,EAAA,CAAAa,MAAA,wDAAqC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAG9Cd,EAAA,CAAAY,cAAA,gBAA2B;UAGrBZ,EAAA,CAAAiB,SAAA,aAA0B;UAC5BjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,uCAAoB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC7Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,iCAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI9Bd,EAAA,CAAAY,cAAA,gBAA2B;UAEvBZ,EAAA,CAAAiB,SAAA,cAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,+BAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC1Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,0BAAY;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAIvBd,EAAA,CAAAY,cAAA,gBAA2B;UAEvBZ,EAAA,CAAAiB,SAAA,cAA+B;UACjCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,sCAAc;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,oCAAsB;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAOrCd,EAAA,CAAAY,cAAA,gBAA8B;UAEtBZ,EAAA,CAAAa,MAAA,qCAAkB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC3Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,6DAAqC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAG9Cd,EAAA,CAAAY,cAAA,gBAA2B;UAGrBZ,EAAA,CAAAiB,SAAA,cAA+B;UACjCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,oCAAsB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,8DAAsC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAIjDd,EAAA,CAAAY,cAAA,gBAA2B;UAEvBZ,EAAA,CAAAiB,SAAA,cAAkC;UACpCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,0CAA4B;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,gEAAmC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI9Cd,EAAA,CAAAY,cAAA,gBAA2B;UAEvBZ,EAAA,CAAAiB,SAAA,aAAiC;UACnCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,6BAAe;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACxBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,yEAAsD;UAAAb,EAAA,CAAAc,YAAA,EAAI;;;UAxL/Dd,EAAA,CAAAsB,SAAA,GAAyB;UAAzBtB,EAAA,CAAAuB,kBAAA,gBAAAZ,GAAA,CAAAlC,QAAA,KAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}