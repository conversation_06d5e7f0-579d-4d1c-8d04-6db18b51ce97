{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'accueil',\n  component: AccueilComponent\n}, {\n  path: 'suivantacc',\n  component: SuivantaccComponent\n}, {\n  path: 'typeiris',\n  component: TypeirisComponent\n}, {\n  path: 'iris2',\n  component: Iris2Component\n}, {\n  path: 'fleur',\n  component: FleurComponent\n}, {\n  path: 'bijou',\n  component: BijouComponent\n}, {\n  path: 'flux',\n  component: FluxComponent\n}, {\n  path: 'shaker',\n  component: ShakerComponent\n}, {\n  path: '',\n  redirectTo: '/accueil',\n  pathMatch: 'full'\n} // Rediriger vers la page d'accueil par défaut\n];\n\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AccueilComponent", "SuivantaccComponent", "TypeirisComponent", "Iris2Component", "FleurComponent", "BijouComponent", "FluxComponent", "ShakerComponent", "routes", "path", "component", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\n\nconst routes: Routes = [\n  { path: 'accueil', component: AccueilComponent },  // Page d'accueil\n  { path: 'suivantacc', component: SuivantaccComponent },  // Page après avoir cliqué sur \"Commencer\"\n  { path: 'typeiris', component: TypeirisComponent },  // Page après avoir cliqué sur \"Suivant\"\n  { path: 'iris2' , component: Iris2Component},\n  { path: 'fleur', component: FleurComponent },\n  { path: 'bijou', component: BijouComponent},\n  { path: 'flux', component: FluxComponent },\n  { path: 'shaker', component: ShakerComponent },\n  { path: '', redirectTo: '/accueil', pathMatch: 'full' },  // Rediriger vers la page d'accueil par défaut\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,2BAA2B;;;AAI3D,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEV;AAAgB,CAAE,EAChD;EAAES,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAET;AAAmB,CAAE,EACtD;EAAEQ,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAER;AAAiB,CAAE,EAClD;EAAEO,IAAI,EAAE,OAAO;EAAGC,SAAS,EAAEP;AAAc,CAAC,EAC5C;EAAEM,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEN;AAAc,CAAE,EAC5C;EAAEK,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEL;AAAc,CAAC,EAC3C;EAAEI,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEJ;AAAa,CAAE,EAC1C;EAAEG,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEH;AAAe,CAAE,EAC9C;EAAEE,IAAI,EAAE,EAAE;EAAEE,UAAU,EAAE,UAAU;EAAEC,SAAS,EAAE;AAAM,CAAE,CAAG;AAAA,CAC3D;;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBd,YAAY,CAACe,OAAO,CAACN,MAAM,CAAC,EAC5BT,YAAY;IAAA;EAAA;;;2EAEXc,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAjB,YAAA;IAAAkB,OAAA,GAFjBlB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}