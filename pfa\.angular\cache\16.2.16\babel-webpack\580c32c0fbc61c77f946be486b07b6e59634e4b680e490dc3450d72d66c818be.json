{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n// Firebase imports\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\nimport { provideFirestore, getFirestore } from '@angular/fire/firestore';\n// Firebase configuration pour ProfilingIris\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDummy\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"*********\",\n  appId: \"1:*********:web:dummy\"\n};\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { IrisFormComponent } from './iris-form/iris-form.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { FooterComponent } from './shared/footer/footer.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\nimport { AdminComponent } from './admin/admin.component';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, AccueilComponent, SuivantaccComponent, TypeirisComponent, Iris2Component, FleurComponent, BijouComponent, FluxComponent, ShakerComponent, IrisFormComponent, LoginComponent, SignupComponent, IrisDiversityComponent, DashboardComponent, FooterComponent, PersonalityTestComponent, AdminComponent],\n  imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, provideFirebaseApp(() => initializeApp(firebaseConfig)), provideFirestore(() => getFirestore())],\n  providers: [],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "FormsModule", "ReactiveFormsModule", "AppRoutingModule", "AppComponent", "initializeApp", "provideFirebaseApp", "provideFirestore", "getFirestore", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "AccueilComponent", "SuivantaccComponent", "TypeirisComponent", "Iris2Component", "FleurComponent", "BijouComponent", "FluxComponent", "ShakerComponent", "IrisFormComponent", "LoginComponent", "SignupComponent", "IrisDiversityComponent", "DashboardComponent", "FooterComponent", "PersonalityTestComponent", "AdminComponent", "AppModule", "__decorate", "declarations", "imports", "providers", "bootstrap"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n\n// Firebase imports\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\nimport { provideFirestore, getFirestore } from '@angular/fire/firestore';\n\n// Firebase configuration pour ProfilingIris\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDummy\", // Remplacez par votre vraie clé API\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"*********\",\n  appId: \"1:*********:web:dummy\"\n};\n\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { IrisFormComponent } from './iris-form/iris-form.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { FooterComponent } from './shared/footer/footer.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\nimport { AdminComponent } from './admin/admin.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    AccueilComponent,\n    SuivantaccComponent,\n    TypeirisComponent,\n    Iris2Component,\n    FleurComponent,\n    BijouComponent,\n    FluxComponent,\n    ShakerComponent,\n    IrisFormComponent,\n    LoginComponent,\n    SignupComponent,\n    IrisDiversityComponent,\n    DashboardComponent,\n    FooterComponent,\n    PersonalityTestComponent,\n    AdminComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    provideFirebaseApp(() => initializeApp(firebaseConfig)),\n    provideFirestore(() => getFirestore())\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB,CAAC,CAAC;AACnE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AAExE;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,aAAa;EACrBC,UAAU,EAAE,+BAA+B;EAC3CC,SAAS,EAAE,eAAe;EAC1BC,aAAa,EAAE,2BAA2B;EAC1CC,iBAAiB,EAAE,WAAW;EAC9BC,KAAK,EAAE;CACR;AAED,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,cAAc,QAAQ,yBAAyB;AAiCjD,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EA/BrBlC,QAAQ,CAAC;EACRmC,YAAY,EAAE,CACZ9B,YAAY,EACZY,gBAAgB,EAChBC,mBAAmB,EACnBC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,eAAe,EACfC,sBAAsB,EACtBC,kBAAkB,EAClBC,eAAe,EACfC,wBAAwB,EACxBC,cAAc,CACf;EACDI,OAAO,EAAE,CACPnC,aAAa,EACbG,gBAAgB,EAChBF,WAAW,EACXC,mBAAmB,EACnBI,kBAAkB,CAAC,MAAMD,aAAa,CAACI,cAAc,CAAC,CAAC,EACvDF,gBAAgB,CAAC,MAAMC,YAAY,EAAE,CAAC,CACvC;EACD4B,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,CAACjC,YAAY;CACzB,CAAC,C,EACW4B,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}