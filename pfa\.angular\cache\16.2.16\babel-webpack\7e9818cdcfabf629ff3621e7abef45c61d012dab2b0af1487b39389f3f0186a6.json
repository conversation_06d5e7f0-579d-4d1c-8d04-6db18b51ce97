{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { query } from '@angular/fire/database';\nimport { from } from 'rxjs';\nimport { PERSONALITY_QUESTIONS, PERSONALITY_DESCRIPTIONS } from '../models/personality-test.model';\nexport let PersonalityTestService = class PersonalityTestService {\n  constructor(firestore, mockFirebaseService) {\n    this.firestore = firestore;\n    this.mockFirebaseService = mockFirebaseService;\n    this.COLLECTION_NAME = 'personality_tests';\n    this.USE_MOCK = true; // Mettre à false pour utiliser Firebase réel\n  }\n  /**\n   * Calcule les scores pour chaque classe de personnalité\n   */\n  calculateScores(responses) {\n    const scores = {\n      flower: 0,\n      jewel: 0,\n      shaker: 0,\n      stream: 0,\n      flowerJewel: 0,\n      jewelShaker: 0,\n      shakerStream: 0,\n      streamFlower: 0\n    };\n    responses.forEach(response => {\n      const question = PERSONALITY_QUESTIONS.find(q => q.id === response.questionId);\n      if (!question) return;\n      const isCorrectAnswer = response.answer === question.expectedAnswer;\n      if (!isCorrectAnswer) return;\n      // Attribution des points selon les classes de la question\n      question.classes.forEach(className => {\n        switch (className) {\n          case 'Flower':\n            scores.flower += 1;\n            break;\n          case 'Jewel':\n            scores.jewel += 1;\n            break;\n          case 'Shaker':\n            scores.shaker += 1;\n            break;\n          case 'Stream':\n            scores.stream += 1;\n            break;\n          case 'Flower-Jewel':\n            scores.flowerJewel += 1;\n            // Contribue aussi aux classes de base\n            scores.flower += 0.5;\n            scores.jewel += 0.5;\n            break;\n          case 'Jewel-Shaker':\n            scores.jewelShaker += 1;\n            scores.jewel += 0.5;\n            scores.shaker += 0.5;\n            break;\n          case 'Shaker-Stream':\n            scores.shakerStream += 1;\n            scores.shaker += 0.5;\n            scores.stream += 0.5;\n            break;\n          case 'Stream-Flower':\n            scores.streamFlower += 1;\n            scores.stream += 0.5;\n            scores.flower += 0.5;\n            break;\n        }\n      });\n    });\n    return scores;\n  }\n  /**\n   * Détermine le profil de personnalité basé sur les scores\n   */\n  determineProfile(scores) {\n    // Scores des classes de base\n    const baseScores = [{\n      class: 'Flower',\n      score: scores.flower\n    }, {\n      class: 'Jewel',\n      score: scores.jewel\n    }, {\n      class: 'Shaker',\n      score: scores.shaker\n    }, {\n      class: 'Stream',\n      score: scores.stream\n    }];\n    // Scores des classes intermédiaires\n    const intermediateScores = [{\n      class: 'Flower-Jewel',\n      score: scores.flowerJewel\n    }, {\n      class: 'Jewel-Shaker',\n      score: scores.jewelShaker\n    }, {\n      class: 'Shaker-Stream',\n      score: scores.shakerStream\n    }, {\n      class: 'Stream-Flower',\n      score: scores.streamFlower\n    }];\n    // Trier par score décroissant\n    baseScores.sort((a, b) => b.score - a.score);\n    intermediateScores.sort((a, b) => b.score - a.score);\n    const maxBase = baseScores[0];\n    const secondMaxBase = baseScores[1];\n    const maxIntermediate = intermediateScores[0];\n    // Logique de détermination du profil\n    let primaryClass;\n    let secondaryClass;\n    let isIntermediate = false;\n    let confidenceScore = 0;\n    // Si une classe intermédiaire a un score élevé\n    if (maxIntermediate.score >= 2) {\n      primaryClass = maxIntermediate.class;\n      isIntermediate = true;\n      confidenceScore = maxIntermediate.score / 3 * 100; // Max 3 questions par classe intermédiaire\n    }\n    // Si deux classes de base sont proches et élevées\n    else if (maxBase.score >= 2 && secondMaxBase.score >= 2 && maxBase.score - secondMaxBase.score <= 1) {\n      // Créer un profil intermédiaire\n      const combinedClasses = [maxBase.class, secondMaxBase.class].sort();\n      primaryClass = `${combinedClasses[0]}-${combinedClasses[1]}`;\n      isIntermediate = true;\n      confidenceScore = (maxBase.score + secondMaxBase.score) / 8 * 100; // Max 4 questions par classe de base\n    }\n    // Sinon, classe dominante\n    else {\n      primaryClass = maxBase.class;\n      if (secondMaxBase.score >= 1) {\n        secondaryClass = secondMaxBase.class;\n      }\n      confidenceScore = maxBase.score / 4 * 100; // Max 4 questions par classe de base\n    }\n\n    return {\n      primaryClass,\n      secondaryClass,\n      isIntermediate,\n      confidenceScore: Math.round(confidenceScore),\n      description: PERSONALITY_DESCRIPTIONS[primaryClass]\n    };\n  }\n  /**\n   * Sauvegarde une session de test dans Firebase\n   */\n  saveTestSession(session) {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.saveTestSession(session);\n    }\n    const testCollection = collection(this.firestore, this.COLLECTION_NAME);\n    return from(addDoc(testCollection, {\n      ...session,\n      completedAt: session.completedAt,\n      startedAt: session.startedAt\n    }).then(docRef => docRef.id));\n  }\n  /**\n   * Met à jour une session de test existante\n   */\n  updateTestSession(sessionId, updates) {\n    const sessionDoc = doc(this.firestore, this.COLLECTION_NAME, sessionId);\n    return from(updateDoc(sessionDoc, updates));\n  }\n  /**\n   * Récupère toutes les sessions de test\n   */\n  getAllTestSessions() {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.getAllTestSessions();\n    }\n    const testCollection = collection(this.firestore, this.COLLECTION_NAME);\n    const q = query(testCollection, orderBy('completedAt', 'desc'));\n    return from(getDocs(q).then(snapshot => snapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    }))));\n  }\n  /**\n   * Récupère les sessions de test d'un utilisateur spécifique\n   */\n  getUserTestSessions(userEmail) {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.getUserTestSessions(userEmail);\n    }\n    const testCollection = collection(this.firestore, this.COLLECTION_NAME);\n    const q = query(testCollection, where('userEmail', '==', userEmail), orderBy('completedAt', 'desc'));\n    return from(getDocs(q).then(snapshot => snapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    }))));\n  }\n  /**\n   * Crée une nouvelle session de test\n   */\n  createTestSession(userName, userEmail) {\n    return {\n      userName: userName || 'Utilisateur anonyme',\n      userEmail: userEmail || '',\n      responses: [],\n      scores: {\n        flower: 0,\n        jewel: 0,\n        shaker: 0,\n        stream: 0,\n        flowerJewel: 0,\n        jewelShaker: 0,\n        shakerStream: 0,\n        streamFlower: 0\n      },\n      finalProfile: {\n        primaryClass: 'Flower',\n        isIntermediate: false,\n        confidenceScore: 0,\n        description: ''\n      },\n      startedAt: new Date(),\n      completedAt: new Date()\n    };\n  }\n  /**\n   * Traite les réponses et calcule le profil final\n   */\n  processTestResults(responses) {\n    const scores = this.calculateScores(responses);\n    const profile = this.determineProfile(scores);\n    return {\n      scores,\n      profile\n    };\n  }\n};\nPersonalityTestService = __decorate([Injectable({\n  providedIn: 'root'\n})], PersonalityTestService);", "map": {"version": 3, "names": ["Injectable", "query", "from", "PERSONALITY_QUESTIONS", "PERSONALITY_DESCRIPTIONS", "PersonalityTestService", "constructor", "firestore", "mockFirebaseService", "COLLECTION_NAME", "USE_MOCK", "calculateScores", "responses", "scores", "flower", "jewel", "shaker", "stream", "<PERSON><PERSON><PERSON><PERSON>", "jewelShaker", "shakerStream", "streamFlower", "for<PERSON>ach", "response", "question", "find", "q", "id", "questionId", "isCorrectAnswer", "answer", "expectedAnswer", "classes", "className", "determineProfile", "baseScores", "class", "score", "intermediateScores", "sort", "a", "b", "maxBase", "secondMaxBase", "maxIntermediate", "primaryClass", "secondaryClass", "isIntermediate", "confidenceScore", "combinedClasses", "Math", "round", "description", "saveTestSession", "session", "testCollection", "collection", "addDoc", "completedAt", "startedAt", "then", "doc<PERSON>ef", "updateTestSession", "sessionId", "updates", "sessionDoc", "doc", "updateDoc", "getAllTestSessions", "orderBy", "getDocs", "snapshot", "docs", "map", "data", "getUserTestSessions", "userEmail", "where", "createTestSession", "userName", "finalProfile", "Date", "processTestResults", "profile", "__decorate", "providedIn"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\services\\personality-test.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Database, ref, push, set, get, query, orderByChild, equalTo } from '@angular/fire/database';\nimport { Observable, from } from 'rxjs';\nimport { MockFirebaseService } from './mock-firebase.service';\nimport {\n  TestSession,\n  UserResponse,\n  PersonalityScores,\n  PersonalityProfile,\n  PersonalityClass,\n  PERSONALITY_QUESTIONS,\n  PERSONALITY_DESCRIPTIONS\n} from '../models/personality-test.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PersonalityTestService {\n  private readonly COLLECTION_NAME = 'personality_tests';\n  private readonly USE_MOCK = true; // Mettre à false pour utiliser Firebase réel\n\n  constructor(\n    private firestore: Firestore,\n    private mockFirebaseService: MockFirebaseService\n  ) {}\n\n  /**\n   * Calcule les scores pour chaque classe de personnalité\n   */\n  calculateScores(responses: UserResponse[]): PersonalityScores {\n    const scores: PersonalityScores = {\n      flower: 0,\n      jewel: 0,\n      shaker: 0,\n      stream: 0,\n      flowerJewel: 0,\n      jewelShaker: 0,\n      shakerStream: 0,\n      streamFlower: 0\n    };\n\n    responses.forEach(response => {\n      const question = PERSONALITY_QUESTIONS.find(q => q.id === response.questionId);\n      if (!question) return;\n\n      const isCorrectAnswer = response.answer === question.expectedAnswer;\n      if (!isCorrectAnswer) return;\n\n      // Attribution des points selon les classes de la question\n      question.classes.forEach(className => {\n        switch (className) {\n          case 'Flower':\n            scores.flower += 1;\n            break;\n          case 'Jewel':\n            scores.jewel += 1;\n            break;\n          case 'Shaker':\n            scores.shaker += 1;\n            break;\n          case 'Stream':\n            scores.stream += 1;\n            break;\n          case 'Flower-Jewel':\n            scores.flowerJewel += 1;\n            // Contribue aussi aux classes de base\n            scores.flower += 0.5;\n            scores.jewel += 0.5;\n            break;\n          case 'Jewel-Shaker':\n            scores.jewelShaker += 1;\n            scores.jewel += 0.5;\n            scores.shaker += 0.5;\n            break;\n          case 'Shaker-Stream':\n            scores.shakerStream += 1;\n            scores.shaker += 0.5;\n            scores.stream += 0.5;\n            break;\n          case 'Stream-Flower':\n            scores.streamFlower += 1;\n            scores.stream += 0.5;\n            scores.flower += 0.5;\n            break;\n        }\n      });\n    });\n\n    return scores;\n  }\n\n  /**\n   * Détermine le profil de personnalité basé sur les scores\n   */\n  determineProfile(scores: PersonalityScores): PersonalityProfile {\n    // Scores des classes de base\n    const baseScores = [\n      { class: 'Flower' as PersonalityClass, score: scores.flower },\n      { class: 'Jewel' as PersonalityClass, score: scores.jewel },\n      { class: 'Shaker' as PersonalityClass, score: scores.shaker },\n      { class: 'Stream' as PersonalityClass, score: scores.stream }\n    ];\n\n    // Scores des classes intermédiaires\n    const intermediateScores = [\n      { class: 'Flower-Jewel' as PersonalityClass, score: scores.flowerJewel },\n      { class: 'Jewel-Shaker' as PersonalityClass, score: scores.jewelShaker },\n      { class: 'Shaker-Stream' as PersonalityClass, score: scores.shakerStream },\n      { class: 'Stream-Flower' as PersonalityClass, score: scores.streamFlower }\n    ];\n\n    // Trier par score décroissant\n    baseScores.sort((a, b) => b.score - a.score);\n    intermediateScores.sort((a, b) => b.score - a.score);\n\n    const maxBase = baseScores[0];\n    const secondMaxBase = baseScores[1];\n    const maxIntermediate = intermediateScores[0];\n\n    // Logique de détermination du profil\n    let primaryClass: PersonalityClass;\n    let secondaryClass: PersonalityClass | undefined;\n    let isIntermediate = false;\n    let confidenceScore = 0;\n\n    // Si une classe intermédiaire a un score élevé\n    if (maxIntermediate.score >= 2) {\n      primaryClass = maxIntermediate.class;\n      isIntermediate = true;\n      confidenceScore = (maxIntermediate.score / 3) * 100; // Max 3 questions par classe intermédiaire\n    }\n    // Si deux classes de base sont proches et élevées\n    else if (maxBase.score >= 2 && secondMaxBase.score >= 2 && (maxBase.score - secondMaxBase.score) <= 1) {\n      // Créer un profil intermédiaire\n      const combinedClasses = [maxBase.class, secondMaxBase.class].sort();\n      primaryClass = `${combinedClasses[0]}-${combinedClasses[1]}` as PersonalityClass;\n      isIntermediate = true;\n      confidenceScore = ((maxBase.score + secondMaxBase.score) / 8) * 100; // Max 4 questions par classe de base\n    }\n    // Sinon, classe dominante\n    else {\n      primaryClass = maxBase.class;\n      if (secondMaxBase.score >= 1) {\n        secondaryClass = secondMaxBase.class;\n      }\n      confidenceScore = (maxBase.score / 4) * 100; // Max 4 questions par classe de base\n    }\n\n    return {\n      primaryClass,\n      secondaryClass,\n      isIntermediate,\n      confidenceScore: Math.round(confidenceScore),\n      description: PERSONALITY_DESCRIPTIONS[primaryClass]\n    };\n  }\n\n  /**\n   * Sauvegarde une session de test dans Firebase\n   */\n  saveTestSession(session: TestSession): Observable<string> {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.saveTestSession(session);\n    }\n\n    const testCollection = collection(this.firestore, this.COLLECTION_NAME);\n    return from(addDoc(testCollection, {\n      ...session,\n      completedAt: session.completedAt,\n      startedAt: session.startedAt\n    }).then(docRef => docRef.id));\n  }\n\n  /**\n   * Met à jour une session de test existante\n   */\n  updateTestSession(sessionId: string, updates: Partial<TestSession>): Observable<void> {\n    const sessionDoc = doc(this.firestore, this.COLLECTION_NAME, sessionId);\n    return from(updateDoc(sessionDoc, updates));\n  }\n\n  /**\n   * Récupère toutes les sessions de test\n   */\n  getAllTestSessions(): Observable<TestSession[]> {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.getAllTestSessions();\n    }\n\n    const testCollection = collection(this.firestore, this.COLLECTION_NAME);\n    const q = query(testCollection, orderBy('completedAt', 'desc'));\n\n    return from(getDocs(q).then(snapshot =>\n      snapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      } as TestSession))\n    ));\n  }\n\n  /**\n   * Récupère les sessions de test d'un utilisateur spécifique\n   */\n  getUserTestSessions(userEmail: string): Observable<TestSession[]> {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.getUserTestSessions(userEmail);\n    }\n\n    const testCollection = collection(this.firestore, this.COLLECTION_NAME);\n    const q = query(\n      testCollection,\n      where('userEmail', '==', userEmail),\n      orderBy('completedAt', 'desc')\n    );\n\n    return from(getDocs(q).then(snapshot =>\n      snapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      } as TestSession))\n    ));\n  }\n\n  /**\n   * Crée une nouvelle session de test\n   */\n  createTestSession(userName?: string, userEmail?: string): TestSession {\n    return {\n      userName: userName || 'Utilisateur anonyme',\n      userEmail: userEmail || '',\n      responses: [],\n      scores: {\n        flower: 0,\n        jewel: 0,\n        shaker: 0,\n        stream: 0,\n        flowerJewel: 0,\n        jewelShaker: 0,\n        shakerStream: 0,\n        streamFlower: 0\n      },\n      finalProfile: {\n        primaryClass: 'Flower',\n        isIntermediate: false,\n        confidenceScore: 0,\n        description: ''\n      },\n      startedAt: new Date(),\n      completedAt: new Date()\n    };\n  }\n\n  /**\n   * Traite les réponses et calcule le profil final\n   */\n  processTestResults(responses: UserResponse[]): { scores: PersonalityScores, profile: PersonalityProfile } {\n    const scores = this.calculateScores(responses);\n    const profile = this.determineProfile(scores);\n\n    return { scores, profile };\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAAwCC,KAAK,QAA+B,wBAAwB;AACpG,SAAqBC,IAAI,QAAQ,MAAM;AAEvC,SAMEC,qBAAqB,EACrBC,wBAAwB,QACnB,kCAAkC;AAKlC,WAAMC,sBAAsB,GAA5B,MAAMA,sBAAsB;EAIjCC,YACUC,SAAoB,EACpBC,mBAAwC;IADxC,KAAAD,SAAS,GAATA,SAAS;IACT,KAAAC,mBAAmB,GAAnBA,mBAAmB;IALZ,KAAAC,eAAe,GAAG,mBAAmB;IACrC,KAAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;EAK/B;EAEH;;;EAGAC,eAAeA,CAACC,SAAyB;IACvC,MAAMC,MAAM,GAAsB;MAChCC,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC;MACfC,YAAY,EAAE;KACf;IAEDT,SAAS,CAACU,OAAO,CAACC,QAAQ,IAAG;MAC3B,MAAMC,QAAQ,GAAGrB,qBAAqB,CAACsB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,QAAQ,CAACK,UAAU,CAAC;MAC9E,IAAI,CAACJ,QAAQ,EAAE;MAEf,MAAMK,eAAe,GAAGN,QAAQ,CAACO,MAAM,KAAKN,QAAQ,CAACO,cAAc;MACnE,IAAI,CAACF,eAAe,EAAE;MAEtB;MACAL,QAAQ,CAACQ,OAAO,CAACV,OAAO,CAACW,SAAS,IAAG;QACnC,QAAQA,SAAS;UACf,KAAK,QAAQ;YACXpB,MAAM,CAACC,MAAM,IAAI,CAAC;YAClB;UACF,KAAK,OAAO;YACVD,MAAM,CAACE,KAAK,IAAI,CAAC;YACjB;UACF,KAAK,QAAQ;YACXF,MAAM,CAACG,MAAM,IAAI,CAAC;YAClB;UACF,KAAK,QAAQ;YACXH,MAAM,CAACI,MAAM,IAAI,CAAC;YAClB;UACF,KAAK,cAAc;YACjBJ,MAAM,CAACK,WAAW,IAAI,CAAC;YACvB;YACAL,MAAM,CAACC,MAAM,IAAI,GAAG;YACpBD,MAAM,CAACE,KAAK,IAAI,GAAG;YACnB;UACF,KAAK,cAAc;YACjBF,MAAM,CAACM,WAAW,IAAI,CAAC;YACvBN,MAAM,CAACE,KAAK,IAAI,GAAG;YACnBF,MAAM,CAACG,MAAM,IAAI,GAAG;YACpB;UACF,KAAK,eAAe;YAClBH,MAAM,CAACO,YAAY,IAAI,CAAC;YACxBP,MAAM,CAACG,MAAM,IAAI,GAAG;YACpBH,MAAM,CAACI,MAAM,IAAI,GAAG;YACpB;UACF,KAAK,eAAe;YAClBJ,MAAM,CAACQ,YAAY,IAAI,CAAC;YACxBR,MAAM,CAACI,MAAM,IAAI,GAAG;YACpBJ,MAAM,CAACC,MAAM,IAAI,GAAG;YACpB;;MAEN,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOD,MAAM;EACf;EAEA;;;EAGAqB,gBAAgBA,CAACrB,MAAyB;IACxC;IACA,MAAMsB,UAAU,GAAG,CACjB;MAAEC,KAAK,EAAE,QAA4B;MAAEC,KAAK,EAAExB,MAAM,CAACC;IAAM,CAAE,EAC7D;MAAEsB,KAAK,EAAE,OAA2B;MAAEC,KAAK,EAAExB,MAAM,CAACE;IAAK,CAAE,EAC3D;MAAEqB,KAAK,EAAE,QAA4B;MAAEC,KAAK,EAAExB,MAAM,CAACG;IAAM,CAAE,EAC7D;MAAEoB,KAAK,EAAE,QAA4B;MAAEC,KAAK,EAAExB,MAAM,CAACI;IAAM,CAAE,CAC9D;IAED;IACA,MAAMqB,kBAAkB,GAAG,CACzB;MAAEF,KAAK,EAAE,cAAkC;MAAEC,KAAK,EAAExB,MAAM,CAACK;IAAW,CAAE,EACxE;MAAEkB,KAAK,EAAE,cAAkC;MAAEC,KAAK,EAAExB,MAAM,CAACM;IAAW,CAAE,EACxE;MAAEiB,KAAK,EAAE,eAAmC;MAAEC,KAAK,EAAExB,MAAM,CAACO;IAAY,CAAE,EAC1E;MAAEgB,KAAK,EAAE,eAAmC;MAAEC,KAAK,EAAExB,MAAM,CAACQ;IAAY,CAAE,CAC3E;IAED;IACAc,UAAU,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,KAAK,GAAGG,CAAC,CAACH,KAAK,CAAC;IAC5CC,kBAAkB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,KAAK,GAAGG,CAAC,CAACH,KAAK,CAAC;IAEpD,MAAMK,OAAO,GAAGP,UAAU,CAAC,CAAC,CAAC;IAC7B,MAAMQ,aAAa,GAAGR,UAAU,CAAC,CAAC,CAAC;IACnC,MAAMS,eAAe,GAAGN,kBAAkB,CAAC,CAAC,CAAC;IAE7C;IACA,IAAIO,YAA8B;IAClC,IAAIC,cAA4C;IAChD,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAIC,eAAe,GAAG,CAAC;IAEvB;IACA,IAAIJ,eAAe,CAACP,KAAK,IAAI,CAAC,EAAE;MAC9BQ,YAAY,GAAGD,eAAe,CAACR,KAAK;MACpCW,cAAc,GAAG,IAAI;MACrBC,eAAe,GAAIJ,eAAe,CAACP,KAAK,GAAG,CAAC,GAAI,GAAG,CAAC,CAAC;;IAEvD;IAAA,KACK,IAAIK,OAAO,CAACL,KAAK,IAAI,CAAC,IAAIM,aAAa,CAACN,KAAK,IAAI,CAAC,IAAKK,OAAO,CAACL,KAAK,GAAGM,aAAa,CAACN,KAAK,IAAK,CAAC,EAAE;MACrG;MACA,MAAMY,eAAe,GAAG,CAACP,OAAO,CAACN,KAAK,EAAEO,aAAa,CAACP,KAAK,CAAC,CAACG,IAAI,EAAE;MACnEM,YAAY,GAAG,GAAGI,eAAe,CAAC,CAAC,CAAC,IAAIA,eAAe,CAAC,CAAC,CAAC,EAAsB;MAChFF,cAAc,GAAG,IAAI;MACrBC,eAAe,GAAI,CAACN,OAAO,CAACL,KAAK,GAAGM,aAAa,CAACN,KAAK,IAAI,CAAC,GAAI,GAAG,CAAC,CAAC;;IAEvE;IAAA,KACK;MACHQ,YAAY,GAAGH,OAAO,CAACN,KAAK;MAC5B,IAAIO,aAAa,CAACN,KAAK,IAAI,CAAC,EAAE;QAC5BS,cAAc,GAAGH,aAAa,CAACP,KAAK;;MAEtCY,eAAe,GAAIN,OAAO,CAACL,KAAK,GAAG,CAAC,GAAI,GAAG,CAAC,CAAC;;;IAG/C,OAAO;MACLQ,YAAY;MACZC,cAAc;MACdC,cAAc;MACdC,eAAe,EAAEE,IAAI,CAACC,KAAK,CAACH,eAAe,CAAC;MAC5CI,WAAW,EAAEhD,wBAAwB,CAACyC,YAAY;KACnD;EACH;EAEA;;;EAGAQ,eAAeA,CAACC,OAAoB;IAClC,IAAI,IAAI,CAAC5C,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACF,mBAAmB,CAAC6C,eAAe,CAACC,OAAO,CAAC;;IAG1D,MAAMC,cAAc,GAAGC,UAAU,CAAC,IAAI,CAACjD,SAAS,EAAE,IAAI,CAACE,eAAe,CAAC;IACvE,OAAOP,IAAI,CAACuD,MAAM,CAACF,cAAc,EAAE;MACjC,GAAGD,OAAO;MACVI,WAAW,EAAEJ,OAAO,CAACI,WAAW;MAChCC,SAAS,EAAEL,OAAO,CAACK;KACpB,CAAC,CAACC,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAClC,EAAE,CAAC,CAAC;EAC/B;EAEA;;;EAGAmC,iBAAiBA,CAACC,SAAiB,EAAEC,OAA6B;IAChE,MAAMC,UAAU,GAAGC,GAAG,CAAC,IAAI,CAAC3D,SAAS,EAAE,IAAI,CAACE,eAAe,EAAEsD,SAAS,CAAC;IACvE,OAAO7D,IAAI,CAACiE,SAAS,CAACF,UAAU,EAAED,OAAO,CAAC,CAAC;EAC7C;EAEA;;;EAGAI,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC1D,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACF,mBAAmB,CAAC4D,kBAAkB,EAAE;;IAGtD,MAAMb,cAAc,GAAGC,UAAU,CAAC,IAAI,CAACjD,SAAS,EAAE,IAAI,CAACE,eAAe,CAAC;IACvE,MAAMiB,CAAC,GAAGzB,KAAK,CAACsD,cAAc,EAAEc,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;IAE/D,OAAOnE,IAAI,CAACoE,OAAO,CAAC5C,CAAC,CAAC,CAACkC,IAAI,CAACW,QAAQ,IAClCA,QAAQ,CAACC,IAAI,CAACC,GAAG,CAACP,GAAG,KAAK;MACxBvC,EAAE,EAAEuC,GAAG,CAACvC,EAAE;MACV,GAAGuC,GAAG,CAACQ,IAAI;KACI,EAAC,CACnB,CAAC;EACJ;EAEA;;;EAGAC,mBAAmBA,CAACC,SAAiB;IACnC,IAAI,IAAI,CAAClE,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACF,mBAAmB,CAACmE,mBAAmB,CAACC,SAAS,CAAC;;IAGhE,MAAMrB,cAAc,GAAGC,UAAU,CAAC,IAAI,CAACjD,SAAS,EAAE,IAAI,CAACE,eAAe,CAAC;IACvE,MAAMiB,CAAC,GAAGzB,KAAK,CACbsD,cAAc,EACdsB,KAAK,CAAC,WAAW,EAAE,IAAI,EAAED,SAAS,CAAC,EACnCP,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAC/B;IAED,OAAOnE,IAAI,CAACoE,OAAO,CAAC5C,CAAC,CAAC,CAACkC,IAAI,CAACW,QAAQ,IAClCA,QAAQ,CAACC,IAAI,CAACC,GAAG,CAACP,GAAG,KAAK;MACxBvC,EAAE,EAAEuC,GAAG,CAACvC,EAAE;MACV,GAAGuC,GAAG,CAACQ,IAAI;KACI,EAAC,CACnB,CAAC;EACJ;EAEA;;;EAGAI,iBAAiBA,CAACC,QAAiB,EAAEH,SAAkB;IACrD,OAAO;MACLG,QAAQ,EAAEA,QAAQ,IAAI,qBAAqB;MAC3CH,SAAS,EAAEA,SAAS,IAAI,EAAE;MAC1BhE,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE;QACNC,MAAM,EAAE,CAAC;QACTC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACTC,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE;OACf;MACD2D,YAAY,EAAE;QACZnC,YAAY,EAAE,QAAQ;QACtBE,cAAc,EAAE,KAAK;QACrBC,eAAe,EAAE,CAAC;QAClBI,WAAW,EAAE;OACd;MACDO,SAAS,EAAE,IAAIsB,IAAI,EAAE;MACrBvB,WAAW,EAAE,IAAIuB,IAAI;KACtB;EACH;EAEA;;;EAGAC,kBAAkBA,CAACtE,SAAyB;IAC1C,MAAMC,MAAM,GAAG,IAAI,CAACF,eAAe,CAACC,SAAS,CAAC;IAC9C,MAAMuE,OAAO,GAAG,IAAI,CAACjD,gBAAgB,CAACrB,MAAM,CAAC;IAE7C,OAAO;MAAEA,MAAM;MAAEsE;IAAO,CAAE;EAC5B;CACD;AApPY9E,sBAAsB,GAAA+E,UAAA,EAHlCpF,UAAU,CAAC;EACVqF,UAAU,EAAE;CACb,CAAC,C,EACWhF,sBAAsB,CAoPlC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}