{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { AcceuilComponent } from './acceuil/acceuil.component';\nimport { AccueilComponent } from './accueil/accueil.component';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, SignupComponent, AcceuilComponent, AccueilComponent],\n  imports: [BrowserModule, AppRoutingModule, FormsModule],\n  providers: [],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "FormsModule", "AppRoutingModule", "AppComponent", "SignupComponent", "AcceuilComponent", "AccueilComponent", "AppModule", "__decorate", "declarations", "imports", "providers", "bootstrap"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { AcceuilComponent } from './acceuil/acceuil.component';\nimport { AccueilComponent } from './accueil/accueil.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    SignupComponent,\n    AcceuilComponent,\n    AccueilComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,FormsModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,gBAAgB,CAAC,CAAC;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,gBAAgB,QAAQ,6BAA6B;AAgBvD,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EAdrBT,QAAQ,CAAC;EACRU,YAAY,EAAE,CACZN,YAAY,EACZC,eAAe,EACfC,gBAAgB,EAChBC,gBAAgB,CACjB;EACDI,OAAO,EAAE,CACPV,aAAa,EACbE,gBAAgB,EAACD,WAAW,CAC7B;EACDU,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,CAACT,YAAY;CACzB,CAAC,C,EACWI,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}