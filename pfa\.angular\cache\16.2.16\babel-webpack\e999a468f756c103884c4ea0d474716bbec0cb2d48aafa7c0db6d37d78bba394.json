{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjetPfa/pfa/pfa/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction LoginComponent_div_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"L'email est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Veuillez entrer un email valide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, LoginComponent_div_20_span_1_Template, 2, 0, \"span\", 28);\n    i0.ɵɵtemplate(2, LoginComponent_div_20_span_2_Template, 2, 0, \"span\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r1 = i0.ɵɵreference(19);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"email\"]);\n  }\n}\nfunction LoginComponent_div_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le mot de passe est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_31_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le mot de passe doit contenir au moins 6 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, LoginComponent_div_31_span_1_Template, 2, 0, \"span\", 28);\n    i0.ɵɵtemplate(2, LoginComponent_div_31_span_2_Template, 2, 0, \"span\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(28);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r3.errors == null ? null : _r3.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r3.errors == null ? null : _r3.errors[\"minlength\"]);\n  }\n}\nfunction LoginComponent_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Connexion en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(router) {\n    this.router = router;\n    this.loginData = {\n      email: '',\n      password: '',\n      rememberMe: false\n    };\n    this.showPassword = false;\n    this.isLoading = false;\n  }\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  onSubmit() {\n    if (this.isLoading) return;\n    this.isLoading = true;\n    // Simuler une connexion avec un délai\n    setTimeout(() => {\n      console.log('Tentative de connexion avec:', this.loginData);\n      // Vérifier si les identifiants correspondent à un compte statique\n      const account = this.staticAccounts.find(acc => acc.email === this.loginData.email && acc.password === this.loginData.password);\n      if (account) {\n        console.log('Connexion réussie avec le compte:', account.name);\n        // Sauvegarder l'image d'iris si elle a été analysée\n        if (this.selectedIrisFile && this.irisAnalysisResult) {\n          this.saveIrisImage(account.email, account.name);\n        }\n        // Sauvegarder les informations de l'utilisateur connecté\n        localStorage.setItem('currentUser', JSON.stringify({\n          name: account.name,\n          email: account.email,\n          role: account.role,\n          hasIrisImage: this.selectedIrisFile !== null,\n          irisAnalysis: this.irisAnalysisResult\n        }));\n        // Rediriger selon le rôle\n        if (account.role === 'admin') {\n          this.router.navigate(['/dashboard']);\n        } else {\n          // Les utilisateurs normaux vont directement au test de personnalité\n          this.router.navigate(['/personality-test']);\n        }\n      } else {\n        console.log('Identifiants incorrects');\n        alert('Email ou mot de passe incorrect. Utilisez un des comptes de test.');\n      }\n      this.isLoading = false;\n    }, 1500);\n  }\n  /**\n   * Remplit automatiquement le formulaire avec un compte de test\n   */\n  fillTestAccount(account) {\n    this.loginData.email = account.email;\n    this.loginData.password = account.password;\n  }\n  /**\n   * Affiche/masque la section d'upload d'iris\n   */\n  toggleIrisSection() {\n    this.showIrisSection = !this.showIrisSection;\n  }\n  /**\n   * Gère la sélection d'un fichier d'iris\n   */\n  onIrisFileSelected(event) {\n    const file = event.target.files[0];\n    if (!file) return;\n    // Valider le fichier\n    const validation = this.irisImageService.validateImageFile(file);\n    if (!validation.isValid) {\n      alert(validation.error);\n      return;\n    }\n    this.selectedIrisFile = file;\n    // Créer un aperçu de l'image\n    const reader = new FileReader();\n    reader.onload = e => {\n      this.irisPreviewUrl = e.target.result;\n    };\n    reader.readAsDataURL(file);\n    console.log('📸 Image d\\'iris sélectionnée:', file.name);\n  }\n  /**\n   * Analyse l'image d'iris\n   */\n  analyzeIris() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.selectedIrisFile) {\n        alert('Veuillez d\\'abord sélectionner une image d\\'iris');\n        return;\n      }\n      _this.isAnalyzingIris = true;\n      console.log('🔍 Début de l\\'analyse de l\\'iris...');\n      try {\n        // Convertir l'image en base64\n        const imageBase64 = yield _this.irisImageService.convertImageToBase64(_this.selectedIrisFile);\n        // Obtenir les dimensions\n        const dimensions = yield _this.irisImageService.getImageDimensions(_this.selectedIrisFile);\n        // Analyser l'iris\n        _this.irisAnalysisResult = yield _this.irisImageService.analyzeIrisImage(imageBase64);\n        console.log('✅ Analyse terminée:', _this.irisAnalysisResult);\n      } catch (error) {\n        console.error('❌ Erreur lors de l\\'analyse:', error);\n        alert('Erreur lors de l\\'analyse de l\\'iris. Veuillez réessayer.');\n      } finally {\n        _this.isAnalyzingIris = false;\n      }\n    })();\n  }\n  /**\n   * Sauvegarde l'image d'iris lors de la connexion\n   */\n  saveIrisImage(userEmail, userName) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.selectedIrisFile || !_this2.irisAnalysisResult) return;\n      try {\n        const imageBase64 = yield _this2.irisImageService.convertImageToBase64(_this2.selectedIrisFile);\n        const dimensions = yield _this2.irisImageService.getImageDimensions(_this2.selectedIrisFile);\n        const irisData = {\n          userEmail: userEmail,\n          userName: userName,\n          imageUrl: '',\n          imageBase64: imageBase64,\n          uploadedAt: new Date(),\n          analysisResult: _this2.irisAnalysisResult,\n          metadata: {\n            fileName: _this2.selectedIrisFile.name,\n            fileSize: _this2.selectedIrisFile.size,\n            fileType: _this2.selectedIrisFile.type,\n            imageWidth: dimensions.width,\n            imageHeight: dimensions.height\n          }\n        };\n        // Sauvegarder dans Firebase\n        _this2.irisImageService.saveIrisImage(irisData).subscribe({\n          next: docId => {\n            console.log('✅ Image d\\'iris sauvegardée avec l\\'ID:', docId);\n          },\n          error: error => {\n            console.error('❌ Erreur sauvegarde iris:', error);\n          }\n        });\n      } catch (error) {\n        console.error('❌ Erreur lors de la sauvegarde de l\\'iris:', error);\n      }\n    })();\n  }\n  /**\n   * Supprime l'image sélectionnée\n   */\n  removeIrisImage() {\n    this.selectedIrisFile = null;\n    this.irisPreviewUrl = null;\n    this.irisAnalysisResult = null;\n    // Reset du input file\n    const fileInput = document.getElementById('irisFile');\n    if (fileInput) {\n      fileInput.value = '';\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 65,\n      vars: 10,\n      consts: [[1, \"auth-container\", \"login\"], [1, \"container\"], [1, \"auth-card\"], [1, \"auth-header\"], [1, \"title\"], [1, \"subtitle\"], [1, \"divider\"], [1, \"auth-form\"], [3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"email\"], [1, \"input-container\"], [1, \"input-icon\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"Votre adresse email\", 3, \"ngModel\", \"ngModelChange\"], [\"email\", \"ngModel\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"for\", \"password\"], [\"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"minlength\", \"6\", \"placeholder\", \"Votre mot de passe\", 3, \"type\", \"ngModel\", \"ngModelChange\"], [\"password\", \"ngModel\"], [\"type\", \"button\", 1, \"toggle-password\", 3, \"click\"], [1, \"form-options\"], [1, \"remember-me\"], [\"type\", \"checkbox\", \"id\", \"remember\", \"name\", \"remember\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"remember\"], [\"href\", \"#\", 1, \"forgot-password\"], [1, \"form-actions\"], [\"type\", \"submit\", 1, \"submit-btn\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"social-login\"], [1, \"separator\"], [1, \"social-buttons\"], [1, \"social-btn\", \"google\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google\"], [1, \"social-btn\", \"facebook\"], [1, \"facebook-icon\"], [1, \"auth-footer\"], [\"routerLink\", \"/signup\"], [1, \"navigation\"], [\"routerLink\", \"/accueil\", 1, \"back-btn\"], [1, \"icon\"], [1, \"error-message\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"Connexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Acc\\u00E9dez \\u00E0 votre compte IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"form\", 8, 9);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"label\", 11);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"span\", 13);\n          i0.ɵɵtext(17, \"\\u2709\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 14, 15);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.loginData.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(20, LoginComponent_div_20_Template, 3, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"label\", 17);\n          i0.ɵɵtext(23, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"span\", 13);\n          i0.ɵɵtext(26, \"\\uD83D\\uDD12\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 18, 19);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_27_listener($event) {\n            return ctx.loginData.password = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_29_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, LoginComponent_div_31_Template, 3, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 21)(33, \"div\", 22)(34, \"input\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_34_listener($event) {\n            return ctx.loginData.rememberMe = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"label\", 24);\n          i0.ɵɵtext(36, \"Se souvenir de moi\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"a\", 25);\n          i0.ɵɵtext(38, \"Mot de passe oubli\\u00E9?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 26)(40, \"button\", 27);\n          i0.ɵɵtemplate(41, LoginComponent_span_41_Template, 2, 0, \"span\", 28);\n          i0.ɵɵtemplate(42, LoginComponent_span_42_Template, 2, 0, \"span\", 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"div\", 29)(44, \"p\", 30);\n          i0.ɵɵtext(45, \"Ou connectez-vous avec\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 31)(47, \"button\", 32);\n          i0.ɵɵelement(48, \"img\", 33);\n          i0.ɵɵtext(49, \" Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"button\", 34)(51, \"span\", 35);\n          i0.ɵɵtext(52, \"f\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" Facebook \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 36)(55, \"p\");\n          i0.ɵɵtext(56, \"Vous n'avez pas de compte? \");\n          i0.ɵɵelementStart(57, \"a\", 37);\n          i0.ɵɵtext(58, \"Inscrivez-vous\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(59, \"div\", 38)(60, \"a\", 39)(61, \"span\", 40);\n          i0.ɵɵtext(62, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\");\n          i0.ɵɵtext(64, \"Retour \\u00E0 l'accueil\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(11);\n          const _r1 = i0.ɵɵreference(19);\n          const _r3 = i0.ɵɵreference(28);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngModel\", ctx.loginData.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && (_r1.dirty || _r1.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\")(\"ngModel\", ctx.loginData.password);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.showPassword ? \"\\uD83D\\uDC41\\uFE0F\" : \"\\uD83D\\uDC41\\uFE0F\\u200D\\uD83D\\uDDE8\\uFE0F\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r3.invalid && (_r3.dirty || _r3.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.loginData.rememberMe);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", _r0.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i2.NgIf, i1.RouterLink, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MinLengthValidator, i3.EmailValidator, i3.NgModel, i3.NgForm],\n      styles: [\".auth-container.login[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\\n  padding: 40px 0;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 20px;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  max-width: 500px;\\n  margin: 0 auto 40px;\\n  padding: 40px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2.2rem;\\n  color: #333;\\n  margin-bottom: 10px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1rem;\\n  margin-bottom: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  margin: 0 auto;\\n  border-radius: 3px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  color: #555;\\n  margin-bottom: 8px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 15px;\\n  font-size: 1.1rem;\\n  color: #aaa;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 15px 12px 45px;\\n  border: 1px solid #ddd;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--fleur-primary);\\n  box-shadow: 0 0 0 3px rgba(138, 79, 255, 0.1);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .toggle-password[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  background: none;\\n  border: none;\\n  font-size: 1.1rem;\\n  cursor: pointer;\\n  color: #aaa;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .toggle-password[_ngcontent-%COMP%]:hover {\\n  color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #e74c3c;\\n  margin-top: 5px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 3px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 25px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n  cursor: pointer;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--fleur-primary);\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 25px rgba(138, 79, 255, 0.4);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%] {\\n  text-align: center;\\n  position: relative;\\n  color: #999;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:before, .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 40%;\\n  height: 1px;\\n  background-color: #ddd;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:before {\\n  left: 0;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:after {\\n  right: 0;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  padding: 12px;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 1px solid #ddd;\\n  background-color: white;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]   .facebook-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background-color: #1877f2;\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn.google[_ngcontent-%COMP%]:hover {\\n  border-color: #ea4335;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn.facebook[_ngcontent-%COMP%]:hover {\\n  border-color: #1877f2;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 12px;\\n  border: 1px solid #dee2e6;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.1rem;\\n  margin-bottom: 10px;\\n  text-align: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-accounts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 15px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  border-color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 0.95rem;\\n  margin: 0 0 8px 0;\\n  font-weight: 600;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .email[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.8rem;\\n  margin: 0 0 5px 0;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .password[_ngcontent-%COMP%] {\\n  color: #888;\\n  font-size: 0.75rem;\\n  margin: 0 0 10px 0;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .role-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 0.7rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .role-badge.role-admin[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);\\n  color: white;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .role-badge.role-user[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);\\n  color: white;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 12px;\\n  overflow: hidden;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-toggle[_ngcontent-%COMP%] {\\n  padding: 15px 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-toggle[_ngcontent-%COMP%]   .toggle-iris-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  background: none;\\n  border: none;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  color: #333;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-toggle[_ngcontent-%COMP%]   .toggle-iris-btn[_ngcontent-%COMP%]:hover {\\n  color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-toggle[_ngcontent-%COMP%]   .toggle-iris-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-toggle[_ngcontent-%COMP%]   .toggle-iris-btn[_ngcontent-%COMP%]   .arrow[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-toggle[_ngcontent-%COMP%]   .iris-info[_ngcontent-%COMP%] {\\n  margin: 10px 0 0 0;\\n  font-size: 0.85rem;\\n  color: #666;\\n  font-style: italic;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%] {\\n  border: 2px dashed #ddd;\\n  border-radius: 12px;\\n  padding: 30px;\\n  text-align: center;\\n  transition: all 0.3s ease;\\n  background: #fafafa;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]:hover {\\n  border-color: var(--fleur-primary);\\n  background: rgba(138, 79, 255, 0.02);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area.has-image[_ngcontent-%COMP%] {\\n  border-style: solid;\\n  border-color: var(--fleur-primary);\\n  background: rgba(138, 79, 255, 0.05);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-content[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-bottom: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin: 0 0 10px 0;\\n  font-size: 1.1rem;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0 0 20px 0;\\n  font-size: 0.9rem;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-content[_ngcontent-%COMP%]   .upload-btn[_ngcontent-%COMP%] {\\n  padding: 10px 25px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-content[_ngcontent-%COMP%]   .upload-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]   .iris-preview[_ngcontent-%COMP%] {\\n  max-width: 200px;\\n  max-height: 200px;\\n  border-radius: 12px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]   .image-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  justify-content: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]   .image-actions[_ngcontent-%COMP%]   .btn-analyze[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]   .image-actions[_ngcontent-%COMP%]   .btn-remove[_ngcontent-%COMP%] {\\n  padding: 8px 16px;\\n  border: none;\\n  border-radius: 6px;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]   .image-actions[_ngcontent-%COMP%]   .btn-analyze[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);\\n  color: white;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]   .image-actions[_ngcontent-%COMP%]   .btn-analyze[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(78, 205, 196, 0.3);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]   .image-actions[_ngcontent-%COMP%]   .btn-analyze[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]   .image-actions[_ngcontent-%COMP%]   .btn-remove[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);\\n  color: white;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]   .image-actions[_ngcontent-%COMP%]   .btn-remove[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  padding: 20px;\\n  background: linear-gradient(135deg, rgba(138, 79, 255, 0.05) 0%, rgba(78, 205, 196, 0.05) 100%);\\n  border-radius: 12px;\\n  border: 1px solid rgba(138, 79, 255, 0.2);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin: 0 0 15px 0;\\n  font-size: 1.1rem;\\n  text-align: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n  padding-bottom: 10px;\\n  border-bottom: 1px solid rgba(138, 79, 255, 0.2);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   .iris-type[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--fleur-primary);\\n  font-size: 1rem;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .result-header[_ngcontent-%COMP%]   .confidence[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #4ecdc4;\\n  font-size: 0.9rem;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .characteristics[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .characteristics[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #333;\\n  margin: 0 0 8px 0;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .characteristics[_ngcontent-%COMP%]   .char-tags[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .characteristics[_ngcontent-%COMP%]   .color-tags[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%]   .char-tags[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%]   .color-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 6px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .characteristics[_ngcontent-%COMP%]   .char-tags[_ngcontent-%COMP%]   .char-tag[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .characteristics[_ngcontent-%COMP%]   .char-tags[_ngcontent-%COMP%]   .color-tag[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .characteristics[_ngcontent-%COMP%]   .color-tags[_ngcontent-%COMP%]   .char-tag[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .characteristics[_ngcontent-%COMP%]   .color-tags[_ngcontent-%COMP%]   .color-tag[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%]   .char-tags[_ngcontent-%COMP%]   .char-tag[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%]   .char-tags[_ngcontent-%COMP%]   .color-tag[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%]   .color-tags[_ngcontent-%COMP%]   .char-tag[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%]   .color-tags[_ngcontent-%COMP%]   .color-tag[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 4px 10px;\\n  background: rgba(138, 79, 255, 0.1);\\n  color: var(--fleur-primary);\\n  border-radius: 15px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .characteristics[_ngcontent-%COMP%]   .char-tags[_ngcontent-%COMP%]   .color-tag[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .characteristics[_ngcontent-%COMP%]   .color-tags[_ngcontent-%COMP%]   .color-tag[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%]   .char-tags[_ngcontent-%COMP%]   .color-tag[_ngcontent-%COMP%], .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%]   .color-tags[_ngcontent-%COMP%]   .color-tag[_ngcontent-%COMP%] {\\n  background: rgba(78, 205, 196, 0.1);\\n  color: #4ecdc4;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .compatibility-preview[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 10px;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 8px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .iris-section[_ngcontent-%COMP%]   .iris-upload[_ngcontent-%COMP%]   .analysis-results[_ngcontent-%COMP%]   .result-card[_ngcontent-%COMP%]   .compatibility-preview[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  font-size: 1rem;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--fleur-primary);\\n  text-decoration: none;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 12px 25px;\\n  background-color: white;\\n  color: #555;\\n  border-radius: 50px;\\n  font-weight: 500;\\n  text-decoration: none;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\\n  color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n@media (max-width: 576px) {\\n  .auth-container.login[_ngcontent-%COMP%] {\\n    padding: 20px 0;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%] {\\n    padding: 25px;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "LoginComponent_div_20_span_1_Template", "LoginComponent_div_20_span_2_Template", "ɵɵadvance", "ɵɵproperty", "_r1", "errors", "LoginComponent_div_31_span_1_Template", "LoginComponent_div_31_span_2_Template", "_r3", "LoginComponent", "constructor", "router", "loginData", "email", "password", "rememberMe", "showPassword", "isLoading", "togglePasswordVisibility", "onSubmit", "setTimeout", "console", "log", "account", "staticAccounts", "find", "acc", "name", "selected<PERSON>ris<PERSON><PERSON>", "irisAnalysisResult", "saveIrisImage", "localStorage", "setItem", "JSON", "stringify", "role", "hasIrisImage", "irisAnalysis", "navigate", "alert", "fillTestAccount", "toggleIrisSection", "showIrisSection", "onIrisFileSelected", "event", "file", "target", "files", "validation", "irisImageService", "validateImageFile", "<PERSON><PERSON><PERSON><PERSON>", "error", "reader", "FileReader", "onload", "e", "irisPreviewUrl", "result", "readAsDataURL", "analyzeIris", "_this", "_asyncToGenerator", "isAnalyzingIris", "imageBase64", "convertImageToBase64", "dimensions", "getImageDimensions", "analyzeIrisImage", "userEmail", "userName", "_this2", "irisData", "imageUrl", "uploadedAt", "Date", "analysisResult", "metadata", "fileName", "fileSize", "size", "fileType", "type", "imageWidth", "width", "imageHeight", "height", "subscribe", "next", "docId", "removeIrisImage", "fileInput", "document", "getElementById", "value", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_10_listener", "LoginComponent_Template_input_ngModelChange_18_listener", "$event", "LoginComponent_div_20_Template", "LoginComponent_Template_input_ngModelChange_27_listener", "LoginComponent_Template_button_click_29_listener", "LoginComponent_div_31_Template", "LoginComponent_Template_input_ngModelChange_34_listener", "LoginComponent_span_41_Template", "LoginComponent_span_42_Template", "invalid", "dirty", "touched", "ɵɵtextInterpolate1", "_r0"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\login\\login.component.ts", "D:\\ProjetPfa\\pfa\\pfa\\src\\app\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\ninterface LoginData {\n  email: string;\n  password: string;\n  rememberMe: boolean;\n}\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n})\nexport class LoginComponent {\n  loginData: LoginData = {\n    email: '',\n    password: '',\n    rememberMe: false\n  };\n\n  showPassword: boolean = false;\n  isLoading: boolean = false;\n\n\n\n  constructor(private router: Router) {}\n\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  onSubmit(): void {\n    if (this.isLoading) return;\n\n    this.isLoading = true;\n\n    // Simuler une connexion avec un délai\n    setTimeout(() => {\n      console.log('Tentative de connexion avec:', this.loginData);\n\n      // Vérifier si les identifiants correspondent à un compte statique\n      const account = this.staticAccounts.find(acc =>\n        acc.email === this.loginData.email && acc.password === this.loginData.password\n      );\n\n      if (account) {\n        console.log('Connexion réussie avec le compte:', account.name);\n\n        // Sauvegarder l'image d'iris si elle a été analysée\n        if (this.selectedIrisFile && this.irisAnalysisResult) {\n          this.saveIrisImage(account.email, account.name);\n        }\n\n        // Sauvegarder les informations de l'utilisateur connecté\n        localStorage.setItem('currentUser', JSON.stringify({\n          name: account.name,\n          email: account.email,\n          role: account.role,\n          hasIrisImage: this.selectedIrisFile !== null,\n          irisAnalysis: this.irisAnalysisResult\n        }));\n\n        // Rediriger selon le rôle\n        if (account.role === 'admin') {\n          this.router.navigate(['/dashboard']);\n        } else {\n          // Les utilisateurs normaux vont directement au test de personnalité\n          this.router.navigate(['/personality-test']);\n        }\n      } else {\n        console.log('Identifiants incorrects');\n        alert('Email ou mot de passe incorrect. Utilisez un des comptes de test.');\n      }\n\n      this.isLoading = false;\n    }, 1500);\n  }\n\n  /**\n   * Remplit automatiquement le formulaire avec un compte de test\n   */\n  fillTestAccount(account: any): void {\n    this.loginData.email = account.email;\n    this.loginData.password = account.password;\n  }\n\n  /**\n   * Affiche/masque la section d'upload d'iris\n   */\n  toggleIrisSection(): void {\n    this.showIrisSection = !this.showIrisSection;\n  }\n\n  /**\n   * Gère la sélection d'un fichier d'iris\n   */\n  onIrisFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    // Valider le fichier\n    const validation = this.irisImageService.validateImageFile(file);\n    if (!validation.isValid) {\n      alert(validation.error);\n      return;\n    }\n\n    this.selectedIrisFile = file;\n\n    // Créer un aperçu de l'image\n    const reader = new FileReader();\n    reader.onload = (e: any) => {\n      this.irisPreviewUrl = e.target.result;\n    };\n    reader.readAsDataURL(file);\n\n    console.log('📸 Image d\\'iris sélectionnée:', file.name);\n  }\n\n  /**\n   * Analyse l'image d'iris\n   */\n  async analyzeIris(): Promise<void> {\n    if (!this.selectedIrisFile) {\n      alert('Veuillez d\\'abord sélectionner une image d\\'iris');\n      return;\n    }\n\n    this.isAnalyzingIris = true;\n    console.log('🔍 Début de l\\'analyse de l\\'iris...');\n\n    try {\n      // Convertir l'image en base64\n      const imageBase64 = await this.irisImageService.convertImageToBase64(this.selectedIrisFile);\n\n      // Obtenir les dimensions\n      const dimensions = await this.irisImageService.getImageDimensions(this.selectedIrisFile);\n\n      // Analyser l'iris\n      this.irisAnalysisResult = await this.irisImageService.analyzeIrisImage(imageBase64);\n\n      console.log('✅ Analyse terminée:', this.irisAnalysisResult);\n\n    } catch (error) {\n      console.error('❌ Erreur lors de l\\'analyse:', error);\n      alert('Erreur lors de l\\'analyse de l\\'iris. Veuillez réessayer.');\n    } finally {\n      this.isAnalyzingIris = false;\n    }\n  }\n\n  /**\n   * Sauvegarde l'image d'iris lors de la connexion\n   */\n  private async saveIrisImage(userEmail: string, userName: string): Promise<void> {\n    if (!this.selectedIrisFile || !this.irisAnalysisResult) return;\n\n    try {\n      const imageBase64 = await this.irisImageService.convertImageToBase64(this.selectedIrisFile);\n      const dimensions = await this.irisImageService.getImageDimensions(this.selectedIrisFile);\n\n      const irisData: IrisImageData = {\n        userEmail: userEmail,\n        userName: userName,\n        imageUrl: '', // Pas d'URL externe pour l'instant\n        imageBase64: imageBase64,\n        uploadedAt: new Date(),\n        analysisResult: this.irisAnalysisResult,\n        metadata: {\n          fileName: this.selectedIrisFile.name,\n          fileSize: this.selectedIrisFile.size,\n          fileType: this.selectedIrisFile.type,\n          imageWidth: dimensions.width,\n          imageHeight: dimensions.height\n        }\n      };\n\n      // Sauvegarder dans Firebase\n      this.irisImageService.saveIrisImage(irisData).subscribe({\n        next: (docId) => {\n          console.log('✅ Image d\\'iris sauvegardée avec l\\'ID:', docId);\n        },\n        error: (error) => {\n          console.error('❌ Erreur sauvegarde iris:', error);\n        }\n      });\n\n    } catch (error) {\n      console.error('❌ Erreur lors de la sauvegarde de l\\'iris:', error);\n    }\n  }\n\n  /**\n   * Supprime l'image sélectionnée\n   */\n  removeIrisImage(): void {\n    this.selectedIrisFile = null;\n    this.irisPreviewUrl = null;\n    this.irisAnalysisResult = null;\n\n    // Reset du input file\n    const fileInput = document.getElementById('irisFile') as HTMLInputElement;\n    if (fileInput) {\n      fileInput.value = '';\n    }\n  }\n}\n", "<div class=\"auth-container login\">\n  <div class=\"container\">\n    <div class=\"auth-card\">\n      <div class=\"auth-header\">\n        <h1 class=\"title\">Connexion</h1>\n        <p class=\"subtitle\">Accédez à votre compte IrisLock</p>\n        <div class=\"divider\"></div>\n      </div>\n\n      <div class=\"auth-form\">\n        <form (ngSubmit)=\"onSubmit()\" #loginForm=\"ngForm\">\n          <div class=\"form-group\">\n            <label for=\"email\">Email</label>\n            <div class=\"input-container\">\n              <span class=\"input-icon\">✉️</span>\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                [(ngModel)]=\"loginData.email\"\n                required\n                email\n                #email=\"ngModel\"\n                placeholder=\"Votre adresse email\"\n              >\n            </div>\n            <div class=\"error-message\" *ngIf=\"email.invalid && (email.dirty || email.touched)\">\n              <span *ngIf=\"email.errors?.['required']\">L'email est requis</span>\n              <span *ngIf=\"email.errors?.['email']\">Veuillez entrer un email valide</span>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"password\">Mot de passe</label>\n            <div class=\"input-container\">\n              <span class=\"input-icon\">🔒</span>\n              <input\n                [type]=\"showPassword ? 'text' : 'password'\"\n                id=\"password\"\n                name=\"password\"\n                [(ngModel)]=\"loginData.password\"\n                required\n                minlength=\"6\"\n                #password=\"ngModel\"\n                placeholder=\"Votre mot de passe\"\n              >\n              <button\n                type=\"button\"\n                class=\"toggle-password\"\n                (click)=\"togglePasswordVisibility()\"\n              >\n                {{ showPassword ? '👁️' : '👁️‍🗨️' }}\n              </button>\n            </div>\n            <div class=\"error-message\" *ngIf=\"password.invalid && (password.dirty || password.touched)\">\n              <span *ngIf=\"password.errors?.['required']\">Le mot de passe est requis</span>\n              <span *ngIf=\"password.errors?.['minlength']\">Le mot de passe doit contenir au moins 6 caractères</span>\n            </div>\n          </div>\n\n          <div class=\"form-options\">\n            <div class=\"remember-me\">\n              <input\n                type=\"checkbox\"\n                id=\"remember\"\n                name=\"remember\"\n                [(ngModel)]=\"loginData.rememberMe\"\n              >\n              <label for=\"remember\">Se souvenir de moi</label>\n            </div>\n            <a href=\"#\" class=\"forgot-password\">Mot de passe oublié?</a>\n          </div>\n\n\n\n          <div class=\"form-actions\">\n            <button\n              type=\"submit\"\n              class=\"submit-btn\"\n              [disabled]=\"loginForm.invalid || isLoading\"\n            >\n              <span *ngIf=\"!isLoading\">Se connecter</span>\n              <span *ngIf=\"isLoading\">Connexion en cours...</span>\n            </button>\n          </div>\n        </form>\n\n        <div class=\"social-login\">\n          <p class=\"separator\">Ou connectez-vous avec</p>\n          <div class=\"social-buttons\">\n            <button class=\"social-btn google\">\n              <img src=\"assets/google-icon.png\" alt=\"Google\">\n              Google\n            </button>\n            <button class=\"social-btn facebook\">\n              <span class=\"facebook-icon\">f</span>\n              Facebook\n            </button>\n          </div>\n        </div>\n\n\n\n        <div class=\"auth-footer\">\n          <p>Vous n'avez pas de compte? <a routerLink=\"/signup\">Inscrivez-vous</a></p>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"navigation\">\n      <a routerLink=\"/accueil\" class=\"back-btn\">\n        <span class=\"icon\">←</span>\n        <span>Retour à l'accueil</span>\n      </a>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;IC2BcA,EAAA,CAAAC,cAAA,WAAyC;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAClEH,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF9EH,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAI,UAAA,IAAAC,qCAAA,mBAAkE;IAClEL,EAAA,CAAAI,UAAA,IAAAE,qCAAA,mBAA4E;IAC9EN,EAAA,CAAAG,YAAA,EAAM;;;;;IAFGH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAgC;IAChCV,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,UAA6B;;;;;IA2BpCV,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7EH,EAAA,CAAAC,cAAA,WAA6C;IAAAD,EAAA,CAAAE,MAAA,+DAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFzGH,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAI,UAAA,IAAAO,qCAAA,mBAA6E;IAC7EX,EAAA,CAAAI,UAAA,IAAAQ,qCAAA,mBAAuG;IACzGZ,EAAA,CAAAG,YAAA,EAAM;;;;;IAFGH,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAAH,MAAA,kBAAAG,GAAA,CAAAH,MAAA,aAAmC;IACnCV,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAAH,MAAA,kBAAAG,GAAA,CAAAH,MAAA,cAAoC;;;;;IAyB3CV,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5CH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADpElE,OAAM,MAAOW,cAAc;EAYzBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAX1B,KAAAC,SAAS,GAAc;MACrBC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;KACb;IAED,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,SAAS,GAAY,KAAK;EAIW;EAErCC,wBAAwBA,CAAA;IACtB,IAAI,CAACF,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAG,QAAQA,CAAA;IACN,IAAI,IAAI,CAACF,SAAS,EAAE;IAEpB,IAAI,CAACA,SAAS,GAAG,IAAI;IAErB;IACAG,UAAU,CAAC,MAAK;MACdC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACV,SAAS,CAAC;MAE3D;MACA,MAAMW,OAAO,GAAG,IAAI,CAACC,cAAc,CAACC,IAAI,CAACC,GAAG,IAC1CA,GAAG,CAACb,KAAK,KAAK,IAAI,CAACD,SAAS,CAACC,KAAK,IAAIa,GAAG,CAACZ,QAAQ,KAAK,IAAI,CAACF,SAAS,CAACE,QAAQ,CAC/E;MAED,IAAIS,OAAO,EAAE;QACXF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEC,OAAO,CAACI,IAAI,CAAC;QAE9D;QACA,IAAI,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACC,kBAAkB,EAAE;UACpD,IAAI,CAACC,aAAa,CAACP,OAAO,CAACV,KAAK,EAAEU,OAAO,CAACI,IAAI,CAAC;;QAGjD;QACAI,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAAC;UACjDP,IAAI,EAAEJ,OAAO,CAACI,IAAI;UAClBd,KAAK,EAAEU,OAAO,CAACV,KAAK;UACpBsB,IAAI,EAAEZ,OAAO,CAACY,IAAI;UAClBC,YAAY,EAAE,IAAI,CAACR,gBAAgB,KAAK,IAAI;UAC5CS,YAAY,EAAE,IAAI,CAACR;SACpB,CAAC,CAAC;QAEH;QACA,IAAIN,OAAO,CAACY,IAAI,KAAK,OAAO,EAAE;UAC5B,IAAI,CAACxB,MAAM,CAAC2B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;SACrC,MAAM;UACL;UACA,IAAI,CAAC3B,MAAM,CAAC2B,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;;OAE9C,MAAM;QACLjB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACtCiB,KAAK,CAAC,mEAAmE,CAAC;;MAG5E,IAAI,CAACtB,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGAuB,eAAeA,CAACjB,OAAY;IAC1B,IAAI,CAACX,SAAS,CAACC,KAAK,GAAGU,OAAO,CAACV,KAAK;IACpC,IAAI,CAACD,SAAS,CAACE,QAAQ,GAAGS,OAAO,CAACT,QAAQ;EAC5C;EAEA;;;EAGA2B,iBAAiBA,CAAA;IACf,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA;;;EAGAC,kBAAkBA,CAACC,KAAU;IAC3B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;IAEX;IACA,MAAMG,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACC,iBAAiB,CAACL,IAAI,CAAC;IAChE,IAAI,CAACG,UAAU,CAACG,OAAO,EAAE;MACvBZ,KAAK,CAACS,UAAU,CAACI,KAAK,CAAC;MACvB;;IAGF,IAAI,CAACxB,gBAAgB,GAAGiB,IAAI;IAE5B;IACA,MAAMQ,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;MACzB,IAAI,CAACC,cAAc,GAAGD,CAAC,CAACV,MAAM,CAACY,MAAM;IACvC,CAAC;IACDL,MAAM,CAACM,aAAa,CAACd,IAAI,CAAC;IAE1BxB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEuB,IAAI,CAAClB,IAAI,CAAC;EAC1D;EAEA;;;EAGMiC,WAAWA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACf,IAAI,CAACD,KAAI,CAACjC,gBAAgB,EAAE;QAC1BW,KAAK,CAAC,kDAAkD,CAAC;QACzD;;MAGFsB,KAAI,CAACE,eAAe,GAAG,IAAI;MAC3B1C,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MAEnD,IAAI;QACF;QACA,MAAM0C,WAAW,SAASH,KAAI,CAACZ,gBAAgB,CAACgB,oBAAoB,CAACJ,KAAI,CAACjC,gBAAgB,CAAC;QAE3F;QACA,MAAMsC,UAAU,SAASL,KAAI,CAACZ,gBAAgB,CAACkB,kBAAkB,CAACN,KAAI,CAACjC,gBAAgB,CAAC;QAExF;QACAiC,KAAI,CAAChC,kBAAkB,SAASgC,KAAI,CAACZ,gBAAgB,CAACmB,gBAAgB,CAACJ,WAAW,CAAC;QAEnF3C,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuC,KAAI,CAAChC,kBAAkB,CAAC;OAE5D,CAAC,OAAOuB,KAAK,EAAE;QACd/B,OAAO,CAAC+B,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDb,KAAK,CAAC,2DAA2D,CAAC;OACnE,SAAS;QACRsB,KAAI,CAACE,eAAe,GAAG,KAAK;;IAC7B;EACH;EAEA;;;EAGcjC,aAAaA,CAACuC,SAAiB,EAAEC,QAAgB;IAAA,IAAAC,MAAA;IAAA,OAAAT,iBAAA;MAC7D,IAAI,CAACS,MAAI,CAAC3C,gBAAgB,IAAI,CAAC2C,MAAI,CAAC1C,kBAAkB,EAAE;MAExD,IAAI;QACF,MAAMmC,WAAW,SAASO,MAAI,CAACtB,gBAAgB,CAACgB,oBAAoB,CAACM,MAAI,CAAC3C,gBAAgB,CAAC;QAC3F,MAAMsC,UAAU,SAASK,MAAI,CAACtB,gBAAgB,CAACkB,kBAAkB,CAACI,MAAI,CAAC3C,gBAAgB,CAAC;QAExF,MAAM4C,QAAQ,GAAkB;UAC9BH,SAAS,EAAEA,SAAS;UACpBC,QAAQ,EAAEA,QAAQ;UAClBG,QAAQ,EAAE,EAAE;UACZT,WAAW,EAAEA,WAAW;UACxBU,UAAU,EAAE,IAAIC,IAAI,EAAE;UACtBC,cAAc,EAAEL,MAAI,CAAC1C,kBAAkB;UACvCgD,QAAQ,EAAE;YACRC,QAAQ,EAAEP,MAAI,CAAC3C,gBAAgB,CAACD,IAAI;YACpCoD,QAAQ,EAAER,MAAI,CAAC3C,gBAAgB,CAACoD,IAAI;YACpCC,QAAQ,EAAEV,MAAI,CAAC3C,gBAAgB,CAACsD,IAAI;YACpCC,UAAU,EAAEjB,UAAU,CAACkB,KAAK;YAC5BC,WAAW,EAAEnB,UAAU,CAACoB;;SAE3B;QAED;QACAf,MAAI,CAACtB,gBAAgB,CAACnB,aAAa,CAAC0C,QAAQ,CAAC,CAACe,SAAS,CAAC;UACtDC,IAAI,EAAGC,KAAK,IAAI;YACdpE,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEmE,KAAK,CAAC;UAC/D,CAAC;UACDrC,KAAK,EAAGA,KAAK,IAAI;YACf/B,OAAO,CAAC+B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACnD;SACD,CAAC;OAEH,CAAC,OAAOA,KAAK,EAAE;QACd/B,OAAO,CAAC+B,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;;IACnE;EACH;EAEA;;;EAGAsC,eAAeA,CAAA;IACb,IAAI,CAAC9D,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC6B,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC5B,kBAAkB,GAAG,IAAI;IAE9B;IACA,MAAM8D,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAqB;IACzE,IAAIF,SAAS,EAAE;MACbA,SAAS,CAACG,KAAK,GAAG,EAAE;;EAExB;;;uBAhMWrF,cAAc,EAAAd,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdxF,cAAc;MAAAyF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd3B7G,EAAA,CAAAC,cAAA,aAAkC;UAIRD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,WAAoB;UAAAD,EAAA,CAAAE,MAAA,gDAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvDH,EAAA,CAAA+G,SAAA,aAA2B;UAC7B/G,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAAuB;UACfD,EAAA,CAAAgH,UAAA,sBAAAC,kDAAA;YAAA,OAAYH,GAAA,CAAAtF,QAAA,EAAU;UAAA,EAAC;UAC3BxB,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,eAA6B;UACFD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,qBASC;UALCD,EAAA,CAAAgH,UAAA,2BAAAE,wDAAAC,MAAA;YAAA,OAAAL,GAAA,CAAA7F,SAAA,CAAAC,KAAA,GAAAiG,MAAA;UAAA,EAA6B;UAJ/BnH,EAAA,CAAAG,YAAA,EASC;UAEHH,EAAA,CAAAI,UAAA,KAAAgH,8BAAA,kBAGM;UACRpH,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,eAA6B;UACFD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,qBASC;UALCD,EAAA,CAAAgH,UAAA,2BAAAK,wDAAAF,MAAA;YAAA,OAAAL,GAAA,CAAA7F,SAAA,CAAAE,QAAA,GAAAgG,MAAA;UAAA,EAAgC;UAJlCnH,EAAA,CAAAG,YAAA,EASC;UACDH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAgH,UAAA,mBAAAM,iDAAA;YAAA,OAASR,GAAA,CAAAvF,wBAAA,EAA0B;UAAA,EAAC;UAEpCvB,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAI,UAAA,KAAAmH,8BAAA,kBAGM;UACRvH,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA0B;UAMpBD,EAAA,CAAAgH,UAAA,2BAAAQ,wDAAAL,MAAA;YAAA,OAAAL,GAAA,CAAA7F,SAAA,CAAAG,UAAA,GAAA+F,MAAA;UAAA,EAAkC;UAJpCnH,EAAA,CAAAG,YAAA,EAKC;UACDH,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAElDH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,iCAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK9DH,EAAA,CAAAC,cAAA,eAA0B;UAMtBD,EAAA,CAAAI,UAAA,KAAAqH,+BAAA,mBAA4C;UAC5CzH,EAAA,CAAAI,UAAA,KAAAsH,+BAAA,mBAAoD;UACtD1H,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA0B;UACHD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC/CH,EAAA,CAAAC,cAAA,eAA4B;UAExBD,EAAA,CAAA+G,SAAA,eAA+C;UAC/C/G,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAoC;UACND,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpCH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAMbH,EAAA,CAAAC,cAAA,eAAyB;UACpBD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAC,cAAA,aAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK9EH,EAAA,CAAAC,cAAA,eAAwB;UAEDD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,+BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;UA7FvBH,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAQ,UAAA,YAAAsG,GAAA,CAAA7F,SAAA,CAAAC,KAAA,CAA6B;UAOLlB,EAAA,CAAAO,SAAA,GAAqD;UAArDP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAkH,OAAA,KAAAlH,GAAA,CAAAmH,KAAA,IAAAnH,GAAA,CAAAoH,OAAA,EAAqD;UAW7E7H,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,UAAA,SAAAsG,GAAA,CAAAzF,YAAA,uBAA2C,YAAAyF,GAAA,CAAA7F,SAAA,CAAAE,QAAA;UAc3CnB,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAA8H,kBAAA,MAAAhB,GAAA,CAAAzF,YAAA,4EACF;UAE0BrB,EAAA,CAAAO,SAAA,GAA8D;UAA9DP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAA8G,OAAA,KAAA9G,GAAA,CAAA+G,KAAA,IAAA/G,GAAA,CAAAgH,OAAA,EAA8D;UAYtF7H,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,YAAAsG,GAAA,CAAA7F,SAAA,CAAAG,UAAA,CAAkC;UAapCpB,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,UAAA,aAAAuH,GAAA,CAAAJ,OAAA,IAAAb,GAAA,CAAAxF,SAAA,CAA2C;UAEpCtB,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAQ,UAAA,UAAAsG,GAAA,CAAAxF,SAAA,CAAgB;UAChBtB,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAAsG,GAAA,CAAAxF,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}