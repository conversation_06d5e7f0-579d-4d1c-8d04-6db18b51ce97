{"ast": null, "code": "import { collection, getDocs } from '@angular/fire/firestore';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/firebase-data-init.service\";\nimport * as i2 from \"../services/personality-test.service\";\nimport * as i3 from \"@angular/fire/firestore\";\nimport * as i4 from \"@angular/common\";\nfunction AdminComponent_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u23F3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDE80\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminComponent_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u23F3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminComponent_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDD04\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AdminComponent_div_27_div_4_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const char_r13 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", char_r13, \" \");\n  }\n}\nfunction AdminComponent_div_27_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"h3\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 22);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 23);\n    i0.ɵɵtemplate(9, AdminComponent_div_27_div_4_span_9_Template, 2, 1, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 25)(11, \"span\", 26);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const family_r11 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(family_r11.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"ID: \", family_r11.id, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(family_r11.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", family_r11.characteristics);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\\uD83D\\uDCDD \", family_r11.questions.length, \" questions\");\n  }\n}\nfunction AdminComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"h2\");\n    i0.ɵɵtext(2, \"\\uD83C\\uDF1F Familles de Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17);\n    i0.ɵɵtemplate(4, AdminComponent_div_27_div_4_Template, 13, 5, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.families);\n  }\n}\nfunction AdminComponent_div_28_tr_20_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const cls_r17 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(cls_r17);\n  }\n}\nfunction AdminComponent_div_28_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 32);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtemplate(9, AdminComponent_div_28_tr_20_span_9_Template, 2, 1, \"span\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"span\", 34);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const question_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(question_r15.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(question_r15.question);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"yes\", question_r15.expectedAnswer)(\"no\", !question_r15.expectedAnswer);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", question_r15.expectedAnswer ? \"Oui\" : \"Non\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", question_r15.classes);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(question_r15.weight);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(question_r15.category);\n  }\n}\nfunction AdminComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"h2\");\n    i0.ɵɵtext(2, \"\\u2753 Questions du Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29)(4, \"table\")(5, \"thead\")(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Question\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"R\\u00E9ponse Attendue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Classes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Poids\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Cat\\u00E9gorie\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"tbody\");\n    i0.ɵɵtemplate(20, AdminComponent_div_28_tr_20_Template, 15, 10, \"tr\", 30);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.questions);\n  }\n}\nfunction AdminComponent_div_29_tr_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 40);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 32);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const response_r20 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(response_r20.userId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(response_r20.sessionId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(response_r20.questionId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"yes\", response_r20.answer)(\"no\", !response_r20.answer);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", response_r20.answer ? \"Oui\" : \"Non\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", response_r20.responseTime || 0, \"ms\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r18.formatDate(response_r20.timestamp));\n  }\n}\nfunction AdminComponent_div_29_p_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 41);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Affichage des 100 premi\\u00E8res r\\u00E9ponses sur \", ctx_r19.userResponses.length, \" total. \");\n  }\n}\nfunction AdminComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"h2\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDCAC R\\u00E9ponses des Utilisateurs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"table\")(5, \"thead\")(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Session\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Question\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"R\\u00E9ponse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Temps de R\\u00E9ponse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Timestamp\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"tbody\");\n    i0.ɵɵtemplate(20, AdminComponent_div_29_tr_20_Template, 14, 10, \"tr\", 30);\n    i0.ɵɵpipe(21, \"slice\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, AdminComponent_div_29_p_22_Template, 2, 1, \"p\", 38);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(21, 2, ctx_r6.userResponses, 0, 100));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.userResponses.length > 100);\n  }\n}\nfunction AdminComponent_div_30_div_4_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"span\", 50);\n    i0.ɵɵtext(2, \"Profil:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 53);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(stat_r22.profile.primaryClass);\n  }\n}\nfunction AdminComponent_div_30_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 48)(7, \"div\", 49)(8, \"span\", 50);\n    i0.ɵɵtext(9, \"Utilisateur:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 51);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 49)(13, \"span\", 50);\n    i0.ɵɵtext(14, \"Questions:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 51);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 49)(18, \"span\", 50);\n    i0.ɵɵtext(19, \"Taux de completion:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 51);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 49)(23, \"span\", 50);\n    i0.ɵɵtext(24, \"Temps moyen:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 51);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(27, AdminComponent_div_30_div_4_div_27_Template, 5, 1, \"div\", 52);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r22 = ctx.$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Session: \", stat_r22.sessionId, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r21.formatDate(stat_r22.completedAt));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(stat_r22.userId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", stat_r22.totalResponses, \"/\", stat_r22.totalQuestions, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", stat_r22.completionRate, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", stat_r22.averageResponseTime, \"ms\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", stat_r22.profile);\n  }\n}\nfunction AdminComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"h2\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDCCA Statistiques de Session\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 43);\n    i0.ɵɵtemplate(4, AdminComponent_div_30_div_4_Template, 28, 8, \"div\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.sessionStats);\n  }\n}\nfunction AdminComponent_div_31_tr_22_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const test_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", test_r26.finalProfile.primaryClass, \" \");\n  }\n}\nfunction AdminComponent_div_31_tr_22_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const test_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", test_r26.finalProfile.secondaryClass, \" \");\n  }\n}\nfunction AdminComponent_div_31_tr_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtemplate(6, AdminComponent_div_31_tr_22_span_6_Template, 2, 1, \"span\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtemplate(8, AdminComponent_div_31_tr_22_span_8_Template, 2, 1, \"span\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const test_r26 = ctx.$implicit;\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(test_r26.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(test_r26.userId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", test_r26.finalProfile);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", test_r26.finalProfile);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.formatDate(test_r26.startedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r25.formatDate(test_r26.completedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((test_r26.responses == null ? null : test_r26.responses.length) || 0);\n  }\n}\nfunction AdminComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"h2\");\n    i0.ɵɵtext(2, \"\\uD83E\\uDDEA Tests de Personnalit\\u00E9 Complets\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 55)(4, \"table\")(5, \"thead\")(6, \"tr\")(7, \"th\");\n    i0.ɵɵtext(8, \"ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Profil Principal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Profil Secondaire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"D\\u00E9marr\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Termin\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"R\\u00E9ponses\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"tbody\");\n    i0.ɵɵtemplate(22, AdminComponent_div_31_tr_22_Template, 15, 7, \"tr\", 30);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.personalityTests);\n  }\n}\nfunction AdminComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵelement(2, \"div\", 63);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class AdminComponent {\n  constructor(firebaseDataInitService, personalityTestService, firestore) {\n    this.firebaseDataInitService = firebaseDataInitService;\n    this.personalityTestService = personalityTestService;\n    this.firestore = firestore;\n    this.families = [];\n    this.questions = [];\n    this.userResponses = [];\n    this.sessionStats = [];\n    this.personalityTests = [];\n    this.isLoading = false;\n    this.selectedTab = 'families';\n  }\n  ngOnInit() {\n    this.loadAllData();\n  }\n  /**\n   * Charge toutes les données depuis Firebase\n   */\n  loadAllData() {\n    this.isLoading = true;\n    // Charger les familles\n    this.firebaseDataInitService.getPersonalityFamilies().subscribe({\n      next: families => {\n        this.families = families;\n        console.log('✅ Familles chargées:', families.length);\n      },\n      error: error => {\n        console.error('❌ Erreur chargement familles:', error);\n      }\n    });\n    // Charger les questions\n    this.firebaseDataInitService.getAllQuestions().subscribe({\n      next: questions => {\n        this.questions = questions;\n        console.log('✅ Questions chargées:', questions.length);\n      },\n      error: error => {\n        console.error('❌ Erreur chargement questions:', error);\n      }\n    });\n    // Charger les réponses utilisateur\n    this.loadUserResponses();\n    // Charger les statistiques de session\n    this.loadSessionStats();\n    // Charger les tests de personnalité\n    this.loadPersonalityTests();\n    this.isLoading = false;\n  }\n  /**\n   * Charge les réponses utilisateur\n   */\n  loadUserResponses() {\n    const responsesCollection = collection(this.firestore, 'user_responses');\n    getDocs(responsesCollection).then(snapshot => {\n      this.userResponses = [];\n      snapshot.forEach(doc => {\n        this.userResponses.push({\n          id: doc.id,\n          ...doc.data()\n        });\n      });\n      this.userResponses.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n      console.log('✅ Réponses utilisateur chargées:', this.userResponses.length);\n    }).catch(error => {\n      console.error('❌ Erreur chargement réponses:', error);\n    });\n  }\n  /**\n   * Charge les statistiques de session\n   */\n  loadSessionStats() {\n    const statsCollection = collection(this.firestore, 'session_stats');\n    getDocs(statsCollection).then(snapshot => {\n      this.sessionStats = [];\n      snapshot.forEach(doc => {\n        this.sessionStats.push({\n          sessionId: doc.id,\n          ...doc.data()\n        });\n      });\n      console.log('✅ Statistiques de session chargées:', this.sessionStats.length);\n    }).catch(error => {\n      console.error('❌ Erreur chargement stats:', error);\n    });\n  }\n  /**\n   * Charge les tests de personnalité\n   */\n  loadPersonalityTests() {\n    const testsCollection = collection(this.firestore, 'personality_tests');\n    getDocs(testsCollection).then(snapshot => {\n      this.personalityTests = [];\n      snapshot.forEach(doc => {\n        this.personalityTests.push({\n          id: doc.id,\n          ...doc.data()\n        });\n      });\n      console.log('✅ Tests de personnalité chargés:', this.personalityTests.length);\n    }).catch(error => {\n      console.error('❌ Erreur chargement tests:', error);\n    });\n  }\n  /**\n   * Aplatit les réponses utilisateur pour l'affichage\n   */\n  flattenUserResponses(data) {\n    const responses = [];\n    Object.keys(data).forEach(userId => {\n      Object.keys(data[userId]).forEach(sessionId => {\n        Object.keys(data[userId][sessionId]).forEach(questionId => {\n          responses.push({\n            userId,\n            sessionId,\n            questionId,\n            ...data[userId][sessionId][questionId]\n          });\n        });\n      });\n    });\n    return responses.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n  }\n  /**\n   * Initialise les données de base\n   */\n  initializeData() {\n    this.isLoading = true;\n    this.firebaseDataInitService.initializeBaseData().subscribe({\n      next: success => {\n        if (success) {\n          console.log('✅ Données initialisées avec succès');\n          this.loadAllData();\n        } else {\n          console.error('❌ Erreur lors de l\\'initialisation');\n        }\n        this.isLoading = false;\n      },\n      error: error => {\n        console.error('❌ Erreur initialisation:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n  /**\n   * Change l'onglet actif\n   */\n  selectTab(tab) {\n    this.selectedTab = tab;\n  }\n  /**\n   * Formate une date pour l'affichage\n   */\n  formatDate(dateString) {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleString('fr-FR');\n  }\n  /**\n   * Obtient le nombre total de questions par famille\n   */\n  getTotalQuestionsByFamily(familyId) {\n    const family = this.families.find(f => f.id === familyId);\n    return family ? family.questions.length : 0;\n  }\n  /**\n   * Obtient les réponses pour un utilisateur spécifique\n   */\n  getResponsesForUser(userId) {\n    return this.userResponses.filter(response => response.userId === userId);\n  }\n  /**\n   * Obtient les statistiques pour une session spécifique\n   */\n  getStatsForSession(sessionId) {\n    return this.sessionStats.find(stat => stat.sessionId === sessionId);\n  }\n  /**\n   * Exporte les données en JSON\n   */\n  exportData() {\n    const exportData = {\n      families: this.families,\n      questions: this.questions,\n      userResponses: this.userResponses,\n      sessionStats: this.sessionStats,\n      personalityTests: this.personalityTests,\n      exportedAt: new Date().toISOString()\n    };\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataBlob = new Blob([dataStr], {\n      type: 'application/json'\n    });\n    const url = URL.createObjectURL(dataBlob);\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `personality-test-data-${new Date().toISOString().split('T')[0]}.json`;\n    link.click();\n    URL.revokeObjectURL(url);\n  }\n  static {\n    this.ɵfac = function AdminComponent_Factory(t) {\n      return new (t || AdminComponent)(i0.ɵɵdirectiveInject(i1.FirebaseDataInitService), i0.ɵɵdirectiveInject(i2.PersonalityTestService), i0.ɵɵdirectiveInject(i3.Firestore));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminComponent,\n      selectors: [[\"app-admin\"]],\n      decls: 33,\n      vars: 27,\n      consts: [[1, \"admin-container\"], [1, \"admin-header\"], [1, \"admin-actions\"], [1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"], [4, \"ngIf\"], [1, \"btn\", \"btn-secondary\", 3, \"disabled\", \"click\"], [1, \"btn\", \"btn-success\", 3, \"click\"], [1, \"tabs\"], [1, \"tab-button\", 3, \"click\"], [1, \"tab-content\"], [\"class\", \"families-tab\", 4, \"ngIf\"], [\"class\", \"questions-tab\", 4, \"ngIf\"], [\"class\", \"responses-tab\", 4, \"ngIf\"], [\"class\", \"stats-tab\", 4, \"ngIf\"], [\"class\", \"tests-tab\", 4, \"ngIf\"], [\"class\", \"loading-overlay\", 4, \"ngIf\"], [1, \"families-tab\"], [1, \"families-grid\"], [\"class\", \"family-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"family-card\"], [1, \"family-header\"], [1, \"family-id\"], [1, \"family-description\"], [1, \"family-characteristics\"], [\"class\", \"characteristic-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"family-stats\"], [1, \"stat\"], [1, \"characteristic-tag\"], [1, \"questions-tab\"], [1, \"questions-table\"], [4, \"ngFor\", \"ngForOf\"], [1, \"question-text\"], [1, \"answer-badge\"], [\"class\", \"class-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"category-tag\"], [1, \"class-tag\"], [1, \"responses-tab\"], [1, \"responses-table\"], [\"class\", \"table-note\", 4, \"ngIf\"], [1, \"user-id\"], [1, \"session-id\"], [1, \"table-note\"], [1, \"stats-tab\"], [1, \"stats-grid\"], [\"class\", \"stat-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"stat-card\"], [1, \"stat-header\"], [1, \"stat-date\"], [1, \"stat-details\"], [1, \"stat-item\"], [1, \"label\"], [1, \"value\"], [\"class\", \"stat-item\", 4, \"ngIf\"], [1, \"value\", \"profile-badge\"], [1, \"tests-tab\"], [1, \"tests-table\"], [1, \"test-id\"], [\"class\", \"profile-badge primary\", 4, \"ngIf\"], [\"class\", \"profile-badge secondary\", 4, \"ngIf\"], [1, \"profile-badge\", \"primary\"], [1, \"profile-badge\", \"secondary\"], [1, \"loading-overlay\"], [1, \"loading-spinner\"], [1, \"spinner\"]],\n      template: function AdminComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\");\n          i0.ɵɵtext(3, \"\\uD83D\\uDD27 Administration - Donn\\u00E9es Firebase\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 2)(5, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function AdminComponent_Template_button_click_5_listener() {\n            return ctx.initializeData();\n          });\n          i0.ɵɵtemplate(6, AdminComponent_span_6_Template, 2, 0, \"span\", 4);\n          i0.ɵɵtemplate(7, AdminComponent_span_7_Template, 2, 0, \"span\", 4);\n          i0.ɵɵtext(8, \" Initialiser les donn\\u00E9es \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function AdminComponent_Template_button_click_9_listener() {\n            return ctx.loadAllData();\n          });\n          i0.ɵɵtemplate(10, AdminComponent_span_10_Template, 2, 0, \"span\", 4);\n          i0.ɵɵtemplate(11, AdminComponent_span_11_Template, 2, 0, \"span\", 4);\n          i0.ɵɵtext(12, \" Recharger \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function AdminComponent_Template_button_click_13_listener() {\n            return ctx.exportData();\n          });\n          i0.ɵɵtext(14, \" \\uD83D\\uDCE5 Exporter \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AdminComponent_Template_button_click_16_listener() {\n            return ctx.selectTab(\"families\");\n          });\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AdminComponent_Template_button_click_18_listener() {\n            return ctx.selectTab(\"questions\");\n          });\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AdminComponent_Template_button_click_20_listener() {\n            return ctx.selectTab(\"responses\");\n          });\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AdminComponent_Template_button_click_22_listener() {\n            return ctx.selectTab(\"stats\");\n          });\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AdminComponent_Template_button_click_24_listener() {\n            return ctx.selectTab(\"tests\");\n          });\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 9);\n          i0.ɵɵtemplate(27, AdminComponent_div_27_Template, 5, 1, \"div\", 10);\n          i0.ɵɵtemplate(28, AdminComponent_div_28_Template, 21, 1, \"div\", 11);\n          i0.ɵɵtemplate(29, AdminComponent_div_29_Template, 23, 6, \"div\", 12);\n          i0.ɵɵtemplate(30, AdminComponent_div_30_Template, 5, 1, \"div\", 13);\n          i0.ɵɵtemplate(31, AdminComponent_div_31_Template, 23, 1, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(32, AdminComponent_div_32_Template, 5, 0, \"div\", 15);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"active\", ctx.selectedTab === \"families\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \\uD83D\\uDC65 Familles (\", ctx.families.length, \") \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.selectedTab === \"questions\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \\u2753 Questions (\", ctx.questions.length, \") \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.selectedTab === \"responses\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \\uD83D\\uDCAC R\\u00E9ponses (\", ctx.userResponses.length, \") \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.selectedTab === \"stats\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \\uD83D\\uDCCA Statistiques (\", ctx.sessionStats.length, \") \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"active\", ctx.selectedTab === \"tests\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵtextInterpolate1(\" \\uD83E\\uDDEA Tests (\", ctx.personalityTests.length, \") \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"families\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"questions\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"responses\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"stats\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedTab === \"tests\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i4.SlicePipe],\n      styles: [\".admin-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\\n}\\n\\n.admin-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 30px;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 12px;\\n  color: white;\\n}\\n\\n.admin-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.8rem;\\n}\\n\\n.admin-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0,0,0,0.2);\\n}\\n\\n.btn-primary[_ngcontent-%COMP%] {\\n  background: #4CAF50;\\n  color: white;\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background: #2196F3;\\n  color: white;\\n}\\n\\n.btn-success[_ngcontent-%COMP%] {\\n  background: #FF9800;\\n  color: white;\\n}\\n\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none;\\n}\\n\\n\\n\\n.tabs[_ngcontent-%COMP%] {\\n  display: flex;\\n  background: #f5f5f5;\\n  border-radius: 12px;\\n  padding: 5px;\\n  margin-bottom: 20px;\\n  overflow-x: auto;\\n}\\n\\n.tab-button[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 20px;\\n  border: none;\\n  background: transparent;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  white-space: nowrap;\\n}\\n\\n.tab-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255,255,255,0.5);\\n}\\n\\n.tab-button.active[_ngcontent-%COMP%] {\\n  background: white;\\n  color: #667eea;\\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1);\\n}\\n\\n\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 20px;\\n  box-shadow: 0 4px 20px rgba(0,0,0,0.1);\\n  min-height: 500px;\\n}\\n\\n\\n\\n.families-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\\n  gap: 20px;\\n  margin-top: 20px;\\n}\\n\\n.family-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n  border-radius: 12px;\\n  padding: 20px;\\n  color: white;\\n  box-shadow: 0 4px 15px rgba(0,0,0,0.1);\\n}\\n\\n.family-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n.family-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.3rem;\\n}\\n\\n.family-id[_ngcontent-%COMP%] {\\n  background: rgba(255,255,255,0.2);\\n  padding: 4px 8px;\\n  border-radius: 6px;\\n  font-size: 0.8rem;\\n}\\n\\n.family-description[_ngcontent-%COMP%] {\\n  margin: 10px 0;\\n  opacity: 0.9;\\n}\\n\\n.family-characteristics[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n  margin: 15px 0;\\n}\\n\\n.characteristic-tag[_ngcontent-%COMP%] {\\n  background: rgba(255,255,255,0.2);\\n  padding: 4px 12px;\\n  border-radius: 20px;\\n  font-size: 0.85rem;\\n}\\n\\n.family-stats[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  padding-top: 15px;\\n  border-top: 1px solid rgba(255,255,255,0.2);\\n}\\n\\n\\n\\n.questions-table[_ngcontent-%COMP%], .responses-table[_ngcontent-%COMP%], .tests-table[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  margin-top: 20px;\\n}\\n\\ntable[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  background: white;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\\n}\\n\\nth[_ngcontent-%COMP%] {\\n  background: #667eea;\\n  color: white;\\n  padding: 15px 10px;\\n  text-align: left;\\n  font-weight: 600;\\n}\\n\\ntd[_ngcontent-%COMP%] {\\n  padding: 12px 10px;\\n  border-bottom: 1px solid #eee;\\n}\\n\\ntr[_ngcontent-%COMP%]:hover {\\n  background: #f8f9ff;\\n}\\n\\n.question-text[_ngcontent-%COMP%] {\\n  max-width: 300px;\\n  word-wrap: break-word;\\n}\\n\\n.answer-badge[_ngcontent-%COMP%] {\\n  padding: 4px 12px;\\n  border-radius: 20px;\\n  font-size: 0.85rem;\\n  font-weight: 600;\\n}\\n\\n.answer-badge.yes[_ngcontent-%COMP%] {\\n  background: #4CAF50;\\n  color: white;\\n}\\n\\n.answer-badge.no[_ngcontent-%COMP%] {\\n  background: #f44336;\\n  color: white;\\n}\\n\\n.class-tag[_ngcontent-%COMP%], .category-tag[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  color: #1976d2;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  margin-right: 5px;\\n  display: inline-block;\\n  margin-bottom: 2px;\\n}\\n\\n.user-id[_ngcontent-%COMP%], .session-id[_ngcontent-%COMP%], .test-id[_ngcontent-%COMP%] {\\n  font-family: monospace;\\n  font-size: 0.85rem;\\n  background: #f5f5f5;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n}\\n\\n\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\\n  gap: 20px;\\n  margin-top: 20px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 12px;\\n  padding: 20px;\\n  color: white;\\n  box-shadow: 0 4px 15px rgba(0,0,0,0.1);\\n}\\n\\n.stat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 15px;\\n  padding-bottom: 10px;\\n  border-bottom: 1px solid rgba(255,255,255,0.2);\\n}\\n\\n.stat-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n}\\n\\n.stat-date[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  opacity: 0.8;\\n}\\n\\n.stat-details[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 8px;\\n}\\n\\n.stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.stat-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n}\\n\\n.stat-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n.profile-badge[_ngcontent-%COMP%] {\\n  background: rgba(255,255,255,0.2);\\n  padding: 4px 12px;\\n  border-radius: 20px;\\n  font-size: 0.85rem;\\n}\\n\\n.profile-badge.primary[_ngcontent-%COMP%] {\\n  background: #4CAF50;\\n}\\n\\n.profile-badge.secondary[_ngcontent-%COMP%] {\\n  background: #FF9800;\\n}\\n\\n\\n\\n.loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: rgba(0,0,0,0.5);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 1000;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 40px;\\n  border-radius: 12px;\\n  text-align: center;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 4px solid #f3f3f3;\\n  border-top: 4px solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin: 0 auto 20px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% { transform: rotate(0deg); }\\n  100% { transform: rotate(360deg); }\\n}\\n\\n.table-note[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  padding: 10px;\\n  background: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 6px;\\n  color: #856404;\\n  text-align: center;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .admin-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n    text-align: center;\\n  }\\n  \\n  .admin-actions[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n    justify-content: center;\\n  }\\n  \\n  .families-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .tabs[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .tab-button[_ngcontent-%COMP%] {\\n    flex: none;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["collection", "getDocs", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "char_r13", "ɵɵtemplate", "AdminComponent_div_27_div_4_span_9_Template", "ɵɵtextInterpolate", "family_r11", "name", "id", "description", "ɵɵproperty", "characteristics", "questions", "length", "AdminComponent_div_27_div_4_Template", "ctx_r4", "families", "cls_r17", "AdminComponent_div_28_tr_20_span_9_Template", "question_r15", "question", "ɵɵclassProp", "expectedAnswer", "classes", "weight", "category", "AdminComponent_div_28_tr_20_Template", "ctx_r5", "response_r20", "userId", "sessionId", "questionId", "answer", "responseTime", "ctx_r18", "formatDate", "timestamp", "ctx_r19", "userResponses", "AdminComponent_div_29_tr_20_Template", "AdminComponent_div_29_p_22_Template", "ɵɵpipeBind3", "ctx_r6", "stat_r22", "profile", "primaryClass", "AdminComponent_div_30_div_4_div_27_Template", "ctx_r21", "completedAt", "ɵɵtextInterpolate2", "totalResponses", "totalQuestions", "completionRate", "averageResponseTime", "AdminComponent_div_30_div_4_Template", "ctx_r7", "sessionStats", "test_r26", "finalProfile", "secondaryClass", "AdminComponent_div_31_tr_22_span_6_Template", "AdminComponent_div_31_tr_22_span_8_Template", "ctx_r25", "startedAt", "responses", "AdminComponent_div_31_tr_22_Template", "ctx_r8", "personalityTests", "ɵɵelement", "AdminComponent", "constructor", "firebaseDataInitService", "personalityTestService", "firestore", "isLoading", "selectedTab", "ngOnInit", "loadAllData", "getPersonalityFamilies", "subscribe", "next", "console", "log", "error", "getAllQuestions", "loadUserResponses", "loadSessionStats", "loadPersonalityTests", "responsesCollection", "then", "snapshot", "for<PERSON>ach", "doc", "push", "data", "sort", "a", "b", "Date", "getTime", "catch", "statsCollection", "testsCollection", "flattenUserResponses", "Object", "keys", "initializeData", "initializeBaseData", "success", "selectTab", "tab", "dateString", "toLocaleString", "getTotalQuestionsByFamily", "familyId", "family", "find", "f", "getResponsesForUser", "filter", "response", "getStatsForSession", "stat", "exportData", "exportedAt", "toISOString", "dataStr", "JSON", "stringify", "dataBlob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "split", "click", "revokeObjectURL", "ɵɵdirectiveInject", "i1", "FirebaseDataInitService", "i2", "PersonalityTestService", "i3", "Firestore", "selectors", "decls", "vars", "consts", "template", "AdminComponent_Template", "rf", "ctx", "ɵɵlistener", "AdminComponent_Template_button_click_5_listener", "AdminComponent_span_6_Template", "AdminComponent_span_7_Template", "AdminComponent_Template_button_click_9_listener", "AdminComponent_span_10_Template", "AdminComponent_span_11_Template", "AdminComponent_Template_button_click_13_listener", "AdminComponent_Template_button_click_16_listener", "AdminComponent_Template_button_click_18_listener", "AdminComponent_Template_button_click_20_listener", "AdminComponent_Template_button_click_22_listener", "AdminComponent_Template_button_click_24_listener", "AdminComponent_div_27_Template", "AdminComponent_div_28_Template", "AdminComponent_div_29_Template", "AdminComponent_div_30_Template", "AdminComponent_div_31_Template", "AdminComponent_div_32_Template"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\admin\\admin.component.ts", "D:\\ProjetPfa\\pfa\\pfa\\src\\app\\admin\\admin.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FirebaseDataInitService, QuestionFamily } from '../services/firebase-data-init.service';\nimport { PersonalityTestService } from '../services/personality-test.service';\nimport { Firestore, collection, getDocs } from '@angular/fire/firestore';\n\n@Component({\n  selector: 'app-admin',\n  templateUrl: './admin.component.html',\n  styleUrls: ['./admin.component.css']\n})\nexport class AdminComponent implements OnInit {\n  families: QuestionFamily[] = [];\n  questions: any[] = [];\n  userResponses: any[] = [];\n  sessionStats: any[] = [];\n  personalityTests: any[] = [];\n  isLoading = false;\n  selectedTab = 'families';\n\n  constructor(\n    private firebaseDataInitService: FirebaseDataInitService,\n    private personalityTestService: PersonalityTestService,\n    private firestore: Firestore\n  ) {}\n\n  ngOnInit(): void {\n    this.loadAllData();\n  }\n\n  /**\n   * Charge toutes les données depuis Firebase\n   */\n  loadAllData(): void {\n    this.isLoading = true;\n\n    // Charger les familles\n    this.firebaseDataInitService.getPersonalityFamilies().subscribe({\n      next: (families) => {\n        this.families = families;\n        console.log('✅ Familles chargées:', families.length);\n      },\n      error: (error) => {\n        console.error('❌ Erreur chargement familles:', error);\n      }\n    });\n\n    // Charger les questions\n    this.firebaseDataInitService.getAllQuestions().subscribe({\n      next: (questions) => {\n        this.questions = questions;\n        console.log('✅ Questions chargées:', questions.length);\n      },\n      error: (error) => {\n        console.error('❌ Erreur chargement questions:', error);\n      }\n    });\n\n    // Charger les réponses utilisateur\n    this.loadUserResponses();\n\n    // Charger les statistiques de session\n    this.loadSessionStats();\n\n    // Charger les tests de personnalité\n    this.loadPersonalityTests();\n\n    this.isLoading = false;\n  }\n\n  /**\n   * Charge les réponses utilisateur\n   */\n  private loadUserResponses(): void {\n    const responsesCollection = collection(this.firestore, 'user_responses');\n    getDocs(responsesCollection).then(snapshot => {\n      this.userResponses = [];\n      snapshot.forEach(doc => {\n        this.userResponses.push({\n          id: doc.id,\n          ...doc.data()\n        });\n      });\n      this.userResponses.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n      console.log('✅ Réponses utilisateur chargées:', this.userResponses.length);\n    }).catch(error => {\n      console.error('❌ Erreur chargement réponses:', error);\n    });\n  }\n\n  /**\n   * Charge les statistiques de session\n   */\n  private loadSessionStats(): void {\n    const statsCollection = collection(this.firestore, 'session_stats');\n    getDocs(statsCollection).then(snapshot => {\n      this.sessionStats = [];\n      snapshot.forEach(doc => {\n        this.sessionStats.push({\n          sessionId: doc.id,\n          ...doc.data()\n        });\n      });\n      console.log('✅ Statistiques de session chargées:', this.sessionStats.length);\n    }).catch(error => {\n      console.error('❌ Erreur chargement stats:', error);\n    });\n  }\n\n  /**\n   * Charge les tests de personnalité\n   */\n  private loadPersonalityTests(): void {\n    const testsCollection = collection(this.firestore, 'personality_tests');\n    getDocs(testsCollection).then(snapshot => {\n      this.personalityTests = [];\n      snapshot.forEach(doc => {\n        this.personalityTests.push({\n          id: doc.id,\n          ...doc.data()\n        });\n      });\n      console.log('✅ Tests de personnalité chargés:', this.personalityTests.length);\n    }).catch(error => {\n      console.error('❌ Erreur chargement tests:', error);\n    });\n  }\n\n  /**\n   * Aplatit les réponses utilisateur pour l'affichage\n   */\n  private flattenUserResponses(data: any): any[] {\n    const responses: any[] = [];\n\n    Object.keys(data).forEach(userId => {\n      Object.keys(data[userId]).forEach(sessionId => {\n        Object.keys(data[userId][sessionId]).forEach(questionId => {\n          responses.push({\n            userId,\n            sessionId,\n            questionId,\n            ...data[userId][sessionId][questionId]\n          });\n        });\n      });\n    });\n\n    return responses.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n  }\n\n  /**\n   * Initialise les données de base\n   */\n  initializeData(): void {\n    this.isLoading = true;\n    this.firebaseDataInitService.initializeBaseData().subscribe({\n      next: (success) => {\n        if (success) {\n          console.log('✅ Données initialisées avec succès');\n          this.loadAllData();\n        } else {\n          console.error('❌ Erreur lors de l\\'initialisation');\n        }\n        this.isLoading = false;\n      },\n      error: (error) => {\n        console.error('❌ Erreur initialisation:', error);\n        this.isLoading = false;\n      }\n    });\n  }\n\n  /**\n   * Change l'onglet actif\n   */\n  selectTab(tab: string): void {\n    this.selectedTab = tab;\n  }\n\n  /**\n   * Formate une date pour l'affichage\n   */\n  formatDate(dateString: string): string {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleString('fr-FR');\n  }\n\n  /**\n   * Obtient le nombre total de questions par famille\n   */\n  getTotalQuestionsByFamily(familyId: string): number {\n    const family = this.families.find(f => f.id === familyId);\n    return family ? family.questions.length : 0;\n  }\n\n  /**\n   * Obtient les réponses pour un utilisateur spécifique\n   */\n  getResponsesForUser(userId: string): any[] {\n    return this.userResponses.filter(response => response.userId === userId);\n  }\n\n  /**\n   * Obtient les statistiques pour une session spécifique\n   */\n  getStatsForSession(sessionId: string): any {\n    return this.sessionStats.find(stat => stat.sessionId === sessionId);\n  }\n\n  /**\n   * Exporte les données en JSON\n   */\n  exportData(): void {\n    const exportData = {\n      families: this.families,\n      questions: this.questions,\n      userResponses: this.userResponses,\n      sessionStats: this.sessionStats,\n      personalityTests: this.personalityTests,\n      exportedAt: new Date().toISOString()\n    };\n\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataBlob = new Blob([dataStr], { type: 'application/json' });\n    const url = URL.createObjectURL(dataBlob);\n\n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `personality-test-data-${new Date().toISOString().split('T')[0]}.json`;\n    link.click();\n\n    URL.revokeObjectURL(url);\n  }\n}\n", "<div class=\"admin-container\">\n  <div class=\"admin-header\">\n    <h1>🔧 Administration - Données Firebase</h1>\n    <div class=\"admin-actions\">\n      <button class=\"btn btn-primary\" (click)=\"initializeData()\" [disabled]=\"isLoading\">\n        <span *ngIf=\"isLoading\">⏳</span>\n        <span *ngIf=\"!isLoading\">🚀</span>\n        Initialiser les données\n      </button>\n      <button class=\"btn btn-secondary\" (click)=\"loadAllData()\" [disabled]=\"isLoading\">\n        <span *ngIf=\"isLoading\">⏳</span>\n        <span *ngIf=\"!isLoading\">🔄</span>\n        Recharger\n      </button>\n      <button class=\"btn btn-success\" (click)=\"exportData()\">\n        📥 Exporter\n      </button>\n    </div>\n  </div>\n\n  <!-- Navigation par onglets -->\n  <div class=\"tabs\">\n    <button \n      class=\"tab-button\" \n      [class.active]=\"selectedTab === 'families'\"\n      (click)=\"selectTab('families')\">\n      👥 Familles ({{families.length}})\n    </button>\n    <button \n      class=\"tab-button\" \n      [class.active]=\"selectedTab === 'questions'\"\n      (click)=\"selectTab('questions')\">\n      ❓ Questions ({{questions.length}})\n    </button>\n    <button \n      class=\"tab-button\" \n      [class.active]=\"selectedTab === 'responses'\"\n      (click)=\"selectTab('responses')\">\n      💬 Réponses ({{userResponses.length}})\n    </button>\n    <button \n      class=\"tab-button\" \n      [class.active]=\"selectedTab === 'stats'\"\n      (click)=\"selectTab('stats')\">\n      📊 Statistiques ({{sessionStats.length}})\n    </button>\n    <button \n      class=\"tab-button\" \n      [class.active]=\"selectedTab === 'tests'\"\n      (click)=\"selectTab('tests')\">\n      🧪 Tests ({{personalityTests.length}})\n    </button>\n  </div>\n\n  <!-- Contenu des onglets -->\n  <div class=\"tab-content\">\n    \n    <!-- Onglet Familles -->\n    <div *ngIf=\"selectedTab === 'families'\" class=\"families-tab\">\n      <h2>🌟 Familles de Personnalité</h2>\n      <div class=\"families-grid\">\n        <div *ngFor=\"let family of families\" class=\"family-card\">\n          <div class=\"family-header\">\n            <h3>{{family.name}}</h3>\n            <span class=\"family-id\">ID: {{family.id}}</span>\n          </div>\n          <p class=\"family-description\">{{family.description}}</p>\n          <div class=\"family-characteristics\">\n            <span *ngFor=\"let char of family.characteristics\" class=\"characteristic-tag\">\n              {{char}}\n            </span>\n          </div>\n          <div class=\"family-stats\">\n            <span class=\"stat\">📝 {{family.questions.length}} questions</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Onglet Questions -->\n    <div *ngIf=\"selectedTab === 'questions'\" class=\"questions-tab\">\n      <h2>❓ Questions du Test</h2>\n      <div class=\"questions-table\">\n        <table>\n          <thead>\n            <tr>\n              <th>ID</th>\n              <th>Question</th>\n              <th>Réponse Attendue</th>\n              <th>Classes</th>\n              <th>Poids</th>\n              <th>Catégorie</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let question of questions\">\n              <td>{{question.id}}</td>\n              <td class=\"question-text\">{{question.question}}</td>\n              <td>\n                <span class=\"answer-badge\" [class.yes]=\"question.expectedAnswer\" [class.no]=\"!question.expectedAnswer\">\n                  {{question.expectedAnswer ? 'Oui' : 'Non'}}\n                </span>\n              </td>\n              <td>\n                <span *ngFor=\"let cls of question.classes\" class=\"class-tag\">{{cls}}</span>\n              </td>\n              <td>{{question.weight}}</td>\n              <td>\n                <span class=\"category-tag\">{{question.category}}</span>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Onglet Réponses -->\n    <div *ngIf=\"selectedTab === 'responses'\" class=\"responses-tab\">\n      <h2>💬 Réponses des Utilisateurs</h2>\n      <div class=\"responses-table\">\n        <table>\n          <thead>\n            <tr>\n              <th>Utilisateur</th>\n              <th>Session</th>\n              <th>Question</th>\n              <th>Réponse</th>\n              <th>Temps de Réponse</th>\n              <th>Timestamp</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let response of userResponses | slice:0:100\">\n              <td class=\"user-id\">{{response.userId}}</td>\n              <td class=\"session-id\">{{response.sessionId}}</td>\n              <td>{{response.questionId}}</td>\n              <td>\n                <span class=\"answer-badge\" [class.yes]=\"response.answer\" [class.no]=\"!response.answer\">\n                  {{response.answer ? 'Oui' : 'Non'}}\n                </span>\n              </td>\n              <td>{{response.responseTime || 0}}ms</td>\n              <td>{{formatDate(response.timestamp)}}</td>\n            </tr>\n          </tbody>\n        </table>\n        <p *ngIf=\"userResponses.length > 100\" class=\"table-note\">\n          Affichage des 100 premières réponses sur {{userResponses.length}} total.\n        </p>\n      </div>\n    </div>\n\n    <!-- Onglet Statistiques -->\n    <div *ngIf=\"selectedTab === 'stats'\" class=\"stats-tab\">\n      <h2>📊 Statistiques de Session</h2>\n      <div class=\"stats-grid\">\n        <div *ngFor=\"let stat of sessionStats\" class=\"stat-card\">\n          <div class=\"stat-header\">\n            <h4>Session: {{stat.sessionId}}</h4>\n            <span class=\"stat-date\">{{formatDate(stat.completedAt)}}</span>\n          </div>\n          <div class=\"stat-details\">\n            <div class=\"stat-item\">\n              <span class=\"label\">Utilisateur:</span>\n              <span class=\"value\">{{stat.userId}}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"label\">Questions:</span>\n              <span class=\"value\">{{stat.totalResponses}}/{{stat.totalQuestions}}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"label\">Taux de completion:</span>\n              <span class=\"value\">{{stat.completionRate}}%</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"label\">Temps moyen:</span>\n              <span class=\"value\">{{stat.averageResponseTime}}ms</span>\n            </div>\n            <div class=\"stat-item\" *ngIf=\"stat.profile\">\n              <span class=\"label\">Profil:</span>\n              <span class=\"value profile-badge\">{{stat.profile.primaryClass}}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Onglet Tests -->\n    <div *ngIf=\"selectedTab === 'tests'\" class=\"tests-tab\">\n      <h2>🧪 Tests de Personnalité Complets</h2>\n      <div class=\"tests-table\">\n        <table>\n          <thead>\n            <tr>\n              <th>ID</th>\n              <th>Utilisateur</th>\n              <th>Profil Principal</th>\n              <th>Profil Secondaire</th>\n              <th>Démarré</th>\n              <th>Terminé</th>\n              <th>Réponses</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let test of personalityTests\">\n              <td class=\"test-id\">{{test.id}}</td>\n              <td>{{test.userId}}</td>\n              <td>\n                <span class=\"profile-badge primary\" *ngIf=\"test.finalProfile\">\n                  {{test.finalProfile.primaryClass}}\n                </span>\n              </td>\n              <td>\n                <span class=\"profile-badge secondary\" *ngIf=\"test.finalProfile\">\n                  {{test.finalProfile.secondaryClass}}\n                </span>\n              </td>\n              <td>{{formatDate(test.startedAt)}}</td>\n              <td>{{formatDate(test.completedAt)}}</td>\n              <td>{{test.responses?.length || 0}}</td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n  </div>\n\n  <!-- Indicateur de chargement -->\n  <div *ngIf=\"isLoading\" class=\"loading-overlay\">\n    <div class=\"loading-spinner\">\n      <div class=\"spinner\"></div>\n      <p>Chargement des données...</p>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAGA,SAAoBA,UAAU,EAAEC,OAAO,QAAQ,yBAAyB;;;;;;;;ICEhEC,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChCH,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAIlCH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAChCH,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAyD9BH,EAAA,CAAAC,cAAA,eAA6E;IAC3ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,QAAA,MACF;;;;;IATJN,EAAA,CAAAC,cAAA,cAAyD;IAEjDD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElDH,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxDH,EAAA,CAAAC,cAAA,cAAoC;IAClCD,EAAA,CAAAO,UAAA,IAAAC,2CAAA,mBAEO;IACTR,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACLD,EAAA,CAAAE,MAAA,IAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAV9DH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAS,iBAAA,CAAAC,UAAA,CAAAC,IAAA,CAAe;IACKX,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,kBAAA,SAAAK,UAAA,CAAAE,EAAA,KAAiB;IAEbZ,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAS,iBAAA,CAAAC,UAAA,CAAAG,WAAA,CAAsB;IAE3Bb,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAc,UAAA,YAAAJ,UAAA,CAAAK,eAAA,CAAyB;IAK7Bf,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAK,kBAAA,kBAAAK,UAAA,CAAAM,SAAA,CAAAC,MAAA,eAAwC;;;;;IAfnEjB,EAAA,CAAAC,cAAA,cAA6D;IACvDD,EAAA,CAAAE,MAAA,iDAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAO,UAAA,IAAAW,oCAAA,mBAcM;IACRlB,EAAA,CAAAG,YAAA,EAAM;;;;IAfoBH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAc,UAAA,YAAAK,MAAA,CAAAC,QAAA,CAAW;;;;;IA2C3BpB,EAAA,CAAAC,cAAA,eAA6D;IAAAD,EAAA,CAAAE,MAAA,GAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAdH,EAAA,CAAAI,SAAA,GAAO;IAAPJ,EAAA,CAAAS,iBAAA,CAAAY,OAAA,CAAO;;;;;IATxErB,EAAA,CAAAC,cAAA,SAAuC;IACjCD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAEAD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAO,UAAA,IAAAe,2CAAA,mBAA2E;IAC7EtB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,UAAI;IACyBD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAZrDH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAS,iBAAA,CAAAc,YAAA,CAAAX,EAAA,CAAe;IACOZ,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAS,iBAAA,CAAAc,YAAA,CAAAC,QAAA,CAAqB;IAElBxB,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAyB,WAAA,QAAAF,YAAA,CAAAG,cAAA,CAAqC,QAAAH,YAAA,CAAAG,cAAA;IAC9D1B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAkB,YAAA,CAAAG,cAAA,sBACF;IAGsB1B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAc,UAAA,YAAAS,YAAA,CAAAI,OAAA,CAAmB;IAEvC3B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAS,iBAAA,CAAAc,YAAA,CAAAK,MAAA,CAAmB;IAEM5B,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAS,iBAAA,CAAAc,YAAA,CAAAM,QAAA,CAAqB;;;;;IA5B5D7B,EAAA,CAAAC,cAAA,cAA+D;IACzDD,EAAA,CAAAE,MAAA,+BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,cAA6B;IAIjBD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,6BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGtBH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAO,UAAA,KAAAuB,oCAAA,mBAeK;IACP9B,EAAA,CAAAG,YAAA,EAAQ;;;;IAhBmBH,EAAA,CAAAI,SAAA,IAAY;IAAZJ,EAAA,CAAAc,UAAA,YAAAiB,MAAA,CAAAf,SAAA,CAAY;;;;;IAqCrChB,EAAA,CAAAC,cAAA,SAAyD;IACnCD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAEAD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IATvBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAS,iBAAA,CAAAuB,YAAA,CAAAC,MAAA,CAAmB;IAChBjC,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAS,iBAAA,CAAAuB,YAAA,CAAAE,SAAA,CAAsB;IACzClC,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAS,iBAAA,CAAAuB,YAAA,CAAAG,UAAA,CAAuB;IAEEnC,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAyB,WAAA,QAAAO,YAAA,CAAAI,MAAA,CAA6B,QAAAJ,YAAA,CAAAI,MAAA;IACtDpC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA2B,YAAA,CAAAI,MAAA,sBACF;IAEEpC,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,KAAA2B,YAAA,CAAAK,YAAA,YAAgC;IAChCrC,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAS,iBAAA,CAAA6B,OAAA,CAAAC,UAAA,CAAAP,YAAA,CAAAQ,SAAA,EAAkC;;;;;IAI5CxC,EAAA,CAAAC,cAAA,YAAyD;IACvDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,yDAAAoC,OAAA,CAAAC,aAAA,CAAAzB,MAAA,aACF;;;;;IA/BJjB,EAAA,CAAAC,cAAA,cAA+D;IACzDD,EAAA,CAAAE,MAAA,kDAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,cAA6B;IAIjBD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,6BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGtBH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAO,UAAA,KAAAoC,oCAAA,mBAWK;;IACP3C,EAAA,CAAAG,YAAA,EAAQ;IAEVH,EAAA,CAAAO,UAAA,KAAAqC,mCAAA,gBAEI;IACN5C,EAAA,CAAAG,YAAA,EAAM;;;;IAjByBH,EAAA,CAAAI,SAAA,IAA8B;IAA9BJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAA6C,WAAA,QAAAC,MAAA,CAAAJ,aAAA,UAA8B;IAcvD1C,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAc,UAAA,SAAAgC,MAAA,CAAAJ,aAAA,CAAAzB,MAAA,OAAgC;;;;;IAgChCjB,EAAA,CAAAC,cAAA,cAA4C;IACtBD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAApCH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAS,iBAAA,CAAAsC,QAAA,CAAAC,OAAA,CAAAC,YAAA,CAA6B;;;;;IAxBrEjD,EAAA,CAAAC,cAAA,cAAyD;IAEjDD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjEH,EAAA,CAAAC,cAAA,cAA0B;IAEFD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5CH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA+C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE5EH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAErDH,EAAA,CAAAC,cAAA,eAAuB;IACDD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE3DH,EAAA,CAAAO,UAAA,KAAA2C,2CAAA,kBAGM;IACRlD,EAAA,CAAAG,YAAA,EAAM;;;;;IAxBAH,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,kBAAA,cAAA0C,QAAA,CAAAb,SAAA,KAA2B;IACPlC,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAS,iBAAA,CAAA0C,OAAA,CAAAZ,UAAA,CAAAQ,QAAA,CAAAK,WAAA,EAAgC;IAKlCpD,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAS,iBAAA,CAAAsC,QAAA,CAAAd,MAAA,CAAe;IAIfjC,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAqD,kBAAA,KAAAN,QAAA,CAAAO,cAAA,OAAAP,QAAA,CAAAQ,cAAA,KAA+C;IAI/CvD,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,kBAAA,KAAA0C,QAAA,CAAAS,cAAA,MAAwB;IAIxBxD,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,kBAAA,KAAA0C,QAAA,CAAAU,mBAAA,OAA8B;IAE5BzD,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAc,UAAA,SAAAiC,QAAA,CAAAC,OAAA,CAAkB;;;;;IAzBlDhD,EAAA,CAAAC,cAAA,cAAuD;IACjDD,EAAA,CAAAE,MAAA,2CAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAO,UAAA,IAAAmD,oCAAA,mBA2BM;IACR1D,EAAA,CAAAG,YAAA,EAAM;;;;IA5BkBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAc,UAAA,YAAA6C,MAAA,CAAAC,YAAA,CAAe;;;;;IAoD7B5D,EAAA,CAAAC,cAAA,eAA8D;IAC5DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAwD,QAAA,CAAAC,YAAA,CAAAb,YAAA,MACF;;;;;IAGAjD,EAAA,CAAAC,cAAA,eAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAwD,QAAA,CAAAC,YAAA,CAAAC,cAAA,MACF;;;;;IAXJ/D,EAAA,CAAAC,cAAA,SAA0C;IACpBD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAO,UAAA,IAAAyD,2CAAA,mBAEO;IACThE,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAO,UAAA,IAAA0D,2CAAA,mBAEO;IACTjE,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAdpBH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAS,iBAAA,CAAAoD,QAAA,CAAAjD,EAAA,CAAW;IAC3BZ,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAS,iBAAA,CAAAoD,QAAA,CAAA5B,MAAA,CAAe;IAEoBjC,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAc,UAAA,SAAA+C,QAAA,CAAAC,YAAA,CAAuB;IAKrB9D,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAc,UAAA,SAAA+C,QAAA,CAAAC,YAAA,CAAuB;IAI5D9D,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAS,iBAAA,CAAAyD,OAAA,CAAA3B,UAAA,CAAAsB,QAAA,CAAAM,SAAA,EAA8B;IAC9BnE,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAS,iBAAA,CAAAyD,OAAA,CAAA3B,UAAA,CAAAsB,QAAA,CAAAT,WAAA,EAAgC;IAChCpD,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAS,iBAAA,EAAAoD,QAAA,CAAAO,SAAA,kBAAAP,QAAA,CAAAO,SAAA,CAAAnD,MAAA,OAA+B;;;;;IA/B7CjB,EAAA,CAAAC,cAAA,cAAuD;IACjDD,EAAA,CAAAE,MAAA,uDAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,cAAyB;IAIbD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,yBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,qBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGrBH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAO,UAAA,KAAA8D,oCAAA,kBAgBK;IACPrE,EAAA,CAAAG,YAAA,EAAQ;;;;IAjBeH,EAAA,CAAAI,SAAA,IAAmB;IAAnBJ,EAAA,CAAAc,UAAA,YAAAwD,MAAA,CAAAC,gBAAA,CAAmB;;;;;IAyBlDvE,EAAA,CAAAC,cAAA,cAA+C;IAE3CD,EAAA,CAAAwE,SAAA,cAA2B;IAC3BxE,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,qCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;AD9NtC,OAAM,MAAOsE,cAAc;EASzBC,YACUC,uBAAgD,EAChDC,sBAA8C,EAC9CC,SAAoB;IAFpB,KAAAF,uBAAuB,GAAvBA,uBAAuB;IACvB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,SAAS,GAATA,SAAS;IAXnB,KAAAzD,QAAQ,GAAqB,EAAE;IAC/B,KAAAJ,SAAS,GAAU,EAAE;IACrB,KAAA0B,aAAa,GAAU,EAAE;IACzB,KAAAkB,YAAY,GAAU,EAAE;IACxB,KAAAW,gBAAgB,GAAU,EAAE;IAC5B,KAAAO,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAG,UAAU;EAMrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEA;;;EAGAA,WAAWA,CAAA;IACT,IAAI,CAACH,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACH,uBAAuB,CAACO,sBAAsB,EAAE,CAACC,SAAS,CAAC;MAC9DC,IAAI,EAAGhE,QAAQ,IAAI;QACjB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;QACxBiE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAElE,QAAQ,CAACH,MAAM,CAAC;MACtD,CAAC;MACDsE,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;IAEF;IACA,IAAI,CAACZ,uBAAuB,CAACa,eAAe,EAAE,CAACL,SAAS,CAAC;MACvDC,IAAI,EAAGpE,SAAS,IAAI;QAClB,IAAI,CAACA,SAAS,GAAGA,SAAS;QAC1BqE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEtE,SAAS,CAACC,MAAM,CAAC;MACxD,CAAC;MACDsE,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;IAEF;IACA,IAAI,CAACE,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACC,gBAAgB,EAAE;IAEvB;IACA,IAAI,CAACC,oBAAoB,EAAE;IAE3B,IAAI,CAACb,SAAS,GAAG,KAAK;EACxB;EAEA;;;EAGQW,iBAAiBA,CAAA;IACvB,MAAMG,mBAAmB,GAAG9F,UAAU,CAAC,IAAI,CAAC+E,SAAS,EAAE,gBAAgB,CAAC;IACxE9E,OAAO,CAAC6F,mBAAmB,CAAC,CAACC,IAAI,CAACC,QAAQ,IAAG;MAC3C,IAAI,CAACpD,aAAa,GAAG,EAAE;MACvBoD,QAAQ,CAACC,OAAO,CAACC,GAAG,IAAG;QACrB,IAAI,CAACtD,aAAa,CAACuD,IAAI,CAAC;UACtBrF,EAAE,EAAEoF,GAAG,CAACpF,EAAE;UACV,GAAGoF,GAAG,CAACE,IAAI;SACZ,CAAC;MACJ,CAAC,CAAC;MACF,IAAI,CAACxD,aAAa,CAACyD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAAC7D,SAAS,CAAC,CAAC+D,OAAO,EAAE,GAAG,IAAID,IAAI,CAACF,CAAC,CAAC5D,SAAS,CAAC,CAAC+D,OAAO,EAAE,CAAC;MACpGlB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC5C,aAAa,CAACzB,MAAM,CAAC;IAC5E,CAAC,CAAC,CAACuF,KAAK,CAACjB,KAAK,IAAG;MACfF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD,CAAC,CAAC;EACJ;EAEA;;;EAGQG,gBAAgBA,CAAA;IACtB,MAAMe,eAAe,GAAG3G,UAAU,CAAC,IAAI,CAAC+E,SAAS,EAAE,eAAe,CAAC;IACnE9E,OAAO,CAAC0G,eAAe,CAAC,CAACZ,IAAI,CAACC,QAAQ,IAAG;MACvC,IAAI,CAAClC,YAAY,GAAG,EAAE;MACtBkC,QAAQ,CAACC,OAAO,CAACC,GAAG,IAAG;QACrB,IAAI,CAACpC,YAAY,CAACqC,IAAI,CAAC;UACrB/D,SAAS,EAAE8D,GAAG,CAACpF,EAAE;UACjB,GAAGoF,GAAG,CAACE,IAAI;SACZ,CAAC;MACJ,CAAC,CAAC;MACFb,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE,IAAI,CAAC1B,YAAY,CAAC3C,MAAM,CAAC;IAC9E,CAAC,CAAC,CAACuF,KAAK,CAACjB,KAAK,IAAG;MACfF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,CAAC;EACJ;EAEA;;;EAGQI,oBAAoBA,CAAA;IAC1B,MAAMe,eAAe,GAAG5G,UAAU,CAAC,IAAI,CAAC+E,SAAS,EAAE,mBAAmB,CAAC;IACvE9E,OAAO,CAAC2G,eAAe,CAAC,CAACb,IAAI,CAACC,QAAQ,IAAG;MACvC,IAAI,CAACvB,gBAAgB,GAAG,EAAE;MAC1BuB,QAAQ,CAACC,OAAO,CAACC,GAAG,IAAG;QACrB,IAAI,CAACzB,gBAAgB,CAAC0B,IAAI,CAAC;UACzBrF,EAAE,EAAEoF,GAAG,CAACpF,EAAE;UACV,GAAGoF,GAAG,CAACE,IAAI;SACZ,CAAC;MACJ,CAAC,CAAC;MACFb,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACf,gBAAgB,CAACtD,MAAM,CAAC;IAC/E,CAAC,CAAC,CAACuF,KAAK,CAACjB,KAAK,IAAG;MACfF,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD,CAAC,CAAC;EACJ;EAEA;;;EAGQoB,oBAAoBA,CAACT,IAAS;IACpC,MAAM9B,SAAS,GAAU,EAAE;IAE3BwC,MAAM,CAACC,IAAI,CAACX,IAAI,CAAC,CAACH,OAAO,CAAC9D,MAAM,IAAG;MACjC2E,MAAM,CAACC,IAAI,CAACX,IAAI,CAACjE,MAAM,CAAC,CAAC,CAAC8D,OAAO,CAAC7D,SAAS,IAAG;QAC5C0E,MAAM,CAACC,IAAI,CAACX,IAAI,CAACjE,MAAM,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC6D,OAAO,CAAC5D,UAAU,IAAG;UACxDiC,SAAS,CAAC6B,IAAI,CAAC;YACbhE,MAAM;YACNC,SAAS;YACTC,UAAU;YACV,GAAG+D,IAAI,CAACjE,MAAM,CAAC,CAACC,SAAS,CAAC,CAACC,UAAU;WACtC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOiC,SAAS,CAAC+B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIC,IAAI,CAACD,CAAC,CAAC7D,SAAS,CAAC,CAAC+D,OAAO,EAAE,GAAG,IAAID,IAAI,CAACF,CAAC,CAAC5D,SAAS,CAAC,CAAC+D,OAAO,EAAE,CAAC;EACpG;EAEA;;;EAGAO,cAAcA,CAAA;IACZ,IAAI,CAAChC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,uBAAuB,CAACoC,kBAAkB,EAAE,CAAC5B,SAAS,CAAC;MAC1DC,IAAI,EAAG4B,OAAO,IAAI;QAChB,IAAIA,OAAO,EAAE;UACX3B,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;UACjD,IAAI,CAACL,WAAW,EAAE;SACnB,MAAM;UACLI,OAAO,CAACE,KAAK,CAAC,oCAAoC,CAAC;;QAErD,IAAI,CAACT,SAAS,GAAG,KAAK;MACxB,CAAC;MACDS,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAACT,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;;;EAGAmC,SAASA,CAACC,GAAW;IACnB,IAAI,CAACnC,WAAW,GAAGmC,GAAG;EACxB;EAEA;;;EAGA3E,UAAUA,CAAC4E,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIb,IAAI,CAACa,UAAU,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC;EACrD;EAEA;;;EAGAC,yBAAyBA,CAACC,QAAgB;IACxC,MAAMC,MAAM,GAAG,IAAI,CAACnG,QAAQ,CAACoG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7G,EAAE,KAAK0G,QAAQ,CAAC;IACzD,OAAOC,MAAM,GAAGA,MAAM,CAACvG,SAAS,CAACC,MAAM,GAAG,CAAC;EAC7C;EAEA;;;EAGAyG,mBAAmBA,CAACzF,MAAc;IAChC,OAAO,IAAI,CAACS,aAAa,CAACiF,MAAM,CAACC,QAAQ,IAAIA,QAAQ,CAAC3F,MAAM,KAAKA,MAAM,CAAC;EAC1E;EAEA;;;EAGA4F,kBAAkBA,CAAC3F,SAAiB;IAClC,OAAO,IAAI,CAAC0B,YAAY,CAAC4D,IAAI,CAACM,IAAI,IAAIA,IAAI,CAAC5F,SAAS,KAAKA,SAAS,CAAC;EACrE;EAEA;;;EAGA6F,UAAUA,CAAA;IACR,MAAMA,UAAU,GAAG;MACjB3G,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBJ,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB0B,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCkB,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BW,gBAAgB,EAAE,IAAI,CAACA,gBAAgB;MACvCyD,UAAU,EAAE,IAAI1B,IAAI,EAAE,CAAC2B,WAAW;KACnC;IAED,MAAMC,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACL,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMM,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAACJ,OAAO,CAAC,EAAE;MAAEK,IAAI,EAAE;IAAkB,CAAE,CAAC;IAClE,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,QAAQ,CAAC;IAEzC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;IACfG,IAAI,CAACI,QAAQ,GAAG,yBAAyB,IAAIzC,IAAI,EAAE,CAAC2B,WAAW,EAAE,CAACe,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IACtFL,IAAI,CAACM,KAAK,EAAE;IAEZR,GAAG,CAACS,eAAe,CAACV,GAAG,CAAC;EAC1B;;;uBA7NW/D,cAAc,EAAAzE,EAAA,CAAAmJ,iBAAA,CAAAC,EAAA,CAAAC,uBAAA,GAAArJ,EAAA,CAAAmJ,iBAAA,CAAAG,EAAA,CAAAC,sBAAA,GAAAvJ,EAAA,CAAAmJ,iBAAA,CAAAK,EAAA,CAAAC,SAAA;IAAA;EAAA;;;YAAdhF,cAAc;MAAAiF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV3BhK,EAAA,CAAAC,cAAA,aAA6B;UAErBD,EAAA,CAAAE,MAAA,0DAAoC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7CH,EAAA,CAAAC,cAAA,aAA2B;UACOD,EAAA,CAAAkK,UAAA,mBAAAC,gDAAA;YAAA,OAASF,GAAA,CAAAnD,cAAA,EAAgB;UAAA,EAAC;UACxD9G,EAAA,CAAAO,UAAA,IAAA6J,8BAAA,kBAAgC;UAChCpK,EAAA,CAAAO,UAAA,IAAA8J,8BAAA,kBAAkC;UAClCrK,EAAA,CAAAE,MAAA,qCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,gBAAiF;UAA/CD,EAAA,CAAAkK,UAAA,mBAAAI,gDAAA;YAAA,OAASL,GAAA,CAAAhF,WAAA,EAAa;UAAA,EAAC;UACvDjF,EAAA,CAAAO,UAAA,KAAAgK,+BAAA,kBAAgC;UAChCvK,EAAA,CAAAO,UAAA,KAAAiK,+BAAA,kBAAkC;UAClCxK,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAAuD;UAAvBD,EAAA,CAAAkK,UAAA,mBAAAO,iDAAA;YAAA,OAASR,GAAA,CAAAlC,UAAA,EAAY;UAAA,EAAC;UACpD/H,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,cAAkB;UAIdD,EAAA,CAAAkK,UAAA,mBAAAQ,iDAAA;YAAA,OAAST,GAAA,CAAAhD,SAAA,CAAU,UAAU,CAAC;UAAA,EAAC;UAC/BjH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAGmC;UAAjCD,EAAA,CAAAkK,UAAA,mBAAAS,iDAAA;YAAA,OAASV,GAAA,CAAAhD,SAAA,CAAU,WAAW,CAAC;UAAA,EAAC;UAChCjH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAGmC;UAAjCD,EAAA,CAAAkK,UAAA,mBAAAU,iDAAA;YAAA,OAASX,GAAA,CAAAhD,SAAA,CAAU,WAAW,CAAC;UAAA,EAAC;UAChCjH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAG+B;UAA7BD,EAAA,CAAAkK,UAAA,mBAAAW,iDAAA;YAAA,OAASZ,GAAA,CAAAhD,SAAA,CAAU,OAAO,CAAC;UAAA,EAAC;UAC5BjH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,iBAG+B;UAA7BD,EAAA,CAAAkK,UAAA,mBAAAY,iDAAA;YAAA,OAASb,GAAA,CAAAhD,SAAA,CAAU,OAAO,CAAC;UAAA,EAAC;UAC5BjH,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAC,cAAA,cAAyB;UAGvBD,EAAA,CAAAO,UAAA,KAAAwK,8BAAA,kBAmBM;UAGN/K,EAAA,CAAAO,UAAA,KAAAyK,8BAAA,mBAkCM;UAGNhL,EAAA,CAAAO,UAAA,KAAA0K,8BAAA,mBAiCM;UAGNjL,EAAA,CAAAO,UAAA,KAAA2K,8BAAA,kBAgCM;UAGNlL,EAAA,CAAAO,UAAA,KAAA4K,8BAAA,mBAoCM;UAERnL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAO,UAAA,KAAA6K,8BAAA,kBAKM;UACRpL,EAAA,CAAAG,YAAA,EAAM;;;UAvO2DH,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAc,UAAA,aAAAmJ,GAAA,CAAAnF,SAAA,CAAsB;UACxE9E,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAc,UAAA,SAAAmJ,GAAA,CAAAnF,SAAA,CAAe;UACf9E,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAc,UAAA,UAAAmJ,GAAA,CAAAnF,SAAA,CAAgB;UAGiC9E,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAc,UAAA,aAAAmJ,GAAA,CAAAnF,SAAA,CAAsB;UACvE9E,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAc,UAAA,SAAAmJ,GAAA,CAAAnF,SAAA,CAAe;UACf9E,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAc,UAAA,UAAAmJ,GAAA,CAAAnF,SAAA,CAAgB;UAazB9E,EAAA,CAAAI,SAAA,GAA2C;UAA3CJ,EAAA,CAAAyB,WAAA,WAAAwI,GAAA,CAAAlF,WAAA,gBAA2C;UAE3C/E,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,6BAAA4J,GAAA,CAAA7I,QAAA,CAAAH,MAAA,OACF;UAGEjB,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAyB,WAAA,WAAAwI,GAAA,CAAAlF,WAAA,iBAA4C;UAE5C/E,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,wBAAA4J,GAAA,CAAAjJ,SAAA,CAAAC,MAAA,OACF;UAGEjB,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAyB,WAAA,WAAAwI,GAAA,CAAAlF,WAAA,iBAA4C;UAE5C/E,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,kCAAA4J,GAAA,CAAAvH,aAAA,CAAAzB,MAAA,OACF;UAGEjB,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAyB,WAAA,WAAAwI,GAAA,CAAAlF,WAAA,aAAwC;UAExC/E,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,iCAAA4J,GAAA,CAAArG,YAAA,CAAA3C,MAAA,OACF;UAGEjB,EAAA,CAAAI,SAAA,GAAwC;UAAxCJ,EAAA,CAAAyB,WAAA,WAAAwI,GAAA,CAAAlF,WAAA,aAAwC;UAExC/E,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,0BAAA4J,GAAA,CAAA1F,gBAAA,CAAAtD,MAAA,OACF;UAOMjB,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAc,UAAA,SAAAmJ,GAAA,CAAAlF,WAAA,gBAAgC;UAsBhC/E,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAc,UAAA,SAAAmJ,GAAA,CAAAlF,WAAA,iBAAiC;UAqCjC/E,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAc,UAAA,SAAAmJ,GAAA,CAAAlF,WAAA,iBAAiC;UAoCjC/E,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAc,UAAA,SAAAmJ,GAAA,CAAAlF,WAAA,aAA6B;UAmC7B/E,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAc,UAAA,SAAAmJ,GAAA,CAAAlF,WAAA,aAA6B;UAyC/B/E,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAc,UAAA,SAAAmJ,GAAA,CAAAnF,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}