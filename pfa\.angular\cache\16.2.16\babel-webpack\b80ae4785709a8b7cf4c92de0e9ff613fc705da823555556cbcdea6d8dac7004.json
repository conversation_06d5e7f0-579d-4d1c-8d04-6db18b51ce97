{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nexport class SignupComponent {\n  constructor() {\n    this.user = {\n      name: '',\n      email: '',\n      password: '',\n      terms: false\n    };\n  }\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 51,\n      vars: 5,\n      consts: [[1, \"signup-container\"], [1, \"left-section\"], [1, \"graphic\"], [1, \"dot\"], [1, \"slogan\"], [1, \"right-section\"], [1, \"form-container\"], [1, \"logo\"], [1, \"custom-logo\"], [1, \"google-btn\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google Icon\"], [1, \"divider\"], [3, \"ngSubmit\"], [\"signupForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \"<PERSON> <PERSON>\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"placeholder\", \"<EMAIL>\", \"required\", \"\", \"email\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"placeholder\", \"At least 8 characters\", \"required\", \"\", \"minlength\", \"8\", 3, \"ngModel\", \"ngModelChange\"], [1, \"checkbox-group\"], [\"type\", \"checkbox\", \"id\", \"terms\", \"name\", \"terms\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"terms\"], [\"href\", \"#\"], [\"type\", \"submit\", 1, \"signup-btn\", 3, \"disabled\"], [1, \"login-link\"], [\"routerLink\", \"/login\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"div\", 3)(4, \"div\", 3)(5, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 4);\n          i0.ɵɵtext(7, \" Each iris is a unique story written by nature, waiting to be decoded by technology \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 5)(9, \"div\", 6)(10, \"div\", 7)(11, \"div\", 8);\n          i0.ɵɵelement(12, \"span\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"h1\");\n          i0.ɵɵtext(14, \"Sign Up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\");\n          i0.ɵɵtext(16, \"Votre identit\\u00E9, red\\u00E9finie par la technologie. Cr\\u00E9ez votre compte.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"button\", 9);\n          i0.ɵɵelement(18, \"img\", 10);\n          i0.ɵɵtext(19, \" Continue with Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 11);\n          i0.ɵɵtext(21, \"or Sign in with Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"form\", 12, 13);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_22_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(24, \"div\", 14)(25, \"label\", 15);\n          i0.ɵɵtext(26, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_27_listener($event) {\n            return ctx.user.name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 14)(29, \"label\", 17);\n          i0.ɵɵtext(30, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"input\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_31_listener($event) {\n            return ctx.user.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 14)(33, \"label\", 19);\n          i0.ɵɵtext(34, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_35_listener($event) {\n            return ctx.user.password = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 21)(37, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_37_listener($event) {\n            return ctx.user.terms = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"label\", 23);\n          i0.ɵɵtext(39, \"I agree with \");\n          i0.ɵɵelementStart(40, \"a\", 24);\n          i0.ɵɵtext(41, \"Terms\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" and \");\n          i0.ɵɵelementStart(43, \"a\", 24);\n          i0.ɵɵtext(44, \"Privacy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"button\", 25);\n          i0.ɵɵtext(46, \" Sign Up \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"p\", 26);\n          i0.ɵɵtext(48, \" Already have an account? \");\n          i0.ɵɵelementStart(49, \"a\", 27);\n          i0.ɵɵtext(50, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(23);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.terms);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", !_r0.valid);\n        }\n      },\n      dependencies: [i1.RouterLink, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MinLengthValidator, i2.CheckboxRequiredValidator, i2.EmailValidator, i2.NgModel, i2.NgForm],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.signup-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  background: #f5f5ff; \\n\\n  overflow: hidden;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  background: #e6e6ff url(\\\"./assets/iris-collage.png\\\") no-repeat center center;\\n  background-size: 300px auto;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n  overflow: hidden;\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\\n}\\n\\n\\n\\n.dot[_ngcontent-%COMP%] {\\n  position: absolute;\\n  background: #ff6f61; \\n\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_pulseDot 2s infinite;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before, .left-section[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #ff6f61;\\n  border-radius: 50%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before {\\n  width: 5px;\\n  height: 5px;\\n  top: 10%;\\n  left: 20%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::after {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 15%;\\n  right: 10%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(1) {\\n  width: 4px;\\n  height: 4px;\\n  top: 5%;\\n  left: 30%;\\n  animation-delay: 0.5s;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(2) {\\n  width: 6px;\\n  height: 6px;\\n  top: 20%;\\n  right: 15%;\\n  animation-delay: 1s;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(3) {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 20%;\\n  left: 10%;\\n  animation-delay: 1.5s;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%]::before, .left-section[_ngcontent-%COMP%]::after, .left-section[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: rgba(255, 182, 193, 0.5); \\n\\n  border-radius: 50%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before {\\n  width: 80px;\\n  height: 80px;\\n  top: 10%;\\n  right: 10%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::after {\\n  width: 50px;\\n  height: 50px;\\n  bottom: 10%;\\n  left: 5%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  background: rgba(200, 200, 255, 0.3); \\n\\n}\\n\\n.slogan[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #6464ad; \\n\\n  text-align: center;\\n  margin-top: 40px;\\n  font-style: italic;\\n  font-family: \\\"Dancing Script\\\", cursive;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_fadeIn 1.5s ease-in-out forwards;\\n}\\n\\n\\n\\n.right-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background: linear-gradient(135deg, #f5f5ff, #ffffff);\\n}\\n\\n\\n\\n.form-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 550px;\\n  padding: 20px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(106, 90, 205, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_slideIn 1s ease-in-out forwards;\\n}\\n\\n\\n\\n.custom-logo[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  margin: 0 auto;\\n  position: relative;\\n  background: transparent;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd;\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  bottom: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd;\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  left: 5px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  right: 5px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.2rem;\\n  color: #333;\\n  font-weight: 700;\\n  margin: 10px 0;\\n  letter-spacing: 1px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n  font-weight: 400;\\n}\\n\\n.google-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  padding: 10px;\\n  background: #fff;\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n  color: #333;\\n  font-weight: 500;\\n  margin-bottom: 15px;\\n  transition: all 0.3s ease;\\n}\\n\\n.google-btn[_ngcontent-%COMP%]:hover {\\n  background: #e6e6fa;\\n  box-shadow: 0 0 15px rgba(106, 90, 205, 0.2);\\n  transform: translateY(-2px);\\n}\\n\\n.google-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 18px;\\n  margin-right: 8px;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #666;\\n  font-size: 0.8rem;\\n  margin: 15px 0;\\n  position: relative;\\n  font-weight: 500;\\n}\\n\\n.divider[_ngcontent-%COMP%]::before, .divider[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 40%;\\n  height: 1px;\\n  background: rgba(106, 90, 205, 0.2);\\n}\\n\\n.divider[_ngcontent-%COMP%]::before {\\n  left: -10px;\\n}\\n\\n.divider[_ngcontent-%COMP%]::after {\\n  right: -10px;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.8rem;\\n  color: #333;\\n  margin-bottom: 6px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  color: #333;\\n  background: rgba(255, 255, 255, 0.5);\\n  transition: all 0.3s ease;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #6a5acd;\\n  box-shadow: 0 0 10px rgba(106, 90, 205, 0.2);\\n  background: #fff;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-weight: 400;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: #5a4bbd;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px;\\n  background: linear-gradient(90deg, #6a5acd, #5a4bbd);\\n  color: #fff; \\n\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  text-transform: uppercase;\\n  transition: all 0.3s ease;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, #5a4bbd, #6a5acd);\\n  box-shadow: 0 0 15px rgba(106, 90, 205, 0.4);\\n  transform: translateY(-2px);\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n  box-shadow: none;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 15px;\\n  font-size: 0.8rem;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: #5a4bbd;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInScale {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.8);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulseDot {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.5);\\n    opacity: 0.5;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(50px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SignupComponent", "constructor", "user", "name", "email", "password", "terms", "onSubmit", "console", "log", "alert", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_22_listener", "SignupComponent_Template_input_ngModelChange_27_listener", "$event", "SignupComponent_Template_input_ngModelChange_31_listener", "SignupComponent_Template_input_ngModelChange_35_listener", "SignupComponent_Template_input_ngModelChange_37_listener", "ɵɵadvance", "ɵɵproperty", "_r0", "valid"], "sources": ["C:\\pfa\\src\\app\\signup\\signup.component.ts", "C:\\pfa\\src\\app\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.scss']\n})\nexport class SignupComponent {\n  user = {\n    name: '',\n    email: '',\n    password: '',\n    terms: false\n  };\n\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n}", "<div class=\"signup-container\">\n  <!-- Section gauche : Graphique et texte -->\n  <div class=\"left-section\">\n    <div class=\"graphic\">\n      <!-- Points décoratifs -->\n      <div class=\"dot\"></div>\n      <div class=\"dot\"></div>\n      <div class=\"dot\"></div>\n    </div>\n    <p class=\"slogan\">\n      Each iris is a unique story written by nature, waiting to be decoded by technology\n    </p>\n  </div>\n\n  <!-- Section droite : Formulaire -->\n  <div class=\"right-section\">\n    <div class=\"form-container\">\n      <div class=\"logo\">\n        <!-- Logo en forme de croix -->\n        <div class=\"custom-logo\"><span></span></div>\n        <h1>Sign Up</h1>\n        <p>Votre identité, redéfinie par la technologie. Créez votre compte.</p>\n      </div>\n\n      <!-- Bouton \"Continue with Google\" -->\n      <button class=\"google-btn\">\n        <img src=\"assets/google-icon.png\" alt=\"Google Icon\" /> Continue with Google\n      </button>\n\n      <div class=\"divider\">or Sign in with Email</div>\n\n      <!-- Formulaire -->\n      <form (ngSubmit)=\"onSubmit()\" #signupForm=\"ngForm\">\n        <div class=\"form-group\">\n          <label for=\"name\">Name</label>\n          <input\n            type=\"text\"\n            id=\"name\"\n            name=\"name\"\n            placeholder=\"Leslie Alexander\"\n            [(ngModel)]=\"user.name\"\n            required\n          />\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"email\">Email</label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            name=\"email\"\n            placeholder=\"<EMAIL>\"\n            [(ngModel)]=\"user.email\"\n            required\n            email\n          />\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"password\">Password</label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            name=\"password\"\n            placeholder=\"At least 8 characters\"\n            [(ngModel)]=\"user.password\"\n            required\n            minlength=\"8\"\n          />\n        </div>\n\n        <div class=\"checkbox-group\">\n          <input\n            type=\"checkbox\"\n            id=\"terms\"\n            name=\"terms\"\n            [(ngModel)]=\"user.terms\"\n            required\n          />\n          <label for=\"terms\">I agree with <a href=\"#\">Terms</a> and <a href=\"#\">Privacy</a></label>\n        </div>\n\n        <button type=\"submit\" class=\"signup-btn\" [disabled]=\"!signupForm.valid\">\n          Sign Up\n        </button>\n      </form>\n\n      <p class=\"login-link\">\n        Already have an account? <a routerLink=\"/login\">Log in</a>\n      </p>\n    </div>\n  </div>\n</div>"], "mappings": ";;;AAOA,OAAM,MAAOA,eAAe;EAL5BC,YAAA;IAME,KAAAC,IAAI,GAAG;MACLC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;KACR;;EAEDC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,IAAI,CAACI,KAAK,EAAE;MACnBE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACP,IAAI,CAAC;MACzC;KACD,MAAM;MACLQ,KAAK,CAAC,+CAA+C,CAAC;;EAE1D;;;uBAfWV,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAW,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,aAA8B;UAKxBD,EAAA,CAAAE,SAAA,aAAuB;UAGzBF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,WAAkB;UAChBD,EAAA,CAAAI,MAAA,2FACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,aAA2B;UAIID,EAAA,CAAAE,SAAA,YAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,wFAAiE;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAI1EH,EAAA,CAAAC,cAAA,iBAA2B;UACzBD,EAAA,CAAAE,SAAA,eAAsD;UAACF,EAAA,CAAAI,MAAA,8BACzD;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAI,MAAA,6BAAqB;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAGhDH,EAAA,CAAAC,cAAA,oBAAmD;UAA7CD,EAAA,CAAAK,UAAA,sBAAAC,mDAAA;YAAA,OAAYP,GAAA,CAAAX,QAAA,EAAU;UAAA,EAAC;UAC3BY,EAAA,CAAAC,cAAA,eAAwB;UACJD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAC,cAAA,iBAOE;UAFAD,EAAA,CAAAK,UAAA,2BAAAE,yDAAAC,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAC,IAAA,GAAAwB,MAAA;UAAA,EAAuB;UALzBR,EAAA,CAAAG,YAAA,EAOE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAI,yDAAAD,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAE,KAAA,GAAAuB,MAAA;UAAA,EAAwB;UAL1BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAK,yDAAAF,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAG,QAAA,GAAAsB,MAAA;UAAA,EAA2B;UAL7BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAA4B;UAKxBD,EAAA,CAAAK,UAAA,2BAAAM,yDAAAH,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAI,KAAA,GAAAqB,MAAA;UAAA,EAAwB;UAJ1BR,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAAmB;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAI,MAAA,aAAI;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGnFH,EAAA,CAAAC,cAAA,kBAAwE;UACtED,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,aAAsB;UACpBD,EAAA,CAAAI,MAAA,kCAAyB;UAAAJ,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;UAhDtDH,EAAA,CAAAY,SAAA,IAAuB;UAAvBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAC,IAAA,CAAuB;UAYvBgB,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAE,KAAA,CAAwB;UAaxBe,EAAA,CAAAY,SAAA,GAA2B;UAA3BZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAG,QAAA,CAA2B;UAW3Bc,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAI,KAAA,CAAwB;UAMaa,EAAA,CAAAY,SAAA,GAA8B;UAA9BZ,EAAA,CAAAa,UAAA,cAAAC,GAAA,CAAAC,KAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}