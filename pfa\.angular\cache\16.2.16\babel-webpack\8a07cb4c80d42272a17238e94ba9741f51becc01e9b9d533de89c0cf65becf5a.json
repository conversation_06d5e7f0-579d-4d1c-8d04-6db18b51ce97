{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjetPfa/pfa/pfa/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction NavbarComponent_ng_container_13_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 14);\n    i0.ɵɵtext(1, \"Dashboard\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavbarComponent_ng_container_13_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵtext(1, \"Admin\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavbarComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 11);\n    i0.ɵɵtext(2, \"Test Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NavbarComponent_ng_container_13_a_3_Template, 2, 0, \"a\", 12);\n    i0.ɵɵtemplate(4, NavbarComponent_ng_container_13_a_4_Template, 2, 0, \"a\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isAdmin);\n  }\n}\nfunction NavbarComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 16);\n    i0.ɵɵtext(2, \"Connexion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 17);\n    i0.ɵɵtext(4, \"Inscription\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NavbarComponent_ng_container_16_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1, \"Admin\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavbarComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_16_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navigateToUserArea());\n    });\n    i0.ɵɵelementStart(3, \"span\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 21);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, NavbarComponent_ng_container_16_span_7_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_16_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.logout());\n    });\n    i0.ɵɵelementStart(9, \"span\", 24);\n    i0.ɵɵtext(10, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.displayName.charAt(0).toUpperCase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.displayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAdmin);\n  }\n}\nexport class NavbarComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.isAuthenticated = false;\n    this.userProfile = null;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // S'abonner à l'état d'authentification\n    this.subscriptions.push(this.authService.isAuthenticated$.subscribe(isAuth => {\n      this.isAuthenticated = isAuth;\n    }));\n    // S'abonner au profil utilisateur\n    this.subscriptions.push(this.authService.userProfile$.subscribe(profile => {\n      this.userProfile = profile;\n    }));\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  /**\n   * Déconnexion\n   */\n  logout() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.authService.signOut();\n    })();\n  }\n  /**\n   * Naviguer vers le dashboard ou le test selon le rôle\n   */\n  navigateToUserArea() {\n    if (this.userProfile?.role === 'admin') {\n      this.router.navigate(['/dashboard']);\n    } else {\n      this.router.navigate(['/personality-test']);\n    }\n  }\n  /**\n   * Vérifier si l'utilisateur est admin\n   */\n  get isAdmin() {\n    return this.userProfile?.role === 'admin';\n  }\n  /**\n   * Obtenir le nom d'affichage de l'utilisateur\n   */\n  get displayName() {\n    return this.userProfile?.displayName || 'Utilisateur';\n  }\n  static {\n    this.ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"app-navbar\"]],\n      decls: 17,\n      vars: 3,\n      consts: [[1, \"navbar\"], [1, \"navbar-container\"], [1, \"navbar-brand\"], [\"routerLink\", \"/accueil\", 1, \"brand-link\"], [1, \"brand-icon\"], [1, \"brand-text\"], [1, \"navbar-nav\"], [\"routerLink\", \"/accueil\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/iris-diversity\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [4, \"ngIf\"], [1, \"navbar-actions\"], [\"routerLink\", \"/personality-test\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\", \"class\", \"nav-link\", 4, \"ngIf\"], [\"routerLink\", \"/admin\", \"routerLinkActive\", \"active\", \"class\", \"nav-link\", 4, \"ngIf\"], [\"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/admin\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/login\", 1, \"btn\", \"btn-outline\"], [\"routerLink\", \"/signup\", 1, \"btn\", \"btn-primary\"], [1, \"user-menu\"], [1, \"user-button\", 3, \"click\"], [1, \"user-avatar\"], [1, \"user-name\"], [\"class\", \"user-role\", 4, \"ngIf\"], [\"title\", \"D\\u00E9connexion\", 1, \"logout-button\", 3, \"click\"], [1, \"logout-icon\"], [1, \"user-role\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"\\uD83D\\uDC41\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"IrisLock\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"a\", 7);\n          i0.ɵɵtext(10, \"Accueil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"a\", 8);\n          i0.ɵɵtext(12, \"Diversit\\u00E9 Iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, NavbarComponent_ng_container_13_Template, 5, 2, \"ng-container\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 10);\n          i0.ɵɵtemplate(15, NavbarComponent_ng_container_15_Template, 5, 0, \"ng-container\", 9);\n          i0.ɵɵtemplate(16, NavbarComponent_ng_container_16_Template, 11, 3, \"ng-container\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterLink, i2.RouterLinkActive],\n      styles: [\".navbar[_ngcontent-%COMP%] {\\n  background: var(--background-primary);\\n  border-bottom: 2px solid var(--border-color);\\n  box-shadow: var(--shadow-sm);\\n  position: sticky;\\n  top: 0;\\n  z-index: 1000;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 1rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 70px;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  text-decoration: none;\\n  color: var(--text-primary);\\n  font-weight: 700;\\n  font-size: 1.5rem;\\n  font-family: \\\"Playfair Display\\\", serif;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%]   .brand-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 2rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: var(--text-secondary);\\n  font-weight: 500;\\n  padding: 0.5rem 1rem;\\n  border-radius: var(--radius-md);\\n  transition: all 0.3s ease;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: var(--text-primary);\\n  background: var(--background-accent);\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  background: var(--background-accent);\\n  font-weight: 600;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1.5rem;\\n  border-radius: var(--radius-md);\\n  text-decoration: none;\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n  border: 2px solid transparent;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn.btn-outline[_ngcontent-%COMP%] {\\n  color: var(--text-primary);\\n  border-color: var(--border-color);\\n  background: transparent;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn.btn-outline[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  color: var(--primary-color);\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #1e293b, #334155);\\n  color: white;\\n  border-color: #475569;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #334155, #475569);\\n  transform: translateY(-1px);\\n  box-shadow: var(--shadow-md);\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.5rem 1rem;\\n  background: var(--background-accent);\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--radius-md);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  box-shadow: var(--shadow-sm);\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #1e293b, #334155);\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  font-size: 0.9rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  color: var(--text-primary);\\n  font-weight: 600;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .user-role[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n  padding: 0.125rem 0.5rem;\\n  border-radius: var(--radius-sm);\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  background: transparent;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--radius-md);\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]:hover {\\n  border-color: #dc2626;\\n  background: #fef2f2;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]:hover   .logout-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .logout-button[_ngcontent-%COMP%]   .logout-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  transition: transform 0.3s ease;\\n}\\n@media (max-width: 768px) {\\n  .navbar[_ngcontent-%COMP%]   .navbar-container[_ngcontent-%COMP%] {\\n    padding: 0 0.5rem;\\n    height: 60px;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    padding: 0.4rem 1rem;\\n    font-size: 0.9rem;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%] {\\n    padding: 0.4rem 0.8rem;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵtemplate", "NavbarComponent_ng_container_13_a_3_Template", "NavbarComponent_ng_container_13_a_4_Template", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "isAdmin", "ɵɵlistener", "NavbarComponent_ng_container_16_Template_button_click_2_listener", "ɵɵrestoreView", "_r7", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "navigateToUserArea", "NavbarComponent_ng_container_16_span_7_Template", "NavbarComponent_ng_container_16_Template_button_click_8_listener", "ctx_r8", "logout", "ɵɵtextInterpolate", "ctx_r2", "displayName", "char<PERSON>t", "toUpperCase", "NavbarComponent", "constructor", "authService", "router", "isAuthenticated", "userProfile", "subscriptions", "ngOnInit", "push", "isAuthenticated$", "subscribe", "isAuth", "userProfile$", "profile", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "_this", "_asyncToGenerator", "signOut", "role", "navigate", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "NavbarComponent_Template", "rf", "ctx", "NavbarComponent_ng_container_13_Template", "NavbarComponent_ng_container_15_Template", "NavbarComponent_ng_container_16_Template"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\components\\navbar\\navbar.component.ts", "D:\\ProjetPfa\\pfa\\pfa\\src\\app\\components\\navbar\\navbar.component.html"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService, UserProfile } from '../../services/auth.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-navbar',\n  templateUrl: './navbar.component.html',\n  styleUrls: ['./navbar.component.scss']\n})\nexport class NavbarComponent implements OnInit, OnDestroy {\n  isAuthenticated = false;\n  userProfile: UserProfile | null = null;\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // S'abonner à l'état d'authentification\n    this.subscriptions.push(\n      this.authService.isAuthenticated$.subscribe(isAuth => {\n        this.isAuthenticated = isAuth;\n      })\n    );\n\n    // S'abonner au profil utilisateur\n    this.subscriptions.push(\n      this.authService.userProfile$.subscribe(profile => {\n        this.userProfile = profile;\n      })\n    );\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Déconnexion\n   */\n  async logout(): Promise<void> {\n    await this.authService.signOut();\n  }\n\n  /**\n   * Naviguer vers le dashboard ou le test selon le rôle\n   */\n  navigateToUserArea(): void {\n    if (this.userProfile?.role === 'admin') {\n      this.router.navigate(['/dashboard']);\n    } else {\n      this.router.navigate(['/personality-test']);\n    }\n  }\n\n  /**\n   * Vérifier si l'utilisateur est admin\n   */\n  get isAdmin(): boolean {\n    return this.userProfile?.role === 'admin';\n  }\n\n  /**\n   * Obtenir le nom d'affichage de l'utilisateur\n   */\n  get displayName(): string {\n    return this.userProfile?.displayName || 'Utilisateur';\n  }\n}\n", "<nav class=\"navbar\">\n  <div class=\"navbar-container\">\n    <!-- Logo et titre -->\n    <div class=\"navbar-brand\">\n      <a routerLink=\"/accueil\" class=\"brand-link\">\n        <span class=\"brand-icon\">👁️</span>\n        <span class=\"brand-text\">IrisLock</span>\n      </a>\n    </div>\n\n    <!-- Navigation principale -->\n    <div class=\"navbar-nav\">\n      <a routerLink=\"/accueil\" routerLinkActive=\"active\" class=\"nav-link\">Accueil</a>\n      <a routerLink=\"/iris-diversity\" routerLinkActive=\"active\" class=\"nav-link\">Diversité Iris</a>\n      \n      <!-- Liens pour utilisateurs connectés -->\n      <ng-container *ngIf=\"isAuthenticated\">\n        <a routerLink=\"/personality-test\" routerLinkActive=\"active\" class=\"nav-link\">Test Personnalité</a>\n        <a *ngIf=\"isAdmin\" routerLink=\"/dashboard\" routerLinkActive=\"active\" class=\"nav-link\">Dashboard</a>\n        <a *ngIf=\"isAdmin\" routerLink=\"/admin\" routerLinkActive=\"active\" class=\"nav-link\">Admin</a>\n      </ng-container>\n    </div>\n\n    <!-- Actions utilisateur -->\n    <div class=\"navbar-actions\">\n      <!-- Utilisateur non connecté -->\n      <ng-container *ngIf=\"!isAuthenticated\">\n        <a routerLink=\"/login\" class=\"btn btn-outline\">Connexion</a>\n        <a routerLink=\"/signup\" class=\"btn btn-primary\">Inscription</a>\n      </ng-container>\n\n      <!-- Utilisateur connecté -->\n      <ng-container *ngIf=\"isAuthenticated\">\n        <div class=\"user-menu\">\n          <button class=\"user-button\" (click)=\"navigateToUserArea()\">\n            <span class=\"user-avatar\">{{ displayName.charAt(0).toUpperCase() }}</span>\n            <span class=\"user-name\">{{ displayName }}</span>\n            <span *ngIf=\"isAdmin\" class=\"user-role\">Admin</span>\n          </button>\n          <button class=\"logout-button\" (click)=\"logout()\" title=\"Déconnexion\">\n            <span class=\"logout-icon\">🚪</span>\n          </button>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</nav>\n"], "mappings": ";;;;;;;ICkBQA,EAAA,CAAAC,cAAA,YAAsF;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACnGH,EAAA,CAAAC,cAAA,YAAkF;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAH7FH,EAAA,CAAAI,uBAAA,GAAsC;IACpCJ,EAAA,CAAAC,cAAA,YAA6E;IAAAD,EAAA,CAAAE,MAAA,6BAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClGH,EAAA,CAAAK,UAAA,IAAAC,4CAAA,gBAAmG;IACnGN,EAAA,CAAAK,UAAA,IAAAE,4CAAA,gBAA2F;IAC7FP,EAAA,CAAAQ,qBAAA,EAAe;;;;IAFTR,EAAA,CAAAS,SAAA,GAAa;IAAbT,EAAA,CAAAU,UAAA,SAAAC,MAAA,CAAAC,OAAA,CAAa;IACbZ,EAAA,CAAAS,SAAA,GAAa;IAAbT,EAAA,CAAAU,UAAA,SAAAC,MAAA,CAAAC,OAAA,CAAa;;;;;IAOnBZ,EAAA,CAAAI,uBAAA,GAAuC;IACrCJ,EAAA,CAAAC,cAAA,YAA+C;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAAC,cAAA,YAAgD;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjEH,EAAA,CAAAQ,qBAAA,EAAe;;;;;IAQTR,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAL1DH,EAAA,CAAAI,uBAAA,GAAsC;IACpCJ,EAAA,CAAAC,cAAA,cAAuB;IACOD,EAAA,CAAAa,UAAA,mBAAAC,iEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IACxDpB,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1EH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAK,UAAA,IAAAgB,+CAAA,mBAAoD;IACtDrB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAqE;IAAvCD,EAAA,CAAAa,UAAA,mBAAAS,iEAAA;MAAAtB,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAvB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAI,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAC9CxB,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGzCH,EAAA,CAAAQ,qBAAA,EAAe;;;;IARiBR,EAAA,CAAAS,SAAA,GAAyC;IAAzCT,EAAA,CAAAyB,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAAC,MAAA,IAAAC,WAAA,GAAyC;IAC3C7B,EAAA,CAAAS,SAAA,GAAiB;IAAjBT,EAAA,CAAAyB,iBAAA,CAAAC,MAAA,CAAAC,WAAA,CAAiB;IAClC3B,EAAA,CAAAS,SAAA,GAAa;IAAbT,EAAA,CAAAU,UAAA,SAAAgB,MAAA,CAAAd,OAAA,CAAa;;;AD3BhC,OAAM,MAAOkB,eAAe;EAK1BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,WAAW,GAAuB,IAAI;IAC9B,KAAAC,aAAa,GAAmB,EAAE;EAKvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,aAAa,CAACE,IAAI,CACrB,IAAI,CAACN,WAAW,CAACO,gBAAgB,CAACC,SAAS,CAACC,MAAM,IAAG;MACnD,IAAI,CAACP,eAAe,GAAGO,MAAM;IAC/B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACL,aAAa,CAACE,IAAI,CACrB,IAAI,CAACN,WAAW,CAACU,YAAY,CAACF,SAAS,CAACG,OAAO,IAAG;MAChD,IAAI,CAACR,WAAW,GAAGQ,OAAO;IAC5B,CAAC,CAAC,CACH;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,aAAa,CAACS,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEA;;;EAGMvB,MAAMA,CAAA;IAAA,IAAAwB,KAAA;IAAA,OAAAC,iBAAA;MACV,MAAMD,KAAI,CAAChB,WAAW,CAACkB,OAAO,EAAE;IAAC;EACnC;EAEA;;;EAGA9B,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACe,WAAW,EAAEgB,IAAI,KAAK,OAAO,EAAE;MACtC,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;KACrC,MAAM;MACL,IAAI,CAACnB,MAAM,CAACmB,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;;EAE/C;EAEA;;;EAGA,IAAIxC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACuB,WAAW,EAAEgB,IAAI,KAAK,OAAO;EAC3C;EAEA;;;EAGA,IAAIxB,WAAWA,CAAA;IACb,OAAO,IAAI,CAACQ,WAAW,EAAER,WAAW,IAAI,aAAa;EACvD;;;uBA5DWG,eAAe,EAAA9B,EAAA,CAAAqD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvD,EAAA,CAAAqD,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAf3B,eAAe;MAAA4B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCV5BhE,EAAA,CAAAC,cAAA,aAAoB;UAKaD,EAAA,CAAAE,MAAA,yBAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnCH,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK5CH,EAAA,CAAAC,cAAA,aAAwB;UAC8CD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC/EH,EAAA,CAAAC,cAAA,YAA2E;UAAAD,EAAA,CAAAE,MAAA,2BAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG7FH,EAAA,CAAAK,UAAA,KAAA6D,wCAAA,0BAIe;UACjBlE,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA4B;UAE1BD,EAAA,CAAAK,UAAA,KAAA8D,wCAAA,0BAGe;UAGfnE,EAAA,CAAAK,UAAA,KAAA+D,wCAAA,2BAWe;UACjBpE,EAAA,CAAAG,YAAA,EAAM;;;UA5BWH,EAAA,CAAAS,SAAA,IAAqB;UAArBT,EAAA,CAAAU,UAAA,SAAAuD,GAAA,CAAA/B,eAAA,CAAqB;UAUrBlC,EAAA,CAAAS,SAAA,GAAsB;UAAtBT,EAAA,CAAAU,UAAA,UAAAuD,GAAA,CAAA/B,eAAA,CAAsB;UAMtBlC,EAAA,CAAAS,SAAA,GAAqB;UAArBT,EAAA,CAAAU,UAAA,SAAAuD,GAAA,CAAA/B,eAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}