{"ast": null, "code": "import _asyncToGenerator from \"E:/aymen/pfa/pfa/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nfunction NavbarComponent_ng_container_13_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 14);\n    i0.ɵɵtext(1, \"Dashboard\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavbarComponent_ng_container_13_a_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵtext(1, \"Admin\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavbarComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 11);\n    i0.ɵɵtext(2, \"Test Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NavbarComponent_ng_container_13_a_3_Template, 2, 0, \"a\", 12);\n    i0.ɵɵtemplate(4, NavbarComponent_ng_container_13_a_4_Template, 2, 0, \"a\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isAdmin);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isAdmin);\n  }\n}\nfunction NavbarComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 16);\n    i0.ɵɵtext(2, \"Connexion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 17);\n    i0.ɵɵtext(4, \"Inscription\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NavbarComponent_ng_container_16_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1, \"Admin\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NavbarComponent_ng_container_16_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_16_div_10_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.navigateToUserArea());\n    });\n    i0.ɵɵelementStart(2, \"span\", 28);\n    i0.ɵɵtext(3, \"\\uD83D\\uDC64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Mon Profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"div\", 29);\n    i0.ɵɵelementStart(7, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_16_div_10_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.logout());\n    });\n    i0.ɵɵelementStart(8, \"span\", 28);\n    i0.ɵɵtext(9, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction NavbarComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 18)(2, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_16_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.toggleMenu());\n    });\n    i0.ɵɵelementStart(3, \"span\", 20);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 21);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, NavbarComponent_ng_container_16_span_7_Template, 2, 0, \"span\", 22);\n    i0.ɵɵelementStart(8, \"span\", 23);\n    i0.ɵɵtext(9, \"\\u25BC\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, NavbarComponent_ng_container_16_div_10_Template, 12, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"open\", ctx_r2.isMenuOpen);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.displayName.charAt(0).toUpperCase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.displayName);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isAdmin);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isMenuOpen);\n  }\n}\nexport class NavbarComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.isAuthenticated = false;\n    this.userProfile = null;\n    this.isMenuOpen = false;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // S'abonner à l'état d'authentification\n    this.subscriptions.push(this.authService.isAuthenticated$.subscribe(isAuth => {\n      this.isAuthenticated = isAuth;\n    }));\n    // S'abonner au profil utilisateur\n    this.subscriptions.push(this.authService.userProfile$.subscribe(profile => {\n      this.userProfile = profile;\n    }));\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  /**\n   * Fermer le menu quand on clique ailleurs\n   */\n  onDocumentClick(event) {\n    const target = event.target;\n    if (!target.closest('.user-menu')) {\n      this.closeMenu();\n    }\n  }\n  /**\n   * Basculer l'ouverture/fermeture du menu\n   */\n  toggleMenu() {\n    this.isMenuOpen = !this.isMenuOpen;\n  }\n  /**\n   * Fermer le menu\n   */\n  closeMenu() {\n    this.isMenuOpen = false;\n  }\n  /**\n   * Déconnexion\n   */\n  logout() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.closeMenu();\n      yield _this.authService.signOut();\n    })();\n  }\n  /**\n   * Naviguer vers le dashboard ou le test selon le rôle\n   */\n  navigateToUserArea() {\n    this.closeMenu();\n    if (this.userProfile?.role === 'admin') {\n      this.router.navigate(['/dashboard']);\n    } else {\n      this.router.navigate(['/personality-test']);\n    }\n  }\n  /**\n   * Vérifier si l'utilisateur est admin\n   */\n  get isAdmin() {\n    return this.userProfile?.role === 'admin';\n  }\n  /**\n   * Obtenir le nom d'affichage de l'utilisateur\n   */\n  get displayName() {\n    return this.userProfile?.displayName || 'Utilisateur';\n  }\n  static {\n    this.ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"app-navbar\"]],\n      hostBindings: function NavbarComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NavbarComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      decls: 17,\n      vars: 3,\n      consts: [[1, \"navbar\"], [1, \"navbar-container\"], [1, \"navbar-brand\"], [\"routerLink\", \"/accueil\", 1, \"brand-link\"], [1, \"brand-icon\"], [1, \"brand-text\"], [1, \"navbar-nav\"], [\"routerLink\", \"/accueil\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/iris-diversity\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [4, \"ngIf\"], [1, \"navbar-actions\"], [\"routerLink\", \"/personality-test\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\", \"class\", \"nav-link\", 4, \"ngIf\"], [\"routerLink\", \"/admin\", \"routerLinkActive\", \"active\", \"class\", \"nav-link\", 4, \"ngIf\"], [\"routerLink\", \"/dashboard\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/admin\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"routerLink\", \"/login\", 1, \"btn\", \"btn-outline\"], [\"routerLink\", \"/signup\", 1, \"btn\", \"btn-primary\"], [1, \"user-menu\"], [1, \"user-button\", 3, \"click\"], [1, \"user-avatar\"], [1, \"user-name\"], [\"class\", \"user-role\", 4, \"ngIf\"], [1, \"dropdown-arrow\"], [\"class\", \"dropdown-menu\", 4, \"ngIf\"], [1, \"user-role\"], [1, \"dropdown-menu\"], [1, \"dropdown-item\", 3, \"click\"], [1, \"item-icon\"], [1, \"dropdown-divider\"], [1, \"dropdown-item\", \"logout-item\", 3, \"click\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"a\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"\\uD83D\\uDC41\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"IrisLock\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"a\", 7);\n          i0.ɵɵtext(10, \"Accueil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"a\", 8);\n          i0.ɵɵtext(12, \"Diversit\\u00E9 Iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(13, NavbarComponent_ng_container_13_Template, 5, 2, \"ng-container\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 10);\n          i0.ɵɵtemplate(15, NavbarComponent_ng_container_15_Template, 5, 0, \"ng-container\", 9);\n          i0.ɵɵtemplate(16, NavbarComponent_ng_container_16_Template, 11, 6, \"ng-container\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterLink, i2.RouterLinkActive],\n      styles: [\".navbar[_ngcontent-%COMP%] {\\n  background: white;\\n  border-bottom: 1px solid #e5e7eb;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  position: sticky;\\n  top: 0;\\n  z-index: 1000;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 60px;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  text-decoration: none;\\n  color: #1f2937;\\n  font-weight: 700;\\n  font-size: 1.25rem;\\n  font-family: \\\"Playfair Display\\\", serif;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%]   .brand-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%]   .brand-text[_ngcontent-%COMP%] {\\n  color: #1f2937;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-brand[_ngcontent-%COMP%]   .brand-link[_ngcontent-%COMP%]:hover {\\n  color: #6366f1;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 2rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: #6b7280;\\n  font-weight: 500;\\n  padding: 0.5rem 1rem;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n  font-size: 0.95rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #1f2937;\\n  background: #f3f4f6;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #6366f1;\\n  background: #eef2ff;\\n  font-weight: 600;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1.25rem;\\n  border-radius: 6px;\\n  text-decoration: none;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n  border: 1px solid transparent;\\n  font-size: 0.9rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn.btn-outline[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  border-color: #d1d5db;\\n  background: transparent;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn.btn-outline[_ngcontent-%COMP%]:hover {\\n  border-color: #6366f1;\\n  color: #6366f1;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: #6366f1;\\n  color: white;\\n  border-color: #6366f1;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #5856eb;\\n  border-color: #5856eb;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.5rem 1rem;\\n  background: white;\\n  border: 1px solid #d1d5db;\\n  border-radius: 50px;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  font-size: 0.9rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]:hover {\\n  border-color: #6366f1;\\n  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .user-avatar[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n  border-radius: 50%;\\n  background: #6366f1;\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n  color: #1f2937;\\n  font-weight: 500;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .user-role[_ngcontent-%COMP%] {\\n  background: #10b981;\\n  color: white;\\n  padding: 0.125rem 0.5rem;\\n  border-radius: 12px;\\n  font-size: 0.7rem;\\n  font-weight: 500;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .dropdown-arrow[_ngcontent-%COMP%] {\\n  color: #6b7280;\\n  font-size: 0.7rem;\\n  transition: transform 0.2s ease;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu.open[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .dropdown-arrow[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 100%;\\n  right: 0;\\n  margin-top: 0.5rem;\\n  background: white;\\n  border: 1px solid #e5e7eb;\\n  border-radius: 8px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n  min-width: 180px;\\n  z-index: 1000;\\n  overflow: hidden;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  width: 100%;\\n  padding: 0.75rem 1rem;\\n  background: none;\\n  border: none;\\n  text-align: left;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n  font-size: 0.9rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]:hover {\\n  background: #f3f4f6;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%] {\\n  color: #dc2626;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item.logout-item[_ngcontent-%COMP%]:hover {\\n  background: #fef2f2;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-item[_ngcontent-%COMP%]   .item-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .dropdown-menu[_ngcontent-%COMP%]   .dropdown-divider[_ngcontent-%COMP%] {\\n  height: 1px;\\n  background: #e5e7eb;\\n  margin: 0.25rem 0;\\n}\\n@media (max-width: 768px) {\\n  .navbar[_ngcontent-%COMP%]   .navbar-container[_ngcontent-%COMP%] {\\n    padding: 0 0.5rem;\\n    height: 60px;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-nav[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%] {\\n    gap: 0.5rem;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    padding: 0.4rem 1rem;\\n    font-size: 0.9rem;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%] {\\n    padding: 0.4rem 0.8rem;\\n  }\\n  .navbar[_ngcontent-%COMP%]   .navbar-actions[_ngcontent-%COMP%]   .user-menu[_ngcontent-%COMP%]   .user-button[_ngcontent-%COMP%]   .user-name[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵtemplate", "NavbarComponent_ng_container_13_a_3_Template", "NavbarComponent_ng_container_13_a_4_Template", "ɵɵelementContainerEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "isAdmin", "ɵɵlistener", "NavbarComponent_ng_container_16_div_10_Template_button_click_1_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "navigateToUserArea", "ɵɵelement", "NavbarComponent_ng_container_16_div_10_Template_button_click_7_listener", "ctx_r9", "logout", "NavbarComponent_ng_container_16_Template_button_click_2_listener", "_r11", "ctx_r10", "toggleMenu", "NavbarComponent_ng_container_16_span_7_Template", "NavbarComponent_ng_container_16_div_10_Template", "ɵɵclassProp", "ctx_r2", "isMenuOpen", "ɵɵtextInterpolate", "displayName", "char<PERSON>t", "toUpperCase", "NavbarComponent", "constructor", "authService", "router", "isAuthenticated", "userProfile", "subscriptions", "ngOnInit", "push", "isAuthenticated$", "subscribe", "isAuth", "userProfile$", "profile", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "onDocumentClick", "event", "target", "closest", "closeMenu", "_this", "_asyncToGenerator", "signOut", "role", "navigate", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "hostBindings", "NavbarComponent_HostBindings", "rf", "ctx", "$event", "ɵɵresolveDocument", "NavbarComponent_ng_container_13_Template", "NavbarComponent_ng_container_15_Template", "NavbarComponent_ng_container_16_Template"], "sources": ["E:\\aymen\\pfa\\pfa\\src\\app\\components\\navbar\\navbar.component.ts", "E:\\aymen\\pfa\\pfa\\src\\app\\components\\navbar\\navbar.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy, HostListener } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService, UserProfile } from '../../services/auth.service';\nimport { Subscription } from 'rxjs';\n\n@Component({\n  selector: 'app-navbar',\n  templateUrl: './navbar.component.html',\n  styleUrls: ['./navbar.component.scss']\n})\nexport class NavbarComponent implements OnInit, OnDestroy {\n  isAuthenticated = false;\n  userProfile: UserProfile | null = null;\n  isMenuOpen = false;\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // S'abonner à l'état d'authentification\n    this.subscriptions.push(\n      this.authService.isAuthenticated$.subscribe(isAuth => {\n        this.isAuthenticated = isAuth;\n      })\n    );\n\n    // S'abonner au profil utilisateur\n    this.subscriptions.push(\n      this.authService.userProfile$.subscribe(profile => {\n        this.userProfile = profile;\n      })\n    );\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  /**\n   * Fermer le menu quand on clique ailleurs\n   */\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: Event): void {\n    const target = event.target as HTMLElement;\n    if (!target.closest('.user-menu')) {\n      this.closeMenu();\n    }\n  }\n\n  /**\n   * Basculer l'ouverture/fermeture du menu\n   */\n  toggleMenu(): void {\n    this.isMenuOpen = !this.isMenuOpen;\n  }\n\n  /**\n   * Fermer le menu\n   */\n  closeMenu(): void {\n    this.isMenuOpen = false;\n  }\n\n  /**\n   * Déconnexion\n   */\n  async logout(): Promise<void> {\n    this.closeMenu();\n    await this.authService.signOut();\n  }\n\n  /**\n   * Naviguer vers le dashboard ou le test selon le rôle\n   */\n  navigateToUserArea(): void {\n    this.closeMenu();\n    if (this.userProfile?.role === 'admin') {\n      this.router.navigate(['/dashboard']);\n    } else {\n      this.router.navigate(['/personality-test']);\n    }\n  }\n\n  /**\n   * Vérifier si l'utilisateur est admin\n   */\n  get isAdmin(): boolean {\n    return this.userProfile?.role === 'admin';\n  }\n\n  /**\n   * Obtenir le nom d'affichage de l'utilisateur\n   */\n  get displayName(): string {\n    return this.userProfile?.displayName || 'Utilisateur';\n  }\n}\n", "<nav class=\"navbar\">\n  <div class=\"navbar-container\">\n    <!-- Logo et titre -->\n    <div class=\"navbar-brand\">\n      <a routerLink=\"/accueil\" class=\"brand-link\">\n        <span class=\"brand-icon\">👁️</span>\n        <span class=\"brand-text\">IrisLock</span>\n      </a>\n    </div>\n\n    <!-- Navigation principale -->\n    <div class=\"navbar-nav\">\n      <a routerLink=\"/accueil\" routerLinkActive=\"active\" class=\"nav-link\">Accueil</a>\n      <a routerLink=\"/iris-diversity\" routerLinkActive=\"active\" class=\"nav-link\">Diversité Iris</a>\n\n      <!-- Liens pour utilisateurs connectés -->\n      <ng-container *ngIf=\"isAuthenticated\">\n        <a routerLink=\"/personality-test\" routerLinkActive=\"active\" class=\"nav-link\">Test Personnalité</a>\n        <a *ngIf=\"isAdmin\" routerLink=\"/dashboard\" routerLinkActive=\"active\" class=\"nav-link\">Dashboard</a>\n        <a *ngIf=\"isAdmin\" routerLink=\"/admin\" routerLinkActive=\"active\" class=\"nav-link\">Admin</a>\n      </ng-container>\n    </div>\n\n    <!-- Actions utilisateur -->\n    <div class=\"navbar-actions\">\n      <!-- Utilisateur non connecté -->\n      <ng-container *ngIf=\"!isAuthenticated\">\n        <a routerLink=\"/login\" class=\"btn btn-outline\">Connexion</a>\n        <a routerLink=\"/signup\" class=\"btn btn-primary\">Inscription</a>\n      </ng-container>\n\n      <!-- Utilisateur connecté -->\n      <ng-container *ngIf=\"isAuthenticated\">\n        <div class=\"user-menu\" [class.open]=\"isMenuOpen\">\n          <button class=\"user-button\" (click)=\"toggleMenu()\">\n            <span class=\"user-avatar\">{{ displayName.charAt(0).toUpperCase() }}</span>\n            <span class=\"user-name\">{{ displayName }}</span>\n            <span *ngIf=\"isAdmin\" class=\"user-role\">Admin</span>\n            <span class=\"dropdown-arrow\">▼</span>\n          </button>\n\n          <!-- Menu déroulant -->\n          <div class=\"dropdown-menu\" *ngIf=\"isMenuOpen\">\n            <button class=\"dropdown-item\" (click)=\"navigateToUserArea()\">\n              <span class=\"item-icon\">👤</span>\n              <span>Mon Profil</span>\n            </button>\n            <div class=\"dropdown-divider\"></div>\n            <button class=\"dropdown-item logout-item\" (click)=\"logout()\">\n              <span class=\"item-icon\">🚪</span>\n              <span>Déconnexion</span>\n            </button>\n          </div>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</nav>\n"], "mappings": ";;;;;;;ICkBQA,EAAA,CAAAC,cAAA,YAAsF;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IACnGH,EAAA,CAAAC,cAAA,YAAkF;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAH7FH,EAAA,CAAAI,uBAAA,GAAsC;IACpCJ,EAAA,CAAAC,cAAA,YAA6E;IAAAD,EAAA,CAAAE,MAAA,6BAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClGH,EAAA,CAAAK,UAAA,IAAAC,4CAAA,gBAAmG;IACnGN,EAAA,CAAAK,UAAA,IAAAE,4CAAA,gBAA2F;IAC7FP,EAAA,CAAAQ,qBAAA,EAAe;;;;IAFTR,EAAA,CAAAS,SAAA,GAAa;IAAbT,EAAA,CAAAU,UAAA,SAAAC,MAAA,CAAAC,OAAA,CAAa;IACbZ,EAAA,CAAAS,SAAA,GAAa;IAAbT,EAAA,CAAAU,UAAA,SAAAC,MAAA,CAAAC,OAAA,CAAa;;;;;IAOnBZ,EAAA,CAAAI,uBAAA,GAAuC;IACrCJ,EAAA,CAAAC,cAAA,YAA+C;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAAC,cAAA,YAAgD;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjEH,EAAA,CAAAQ,qBAAA,EAAe;;;;;IAQTR,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAKtDH,EAAA,CAAAC,cAAA,cAA8C;IACdD,EAAA,CAAAa,UAAA,mBAAAC,wEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAF,MAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IAC1DpB,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEzBH,EAAA,CAAAqB,SAAA,cAAoC;IACpCrB,EAAA,CAAAC,cAAA,iBAA6D;IAAnBD,EAAA,CAAAa,UAAA,mBAAAS,wEAAA;MAAAtB,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAvB,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAI,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAC1DxB,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACjCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,wBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAlBhCH,EAAA,CAAAI,uBAAA,GAAsC;IACpCJ,EAAA,CAAAC,cAAA,cAAiD;IACnBD,EAAA,CAAAa,UAAA,mBAAAY,iEAAA;MAAAzB,EAAA,CAAAe,aAAA,CAAAW,IAAA;MAAA,MAAAC,OAAA,GAAA3B,EAAA,CAAAkB,aAAA;MAAA,OAASlB,EAAA,CAAAmB,WAAA,CAAAQ,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAChD5B,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1EH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAK,UAAA,IAAAwB,+CAAA,mBAAoD;IACpD7B,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIvCH,EAAA,CAAAK,UAAA,KAAAyB,+CAAA,mBAUM;IACR9B,EAAA,CAAAG,YAAA,EAAM;IACRH,EAAA,CAAAQ,qBAAA,EAAe;;;;IArBUR,EAAA,CAAAS,SAAA,GAAyB;IAAzBT,EAAA,CAAA+B,WAAA,SAAAC,MAAA,CAAAC,UAAA,CAAyB;IAElBjC,EAAA,CAAAS,SAAA,GAAyC;IAAzCT,EAAA,CAAAkC,iBAAA,CAAAF,MAAA,CAAAG,WAAA,CAAAC,MAAA,IAAAC,WAAA,GAAyC;IAC3CrC,EAAA,CAAAS,SAAA,GAAiB;IAAjBT,EAAA,CAAAkC,iBAAA,CAAAF,MAAA,CAAAG,WAAA,CAAiB;IAClCnC,EAAA,CAAAS,SAAA,GAAa;IAAbT,EAAA,CAAAU,UAAA,SAAAsB,MAAA,CAAApB,OAAA,CAAa;IAKMZ,EAAA,CAAAS,SAAA,GAAgB;IAAhBT,EAAA,CAAAU,UAAA,SAAAsB,MAAA,CAAAC,UAAA,CAAgB;;;ADhCtD,OAAM,MAAOK,eAAe;EAM1BC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAV,UAAU,GAAG,KAAK;IACV,KAAAW,aAAa,GAAmB,EAAE;EAKvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,aAAa,CAACE,IAAI,CACrB,IAAI,CAACN,WAAW,CAACO,gBAAgB,CAACC,SAAS,CAACC,MAAM,IAAG;MACnD,IAAI,CAACP,eAAe,GAAGO,MAAM;IAC/B,CAAC,CAAC,CACH;IAED;IACA,IAAI,CAACL,aAAa,CAACE,IAAI,CACrB,IAAI,CAACN,WAAW,CAACU,YAAY,CAACF,SAAS,CAACG,OAAO,IAAG;MAChD,IAAI,CAACR,WAAW,GAAGQ,OAAO;IAC5B,CAAC,CAAC,CACH;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,aAAa,CAACS,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEA;;;EAIAC,eAAeA,CAACC,KAAY;IAC1B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAqB;IAC1C,IAAI,CAACA,MAAM,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;MACjC,IAAI,CAACC,SAAS,EAAE;;EAEpB;EAEA;;;EAGAhC,UAAUA,CAAA;IACR,IAAI,CAACK,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;EACpC;EAEA;;;EAGA2B,SAASA,CAAA;IACP,IAAI,CAAC3B,UAAU,GAAG,KAAK;EACzB;EAEA;;;EAGMT,MAAMA,CAAA;IAAA,IAAAqC,KAAA;IAAA,OAAAC,iBAAA;MACVD,KAAI,CAACD,SAAS,EAAE;MAChB,MAAMC,KAAI,CAACrB,WAAW,CAACuB,OAAO,EAAE;IAAC;EACnC;EAEA;;;EAGA3C,kBAAkBA,CAAA;IAChB,IAAI,CAACwC,SAAS,EAAE;IAChB,IAAI,IAAI,CAACjB,WAAW,EAAEqB,IAAI,KAAK,OAAO,EAAE;MACtC,IAAI,CAACvB,MAAM,CAACwB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;KACrC,MAAM;MACL,IAAI,CAACxB,MAAM,CAACwB,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;;EAE/C;EAEA;;;EAGA,IAAIrD,OAAOA,CAAA;IACT,OAAO,IAAI,CAAC+B,WAAW,EAAEqB,IAAI,KAAK,OAAO;EAC3C;EAEA;;;EAGA,IAAI7B,WAAWA,CAAA;IACb,OAAO,IAAI,CAACQ,WAAW,EAAER,WAAW,IAAI,aAAa;EACvD;;;uBAxFWG,eAAe,EAAAtC,EAAA,CAAAkE,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApE,EAAA,CAAAkE,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfhC,eAAe;MAAAiC,SAAA;MAAAC,YAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;mBAAfC,GAAA,CAAAnB,eAAA,CAAAoB,MAAA,CAAuB;UAAA,UAAA5E,EAAA,CAAA6E,iBAAA;;;;;;;;UCVpC7E,EAAA,CAAAC,cAAA,aAAoB;UAKaD,EAAA,CAAAE,MAAA,yBAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACnCH,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAK5CH,EAAA,CAAAC,cAAA,aAAwB;UAC8CD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC/EH,EAAA,CAAAC,cAAA,YAA2E;UAAAD,EAAA,CAAAE,MAAA,2BAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG7FH,EAAA,CAAAK,UAAA,KAAAyE,wCAAA,0BAIe;UACjB9E,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA4B;UAE1BD,EAAA,CAAAK,UAAA,KAAA0E,wCAAA,0BAGe;UAGf/E,EAAA,CAAAK,UAAA,KAAA2E,wCAAA,2BAsBe;UACjBhF,EAAA,CAAAG,YAAA,EAAM;;;UAvCWH,EAAA,CAAAS,SAAA,IAAqB;UAArBT,EAAA,CAAAU,UAAA,SAAAiE,GAAA,CAAAjC,eAAA,CAAqB;UAUrB1C,EAAA,CAAAS,SAAA,GAAsB;UAAtBT,EAAA,CAAAU,UAAA,UAAAiE,GAAA,CAAAjC,eAAA,CAAsB;UAMtB1C,EAAA,CAAAS,SAAA,GAAqB;UAArBT,EAAA,CAAAU,UAAA,SAAAiE,GAAA,CAAAjC,eAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}