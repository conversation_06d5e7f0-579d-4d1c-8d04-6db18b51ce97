{"ast": null, "code": "import { exhaustMap } from './exhaustMap';\nimport { identity } from '../util/identity';\nexport function exhaustAll() {\n  return exhaustMap(identity);\n}", "map": {"version": 3, "names": ["exhaustMap", "identity", "exhaustAll"], "sources": ["E:/aymen/pfa/pfa/node_modules/rxjs/dist/esm/internal/operators/exhaustAll.js"], "sourcesContent": ["import { exhaustMap } from './exhaustMap';\nimport { identity } from '../util/identity';\nexport function exhaustAll() {\n    return exhaustMap(identity);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAO,SAASC,UAAUA,CAAA,EAAG;EACzB,OAAOF,UAAU,CAACC,QAAQ,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}