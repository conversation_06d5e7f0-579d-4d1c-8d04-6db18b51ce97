{"ast": null, "code": "export function identity(x) {\n  return x;\n}", "map": {"version": 3, "names": ["identity", "x"], "sources": ["D:/ProjetPfa/pfa/pfa/node_modules/rxjs/dist/esm/internal/util/identity.js"], "sourcesContent": ["export function identity(x) {\n    return x;\n}\n"], "mappings": "AAAA,OAAO,SAASA,QAAQA,CAACC,CAAC,EAAE;EACxB,OAAOA,CAAC;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}