{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nexport class AccueilComponent {\n  static {\n    this.ɵfac = function AccueilComponent_Factory(t) {\n      return new (t || AccueilComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccueilComponent,\n      selectors: [[\"app-accueil\"]],\n      decls: 61,\n      vars: 2,\n      consts: [[1, \"page-container\", \"accueil\"], [1, \"container\"], [1, \"navbar\"], [1, \"logo\"], [1, \"nav-links\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 3, \"routerLinkActiveOptions\"], [\"href\", \"#\"], [1, \"auth-buttons\"], [\"routerLink\", \"/login\", 1, \"login-button\"], [\"routerLink\", \"/signup\", 1, \"register-button\"], [1, \"content\"], [1, \"card\", \"main-card\"], [1, \"card-inner\"], [1, \"card-front\"], [1, \"text-content\"], [1, \"title\"], [1, \"divider\"], [1, \"description\"], [1, \"image-container\"], [\"src\", \"assets/iris.png\", \"alt\", \"Iris\", 1, \"main-image\"], [1, \"image-decoration\"], [1, \"action-container\"], [\"routerLink\", \"/suivantacc\", 1, \"btn\", \"start-btn\"], [1, \"icon\"], [1, \"features\"], [1, \"feature-card\"]],\n      template: function AccueilComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"nav\", 2)(3, \"div\", 3);\n          i0.ɵɵtext(4, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"ul\", 4)(6, \"li\")(7, \"a\", 5);\n          i0.ɵɵtext(8, \"Accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"li\")(10, \"a\", 6);\n          i0.ɵɵtext(11, \"\\u00C0 propos\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"li\")(13, \"a\", 6);\n          i0.ɵɵtext(14, \"Contact\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"a\", 8);\n          i0.ɵɵtext(17, \"Connexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"a\", 9);\n          i0.ɵɵtext(19, \"Inscription\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 10)(21, \"div\", 11)(22, \"div\", 12)(23, \"div\", 13)(24, \"div\", 14)(25, \"h1\", 15);\n          i0.ɵɵtext(26, \"Iris & Identit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"div\", 16);\n          i0.ɵɵelementStart(28, \"p\", 17);\n          i0.ɵɵtext(29, \" Chaque iris est une signature unique. Notre syst\\u00E8me de profilage biom\\u00E9trique offre une s\\u00E9curit\\u00E9 in\\u00E9gal\\u00E9e, inspir\\u00E9e directement par la nature humaine. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 18);\n          i0.ɵɵelement(31, \"img\", 19)(32, \"div\", 20);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(33, \"div\", 21)(34, \"a\", 22)(35, \"span\");\n          i0.ɵɵtext(36, \"Commencer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\", 23);\n          i0.ɵɵtext(38, \"\\u2192\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(39, \"div\", 24)(40, \"div\", 25)(41, \"div\", 23);\n          i0.ɵɵtext(42, \"\\uD83D\\uDD0D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"h3\");\n          i0.ɵɵtext(44, \"Analyse Pr\\u00E9cise\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"p\");\n          i0.ɵɵtext(46, \"Identification des traits de personnalit\\u00E9 \\u00E0 travers les motifs de l'iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 25)(48, \"div\", 23);\n          i0.ɵɵtext(49, \"\\uD83E\\uDDEC\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"h3\");\n          i0.ɵɵtext(51, \"Base Scientifique\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"p\");\n          i0.ɵɵtext(53, \"Fond\\u00E9e sur des recherches approfondies en iridologie et biom\\u00E9trie\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 25)(55, \"div\", 23);\n          i0.ɵɵtext(56, \"\\uD83D\\uDD10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"h3\");\n          i0.ɵɵtext(58, \"S\\u00E9curit\\u00E9 Avanc\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"p\");\n          i0.ɵɵtext(60, \"Protection des donn\\u00E9es et confidentialit\\u00E9 garanties\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(1, _c0));\n        }\n      },\n      dependencies: [i1.RouterLink, i1.RouterLinkActive],\n      styles: [\".page-container.accueil[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\\n  padding: 20px 0;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 0;\\n  margin-bottom: 40px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  color: #333;\\n  position: relative;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 30px;\\n  height: 2px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  border-radius: 2px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  gap: 30px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #555;\\n  text-decoration: none;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background-color: var(--fleur-primary);\\n  transition: width 0.3s ease;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  color: var(--fleur-primary);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:after, .page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%]:after {\\n  width: 100%;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%], .page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%] {\\n  padding: 10px 25px;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: var(--fleur-primary);\\n  border: 1px solid var(--fleur-primary);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(138, 79, 255, 0.1);\\n  transform: translateY(-3px);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%] {\\n  background-color: var(--fleur-primary);\\n  color: white;\\n  border: 1px solid var(--fleur-primary);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%]:hover {\\n  background-color: #681cff;\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-bottom: 60px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 1000px;\\n  height: 400px;\\n  margin-bottom: 40px;\\n  perspective: 1000px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transition: transform 0.6s;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  backface-visibility: hidden;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  padding: 40px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-right: 40px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 3rem;\\n  color: #333;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  margin-bottom: 20px;\\n  border-radius: 3px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  line-height: 1.7;\\n  color: #666;\\n  margin-bottom: 30px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  object-fit: contain;\\n  background-color: #f8f9fa;\\n  border-radius: 50%;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\\n  z-index: 2;\\n  position: relative;\\n  transition: all 0.3s ease;\\n  padding: 10px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 330px;\\n  height: 330px;\\n  border-radius: 50%;\\n  border: 2px dashed var(--fleur-secondary);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_rotate 20s linear infinite;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .start-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 15px 35px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  color: white;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n  box-shadow: 0 10px 25px rgba(138, 79, 255, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .start-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(138, 79, 255, 0.4);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .start-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 30px;\\n  margin-top: 60px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  margin-bottom: 20px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  color: #333;\\n  margin-bottom: 15px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  color: #666;\\n  line-height: 1.6;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: 30px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%] {\\n    padding-right: 0;\\n    margin-bottom: 30px;\\n    text-align: center;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n    margin: 0 auto 20px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    width: 250px;\\n    height: 250px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n    width: 280px;\\n    height: 280px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%] {\\n    gap: 20px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    width: 200px;\\n    height: 200px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n    width: 230px;\\n    height: 230px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AccueilComponent", "selectors", "decls", "vars", "consts", "template", "AccueilComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-accueil',\n  templateUrl: './accueil.component.html',\n  styleUrls: ['./accueil.component.scss']\n})\nexport class AccueilComponent {\n\n}\n", "<div class=\"page-container accueil\">\n  <div class=\"container\">\n    <nav class=\"navbar\">\n      <div class=\"logo\">IrisLock</div>\n      <ul class=\"nav-links\">\n        <li><a routerLink=\"/\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{ exact: true }\">Accueil</a></li>\n        <li><a href=\"#\">À propos</a></li>\n        <li><a href=\"#\">Contact</a></li>\n      </ul>\n      <div class=\"auth-buttons\">\n        <a routerLink=\"/login\" class=\"login-button\">Connexion</a>\n        <a routerLink=\"/signup\" class=\"register-button\">Inscription</a>\n      </div>\n    </nav>\n\n    <div class=\"content\">\n      <div class=\"card main-card\">\n        <div class=\"card-inner\">\n          <div class=\"card-front\">\n            <div class=\"text-content\">\n              <h1 class=\"title\">Iris & Identité</h1>\n              <div class=\"divider\"></div>\n              <p class=\"description\">\n                Chaque iris est une signature unique. Notre système de profilage biométrique offre une sécurité inégalée, inspirée directement par la nature humaine.\n              </p>\n            </div>\n            <div class=\"image-container\">\n              <img src=\"assets/iris.png\" alt=\"Iris\" class=\"main-image\" />\n              <div class=\"image-decoration\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"action-container\">\n        <a routerLink=\"/suivantacc\" class=\"btn start-btn\">\n          <span>Commencer</span>\n          <span class=\"icon\">→</span>\n        </a>\n      </div>\n    </div>\n\n    <div class=\"features\">\n      <div class=\"feature-card\">\n        <div class=\"icon\">🔍</div>\n        <h3>Analyse Précise</h3>\n        <p>Identification des traits de personnalité à travers les motifs de l'iris</p>\n      </div>\n      <div class=\"feature-card\">\n        <div class=\"icon\">🧬</div>\n        <h3>Base Scientifique</h3>\n        <p>Fondée sur des recherches approfondies en iridologie et biométrie</p>\n      </div>\n      <div class=\"feature-card\">\n        <div class=\"icon\">🔐</div>\n        <h3>Sécurité Avancée</h3>\n        <p>Protection des données et confidentialité garanties</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;AAOA,OAAM,MAAOA,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BE,EAAA,CAAAC,cAAA,aAAoC;UAGZD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,YAAsB;UACwED,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvGH,EAAA,CAAAC,cAAA,SAAI;UAAYD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5BH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE7BH,EAAA,CAAAC,cAAA,cAA0B;UACoBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACzDH,EAAA,CAAAC,cAAA,YAAgD;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAInEH,EAAA,CAAAC,cAAA,eAAqB;UAKOD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAI,SAAA,eAA2B;UAC3BJ,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,MAAA,kMACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAI,SAAA,eAA2D;UAE7DJ,EAAA,CAAAG,YAAA,EAAM;UAKZH,EAAA,CAAAC,cAAA,eAA8B;UAEpBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtBH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKjCH,EAAA,CAAAC,cAAA,eAAsB;UAEAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,0FAAwE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjFH,EAAA,CAAAC,cAAA,eAA0B;UACND,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,mFAAiE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE1EH,EAAA,CAAAC,cAAA,eAA0B;UACND,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,uCAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qEAAmD;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UAnDVH,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAM,UAAA,4BAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}