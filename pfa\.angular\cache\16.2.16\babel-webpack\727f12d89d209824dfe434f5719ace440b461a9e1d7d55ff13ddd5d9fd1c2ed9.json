{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class BijouComponent {\n  static {\n    this.ɵfac = function BijouComponent_Factory(t) {\n      return new (t || BijouComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BijouComponent,\n      selectors: [[\"app-bijou\"]],\n      decls: 24,\n      vars: 0,\n      consts: [[1, \"iris-details\"], [\"src\", \"assets/2.png\", \"alt\", \"Bijou\", 1, \"iris-img\"], [1, \"iris-description\"]],\n      template: function BijouComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4, \"Bijou\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"ul\")(6, \"li\");\n          i0.ɵɵtext(7, \"Le R\\u00E9fl\\u00E9chi\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"li\");\n          i0.ɵɵtext(9, \"Type analytique, mental et tourn\\u00E9 vers la r\\u00E9flexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"li\");\n          i0.ɵɵtext(11, \"Ressent et per\\u00E7oit par l\\u2019analyse interne, peu d\\u2019expression \\u00E9motionnelle ext\\u00E9rieure\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"li\");\n          i0.ɵɵtext(13, \"Apprentissage visuel : observe, lit, cat\\u00E9gorise, puis verbalise\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"li\");\n          i0.ɵɵtext(15, \"Communicateur pr\\u00E9cis, souvent enseignant, critique, scientifique ou leader\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"li\");\n          i0.ɵɵtext(17, \"Orient\\u00E9 vers l\\u2019avenir, porteur de sagesse\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"li\");\n          i0.ɵɵtext(19, \"N\\u2019aime pas \\u00EAtre critiqu\\u00E9 ni contr\\u00F4l\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"li\");\n          i0.ɵɵtext(21, \"Proximit\\u00E9 avec la m\\u00E8re, parfois distante \\u00E9motionnellement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"li\");\n          i0.ɵɵtext(23, \"Le\\u00E7on de vie : apprendre \\u00E0 l\\u00E2cher prise, faire confiance et exprimer ses \\u00E9motions\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\".iris-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  background: linear-gradient(to right, #f8e8ff, #f6f1ff);\\n  min-height: 100vh;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 180px;\\n  height: 180px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\\n  margin-bottom: 25px;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: bold;\\n  color: #5e548e;\\n  margin-bottom: 15px;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n  max-width: 700px;\\n  font-size: 1.1rem;\\n  line-height: 1.7;\\n  color: #3d3d3d;\\n  text-align: center;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  background: rgba(255, 255, 255, 0.8);\\n  padding: 20px;\\n  border-radius: 20px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (max-width: 768px) {\\n  .iris-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n    width: 140px;\\n    height: 140px;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    padding: 15px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYmlqb3UvYmlqb3UuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0E7RUFDSSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsYUFBQTtFQUNBLHVEQUFBO0VBQ0EsaUJBQUE7QUFBSjtBQUVJO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EseUNBQUE7RUFDQSxtQkFBQTtBQUFOO0FBR0k7RUFDRSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQ0FBQTtBQUROO0FBSUk7RUFDRSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQ0FBQTtFQUNBLG9DQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EseUNBQUE7QUFGTjs7QUFPRTtFQUNFO0lBQ0UsYUFBQTtFQUpKO0VBTUk7SUFDRSxZQUFBO0lBQ0EsYUFBQTtFQUpOO0VBT0k7SUFDRSxpQkFBQTtFQUxOO0VBUUk7SUFDRSxlQUFBO0lBQ0EsYUFBQTtFQU5OO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTQ1NTIGNvbW11biBwb3VyIHRvdXMgbGVzIGNvbXBvc2FudHMgSXJpc1xyXG4uaXJpcy1jb250YWluZXIge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBwYWRkaW5nOiA0MHB4O1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCAjZjhlOGZmLCAjZjZmMWZmKTtcclxuICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xyXG4gIFxyXG4gICAgLmlyaXMtaW1hZ2Uge1xyXG4gICAgICB3aWR0aDogMTgwcHg7XHJcbiAgICAgIGhlaWdodDogMTgwcHg7XHJcbiAgICAgIG9iamVjdC1maXQ6IGNvdmVyO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgIGJveC1zaGFkb3c6IDAgOHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjIpO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAyNXB4O1xyXG4gICAgfVxyXG4gIFxyXG4gICAgLmlyaXMtdGl0bGUge1xyXG4gICAgICBmb250LXNpemU6IDJyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgICBjb2xvcjogIzVlNTQ4ZTtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMTVweDtcclxuICAgICAgZm9udC1mYW1pbHk6ICdQb3BwaW5zJywgc2Fucy1zZXJpZjtcclxuICAgIH1cclxuICBcclxuICAgIC5pcmlzLWRlc2NyaXB0aW9uIHtcclxuICAgICAgbWF4LXdpZHRoOiA3MDBweDtcclxuICAgICAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjc7XHJcbiAgICAgIGNvbG9yOiAjM2QzZDNkO1xyXG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgIGZvbnQtZmFtaWx5OiAnUG9wcGlucycsIHNhbnMtc2VyaWY7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmZjYztcclxuICAgICAgcGFkZGluZzogMjBweDtcclxuICAgICAgYm9yZGVyLXJhZGl1czogMjBweDtcclxuICAgICAgYm94LXNoYWRvdzogMCA0cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICB9XHJcbiAgfVxyXG4gIFxyXG4gIC8vIFJlc3BvbnNpdmVcclxuICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAgIC5pcmlzLWNvbnRhaW5lciB7XHJcbiAgICAgIHBhZGRpbmc6IDIwcHg7XHJcbiAgXHJcbiAgICAgIC5pcmlzLWltYWdlIHtcclxuICAgICAgICB3aWR0aDogMTQwcHg7XHJcbiAgICAgICAgaGVpZ2h0OiAxNDBweDtcclxuICAgICAgfVxyXG4gIFxyXG4gICAgICAuaXJpcy10aXRsZSB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxLjVyZW07XHJcbiAgICAgIH1cclxuICBcclxuICAgICAgLmlyaXMtZGVzY3JpcHRpb24ge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgICAgICBwYWRkaW5nOiAxNXB4O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BijouComponent", "selectors", "decls", "vars", "consts", "template", "BijouComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\bijou\\bijou.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\bijou\\bijou.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-bijou',\n  templateUrl: './bijou.component.html',\n  styleUrls: ['./bijou.component.scss']\n})\nexport class BijouComponent {\n\n}\n", "<div class=\"iris-details\">\n    <img src=\"assets/2.png\" alt=\"Bijou\" class=\"iris-img\" />\n    <div class=\"iris-description\">\n      <h2>Bijou</h2>\n      <ul>\n        <li><PERSON> Réfléchi</li>\n        <li>Type analytique, mental et tourné vers la réflexion</li>\n        <li>Ressent et perçoit par l’analyse interne, peu d’expression émotionnelle extérieure</li>\n        <li>Apprentissage visuel : observe, lit, catégorise, puis verbalise</li>\n        <li>Communicateur pré<PERSON>, souvent enseignant, critique, scientifique ou leader</li>\n        <li><PERSON><PERSON> vers l’avenir, porteur de sagesse</li>\n        <li>N’aime pas être critiqué ni contrôlé</li>\n        <li>Proximité avec la mère, parfois distante émotionnellement</li>\n        <li>Leçon de vie : apprendre à lâcher prise, faire confiance et exprimer ses émotions</li>\n      </ul>\n    </div>\n  </div>\n  "], "mappings": ";AAOA,OAAM,MAAOA,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3BE,EAAA,CAAAC,cAAA,aAA0B;UACtBD,EAAA,CAAAE,SAAA,aAAuD;UACvDF,EAAA,CAAAC,cAAA,aAA8B;UACxBD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,SAAI;UACED,EAAA,CAAAG,MAAA,4BAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpBJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,oEAAmD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC5DJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,mHAAkF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC3FJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,4EAA+D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACxEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,uFAA0E;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnFJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,2DAAyC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,qEAAoC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC7CJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,gFAAyD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,6GAAiF;UAAAH,EAAA,CAAAI,YAAA,EAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}