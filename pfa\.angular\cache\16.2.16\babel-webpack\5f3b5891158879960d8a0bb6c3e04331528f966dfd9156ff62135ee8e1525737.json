{"ast": null, "code": "import _asyncToGenerator from \"E:/aymen/pfa/pfa/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Component, ComponentContainer } from '@firebase/component';\nimport { Logger, setUserLogHandler, setLogLevel as setLogLevel$1 } from '@firebase/logger';\nimport { ErrorFactory, base64Decode, getDefaultAppConfig, deepEqual, isBrowser, isWebWorker, FirebaseError, base64urlEncodeWithoutPadding, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nexport { FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass PlatformLoggerServiceImpl {\n  constructor(container) {\n    this.container = container;\n  }\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString() {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers.map(provider => {\n      if (isVersionServiceProvider(provider)) {\n        const service = provider.getImmediate();\n        return `${service.library}/${service.version}`;\n      } else {\n        return null;\n      }\n    }).filter(logString => logString).join(' ');\n  }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider) {\n  const component = provider.getComponent();\n  return (component === null || component === void 0 ? void 0 : component.type) === \"VERSION\" /* ComponentType.VERSION */;\n}\n\nconst name$q = \"@firebase/app\";\nconst version$1 = \"0.13.0\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst logger = new Logger('@firebase/app');\nconst name$p = \"@firebase/app-compat\";\nconst name$o = \"@firebase/analytics-compat\";\nconst name$n = \"@firebase/analytics\";\nconst name$m = \"@firebase/app-check-compat\";\nconst name$l = \"@firebase/app-check\";\nconst name$k = \"@firebase/auth\";\nconst name$j = \"@firebase/auth-compat\";\nconst name$i = \"@firebase/database\";\nconst name$h = \"@firebase/data-connect\";\nconst name$g = \"@firebase/database-compat\";\nconst name$f = \"@firebase/functions\";\nconst name$e = \"@firebase/functions-compat\";\nconst name$d = \"@firebase/installations\";\nconst name$c = \"@firebase/installations-compat\";\nconst name$b = \"@firebase/messaging\";\nconst name$a = \"@firebase/messaging-compat\";\nconst name$9 = \"@firebase/performance\";\nconst name$8 = \"@firebase/performance-compat\";\nconst name$7 = \"@firebase/remote-config\";\nconst name$6 = \"@firebase/remote-config-compat\";\nconst name$5 = \"@firebase/storage\";\nconst name$4 = \"@firebase/storage-compat\";\nconst name$3 = \"@firebase/firestore\";\nconst name$2 = \"@firebase/ai\";\nconst name$1 = \"@firebase/firestore-compat\";\nconst name = \"firebase\";\nconst version = \"11.8.0\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The default app name\n *\n * @internal\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\nconst PLATFORM_LOG_STRING = {\n  [name$q]: 'fire-core',\n  [name$p]: 'fire-core-compat',\n  [name$n]: 'fire-analytics',\n  [name$o]: 'fire-analytics-compat',\n  [name$l]: 'fire-app-check',\n  [name$m]: 'fire-app-check-compat',\n  [name$k]: 'fire-auth',\n  [name$j]: 'fire-auth-compat',\n  [name$i]: 'fire-rtdb',\n  [name$h]: 'fire-data-connect',\n  [name$g]: 'fire-rtdb-compat',\n  [name$f]: 'fire-fn',\n  [name$e]: 'fire-fn-compat',\n  [name$d]: 'fire-iid',\n  [name$c]: 'fire-iid-compat',\n  [name$b]: 'fire-fcm',\n  [name$a]: 'fire-fcm-compat',\n  [name$9]: 'fire-perf',\n  [name$8]: 'fire-perf-compat',\n  [name$7]: 'fire-rc',\n  [name$6]: 'fire-rc-compat',\n  [name$5]: 'fire-gcs',\n  [name$4]: 'fire-gcs-compat',\n  [name$3]: 'fire-fst',\n  [name$1]: 'fire-fst-compat',\n  [name$2]: 'fire-vertex',\n  'fire-js': 'fire-js',\n  // Platform identifier for JS SDK.\n  [name]: 'fire-js-all'\n};\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @internal\n */\nconst _apps = new Map();\n/**\n * @internal\n */\nconst _serverApps = new Map();\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst _components = new Map();\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nfunction _addComponent(app, component) {\n  try {\n    app.container.addComponent(component);\n  } catch (e) {\n    logger.debug(`Component ${component.name} failed to register with FirebaseApp ${app.name}`, e);\n  }\n}\n/**\n *\n * @internal\n */\nfunction _addOrOverwriteComponent(app, component) {\n  app.container.addOrOverwriteComponent(component);\n}\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nfunction _registerComponent(component) {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(`There were multiple attempts to register component ${componentName}.`);\n    return false;\n  }\n  _components.set(componentName, component);\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app, component);\n  }\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp, component);\n  }\n  return true;\n}\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nfunction _getProvider(app, name) {\n  const heartbeatController = app.container.getProvider('heartbeat').getImmediate({\n    optional: true\n  });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return app.container.getProvider(name);\n}\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nfunction _removeServiceInstance(app, name, instanceIdentifier = DEFAULT_ENTRY_NAME) {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n/**\n *\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nfunction _isFirebaseApp(obj) {\n  return obj.options !== undefined;\n}\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nfunction _isFirebaseServerApp(obj) {\n  if (obj === null || obj === undefined) {\n    return false;\n  }\n  return obj.settings !== undefined;\n}\n/**\n * Test only\n *\n * @internal\n */\nfunction _clearComponents() {\n  _components.clear();\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst ERRORS = {\n  [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" + 'call initializeApp() first',\n  [\"bad-app-name\" /* AppError.BAD_APP_NAME */]: \"Illegal App name: '{$appName}'\",\n  [\"duplicate-app\" /* AppError.DUPLICATE_APP */]: \"Firebase App named '{$appName}' already exists with different options or config\",\n  [\"app-deleted\" /* AppError.APP_DELETED */]: \"Firebase App named '{$appName}' already deleted\",\n  [\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */]: 'Firebase Server App has been deleted',\n  [\"no-options\" /* AppError.NO_OPTIONS */]: 'Need to provide options, when not being deployed to hosting via source.',\n  [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' + 'Firebase App instance.',\n  [\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */]: 'First argument to `onLog` must be null or a function.',\n  [\"idb-open\" /* AppError.IDB_OPEN */]: 'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-get\" /* AppError.IDB_GET */]: 'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-set\" /* AppError.IDB_WRITE */]: 'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"idb-delete\" /* AppError.IDB_DELETE */]: 'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */]: 'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */]: 'FirebaseServerApp is not for use in browser environments.'\n};\nconst ERROR_FACTORY = new ErrorFactory('app', 'Firebase', ERRORS);\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass FirebaseAppImpl {\n  constructor(options, config, container) {\n    this._isDeleted = false;\n    this._options = Object.assign({}, options);\n    this._config = Object.assign({}, config);\n    this._name = config.name;\n    this._automaticDataCollectionEnabled = config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(new Component('app', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  }\n\n  get automaticDataCollectionEnabled() {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n  set automaticDataCollectionEnabled(val) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n  get name() {\n    this.checkDestroyed();\n    return this._name;\n  }\n  get options() {\n    this.checkDestroyed();\n    return this._options;\n  }\n  get config() {\n    this.checkDestroyed();\n    return this._config;\n  }\n  get container() {\n    return this._container;\n  }\n  get isDeleted() {\n    return this._isDeleted;\n  }\n  set isDeleted(val) {\n    this._isDeleted = val;\n  }\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  checkDestroyed() {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(\"app-deleted\" /* AppError.APP_DELETED */, {\n        appName: this._name\n      });\n    }\n  }\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Parse the token and check to see if the `exp` claim is in the future.\n// Reports an error to the console if the token or claim could not be parsed, or if `exp` is in\n// the past.\nfunction validateTokenTTL(base64Token, tokenName) {\n  const secondPart = base64Decode(base64Token.split('.')[1]);\n  if (secondPart === null) {\n    console.error(`FirebaseServerApp ${tokenName} is invalid: second part could not be parsed.`);\n    return;\n  }\n  const expClaim = JSON.parse(secondPart).exp;\n  if (expClaim === undefined) {\n    console.error(`FirebaseServerApp ${tokenName} is invalid: expiration claim could not be parsed`);\n    return;\n  }\n  const exp = JSON.parse(secondPart).exp * 1000;\n  const now = new Date().getTime();\n  const diff = exp - now;\n  if (diff <= 0) {\n    console.error(`FirebaseServerApp ${tokenName} is invalid: the token has expired.`);\n  }\n}\nclass FirebaseServerAppImpl extends FirebaseAppImpl {\n  constructor(options, serverConfig, name, container) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled = serverConfig.automaticDataCollectionEnabled !== undefined ? serverConfig.automaticDataCollectionEnabled : true;\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config = {\n      name,\n      automaticDataCollectionEnabled\n    };\n    if (options.apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options, config, container);\n    } else {\n      const appImpl = options;\n      super(appImpl.options, config, container);\n    }\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = Object.assign({\n      automaticDataCollectionEnabled\n    }, serverConfig);\n    // Ensure that the current time is within the `authIdtoken` window of validity.\n    if (this._serverConfig.authIdToken) {\n      validateTokenTTL(this._serverConfig.authIdToken, 'authIdToken');\n    }\n    // Ensure that the current time is within the `appCheckToken` window of validity.\n    if (this._serverConfig.appCheckToken) {\n      validateTokenTTL(this._serverConfig.appCheckToken, 'appCheckToken');\n    }\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n    registerVersion(name$q, version$1, 'serverapp');\n  }\n  toJSON() {\n    return undefined;\n  }\n  get refCount() {\n    return this._refCount;\n  }\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj) {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n  // Decrement the reference count.\n  decRefCount() {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  automaticCleanup() {\n    void deleteApp(this);\n  }\n  get settings() {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  checkDestroyed() {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */);\n    }\n  }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The current SDK version.\n *\n * @public\n */\nconst SDK_VERSION = version;\nfunction initializeApp(_options, rawConfig = {}) {\n  let options = _options;\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = {\n      name\n    };\n  }\n  const config = Object.assign({\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: true\n  }, rawConfig);\n  const name = config.name;\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(\"bad-app-name\" /* AppError.BAD_APP_NAME */, {\n      appName: String(name)\n    });\n  }\n  options || (options = getDefaultAppConfig());\n  if (!options) {\n    throw ERROR_FACTORY.create(\"no-options\" /* AppError.NO_OPTIONS */);\n  }\n\n  const existingApp = _apps.get(name);\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (deepEqual(options, existingApp.options) && deepEqual(config, existingApp.config)) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(\"duplicate-app\" /* AppError.DUPLICATE_APP */, {\n        appName: name\n      });\n    }\n  }\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n  const newApp = new FirebaseAppImpl(options, config, container);\n  _apps.set(name, newApp);\n  return newApp;\n}\nfunction initializeServerApp(_options, _serverAppConfig) {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */);\n  }\n\n  if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n    _serverAppConfig.automaticDataCollectionEnabled = true;\n  }\n  let appOptions;\n  if (_isFirebaseApp(_options)) {\n    appOptions = _options.options;\n  } else {\n    appOptions = _options;\n  }\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = Object.assign(Object.assign({}, _serverAppConfig), appOptions);\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n  const hashCode = s => {\n    return [...s].reduce((hash, c) => Math.imul(31, hash) + c.charCodeAt(0) | 0, 0);\n  };\n  if (_serverAppConfig.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */, {});\n    }\n  }\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString);\n  if (existingApp) {\n    existingApp.incRefCount(_serverAppConfig.releaseOnDeref);\n    return existingApp;\n  }\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n  const newApp = new FirebaseServerAppImpl(appOptions, _serverAppConfig, nameString, container);\n  _serverApps.set(nameString, newApp);\n  return newApp;\n}\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nfunction getApp(name = DEFAULT_ENTRY_NAME) {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, {\n      appName: name\n    });\n  }\n  return app;\n}\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nfunction getApps() {\n  return Array.from(_apps.values());\n}\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nfunction deleteApp(_x) {\n  return _deleteApp.apply(this, arguments);\n}\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nfunction _deleteApp() {\n  _deleteApp = _asyncToGenerator(function* (app) {\n    let cleanupProviders = false;\n    const name = app.name;\n    if (_apps.has(name)) {\n      cleanupProviders = true;\n      _apps.delete(name);\n    } else if (_serverApps.has(name)) {\n      const firebaseServerApp = app;\n      if (firebaseServerApp.decRefCount() <= 0) {\n        _serverApps.delete(name);\n        cleanupProviders = true;\n      }\n    }\n    if (cleanupProviders) {\n      yield Promise.all(app.container.getProviders().map(provider => provider.delete()));\n      app.isDeleted = true;\n    }\n  });\n  return _deleteApp.apply(this, arguments);\n}\nfunction registerVersion(libraryKeyOrName, version, variant) {\n  var _a;\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = (_a = PLATFORM_LOG_STRING[libraryKeyOrName]) !== null && _a !== void 0 ? _a : libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [`Unable to register library \"${library}\" with version \"${version}\":`];\n    if (libraryMismatch) {\n      warning.push(`library name \"${library}\" contains illegal characters (whitespace or \"/\")`);\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(`version name \"${version}\" contains illegal characters (whitespace or \"/\")`);\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(new Component(`${library}-version`, () => ({\n    library,\n    version\n  }), \"VERSION\" /* ComponentType.VERSION */));\n}\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nfunction onLog(logCallback, options) {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */);\n  }\n\n  setUserLogHandler(logCallback, options);\n}\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nfunction setLogLevel(logLevel) {\n  setLogLevel$1(logLevel);\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n  if (!dbPromise) {\n    dbPromise = openDB(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(\"idb-open\" /* AppError.IDB_OPEN */, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\nfunction readHeartbeatsFromIndexedDB(_x2) {\n  return _readHeartbeatsFromIndexedDB.apply(this, arguments);\n}\nfunction _readHeartbeatsFromIndexedDB() {\n  _readHeartbeatsFromIndexedDB = _asyncToGenerator(function* (app) {\n    try {\n      const db = yield getDbPromise();\n      const tx = db.transaction(STORE_NAME);\n      const result = yield tx.objectStore(STORE_NAME).get(computeKey(app));\n      // We already have the value but tx.done can throw,\n      // so we need to await it here to catch errors\n      yield tx.done;\n      return result;\n    } catch (e) {\n      if (e instanceof FirebaseError) {\n        logger.warn(e.message);\n      } else {\n        const idbGetError = ERROR_FACTORY.create(\"idb-get\" /* AppError.IDB_GET */, {\n          originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n        });\n        logger.warn(idbGetError.message);\n      }\n    }\n  });\n  return _readHeartbeatsFromIndexedDB.apply(this, arguments);\n}\nfunction writeHeartbeatsToIndexedDB(_x3, _x4) {\n  return _writeHeartbeatsToIndexedDB.apply(this, arguments);\n}\nfunction _writeHeartbeatsToIndexedDB() {\n  _writeHeartbeatsToIndexedDB = _asyncToGenerator(function* (app, heartbeatObject) {\n    try {\n      const db = yield getDbPromise();\n      const tx = db.transaction(STORE_NAME, 'readwrite');\n      const objectStore = tx.objectStore(STORE_NAME);\n      yield objectStore.put(heartbeatObject, computeKey(app));\n      yield tx.done;\n    } catch (e) {\n      if (e instanceof FirebaseError) {\n        logger.warn(e.message);\n      } else {\n        const idbGetError = ERROR_FACTORY.create(\"idb-set\" /* AppError.IDB_WRITE */, {\n          originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n        });\n        logger.warn(idbGetError.message);\n      }\n    }\n  });\n  return _writeHeartbeatsToIndexedDB.apply(this, arguments);\n}\nfunction computeKey(app) {\n  return `${app.name}!${app.options.appId}`;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst MAX_HEADER_BYTES = 1024;\nconst MAX_NUM_STORED_HEARTBEATS = 30;\nclass HeartbeatServiceImpl {\n  constructor(container) {\n    this.container = container;\n    /**\n     * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n     * the header string.\n     * Stores one record per date. This will be consolidated into the standard\n     * format of one record per user agent string before being sent as a header.\n     * Populated from indexedDB when the controller is instantiated and should\n     * be kept in sync with indexedDB.\n     * Leave public for easier testing.\n     */\n    this._heartbeatsCache = null;\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n  /**\n   * Called to report a heartbeat. The function will generate\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n   * to IndexedDB.\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\n   * already logged, subsequent calls to this function in the same day will be ignored.\n   */\n  triggerHeartbeat() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b;\n      try {\n        const platformLogger = _this.container.getProvider('platform-logger').getImmediate();\n        // This is the \"Firebase user agent\" string from the platform logger\n        // service, not the browser user agent.\n        const agent = platformLogger.getPlatformInfoString();\n        const date = getUTCDateString();\n        if (((_a = _this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null) {\n          _this._heartbeatsCache = yield _this._heartbeatsCachePromise;\n          // If we failed to construct a heartbeats cache, then return immediately.\n          if (((_b = _this._heartbeatsCache) === null || _b === void 0 ? void 0 : _b.heartbeats) == null) {\n            return;\n          }\n        }\n        // Do not store a heartbeat if one is already stored for this day\n        // or if a header has already been sent today.\n        if (_this._heartbeatsCache.lastSentHeartbeatDate === date || _this._heartbeatsCache.heartbeats.some(singleDateHeartbeat => singleDateHeartbeat.date === date)) {\n          return;\n        } else {\n          // There is no entry for this date. Create one.\n          _this._heartbeatsCache.heartbeats.push({\n            date,\n            agent\n          });\n          // If the number of stored heartbeats exceeds the maximum number of stored heartbeats, remove the heartbeat with the earliest date.\n          // Since this is executed each time a heartbeat is pushed, the limit can only be exceeded by one, so only one needs to be removed.\n          if (_this._heartbeatsCache.heartbeats.length > MAX_NUM_STORED_HEARTBEATS) {\n            const earliestHeartbeatIdx = getEarliestHeartbeatIdx(_this._heartbeatsCache.heartbeats);\n            _this._heartbeatsCache.heartbeats.splice(earliestHeartbeatIdx, 1);\n          }\n        }\n        return _this._storage.overwrite(_this._heartbeatsCache);\n      } catch (e) {\n        logger.warn(e);\n      }\n    })();\n  }\n  /**\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n   * It also clears all heartbeats from memory as well as in IndexedDB.\n   *\n   * NOTE: Consuming product SDKs should not send the header if this method\n   * returns an empty string.\n   */\n  getHeartbeatsHeader() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      try {\n        if (_this2._heartbeatsCache === null) {\n          yield _this2._heartbeatsCachePromise;\n        }\n        // If it's still null or the array is empty, there is no data to send.\n        if (((_a = _this2._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null || _this2._heartbeatsCache.heartbeats.length === 0) {\n          return '';\n        }\n        const date = getUTCDateString();\n        // Extract as many heartbeats from the cache as will fit under the size limit.\n        const {\n          heartbeatsToSend,\n          unsentEntries\n        } = extractHeartbeatsForHeader(_this2._heartbeatsCache.heartbeats);\n        const headerString = base64urlEncodeWithoutPadding(JSON.stringify({\n          version: 2,\n          heartbeats: heartbeatsToSend\n        }));\n        // Store last sent date to prevent another being logged/sent for the same day.\n        _this2._heartbeatsCache.lastSentHeartbeatDate = date;\n        if (unsentEntries.length > 0) {\n          // Store any unsent entries if they exist.\n          _this2._heartbeatsCache.heartbeats = unsentEntries;\n          // This seems more likely than emptying the array (below) to lead to some odd state\n          // since the cache isn't empty and this will be called again on the next request,\n          // and is probably safest if we await it.\n          yield _this2._storage.overwrite(_this2._heartbeatsCache);\n        } else {\n          _this2._heartbeatsCache.heartbeats = [];\n          // Do not wait for this, to reduce latency.\n          void _this2._storage.overwrite(_this2._heartbeatsCache);\n        }\n        return headerString;\n      } catch (e) {\n        logger.warn(e);\n        return '';\n      }\n    })();\n  }\n}\nfunction getUTCDateString() {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\nfunction extractHeartbeatsForHeader(heartbeatsCache, maxSize = MAX_HEADER_BYTES) {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(hb => hb.agent === singleDateHeartbeat.agent);\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\nclass HeartbeatStorageImpl {\n  constructor(app) {\n    this.app = app;\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  runIndexedDBEnvironmentCheck() {\n    return _asyncToGenerator(function* () {\n      if (!isIndexedDBAvailable()) {\n        return false;\n      } else {\n        return validateIndexedDBOpenable().then(() => true).catch(() => false);\n      }\n    })();\n  }\n  /**\n   * Read all heartbeats.\n   */\n  read() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const canUseIndexedDB = yield _this3._canUseIndexedDBPromise;\n      if (!canUseIndexedDB) {\n        return {\n          heartbeats: []\n        };\n      } else {\n        const idbHeartbeatObject = yield readHeartbeatsFromIndexedDB(_this3.app);\n        if (idbHeartbeatObject === null || idbHeartbeatObject === void 0 ? void 0 : idbHeartbeatObject.heartbeats) {\n          return idbHeartbeatObject;\n        } else {\n          return {\n            heartbeats: []\n          };\n        }\n      }\n    })();\n  }\n  // overwrite the storage with the provided heartbeats\n  overwrite(heartbeatsObject) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      const canUseIndexedDB = yield _this4._canUseIndexedDBPromise;\n      if (!canUseIndexedDB) {\n        return;\n      } else {\n        const existingHeartbeatsObject = yield _this4.read();\n        return writeHeartbeatsToIndexedDB(_this4.app, {\n          lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n          heartbeats: heartbeatsObject.heartbeats\n        });\n      }\n    })();\n  }\n  // add heartbeats\n  add(heartbeatsObject) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      var _a;\n      const canUseIndexedDB = yield _this5._canUseIndexedDBPromise;\n      if (!canUseIndexedDB) {\n        return;\n      } else {\n        const existingHeartbeatsObject = yield _this5.read();\n        return writeHeartbeatsToIndexedDB(_this5.app, {\n          lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n          heartbeats: [...existingHeartbeatsObject.heartbeats, ...heartbeatsObject.heartbeats]\n        });\n      }\n    })();\n  }\n}\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nfunction countBytes(heartbeatsCache) {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n  // heartbeatsCache wrapper properties\n  JSON.stringify({\n    version: 2,\n    heartbeats: heartbeatsCache\n  })).length;\n}\n/**\n * Returns the index of the heartbeat with the earliest date.\n * If the heartbeats array is empty, -1 is returned.\n */\nfunction getEarliestHeartbeatIdx(heartbeats) {\n  if (heartbeats.length === 0) {\n    return -1;\n  }\n  let earliestHeartbeatIdx = 0;\n  let earliestHeartbeatDate = heartbeats[0].date;\n  for (let i = 1; i < heartbeats.length; i++) {\n    if (heartbeats[i].date < earliestHeartbeatDate) {\n      earliestHeartbeatDate = heartbeats[i].date;\n      earliestHeartbeatIdx = i;\n    }\n  }\n  return earliestHeartbeatIdx;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction registerCoreComponents(variant) {\n  _registerComponent(new Component('platform-logger', container => new PlatformLoggerServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n  _registerComponent(new Component('heartbeat', container => new HeartbeatServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n  // Register `app` package.\n  registerVersion(name$q, version$1, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name$q, version$1, 'esm2017');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n\n/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\nregisterCoreComponents('');\nexport { SDK_VERSION, DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME, _addComponent, _addOrOverwriteComponent, _apps, _clearComponents, _components, _getProvider, _isFirebaseApp, _isFirebaseServerApp, _registerComponent, _removeServiceInstance, _serverApps, deleteApp, getApp, getApps, initializeApp, initializeServerApp, onLog, registerVersion, setLogLevel };", "map": {"version": 3, "names": ["Component", "ComponentContainer", "<PERSON><PERSON>", "setUserLogHandler", "setLogLevel", "setLogLevel$1", "ErrorFactory", "base64Decode", "getDefaultAppConfig", "deepEqual", "<PERSON><PERSON><PERSON><PERSON>", "isWebWorker", "FirebaseError", "base64urlEncodeWithoutPadding", "isIndexedDBAvailable", "validateIndexedDBOpenable", "openDB", "PlatformLoggerServiceImpl", "constructor", "container", "getPlatformInfoString", "providers", "getProviders", "map", "provider", "isVersionServiceProvider", "service", "getImmediate", "library", "version", "filter", "logString", "join", "component", "getComponent", "type", "name$q", "version$1", "logger", "name$p", "name$o", "name$n", "name$m", "name$l", "name$k", "name$j", "name$i", "name$h", "name$g", "name$f", "name$e", "name$d", "name$c", "name$b", "name$a", "name$9", "name$8", "name$7", "name$6", "name$5", "name$4", "name$3", "name$2", "name$1", "name", "DEFAULT_ENTRY_NAME", "PLATFORM_LOG_STRING", "_apps", "Map", "_serverApps", "_components", "_addComponent", "app", "addComponent", "e", "debug", "_addOrOverwriteComponent", "addOrOverwriteComponent", "_registerComponent", "componentName", "has", "set", "values", "serverApp", "_get<PERSON><PERSON><PERSON>", "heartbeatController", "get<PERSON><PERSON><PERSON>", "optional", "triggerHeartbeat", "_removeServiceInstance", "instanceIdentifier", "clearInstance", "_isFirebaseApp", "obj", "options", "undefined", "_isFirebaseServerApp", "settings", "_clearComponents", "clear", "ERRORS", "ERROR_FACTORY", "FirebaseAppImpl", "config", "_isDeleted", "_options", "Object", "assign", "_config", "_name", "_automaticDataCollectionEnabled", "automaticDataCollectionEnabled", "_container", "checkDestroyed", "val", "isDeleted", "create", "appName", "validateTokenTTL", "base64Token", "tokenName", "second<PERSON><PERSON>", "split", "console", "error", "expClaim", "JSON", "parse", "exp", "now", "Date", "getTime", "diff", "FirebaseServerAppImpl", "serverConfig", "<PERSON><PERSON><PERSON><PERSON>", "appImpl", "_serverConfig", "authIdToken", "appCheckToken", "_finalizationRegistry", "FinalizationRegistry", "automaticCleanup", "_refCount", "incRefCount", "releaseOnDeref", "registerVersion", "toJSON", "refCount", "register", "decRefCount", "deleteApp", "SDK_VERSION", "initializeApp", "rawConfig", "String", "existingApp", "get", "newApp", "initializeServerApp", "_serverAppConfig", "appOptions", "nameObj", "hashCode", "s", "reduce", "hash", "c", "Math", "imul", "charCodeAt", "nameString", "stringify", "getApp", "getApps", "Array", "from", "_x", "_deleteApp", "apply", "arguments", "_asyncToGenerator", "cleanupProviders", "delete", "firebaseServerApp", "Promise", "all", "libraryKeyOrName", "variant", "_a", "libraryMismatch", "match", "versionMismatch", "warning", "push", "warn", "onLog", "logCallback", "logLevel", "DB_NAME", "DB_VERSION", "STORE_NAME", "db<PERSON><PERSON><PERSON>", "getDbPromise", "upgrade", "db", "oldVersion", "createObjectStore", "catch", "originalErrorMessage", "message", "readHeartbeatsFromIndexedDB", "_x2", "_readHeartbeatsFromIndexedDB", "tx", "transaction", "result", "objectStore", "computeKey", "done", "idbGetError", "writeHeartbeatsToIndexedDB", "_x3", "_x4", "_writeHeartbeatsToIndexedDB", "heartbeatObject", "put", "appId", "MAX_HEADER_BYTES", "MAX_NUM_STORED_HEARTBEATS", "HeartbeatServiceImpl", "_heartbeatsCache", "_storage", "HeartbeatStorageImpl", "_heartbeatsCachePromise", "read", "then", "_this", "_b", "platformLogger", "agent", "date", "getUTCDateString", "heartbeats", "lastSentHeartbeatDate", "some", "singleDateHeartbeat", "length", "earliestHeartbeatIdx", "getEarliestHeartbeatIdx", "splice", "overwrite", "getHeartbeatsHeader", "_this2", "heartbeatsToSend", "unsentEntries", "extractHeartbeatsForHeader", "headerString", "today", "toISOString", "substring", "heartbeatsCache", "maxSize", "slice", "heartbeatEntry", "find", "hb", "dates", "countBytes", "pop", "_canUseIndexedDBPromise", "runIndexedDBEnvironmentCheck", "_this3", "canUseIndexedDB", "idbHeartbeatObject", "heartbeatsObject", "_this4", "existingHeartbeatsObject", "add", "_this5", "earliestHeartbeatDate", "i", "registerCoreComponents", "_DEFAULT_ENTRY_NAME"], "sources": ["E:/aymen/pfa/pfa/node_modules/@firebase/app/dist/esm/index.esm2017.js"], "sourcesContent": ["import { Component, ComponentContainer } from '@firebase/component';\nimport { <PERSON><PERSON>, setUser<PERSON>og<PERSON><PERSON><PERSON>, setLogLevel as setLogLevel$1 } from '@firebase/logger';\nimport { ErrorFactory, base64Decode, getDefaultAppConfig, deepEqual, isBrowser, isWebWorker, FirebaseError, base64urlEncodeWithoutPadding, isIndexedDBAvailable, validateIndexedDBOpenable } from '@firebase/util';\nexport { FirebaseError } from '@firebase/util';\nimport { openDB } from 'idb';\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass PlatformLoggerServiceImpl {\n    constructor(container) {\n        this.container = container;\n    }\n    // In initial implementation, this will be called by installations on\n    // auth token refresh, and installations will send this string.\n    getPlatformInfoString() {\n        const providers = this.container.getProviders();\n        // Loop through providers and get library/version pairs from any that are\n        // version components.\n        return providers\n            .map(provider => {\n            if (isVersionServiceProvider(provider)) {\n                const service = provider.getImmediate();\n                return `${service.library}/${service.version}`;\n            }\n            else {\n                return null;\n            }\n        })\n            .filter(logString => logString)\n            .join(' ');\n    }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider) {\n    const component = provider.getComponent();\n    return (component === null || component === void 0 ? void 0 : component.type) === \"VERSION\" /* ComponentType.VERSION */;\n}\n\nconst name$q = \"@firebase/app\";\nconst version$1 = \"0.13.0\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst logger = new Logger('@firebase/app');\n\nconst name$p = \"@firebase/app-compat\";\n\nconst name$o = \"@firebase/analytics-compat\";\n\nconst name$n = \"@firebase/analytics\";\n\nconst name$m = \"@firebase/app-check-compat\";\n\nconst name$l = \"@firebase/app-check\";\n\nconst name$k = \"@firebase/auth\";\n\nconst name$j = \"@firebase/auth-compat\";\n\nconst name$i = \"@firebase/database\";\n\nconst name$h = \"@firebase/data-connect\";\n\nconst name$g = \"@firebase/database-compat\";\n\nconst name$f = \"@firebase/functions\";\n\nconst name$e = \"@firebase/functions-compat\";\n\nconst name$d = \"@firebase/installations\";\n\nconst name$c = \"@firebase/installations-compat\";\n\nconst name$b = \"@firebase/messaging\";\n\nconst name$a = \"@firebase/messaging-compat\";\n\nconst name$9 = \"@firebase/performance\";\n\nconst name$8 = \"@firebase/performance-compat\";\n\nconst name$7 = \"@firebase/remote-config\";\n\nconst name$6 = \"@firebase/remote-config-compat\";\n\nconst name$5 = \"@firebase/storage\";\n\nconst name$4 = \"@firebase/storage-compat\";\n\nconst name$3 = \"@firebase/firestore\";\n\nconst name$2 = \"@firebase/ai\";\n\nconst name$1 = \"@firebase/firestore-compat\";\n\nconst name = \"firebase\";\nconst version = \"11.8.0\";\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The default app name\n *\n * @internal\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\nconst PLATFORM_LOG_STRING = {\n    [name$q]: 'fire-core',\n    [name$p]: 'fire-core-compat',\n    [name$n]: 'fire-analytics',\n    [name$o]: 'fire-analytics-compat',\n    [name$l]: 'fire-app-check',\n    [name$m]: 'fire-app-check-compat',\n    [name$k]: 'fire-auth',\n    [name$j]: 'fire-auth-compat',\n    [name$i]: 'fire-rtdb',\n    [name$h]: 'fire-data-connect',\n    [name$g]: 'fire-rtdb-compat',\n    [name$f]: 'fire-fn',\n    [name$e]: 'fire-fn-compat',\n    [name$d]: 'fire-iid',\n    [name$c]: 'fire-iid-compat',\n    [name$b]: 'fire-fcm',\n    [name$a]: 'fire-fcm-compat',\n    [name$9]: 'fire-perf',\n    [name$8]: 'fire-perf-compat',\n    [name$7]: 'fire-rc',\n    [name$6]: 'fire-rc-compat',\n    [name$5]: 'fire-gcs',\n    [name$4]: 'fire-gcs-compat',\n    [name$3]: 'fire-fst',\n    [name$1]: 'fire-fst-compat',\n    [name$2]: 'fire-vertex',\n    'fire-js': 'fire-js', // Platform identifier for JS SDK.\n    [name]: 'fire-js-all'\n};\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * @internal\n */\nconst _apps = new Map();\n/**\n * @internal\n */\nconst _serverApps = new Map();\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst _components = new Map();\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nfunction _addComponent(app, component) {\n    try {\n        app.container.addComponent(component);\n    }\n    catch (e) {\n        logger.debug(`Component ${component.name} failed to register with FirebaseApp ${app.name}`, e);\n    }\n}\n/**\n *\n * @internal\n */\nfunction _addOrOverwriteComponent(app, component) {\n    app.container.addOrOverwriteComponent(component);\n}\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nfunction _registerComponent(component) {\n    const componentName = component.name;\n    if (_components.has(componentName)) {\n        logger.debug(`There were multiple attempts to register component ${componentName}.`);\n        return false;\n    }\n    _components.set(componentName, component);\n    // add the component to existing app instances\n    for (const app of _apps.values()) {\n        _addComponent(app, component);\n    }\n    for (const serverApp of _serverApps.values()) {\n        _addComponent(serverApp, component);\n    }\n    return true;\n}\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nfunction _getProvider(app, name) {\n    const heartbeatController = app.container\n        .getProvider('heartbeat')\n        .getImmediate({ optional: true });\n    if (heartbeatController) {\n        void heartbeatController.triggerHeartbeat();\n    }\n    return app.container.getProvider(name);\n}\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nfunction _removeServiceInstance(app, name, instanceIdentifier = DEFAULT_ENTRY_NAME) {\n    _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n/**\n *\n * @param obj - an object of type FirebaseApp or FirebaseOptions.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nfunction _isFirebaseApp(obj) {\n    return obj.options !== undefined;\n}\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nfunction _isFirebaseServerApp(obj) {\n    if (obj === null || obj === undefined) {\n        return false;\n    }\n    return obj.settings !== undefined;\n}\n/**\n * Test only\n *\n * @internal\n */\nfunction _clearComponents() {\n    _components.clear();\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst ERRORS = {\n    [\"no-app\" /* AppError.NO_APP */]: \"No Firebase App '{$appName}' has been created - \" +\n        'call initializeApp() first',\n    [\"bad-app-name\" /* AppError.BAD_APP_NAME */]: \"Illegal App name: '{$appName}'\",\n    [\"duplicate-app\" /* AppError.DUPLICATE_APP */]: \"Firebase App named '{$appName}' already exists with different options or config\",\n    [\"app-deleted\" /* AppError.APP_DELETED */]: \"Firebase App named '{$appName}' already deleted\",\n    [\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */]: 'Firebase Server App has been deleted',\n    [\"no-options\" /* AppError.NO_OPTIONS */]: 'Need to provide options, when not being deployed to hosting via source.',\n    [\"invalid-app-argument\" /* AppError.INVALID_APP_ARGUMENT */]: 'firebase.{$appName}() takes either no argument or a ' +\n        'Firebase App instance.',\n    [\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */]: 'First argument to `onLog` must be null or a function.',\n    [\"idb-open\" /* AppError.IDB_OPEN */]: 'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n    [\"idb-get\" /* AppError.IDB_GET */]: 'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n    [\"idb-set\" /* AppError.IDB_WRITE */]: 'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n    [\"idb-delete\" /* AppError.IDB_DELETE */]: 'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n    [\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */]: 'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n    [\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */]: 'FirebaseServerApp is not for use in browser environments.'\n};\nconst ERROR_FACTORY = new ErrorFactory('app', 'Firebase', ERRORS);\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nclass FirebaseAppImpl {\n    constructor(options, config, container) {\n        this._isDeleted = false;\n        this._options = Object.assign({}, options);\n        this._config = Object.assign({}, config);\n        this._name = config.name;\n        this._automaticDataCollectionEnabled =\n            config.automaticDataCollectionEnabled;\n        this._container = container;\n        this.container.addComponent(new Component('app', () => this, \"PUBLIC\" /* ComponentType.PUBLIC */));\n    }\n    get automaticDataCollectionEnabled() {\n        this.checkDestroyed();\n        return this._automaticDataCollectionEnabled;\n    }\n    set automaticDataCollectionEnabled(val) {\n        this.checkDestroyed();\n        this._automaticDataCollectionEnabled = val;\n    }\n    get name() {\n        this.checkDestroyed();\n        return this._name;\n    }\n    get options() {\n        this.checkDestroyed();\n        return this._options;\n    }\n    get config() {\n        this.checkDestroyed();\n        return this._config;\n    }\n    get container() {\n        return this._container;\n    }\n    get isDeleted() {\n        return this._isDeleted;\n    }\n    set isDeleted(val) {\n        this._isDeleted = val;\n    }\n    /**\n     * This function will throw an Error if the App has already been deleted -\n     * use before performing API actions on the App.\n     */\n    checkDestroyed() {\n        if (this.isDeleted) {\n            throw ERROR_FACTORY.create(\"app-deleted\" /* AppError.APP_DELETED */, { appName: this._name });\n        }\n    }\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Parse the token and check to see if the `exp` claim is in the future.\n// Reports an error to the console if the token or claim could not be parsed, or if `exp` is in\n// the past.\nfunction validateTokenTTL(base64Token, tokenName) {\n    const secondPart = base64Decode(base64Token.split('.')[1]);\n    if (secondPart === null) {\n        console.error(`FirebaseServerApp ${tokenName} is invalid: second part could not be parsed.`);\n        return;\n    }\n    const expClaim = JSON.parse(secondPart).exp;\n    if (expClaim === undefined) {\n        console.error(`FirebaseServerApp ${tokenName} is invalid: expiration claim could not be parsed`);\n        return;\n    }\n    const exp = JSON.parse(secondPart).exp * 1000;\n    const now = new Date().getTime();\n    const diff = exp - now;\n    if (diff <= 0) {\n        console.error(`FirebaseServerApp ${tokenName} is invalid: the token has expired.`);\n    }\n}\nclass FirebaseServerAppImpl extends FirebaseAppImpl {\n    constructor(options, serverConfig, name, container) {\n        // Build configuration parameters for the FirebaseAppImpl base class.\n        const automaticDataCollectionEnabled = serverConfig.automaticDataCollectionEnabled !== undefined\n            ? serverConfig.automaticDataCollectionEnabled\n            : true;\n        // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n        const config = {\n            name,\n            automaticDataCollectionEnabled\n        };\n        if (options.apiKey !== undefined) {\n            // Construct the parent FirebaseAppImp object.\n            super(options, config, container);\n        }\n        else {\n            const appImpl = options;\n            super(appImpl.options, config, container);\n        }\n        // Now construct the data for the FirebaseServerAppImpl.\n        this._serverConfig = Object.assign({ automaticDataCollectionEnabled }, serverConfig);\n        // Ensure that the current time is within the `authIdtoken` window of validity.\n        if (this._serverConfig.authIdToken) {\n            validateTokenTTL(this._serverConfig.authIdToken, 'authIdToken');\n        }\n        // Ensure that the current time is within the `appCheckToken` window of validity.\n        if (this._serverConfig.appCheckToken) {\n            validateTokenTTL(this._serverConfig.appCheckToken, 'appCheckToken');\n        }\n        this._finalizationRegistry = null;\n        if (typeof FinalizationRegistry !== 'undefined') {\n            this._finalizationRegistry = new FinalizationRegistry(() => {\n                this.automaticCleanup();\n            });\n        }\n        this._refCount = 0;\n        this.incRefCount(this._serverConfig.releaseOnDeref);\n        // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n        // will never trigger.\n        this._serverConfig.releaseOnDeref = undefined;\n        serverConfig.releaseOnDeref = undefined;\n        registerVersion(name$q, version$1, 'serverapp');\n    }\n    toJSON() {\n        return undefined;\n    }\n    get refCount() {\n        return this._refCount;\n    }\n    // Increment the reference count of this server app. If an object is provided, register it\n    // with the finalization registry.\n    incRefCount(obj) {\n        if (this.isDeleted) {\n            return;\n        }\n        this._refCount++;\n        if (obj !== undefined && this._finalizationRegistry !== null) {\n            this._finalizationRegistry.register(obj, this);\n        }\n    }\n    // Decrement the reference count.\n    decRefCount() {\n        if (this.isDeleted) {\n            return 0;\n        }\n        return --this._refCount;\n    }\n    // Invoked by the FinalizationRegistry callback to note that this app should go through its\n    // reference counts and delete itself if no reference count remain. The coordinating logic that\n    // handles this is in deleteApp(...).\n    automaticCleanup() {\n        void deleteApp(this);\n    }\n    get settings() {\n        this.checkDestroyed();\n        return this._serverConfig;\n    }\n    /**\n     * This function will throw an Error if the App has already been deleted -\n     * use before performing API actions on the App.\n     */\n    checkDestroyed() {\n        if (this.isDeleted) {\n            throw ERROR_FACTORY.create(\"server-app-deleted\" /* AppError.SERVER_APP_DELETED */);\n        }\n    }\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * The current SDK version.\n *\n * @public\n */\nconst SDK_VERSION = version;\nfunction initializeApp(_options, rawConfig = {}) {\n    let options = _options;\n    if (typeof rawConfig !== 'object') {\n        const name = rawConfig;\n        rawConfig = { name };\n    }\n    const config = Object.assign({ name: DEFAULT_ENTRY_NAME, automaticDataCollectionEnabled: true }, rawConfig);\n    const name = config.name;\n    if (typeof name !== 'string' || !name) {\n        throw ERROR_FACTORY.create(\"bad-app-name\" /* AppError.BAD_APP_NAME */, {\n            appName: String(name)\n        });\n    }\n    options || (options = getDefaultAppConfig());\n    if (!options) {\n        throw ERROR_FACTORY.create(\"no-options\" /* AppError.NO_OPTIONS */);\n    }\n    const existingApp = _apps.get(name);\n    if (existingApp) {\n        // return the existing app if options and config deep equal the ones in the existing app.\n        if (deepEqual(options, existingApp.options) &&\n            deepEqual(config, existingApp.config)) {\n            return existingApp;\n        }\n        else {\n            throw ERROR_FACTORY.create(\"duplicate-app\" /* AppError.DUPLICATE_APP */, { appName: name });\n        }\n    }\n    const container = new ComponentContainer(name);\n    for (const component of _components.values()) {\n        container.addComponent(component);\n    }\n    const newApp = new FirebaseAppImpl(options, config, container);\n    _apps.set(name, newApp);\n    return newApp;\n}\nfunction initializeServerApp(_options, _serverAppConfig) {\n    if (isBrowser() && !isWebWorker()) {\n        // FirebaseServerApp isn't designed to be run in browsers.\n        throw ERROR_FACTORY.create(\"invalid-server-app-environment\" /* AppError.INVALID_SERVER_APP_ENVIRONMENT */);\n    }\n    if (_serverAppConfig.automaticDataCollectionEnabled === undefined) {\n        _serverAppConfig.automaticDataCollectionEnabled = true;\n    }\n    let appOptions;\n    if (_isFirebaseApp(_options)) {\n        appOptions = _options.options;\n    }\n    else {\n        appOptions = _options;\n    }\n    // Build an app name based on a hash of the configuration options.\n    const nameObj = Object.assign(Object.assign({}, _serverAppConfig), appOptions);\n    // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n    // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n    if (nameObj.releaseOnDeref !== undefined) {\n        delete nameObj.releaseOnDeref;\n    }\n    const hashCode = (s) => {\n        return [...s].reduce((hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0, 0);\n    };\n    if (_serverAppConfig.releaseOnDeref !== undefined) {\n        if (typeof FinalizationRegistry === 'undefined') {\n            throw ERROR_FACTORY.create(\"finalization-registry-not-supported\" /* AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED */, {});\n        }\n    }\n    const nameString = '' + hashCode(JSON.stringify(nameObj));\n    const existingApp = _serverApps.get(nameString);\n    if (existingApp) {\n        existingApp.incRefCount(_serverAppConfig.releaseOnDeref);\n        return existingApp;\n    }\n    const container = new ComponentContainer(nameString);\n    for (const component of _components.values()) {\n        container.addComponent(component);\n    }\n    const newApp = new FirebaseServerAppImpl(appOptions, _serverAppConfig, nameString, container);\n    _serverApps.set(nameString, newApp);\n    return newApp;\n}\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nfunction getApp(name = DEFAULT_ENTRY_NAME) {\n    const app = _apps.get(name);\n    if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n        return initializeApp();\n    }\n    if (!app) {\n        throw ERROR_FACTORY.create(\"no-app\" /* AppError.NO_APP */, { appName: name });\n    }\n    return app;\n}\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nfunction getApps() {\n    return Array.from(_apps.values());\n}\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nasync function deleteApp(app) {\n    let cleanupProviders = false;\n    const name = app.name;\n    if (_apps.has(name)) {\n        cleanupProviders = true;\n        _apps.delete(name);\n    }\n    else if (_serverApps.has(name)) {\n        const firebaseServerApp = app;\n        if (firebaseServerApp.decRefCount() <= 0) {\n            _serverApps.delete(name);\n            cleanupProviders = true;\n        }\n    }\n    if (cleanupProviders) {\n        await Promise.all(app.container\n            .getProviders()\n            .map(provider => provider.delete()));\n        app.isDeleted = true;\n    }\n}\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nfunction registerVersion(libraryKeyOrName, version, variant) {\n    var _a;\n    // TODO: We can use this check to whitelist strings when/if we set up\n    // a good whitelist system.\n    let library = (_a = PLATFORM_LOG_STRING[libraryKeyOrName]) !== null && _a !== void 0 ? _a : libraryKeyOrName;\n    if (variant) {\n        library += `-${variant}`;\n    }\n    const libraryMismatch = library.match(/\\s|\\//);\n    const versionMismatch = version.match(/\\s|\\//);\n    if (libraryMismatch || versionMismatch) {\n        const warning = [\n            `Unable to register library \"${library}\" with version \"${version}\":`\n        ];\n        if (libraryMismatch) {\n            warning.push(`library name \"${library}\" contains illegal characters (whitespace or \"/\")`);\n        }\n        if (libraryMismatch && versionMismatch) {\n            warning.push('and');\n        }\n        if (versionMismatch) {\n            warning.push(`version name \"${version}\" contains illegal characters (whitespace or \"/\")`);\n        }\n        logger.warn(warning.join(' '));\n        return;\n    }\n    _registerComponent(new Component(`${library}-version`, () => ({ library, version }), \"VERSION\" /* ComponentType.VERSION */));\n}\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nfunction onLog(logCallback, options) {\n    if (logCallback !== null && typeof logCallback !== 'function') {\n        throw ERROR_FACTORY.create(\"invalid-log-argument\" /* AppError.INVALID_LOG_ARGUMENT */);\n    }\n    setUserLogHandler(logCallback, options);\n}\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nfunction setLogLevel(logLevel) {\n    setLogLevel$1(logLevel);\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\nlet dbPromise = null;\nfunction getDbPromise() {\n    if (!dbPromise) {\n        dbPromise = openDB(DB_NAME, DB_VERSION, {\n            upgrade: (db, oldVersion) => {\n                // We don't use 'break' in this switch statement, the fall-through\n                // behavior is what we want, because if there are multiple versions between\n                // the old version and the current version, we want ALL the migrations\n                // that correspond to those versions to run, not only the last one.\n                // eslint-disable-next-line default-case\n                switch (oldVersion) {\n                    case 0:\n                        try {\n                            db.createObjectStore(STORE_NAME);\n                        }\n                        catch (e) {\n                            // Safari/iOS browsers throw occasional exceptions on\n                            // db.createObjectStore() that may be a bug. Avoid blocking\n                            // the rest of the app functionality.\n                            console.warn(e);\n                        }\n                }\n            }\n        }).catch(e => {\n            throw ERROR_FACTORY.create(\"idb-open\" /* AppError.IDB_OPEN */, {\n                originalErrorMessage: e.message\n            });\n        });\n    }\n    return dbPromise;\n}\nasync function readHeartbeatsFromIndexedDB(app) {\n    try {\n        const db = await getDbPromise();\n        const tx = db.transaction(STORE_NAME);\n        const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n        // We already have the value but tx.done can throw,\n        // so we need to await it here to catch errors\n        await tx.done;\n        return result;\n    }\n    catch (e) {\n        if (e instanceof FirebaseError) {\n            logger.warn(e.message);\n        }\n        else {\n            const idbGetError = ERROR_FACTORY.create(\"idb-get\" /* AppError.IDB_GET */, {\n                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n            });\n            logger.warn(idbGetError.message);\n        }\n    }\n}\nasync function writeHeartbeatsToIndexedDB(app, heartbeatObject) {\n    try {\n        const db = await getDbPromise();\n        const tx = db.transaction(STORE_NAME, 'readwrite');\n        const objectStore = tx.objectStore(STORE_NAME);\n        await objectStore.put(heartbeatObject, computeKey(app));\n        await tx.done;\n    }\n    catch (e) {\n        if (e instanceof FirebaseError) {\n            logger.warn(e.message);\n        }\n        else {\n            const idbGetError = ERROR_FACTORY.create(\"idb-set\" /* AppError.IDB_WRITE */, {\n                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n            });\n            logger.warn(idbGetError.message);\n        }\n    }\n}\nfunction computeKey(app) {\n    return `${app.name}!${app.options.appId}`;\n}\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst MAX_HEADER_BYTES = 1024;\nconst MAX_NUM_STORED_HEARTBEATS = 30;\nclass HeartbeatServiceImpl {\n    constructor(container) {\n        this.container = container;\n        /**\n         * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n         * the header string.\n         * Stores one record per date. This will be consolidated into the standard\n         * format of one record per user agent string before being sent as a header.\n         * Populated from indexedDB when the controller is instantiated and should\n         * be kept in sync with indexedDB.\n         * Leave public for easier testing.\n         */\n        this._heartbeatsCache = null;\n        const app = this.container.getProvider('app').getImmediate();\n        this._storage = new HeartbeatStorageImpl(app);\n        this._heartbeatsCachePromise = this._storage.read().then(result => {\n            this._heartbeatsCache = result;\n            return result;\n        });\n    }\n    /**\n     * Called to report a heartbeat. The function will generate\n     * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n     * to IndexedDB.\n     * Note that we only store one heartbeat per day. So if a heartbeat for today is\n     * already logged, subsequent calls to this function in the same day will be ignored.\n     */\n    async triggerHeartbeat() {\n        var _a, _b;\n        try {\n            const platformLogger = this.container\n                .getProvider('platform-logger')\n                .getImmediate();\n            // This is the \"Firebase user agent\" string from the platform logger\n            // service, not the browser user agent.\n            const agent = platformLogger.getPlatformInfoString();\n            const date = getUTCDateString();\n            if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null) {\n                this._heartbeatsCache = await this._heartbeatsCachePromise;\n                // If we failed to construct a heartbeats cache, then return immediately.\n                if (((_b = this._heartbeatsCache) === null || _b === void 0 ? void 0 : _b.heartbeats) == null) {\n                    return;\n                }\n            }\n            // Do not store a heartbeat if one is already stored for this day\n            // or if a header has already been sent today.\n            if (this._heartbeatsCache.lastSentHeartbeatDate === date ||\n                this._heartbeatsCache.heartbeats.some(singleDateHeartbeat => singleDateHeartbeat.date === date)) {\n                return;\n            }\n            else {\n                // There is no entry for this date. Create one.\n                this._heartbeatsCache.heartbeats.push({ date, agent });\n                // If the number of stored heartbeats exceeds the maximum number of stored heartbeats, remove the heartbeat with the earliest date.\n                // Since this is executed each time a heartbeat is pushed, the limit can only be exceeded by one, so only one needs to be removed.\n                if (this._heartbeatsCache.heartbeats.length > MAX_NUM_STORED_HEARTBEATS) {\n                    const earliestHeartbeatIdx = getEarliestHeartbeatIdx(this._heartbeatsCache.heartbeats);\n                    this._heartbeatsCache.heartbeats.splice(earliestHeartbeatIdx, 1);\n                }\n            }\n            return this._storage.overwrite(this._heartbeatsCache);\n        }\n        catch (e) {\n            logger.warn(e);\n        }\n    }\n    /**\n     * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n     * It also clears all heartbeats from memory as well as in IndexedDB.\n     *\n     * NOTE: Consuming product SDKs should not send the header if this method\n     * returns an empty string.\n     */\n    async getHeartbeatsHeader() {\n        var _a;\n        try {\n            if (this._heartbeatsCache === null) {\n                await this._heartbeatsCachePromise;\n            }\n            // If it's still null or the array is empty, there is no data to send.\n            if (((_a = this._heartbeatsCache) === null || _a === void 0 ? void 0 : _a.heartbeats) == null ||\n                this._heartbeatsCache.heartbeats.length === 0) {\n                return '';\n            }\n            const date = getUTCDateString();\n            // Extract as many heartbeats from the cache as will fit under the size limit.\n            const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(this._heartbeatsCache.heartbeats);\n            const headerString = base64urlEncodeWithoutPadding(JSON.stringify({ version: 2, heartbeats: heartbeatsToSend }));\n            // Store last sent date to prevent another being logged/sent for the same day.\n            this._heartbeatsCache.lastSentHeartbeatDate = date;\n            if (unsentEntries.length > 0) {\n                // Store any unsent entries if they exist.\n                this._heartbeatsCache.heartbeats = unsentEntries;\n                // This seems more likely than emptying the array (below) to lead to some odd state\n                // since the cache isn't empty and this will be called again on the next request,\n                // and is probably safest if we await it.\n                await this._storage.overwrite(this._heartbeatsCache);\n            }\n            else {\n                this._heartbeatsCache.heartbeats = [];\n                // Do not wait for this, to reduce latency.\n                void this._storage.overwrite(this._heartbeatsCache);\n            }\n            return headerString;\n        }\n        catch (e) {\n            logger.warn(e);\n            return '';\n        }\n    }\n}\nfunction getUTCDateString() {\n    const today = new Date();\n    // Returns date format 'YYYY-MM-DD'\n    return today.toISOString().substring(0, 10);\n}\nfunction extractHeartbeatsForHeader(heartbeatsCache, maxSize = MAX_HEADER_BYTES) {\n    // Heartbeats grouped by user agent in the standard format to be sent in\n    // the header.\n    const heartbeatsToSend = [];\n    // Single date format heartbeats that are not sent.\n    let unsentEntries = heartbeatsCache.slice();\n    for (const singleDateHeartbeat of heartbeatsCache) {\n        // Look for an existing entry with the same user agent.\n        const heartbeatEntry = heartbeatsToSend.find(hb => hb.agent === singleDateHeartbeat.agent);\n        if (!heartbeatEntry) {\n            // If no entry for this user agent exists, create one.\n            heartbeatsToSend.push({\n                agent: singleDateHeartbeat.agent,\n                dates: [singleDateHeartbeat.date]\n            });\n            if (countBytes(heartbeatsToSend) > maxSize) {\n                // If the header would exceed max size, remove the added heartbeat\n                // entry and stop adding to the header.\n                heartbeatsToSend.pop();\n                break;\n            }\n        }\n        else {\n            heartbeatEntry.dates.push(singleDateHeartbeat.date);\n            // If the header would exceed max size, remove the added date\n            // and stop adding to the header.\n            if (countBytes(heartbeatsToSend) > maxSize) {\n                heartbeatEntry.dates.pop();\n                break;\n            }\n        }\n        // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n        // quota and the loop breaks early.)\n        unsentEntries = unsentEntries.slice(1);\n    }\n    return {\n        heartbeatsToSend,\n        unsentEntries\n    };\n}\nclass HeartbeatStorageImpl {\n    constructor(app) {\n        this.app = app;\n        this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n    }\n    async runIndexedDBEnvironmentCheck() {\n        if (!isIndexedDBAvailable()) {\n            return false;\n        }\n        else {\n            return validateIndexedDBOpenable()\n                .then(() => true)\n                .catch(() => false);\n        }\n    }\n    /**\n     * Read all heartbeats.\n     */\n    async read() {\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\n        if (!canUseIndexedDB) {\n            return { heartbeats: [] };\n        }\n        else {\n            const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n            if (idbHeartbeatObject === null || idbHeartbeatObject === void 0 ? void 0 : idbHeartbeatObject.heartbeats) {\n                return idbHeartbeatObject;\n            }\n            else {\n                return { heartbeats: [] };\n            }\n        }\n    }\n    // overwrite the storage with the provided heartbeats\n    async overwrite(heartbeatsObject) {\n        var _a;\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\n        if (!canUseIndexedDB) {\n            return;\n        }\n        else {\n            const existingHeartbeatsObject = await this.read();\n            return writeHeartbeatsToIndexedDB(this.app, {\n                lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n                heartbeats: heartbeatsObject.heartbeats\n            });\n        }\n    }\n    // add heartbeats\n    async add(heartbeatsObject) {\n        var _a;\n        const canUseIndexedDB = await this._canUseIndexedDBPromise;\n        if (!canUseIndexedDB) {\n            return;\n        }\n        else {\n            const existingHeartbeatsObject = await this.read();\n            return writeHeartbeatsToIndexedDB(this.app, {\n                lastSentHeartbeatDate: (_a = heartbeatsObject.lastSentHeartbeatDate) !== null && _a !== void 0 ? _a : existingHeartbeatsObject.lastSentHeartbeatDate,\n                heartbeats: [\n                    ...existingHeartbeatsObject.heartbeats,\n                    ...heartbeatsObject.heartbeats\n                ]\n            });\n        }\n    }\n}\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nfunction countBytes(heartbeatsCache) {\n    // base64 has a restricted set of characters, all of which should be 1 byte.\n    return base64urlEncodeWithoutPadding(\n    // heartbeatsCache wrapper properties\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })).length;\n}\n/**\n * Returns the index of the heartbeat with the earliest date.\n * If the heartbeats array is empty, -1 is returned.\n */\nfunction getEarliestHeartbeatIdx(heartbeats) {\n    if (heartbeats.length === 0) {\n        return -1;\n    }\n    let earliestHeartbeatIdx = 0;\n    let earliestHeartbeatDate = heartbeats[0].date;\n    for (let i = 1; i < heartbeats.length; i++) {\n        if (heartbeats[i].date < earliestHeartbeatDate) {\n            earliestHeartbeatDate = heartbeats[i].date;\n            earliestHeartbeatIdx = i;\n        }\n    }\n    return earliestHeartbeatIdx;\n}\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction registerCoreComponents(variant) {\n    _registerComponent(new Component('platform-logger', container => new PlatformLoggerServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n    _registerComponent(new Component('heartbeat', container => new HeartbeatServiceImpl(container), \"PRIVATE\" /* ComponentType.PRIVATE */));\n    // Register `app` package.\n    registerVersion(name$q, version$1, variant);\n    // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n    registerVersion(name$q, version$1, 'esm2017');\n    // Register platform SDK identifier (no version).\n    registerVersion('fire-js', '');\n}\n\n/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\nregisterCoreComponents('');\n\nexport { SDK_VERSION, DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME, _addComponent, _addOrOverwriteComponent, _apps, _clearComponents, _components, _getProvider, _isFirebaseApp, _isFirebaseServerApp, _registerComponent, _removeServiceInstance, _serverApps, deleteApp, getApp, getApps, initializeApp, initializeServerApp, onLog, registerVersion, setLogLevel };\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,kBAAkB,QAAQ,qBAAqB;AACnE,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,WAAW,IAAIC,aAAa,QAAQ,kBAAkB;AAC1F,SAASC,YAAY,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,WAAW,EAAEC,aAAa,EAAEC,6BAA6B,EAAEC,oBAAoB,EAAEC,yBAAyB,QAAQ,gBAAgB;AAClN,SAASH,aAAa,QAAQ,gBAAgB;AAC9C,SAASI,MAAM,QAAQ,KAAK;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA;EACA;EACAC,qBAAqBA,CAAA,EAAG;IACpB,MAAMC,SAAS,GAAG,IAAI,CAACF,SAAS,CAACG,YAAY,CAAC,CAAC;IAC/C;IACA;IACA,OAAOD,SAAS,CACXE,GAAG,CAACC,QAAQ,IAAI;MACjB,IAAIC,wBAAwB,CAACD,QAAQ,CAAC,EAAE;QACpC,MAAME,OAAO,GAAGF,QAAQ,CAACG,YAAY,CAAC,CAAC;QACvC,OAAQ,GAAED,OAAO,CAACE,OAAQ,IAAGF,OAAO,CAACG,OAAQ,EAAC;MAClD,CAAC,MACI;QACD,OAAO,IAAI;MACf;IACJ,CAAC,CAAC,CACGC,MAAM,CAACC,SAAS,IAAIA,SAAS,CAAC,CAC9BC,IAAI,CAAC,GAAG,CAAC;EAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,wBAAwBA,CAACD,QAAQ,EAAE;EACxC,MAAMS,SAAS,GAAGT,QAAQ,CAACU,YAAY,CAAC,CAAC;EACzC,OAAO,CAACD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,IAAI,MAAM,SAAS,CAAC;AAChG;;AAEA,MAAMC,MAAM,GAAG,eAAe;AAC9B,MAAMC,SAAS,GAAG,QAAQ;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,IAAIpC,MAAM,CAAC,eAAe,CAAC;AAE1C,MAAMqC,MAAM,GAAG,sBAAsB;AAErC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,gBAAgB;AAE/B,MAAMC,MAAM,GAAG,uBAAuB;AAEtC,MAAMC,MAAM,GAAG,oBAAoB;AAEnC,MAAMC,MAAM,GAAG,wBAAwB;AAEvC,MAAMC,MAAM,GAAG,2BAA2B;AAE1C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,yBAAyB;AAExC,MAAMC,MAAM,GAAG,gCAAgC;AAE/C,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,MAAM,GAAG,uBAAuB;AAEtC,MAAMC,MAAM,GAAG,8BAA8B;AAE7C,MAAMC,MAAM,GAAG,yBAAyB;AAExC,MAAMC,MAAM,GAAG,gCAAgC;AAE/C,MAAMC,MAAM,GAAG,mBAAmB;AAElC,MAAMC,MAAM,GAAG,0BAA0B;AAEzC,MAAMC,MAAM,GAAG,qBAAqB;AAEpC,MAAMC,MAAM,GAAG,cAAc;AAE7B,MAAMC,MAAM,GAAG,4BAA4B;AAE3C,MAAMC,IAAI,GAAG,UAAU;AACvB,MAAMnC,OAAO,GAAG,QAAQ;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,kBAAkB,GAAG,WAAW;AACtC,MAAMC,mBAAmB,GAAG;EACxB,CAAC9B,MAAM,GAAG,WAAW;EACrB,CAACG,MAAM,GAAG,kBAAkB;EAC5B,CAACE,MAAM,GAAG,gBAAgB;EAC1B,CAACD,MAAM,GAAG,uBAAuB;EACjC,CAACG,MAAM,GAAG,gBAAgB;EAC1B,CAACD,MAAM,GAAG,uBAAuB;EACjC,CAACE,MAAM,GAAG,WAAW;EACrB,CAACC,MAAM,GAAG,kBAAkB;EAC5B,CAACC,MAAM,GAAG,WAAW;EACrB,CAACC,MAAM,GAAG,mBAAmB;EAC7B,CAACC,MAAM,GAAG,kBAAkB;EAC5B,CAACC,MAAM,GAAG,SAAS;EACnB,CAACC,MAAM,GAAG,gBAAgB;EAC1B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACC,MAAM,GAAG,iBAAiB;EAC3B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACC,MAAM,GAAG,iBAAiB;EAC3B,CAACC,MAAM,GAAG,WAAW;EACrB,CAACC,MAAM,GAAG,kBAAkB;EAC5B,CAACC,MAAM,GAAG,SAAS;EACnB,CAACC,MAAM,GAAG,gBAAgB;EAC1B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACC,MAAM,GAAG,iBAAiB;EAC3B,CAACC,MAAM,GAAG,UAAU;EACpB,CAACE,MAAM,GAAG,iBAAiB;EAC3B,CAACD,MAAM,GAAG,aAAa;EACvB,SAAS,EAAE,SAAS;EAAE;EACtB,CAACE,IAAI,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;AACvB;AACA;AACA;AACA,MAAMC,WAAW,GAAG,IAAID,GAAG,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,WAAW,GAAG,IAAIF,GAAG,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA,SAASG,aAAaA,CAACC,GAAG,EAAEvC,SAAS,EAAE;EACnC,IAAI;IACAuC,GAAG,CAACrD,SAAS,CAACsD,YAAY,CAACxC,SAAS,CAAC;EACzC,CAAC,CACD,OAAOyC,CAAC,EAAE;IACNpC,MAAM,CAACqC,KAAK,CAAE,aAAY1C,SAAS,CAAC+B,IAAK,wCAAuCQ,GAAG,CAACR,IAAK,EAAC,EAAEU,CAAC,CAAC;EAClG;AACJ;AACA;AACA;AACA;AACA;AACA,SAASE,wBAAwBA,CAACJ,GAAG,EAAEvC,SAAS,EAAE;EAC9CuC,GAAG,CAACrD,SAAS,CAAC0D,uBAAuB,CAAC5C,SAAS,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6C,kBAAkBA,CAAC7C,SAAS,EAAE;EACnC,MAAM8C,aAAa,GAAG9C,SAAS,CAAC+B,IAAI;EACpC,IAAIM,WAAW,CAACU,GAAG,CAACD,aAAa,CAAC,EAAE;IAChCzC,MAAM,CAACqC,KAAK,CAAE,sDAAqDI,aAAc,GAAE,CAAC;IACpF,OAAO,KAAK;EAChB;EACAT,WAAW,CAACW,GAAG,CAACF,aAAa,EAAE9C,SAAS,CAAC;EACzC;EACA,KAAK,MAAMuC,GAAG,IAAIL,KAAK,CAACe,MAAM,CAAC,CAAC,EAAE;IAC9BX,aAAa,CAACC,GAAG,EAAEvC,SAAS,CAAC;EACjC;EACA,KAAK,MAAMkD,SAAS,IAAId,WAAW,CAACa,MAAM,CAAC,CAAC,EAAE;IAC1CX,aAAa,CAACY,SAAS,EAAElD,SAAS,CAAC;EACvC;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmD,YAAYA,CAACZ,GAAG,EAAER,IAAI,EAAE;EAC7B,MAAMqB,mBAAmB,GAAGb,GAAG,CAACrD,SAAS,CACpCmE,WAAW,CAAC,WAAW,CAAC,CACxB3D,YAAY,CAAC;IAAE4D,QAAQ,EAAE;EAAK,CAAC,CAAC;EACrC,IAAIF,mBAAmB,EAAE;IACrB,KAAKA,mBAAmB,CAACG,gBAAgB,CAAC,CAAC;EAC/C;EACA,OAAOhB,GAAG,CAACrD,SAAS,CAACmE,WAAW,CAACtB,IAAI,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyB,sBAAsBA,CAACjB,GAAG,EAAER,IAAI,EAAE0B,kBAAkB,GAAGzB,kBAAkB,EAAE;EAChFmB,YAAY,CAACZ,GAAG,EAAER,IAAI,CAAC,CAAC2B,aAAa,CAACD,kBAAkB,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,cAAcA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACC,OAAO,KAAKC,SAAS;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACH,GAAG,EAAE;EAC/B,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE;IACnC,OAAO,KAAK;EAChB;EACA,OAAOF,GAAG,CAACI,QAAQ,KAAKF,SAAS;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,gBAAgBA,CAAA,EAAG;EACxB5B,WAAW,CAAC6B,KAAK,CAAC,CAAC;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG;EACX,CAAC,QAAQ,CAAC,wBAAwB,kDAAkD,GAChF,4BAA4B;EAChC,CAAC,cAAc,CAAC,8BAA8B,gCAAgC;EAC9E,CAAC,eAAe,CAAC,+BAA+B,iFAAiF;EACjI,CAAC,aAAa,CAAC,6BAA6B,iDAAiD;EAC7F,CAAC,oBAAoB,CAAC,oCAAoC,sCAAsC;EAChG,CAAC,YAAY,CAAC,4BAA4B,yEAAyE;EACnH,CAAC,sBAAsB,CAAC,sCAAsC,sDAAsD,GAChH,wBAAwB;EAC5B,CAAC,sBAAsB,CAAC,sCAAsC,uDAAuD;EACrH,CAAC,UAAU,CAAC,0BAA0B,+EAA+E;EACrH,CAAC,SAAS,CAAC,yBAAyB,oFAAoF;EACxH,CAAC,SAAS,CAAC,2BAA2B,kFAAkF;EACxH,CAAC,YAAY,CAAC,4BAA4B,qFAAqF;EAC/H,CAAC,qCAAqC,CAAC,qDAAqD,yGAAyG;EACrM,CAAC,gCAAgC,CAAC,gDAAgD;AACtF,CAAC;AACD,MAAMC,aAAa,GAAG,IAAI/F,YAAY,CAAC,KAAK,EAAE,UAAU,EAAE8F,MAAM,CAAC;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,eAAe,CAAC;EAClBpF,WAAWA,CAAC4E,OAAO,EAAES,MAAM,EAAEpF,SAAS,EAAE;IACpC,IAAI,CAACqF,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEb,OAAO,CAAC;IAC1C,IAAI,CAACc,OAAO,GAAGF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,MAAM,CAAC;IACxC,IAAI,CAACM,KAAK,GAAGN,MAAM,CAACvC,IAAI;IACxB,IAAI,CAAC8C,+BAA+B,GAChCP,MAAM,CAACQ,8BAA8B;IACzC,IAAI,CAACC,UAAU,GAAG7F,SAAS;IAC3B,IAAI,CAACA,SAAS,CAACsD,YAAY,CAAC,IAAIzE,SAAS,CAAC,KAAK,EAAE,MAAM,IAAI,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EACtG;;EACA,IAAI+G,8BAA8BA,CAAA,EAAG;IACjC,IAAI,CAACE,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACH,+BAA+B;EAC/C;EACA,IAAIC,8BAA8BA,CAACG,GAAG,EAAE;IACpC,IAAI,CAACD,cAAc,CAAC,CAAC;IACrB,IAAI,CAACH,+BAA+B,GAAGI,GAAG;EAC9C;EACA,IAAIlD,IAAIA,CAAA,EAAG;IACP,IAAI,CAACiD,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACJ,KAAK;EACrB;EACA,IAAIf,OAAOA,CAAA,EAAG;IACV,IAAI,CAACmB,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACR,QAAQ;EACxB;EACA,IAAIF,MAAMA,CAAA,EAAG;IACT,IAAI,CAACU,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACL,OAAO;EACvB;EACA,IAAIzF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC6F,UAAU;EAC1B;EACA,IAAIG,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACX,UAAU;EAC1B;EACA,IAAIW,SAASA,CAACD,GAAG,EAAE;IACf,IAAI,CAACV,UAAU,GAAGU,GAAG;EACzB;EACA;AACJ;AACA;AACA;EACID,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACE,SAAS,EAAE;MAChB,MAAMd,aAAa,CAACe,MAAM,CAAC,aAAa,CAAC,4BAA4B;QAAEC,OAAO,EAAE,IAAI,CAACR;MAAM,CAAC,CAAC;IACjG;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,gBAAgBA,CAACC,WAAW,EAAEC,SAAS,EAAE;EAC9C,MAAMC,UAAU,GAAGlH,YAAY,CAACgH,WAAW,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1D,IAAID,UAAU,KAAK,IAAI,EAAE;IACrBE,OAAO,CAACC,KAAK,CAAE,qBAAoBJ,SAAU,+CAA8C,CAAC;IAC5F;EACJ;EACA,MAAMK,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACN,UAAU,CAAC,CAACO,GAAG;EAC3C,IAAIH,QAAQ,KAAK9B,SAAS,EAAE;IACxB4B,OAAO,CAACC,KAAK,CAAE,qBAAoBJ,SAAU,mDAAkD,CAAC;IAChG;EACJ;EACA,MAAMQ,GAAG,GAAGF,IAAI,CAACC,KAAK,CAACN,UAAU,CAAC,CAACO,GAAG,GAAG,IAAI;EAC7C,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAChC,MAAMC,IAAI,GAAGJ,GAAG,GAAGC,GAAG;EACtB,IAAIG,IAAI,IAAI,CAAC,EAAE;IACXT,OAAO,CAACC,KAAK,CAAE,qBAAoBJ,SAAU,qCAAoC,CAAC;EACtF;AACJ;AACA,MAAMa,qBAAqB,SAAS/B,eAAe,CAAC;EAChDpF,WAAWA,CAAC4E,OAAO,EAAEwC,YAAY,EAAEtE,IAAI,EAAE7C,SAAS,EAAE;IAChD;IACA,MAAM4F,8BAA8B,GAAGuB,YAAY,CAACvB,8BAA8B,KAAKhB,SAAS,GAC1FuC,YAAY,CAACvB,8BAA8B,GAC3C,IAAI;IACV;IACA,MAAMR,MAAM,GAAG;MACXvC,IAAI;MACJ+C;IACJ,CAAC;IACD,IAAIjB,OAAO,CAACyC,MAAM,KAAKxC,SAAS,EAAE;MAC9B;MACA,KAAK,CAACD,OAAO,EAAES,MAAM,EAAEpF,SAAS,CAAC;IACrC,CAAC,MACI;MACD,MAAMqH,OAAO,GAAG1C,OAAO;MACvB,KAAK,CAAC0C,OAAO,CAAC1C,OAAO,EAAES,MAAM,EAAEpF,SAAS,CAAC;IAC7C;IACA;IACA,IAAI,CAACsH,aAAa,GAAG/B,MAAM,CAACC,MAAM,CAAC;MAAEI;IAA+B,CAAC,EAAEuB,YAAY,CAAC;IACpF;IACA,IAAI,IAAI,CAACG,aAAa,CAACC,WAAW,EAAE;MAChCpB,gBAAgB,CAAC,IAAI,CAACmB,aAAa,CAACC,WAAW,EAAE,aAAa,CAAC;IACnE;IACA;IACA,IAAI,IAAI,CAACD,aAAa,CAACE,aAAa,EAAE;MAClCrB,gBAAgB,CAAC,IAAI,CAACmB,aAAa,CAACE,aAAa,EAAE,eAAe,CAAC;IACvE;IACA,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,OAAOC,oBAAoB,KAAK,WAAW,EAAE;MAC7C,IAAI,CAACD,qBAAqB,GAAG,IAAIC,oBAAoB,CAAC,MAAM;QACxD,IAAI,CAACC,gBAAgB,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN;IACA,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,WAAW,CAAC,IAAI,CAACP,aAAa,CAACQ,cAAc,CAAC;IACnD;IACA;IACA,IAAI,CAACR,aAAa,CAACQ,cAAc,GAAGlD,SAAS;IAC7CuC,YAAY,CAACW,cAAc,GAAGlD,SAAS;IACvCmD,eAAe,CAAC9G,MAAM,EAAEC,SAAS,EAAE,WAAW,CAAC;EACnD;EACA8G,MAAMA,CAAA,EAAG;IACL,OAAOpD,SAAS;EACpB;EACA,IAAIqD,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACL,SAAS;EACzB;EACA;EACA;EACAC,WAAWA,CAACnD,GAAG,EAAE;IACb,IAAI,IAAI,CAACsB,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAAC4B,SAAS,EAAE;IAChB,IAAIlD,GAAG,KAAKE,SAAS,IAAI,IAAI,CAAC6C,qBAAqB,KAAK,IAAI,EAAE;MAC1D,IAAI,CAACA,qBAAqB,CAACS,QAAQ,CAACxD,GAAG,EAAE,IAAI,CAAC;IAClD;EACJ;EACA;EACAyD,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACnC,SAAS,EAAE;MAChB,OAAO,CAAC;IACZ;IACA,OAAO,EAAE,IAAI,CAAC4B,SAAS;EAC3B;EACA;EACA;EACA;EACAD,gBAAgBA,CAAA,EAAG;IACf,KAAKS,SAAS,CAAC,IAAI,CAAC;EACxB;EACA,IAAItD,QAAQA,CAAA,EAAG;IACX,IAAI,CAACgB,cAAc,CAAC,CAAC;IACrB,OAAO,IAAI,CAACwB,aAAa;EAC7B;EACA;AACJ;AACA;AACA;EACIxB,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACE,SAAS,EAAE;MAChB,MAAMd,aAAa,CAACe,MAAM,CAAC,oBAAoB,CAAC,iCAAiC,CAAC;IACtF;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoC,WAAW,GAAG3H,OAAO;AAC3B,SAAS4H,aAAaA,CAAChD,QAAQ,EAAEiD,SAAS,GAAG,CAAC,CAAC,EAAE;EAC7C,IAAI5D,OAAO,GAAGW,QAAQ;EACtB,IAAI,OAAOiD,SAAS,KAAK,QAAQ,EAAE;IAC/B,MAAM1F,IAAI,GAAG0F,SAAS;IACtBA,SAAS,GAAG;MAAE1F;IAAK,CAAC;EACxB;EACA,MAAMuC,MAAM,GAAGG,MAAM,CAACC,MAAM,CAAC;IAAE3C,IAAI,EAAEC,kBAAkB;IAAE8C,8BAA8B,EAAE;EAAK,CAAC,EAAE2C,SAAS,CAAC;EAC3G,MAAM1F,IAAI,GAAGuC,MAAM,CAACvC,IAAI;EACxB,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAE;IACnC,MAAMqC,aAAa,CAACe,MAAM,CAAC,cAAc,CAAC,6BAA6B;MACnEC,OAAO,EAAEsC,MAAM,CAAC3F,IAAI;IACxB,CAAC,CAAC;EACN;EACA8B,OAAO,KAAKA,OAAO,GAAGtF,mBAAmB,CAAC,CAAC,CAAC;EAC5C,IAAI,CAACsF,OAAO,EAAE;IACV,MAAMO,aAAa,CAACe,MAAM,CAAC,YAAY,CAAC,yBAAyB,CAAC;EACtE;;EACA,MAAMwC,WAAW,GAAGzF,KAAK,CAAC0F,GAAG,CAAC7F,IAAI,CAAC;EACnC,IAAI4F,WAAW,EAAE;IACb;IACA,IAAInJ,SAAS,CAACqF,OAAO,EAAE8D,WAAW,CAAC9D,OAAO,CAAC,IACvCrF,SAAS,CAAC8F,MAAM,EAAEqD,WAAW,CAACrD,MAAM,CAAC,EAAE;MACvC,OAAOqD,WAAW;IACtB,CAAC,MACI;MACD,MAAMvD,aAAa,CAACe,MAAM,CAAC,eAAe,CAAC,8BAA8B;QAAEC,OAAO,EAAErD;MAAK,CAAC,CAAC;IAC/F;EACJ;EACA,MAAM7C,SAAS,GAAG,IAAIlB,kBAAkB,CAAC+D,IAAI,CAAC;EAC9C,KAAK,MAAM/B,SAAS,IAAIqC,WAAW,CAACY,MAAM,CAAC,CAAC,EAAE;IAC1C/D,SAAS,CAACsD,YAAY,CAACxC,SAAS,CAAC;EACrC;EACA,MAAM6H,MAAM,GAAG,IAAIxD,eAAe,CAACR,OAAO,EAAES,MAAM,EAAEpF,SAAS,CAAC;EAC9DgD,KAAK,CAACc,GAAG,CAACjB,IAAI,EAAE8F,MAAM,CAAC;EACvB,OAAOA,MAAM;AACjB;AACA,SAASC,mBAAmBA,CAACtD,QAAQ,EAAEuD,gBAAgB,EAAE;EACrD,IAAItJ,SAAS,CAAC,CAAC,IAAI,CAACC,WAAW,CAAC,CAAC,EAAE;IAC/B;IACA,MAAM0F,aAAa,CAACe,MAAM,CAAC,gCAAgC,CAAC,6CAA6C,CAAC;EAC9G;;EACA,IAAI4C,gBAAgB,CAACjD,8BAA8B,KAAKhB,SAAS,EAAE;IAC/DiE,gBAAgB,CAACjD,8BAA8B,GAAG,IAAI;EAC1D;EACA,IAAIkD,UAAU;EACd,IAAIrE,cAAc,CAACa,QAAQ,CAAC,EAAE;IAC1BwD,UAAU,GAAGxD,QAAQ,CAACX,OAAO;EACjC,CAAC,MACI;IACDmE,UAAU,GAAGxD,QAAQ;EACzB;EACA;EACA,MAAMyD,OAAO,GAAGxD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEqD,gBAAgB,CAAC,EAAEC,UAAU,CAAC;EAC9E;EACA;EACA,IAAIC,OAAO,CAACjB,cAAc,KAAKlD,SAAS,EAAE;IACtC,OAAOmE,OAAO,CAACjB,cAAc;EACjC;EACA,MAAMkB,QAAQ,GAAIC,CAAC,IAAK;IACpB,OAAO,CAAC,GAAGA,CAAC,CAAC,CAACC,MAAM,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAMC,IAAI,CAACC,IAAI,CAAC,EAAE,EAAEH,IAAI,CAAC,GAAGC,CAAC,CAACG,UAAU,CAAC,CAAC,CAAC,GAAI,CAAC,EAAE,CAAC,CAAC;EACrF,CAAC;EACD,IAAIV,gBAAgB,CAACf,cAAc,KAAKlD,SAAS,EAAE;IAC/C,IAAI,OAAO8C,oBAAoB,KAAK,WAAW,EAAE;MAC7C,MAAMxC,aAAa,CAACe,MAAM,CAAC,qCAAqC,CAAC,oDAAoD,CAAC,CAAC,CAAC;IAC5H;EACJ;EACA,MAAMuD,UAAU,GAAG,EAAE,GAAGR,QAAQ,CAACrC,IAAI,CAAC8C,SAAS,CAACV,OAAO,CAAC,CAAC;EACzD,MAAMN,WAAW,GAAGvF,WAAW,CAACwF,GAAG,CAACc,UAAU,CAAC;EAC/C,IAAIf,WAAW,EAAE;IACbA,WAAW,CAACZ,WAAW,CAACgB,gBAAgB,CAACf,cAAc,CAAC;IACxD,OAAOW,WAAW;EACtB;EACA,MAAMzI,SAAS,GAAG,IAAIlB,kBAAkB,CAAC0K,UAAU,CAAC;EACpD,KAAK,MAAM1I,SAAS,IAAIqC,WAAW,CAACY,MAAM,CAAC,CAAC,EAAE;IAC1C/D,SAAS,CAACsD,YAAY,CAACxC,SAAS,CAAC;EACrC;EACA,MAAM6H,MAAM,GAAG,IAAIzB,qBAAqB,CAAC4B,UAAU,EAAED,gBAAgB,EAAEW,UAAU,EAAExJ,SAAS,CAAC;EAC7FkD,WAAW,CAACY,GAAG,CAAC0F,UAAU,EAAEb,MAAM,CAAC;EACnC,OAAOA,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,MAAMA,CAAC7G,IAAI,GAAGC,kBAAkB,EAAE;EACvC,MAAMO,GAAG,GAAGL,KAAK,CAAC0F,GAAG,CAAC7F,IAAI,CAAC;EAC3B,IAAI,CAACQ,GAAG,IAAIR,IAAI,KAAKC,kBAAkB,IAAIzD,mBAAmB,CAAC,CAAC,EAAE;IAC9D,OAAOiJ,aAAa,CAAC,CAAC;EAC1B;EACA,IAAI,CAACjF,GAAG,EAAE;IACN,MAAM6B,aAAa,CAACe,MAAM,CAAC,QAAQ,CAAC,uBAAuB;MAAEC,OAAO,EAAErD;IAAK,CAAC,CAAC;EACjF;EACA,OAAOQ,GAAG;AACd;AACA;AACA;AACA;AACA;AACA,SAASsG,OAAOA,CAAA,EAAG;EACf,OAAOC,KAAK,CAACC,IAAI,CAAC7G,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,SAiBeqE,SAASA,CAAA0B,EAAA;EAAA,OAAAC,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAqBxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAAF,WAAA;EAAAA,UAAA,GAAAG,iBAAA,CArBA,WAAyB7G,GAAG,EAAE;IAC1B,IAAI8G,gBAAgB,GAAG,KAAK;IAC5B,MAAMtH,IAAI,GAAGQ,GAAG,CAACR,IAAI;IACrB,IAAIG,KAAK,CAACa,GAAG,CAAChB,IAAI,CAAC,EAAE;MACjBsH,gBAAgB,GAAG,IAAI;MACvBnH,KAAK,CAACoH,MAAM,CAACvH,IAAI,CAAC;IACtB,CAAC,MACI,IAAIK,WAAW,CAACW,GAAG,CAAChB,IAAI,CAAC,EAAE;MAC5B,MAAMwH,iBAAiB,GAAGhH,GAAG;MAC7B,IAAIgH,iBAAiB,CAAClC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE;QACtCjF,WAAW,CAACkH,MAAM,CAACvH,IAAI,CAAC;QACxBsH,gBAAgB,GAAG,IAAI;MAC3B;IACJ;IACA,IAAIA,gBAAgB,EAAE;MAClB,MAAMG,OAAO,CAACC,GAAG,CAAClH,GAAG,CAACrD,SAAS,CAC1BG,YAAY,CAAC,CAAC,CACdC,GAAG,CAACC,QAAQ,IAAIA,QAAQ,CAAC+J,MAAM,CAAC,CAAC,CAAC,CAAC;MACxC/G,GAAG,CAAC2C,SAAS,GAAG,IAAI;IACxB;EACJ,CAAC;EAAA,OAAA+D,UAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AASD,SAASlC,eAAeA,CAACyC,gBAAgB,EAAE9J,OAAO,EAAE+J,OAAO,EAAE;EACzD,IAAIC,EAAE;EACN;EACA;EACA,IAAIjK,OAAO,GAAG,CAACiK,EAAE,GAAG3H,mBAAmB,CAACyH,gBAAgB,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGF,gBAAgB;EAC5G,IAAIC,OAAO,EAAE;IACThK,OAAO,IAAK,IAAGgK,OAAQ,EAAC;EAC5B;EACA,MAAME,eAAe,GAAGlK,OAAO,CAACmK,KAAK,CAAC,OAAO,CAAC;EAC9C,MAAMC,eAAe,GAAGnK,OAAO,CAACkK,KAAK,CAAC,OAAO,CAAC;EAC9C,IAAID,eAAe,IAAIE,eAAe,EAAE;IACpC,MAAMC,OAAO,GAAG,CACX,+BAA8BrK,OAAQ,mBAAkBC,OAAQ,IAAG,CACvE;IACD,IAAIiK,eAAe,EAAE;MACjBG,OAAO,CAACC,IAAI,CAAE,iBAAgBtK,OAAQ,mDAAkD,CAAC;IAC7F;IACA,IAAIkK,eAAe,IAAIE,eAAe,EAAE;MACpCC,OAAO,CAACC,IAAI,CAAC,KAAK,CAAC;IACvB;IACA,IAAIF,eAAe,EAAE;MACjBC,OAAO,CAACC,IAAI,CAAE,iBAAgBrK,OAAQ,mDAAkD,CAAC;IAC7F;IACAS,MAAM,CAAC6J,IAAI,CAACF,OAAO,CAACjK,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9B;EACJ;EACA8C,kBAAkB,CAAC,IAAI9E,SAAS,CAAE,GAAE4B,OAAQ,UAAS,EAAE,OAAO;IAAEA,OAAO;IAAEC;EAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;AAChI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuK,KAAKA,CAACC,WAAW,EAAEvG,OAAO,EAAE;EACjC,IAAIuG,WAAW,KAAK,IAAI,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;IAC3D,MAAMhG,aAAa,CAACe,MAAM,CAAC,sBAAsB,CAAC,mCAAmC,CAAC;EAC1F;;EACAjH,iBAAiB,CAACkM,WAAW,EAAEvG,OAAO,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1F,WAAWA,CAACkM,QAAQ,EAAE;EAC3BjM,aAAa,CAACiM,QAAQ,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,6BAA6B;AAC7C,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,UAAU,GAAG,0BAA0B;AAC7C,IAAIC,SAAS,GAAG,IAAI;AACpB,SAASC,YAAYA,CAAA,EAAG;EACpB,IAAI,CAACD,SAAS,EAAE;IACZA,SAAS,GAAG1L,MAAM,CAACuL,OAAO,EAAEC,UAAU,EAAE;MACpCI,OAAO,EAAEA,CAACC,EAAE,EAAEC,UAAU,KAAK;QACzB;QACA;QACA;QACA;QACA;QACA,QAAQA,UAAU;UACd,KAAK,CAAC;YACF,IAAI;cACAD,EAAE,CAACE,iBAAiB,CAACN,UAAU,CAAC;YACpC,CAAC,CACD,OAAO/H,CAAC,EAAE;cACN;cACA;cACA;cACAiD,OAAO,CAACwE,IAAI,CAACzH,CAAC,CAAC;YACnB;QACR;MACJ;IACJ,CAAC,CAAC,CAACsI,KAAK,CAACtI,CAAC,IAAI;MACV,MAAM2B,aAAa,CAACe,MAAM,CAAC,UAAU,CAAC,yBAAyB;QAC3D6F,oBAAoB,EAAEvI,CAAC,CAACwI;MAC5B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA,OAAOR,SAAS;AACpB;AAAC,SACcS,2BAA2BA,CAAAC,GAAA;EAAA,OAAAC,4BAAA,CAAAlC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAiC,6BAAA;EAAAA,4BAAA,GAAAhC,iBAAA,CAA1C,WAA2C7G,GAAG,EAAE;IAC5C,IAAI;MACA,MAAMqI,EAAE,SAASF,YAAY,CAAC,CAAC;MAC/B,MAAMW,EAAE,GAAGT,EAAE,CAACU,WAAW,CAACd,UAAU,CAAC;MACrC,MAAMe,MAAM,SAASF,EAAE,CAACG,WAAW,CAAChB,UAAU,CAAC,CAAC5C,GAAG,CAAC6D,UAAU,CAAClJ,GAAG,CAAC,CAAC;MACpE;MACA;MACA,MAAM8I,EAAE,CAACK,IAAI;MACb,OAAOH,MAAM;IACjB,CAAC,CACD,OAAO9I,CAAC,EAAE;MACN,IAAIA,CAAC,YAAY9D,aAAa,EAAE;QAC5B0B,MAAM,CAAC6J,IAAI,CAACzH,CAAC,CAACwI,OAAO,CAAC;MAC1B,CAAC,MACI;QACD,MAAMU,WAAW,GAAGvH,aAAa,CAACe,MAAM,CAAC,SAAS,CAAC,wBAAwB;UACvE6F,oBAAoB,EAAEvI,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACwI;QAClE,CAAC,CAAC;QACF5K,MAAM,CAAC6J,IAAI,CAACyB,WAAW,CAACV,OAAO,CAAC;MACpC;IACJ;EACJ,CAAC;EAAA,OAAAG,4BAAA,CAAAlC,KAAA,OAAAC,SAAA;AAAA;AAAA,SACcyC,0BAA0BA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,2BAAA,CAAA7C,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA4C,4BAAA;EAAAA,2BAAA,GAAA3C,iBAAA,CAAzC,WAA0C7G,GAAG,EAAEyJ,eAAe,EAAE;IAC5D,IAAI;MACA,MAAMpB,EAAE,SAASF,YAAY,CAAC,CAAC;MAC/B,MAAMW,EAAE,GAAGT,EAAE,CAACU,WAAW,CAACd,UAAU,EAAE,WAAW,CAAC;MAClD,MAAMgB,WAAW,GAAGH,EAAE,CAACG,WAAW,CAAChB,UAAU,CAAC;MAC9C,MAAMgB,WAAW,CAACS,GAAG,CAACD,eAAe,EAAEP,UAAU,CAAClJ,GAAG,CAAC,CAAC;MACvD,MAAM8I,EAAE,CAACK,IAAI;IACjB,CAAC,CACD,OAAOjJ,CAAC,EAAE;MACN,IAAIA,CAAC,YAAY9D,aAAa,EAAE;QAC5B0B,MAAM,CAAC6J,IAAI,CAACzH,CAAC,CAACwI,OAAO,CAAC;MAC1B,CAAC,MACI;QACD,MAAMU,WAAW,GAAGvH,aAAa,CAACe,MAAM,CAAC,SAAS,CAAC,0BAA0B;UACzE6F,oBAAoB,EAAEvI,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACwI;QAClE,CAAC,CAAC;QACF5K,MAAM,CAAC6J,IAAI,CAACyB,WAAW,CAACV,OAAO,CAAC;MACpC;IACJ;EACJ,CAAC;EAAA,OAAAc,2BAAA,CAAA7C,KAAA,OAAAC,SAAA;AAAA;AACD,SAASsC,UAAUA,CAAClJ,GAAG,EAAE;EACrB,OAAQ,GAAEA,GAAG,CAACR,IAAK,IAAGQ,GAAG,CAACsB,OAAO,CAACqI,KAAM,EAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,IAAI;AAC7B,MAAMC,yBAAyB,GAAG,EAAE;AACpC,MAAMC,oBAAoB,CAAC;EACvBpN,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoN,gBAAgB,GAAG,IAAI;IAC5B,MAAM/J,GAAG,GAAG,IAAI,CAACrD,SAAS,CAACmE,WAAW,CAAC,KAAK,CAAC,CAAC3D,YAAY,CAAC,CAAC;IAC5D,IAAI,CAAC6M,QAAQ,GAAG,IAAIC,oBAAoB,CAACjK,GAAG,CAAC;IAC7C,IAAI,CAACkK,uBAAuB,GAAG,IAAI,CAACF,QAAQ,CAACG,IAAI,CAAC,CAAC,CAACC,IAAI,CAACpB,MAAM,IAAI;MAC/D,IAAI,CAACe,gBAAgB,GAAGf,MAAM;MAC9B,OAAOA,MAAM;IACjB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUhI,gBAAgBA,CAAA,EAAG;IAAA,IAAAqJ,KAAA;IAAA,OAAAxD,iBAAA;MACrB,IAAIQ,EAAE,EAAEiD,EAAE;MACV,IAAI;QACA,MAAMC,cAAc,GAAGF,KAAI,CAAC1N,SAAS,CAChCmE,WAAW,CAAC,iBAAiB,CAAC,CAC9B3D,YAAY,CAAC,CAAC;QACnB;QACA;QACA,MAAMqN,KAAK,GAAGD,cAAc,CAAC3N,qBAAqB,CAAC,CAAC;QACpD,MAAM6N,IAAI,GAAGC,gBAAgB,CAAC,CAAC;QAC/B,IAAI,CAAC,CAACrD,EAAE,GAAGgD,KAAI,CAACN,gBAAgB,MAAM,IAAI,IAAI1C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsD,UAAU,KAAK,IAAI,EAAE;UAC3FN,KAAI,CAACN,gBAAgB,SAASM,KAAI,CAACH,uBAAuB;UAC1D;UACA,IAAI,CAAC,CAACI,EAAE,GAAGD,KAAI,CAACN,gBAAgB,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACK,UAAU,KAAK,IAAI,EAAE;YAC3F;UACJ;QACJ;QACA;QACA;QACA,IAAIN,KAAI,CAACN,gBAAgB,CAACa,qBAAqB,KAAKH,IAAI,IACpDJ,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAACE,IAAI,CAACC,mBAAmB,IAAIA,mBAAmB,CAACL,IAAI,KAAKA,IAAI,CAAC,EAAE;UACjG;QACJ,CAAC,MACI;UACD;UACAJ,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAACjD,IAAI,CAAC;YAAE+C,IAAI;YAAED;UAAM,CAAC,CAAC;UACtD;UACA;UACA,IAAIH,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAACI,MAAM,GAAGlB,yBAAyB,EAAE;YACrE,MAAMmB,oBAAoB,GAAGC,uBAAuB,CAACZ,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAAC;YACtFN,KAAI,CAACN,gBAAgB,CAACY,UAAU,CAACO,MAAM,CAACF,oBAAoB,EAAE,CAAC,CAAC;UACpE;QACJ;QACA,OAAOX,KAAI,CAACL,QAAQ,CAACmB,SAAS,CAACd,KAAI,CAACN,gBAAgB,CAAC;MACzD,CAAC,CACD,OAAO7J,CAAC,EAAE;QACNpC,MAAM,CAAC6J,IAAI,CAACzH,CAAC,CAAC;MAClB;IAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUkL,mBAAmBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAxE,iBAAA;MACxB,IAAIQ,EAAE;MACN,IAAI;QACA,IAAIgE,MAAI,CAACtB,gBAAgB,KAAK,IAAI,EAAE;UAChC,MAAMsB,MAAI,CAACnB,uBAAuB;QACtC;QACA;QACA,IAAI,CAAC,CAAC7C,EAAE,GAAGgE,MAAI,CAACtB,gBAAgB,MAAM,IAAI,IAAI1C,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsD,UAAU,KAAK,IAAI,IACzFU,MAAI,CAACtB,gBAAgB,CAACY,UAAU,CAACI,MAAM,KAAK,CAAC,EAAE;UAC/C,OAAO,EAAE;QACb;QACA,MAAMN,IAAI,GAAGC,gBAAgB,CAAC,CAAC;QAC/B;QACA,MAAM;UAAEY,gBAAgB;UAAEC;QAAc,CAAC,GAAGC,0BAA0B,CAACH,MAAI,CAACtB,gBAAgB,CAACY,UAAU,CAAC;QACxG,MAAMc,YAAY,GAAGpP,6BAA6B,CAACiH,IAAI,CAAC8C,SAAS,CAAC;UAAE/I,OAAO,EAAE,CAAC;UAAEsN,UAAU,EAAEW;QAAiB,CAAC,CAAC,CAAC;QAChH;QACAD,MAAI,CAACtB,gBAAgB,CAACa,qBAAqB,GAAGH,IAAI;QAClD,IAAIc,aAAa,CAACR,MAAM,GAAG,CAAC,EAAE;UAC1B;UACAM,MAAI,CAACtB,gBAAgB,CAACY,UAAU,GAAGY,aAAa;UAChD;UACA;UACA;UACA,MAAMF,MAAI,CAACrB,QAAQ,CAACmB,SAAS,CAACE,MAAI,CAACtB,gBAAgB,CAAC;QACxD,CAAC,MACI;UACDsB,MAAI,CAACtB,gBAAgB,CAACY,UAAU,GAAG,EAAE;UACrC;UACA,KAAKU,MAAI,CAACrB,QAAQ,CAACmB,SAAS,CAACE,MAAI,CAACtB,gBAAgB,CAAC;QACvD;QACA,OAAO0B,YAAY;MACvB,CAAC,CACD,OAAOvL,CAAC,EAAE;QACNpC,MAAM,CAAC6J,IAAI,CAACzH,CAAC,CAAC;QACd,OAAO,EAAE;MACb;IAAC;EACL;AACJ;AACA,SAASwK,gBAAgBA,CAAA,EAAG;EACxB,MAAMgB,KAAK,GAAG,IAAIhI,IAAI,CAAC,CAAC;EACxB;EACA,OAAOgI,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;AAC/C;AACA,SAASJ,0BAA0BA,CAACK,eAAe,EAAEC,OAAO,GAAGlC,gBAAgB,EAAE;EAC7E;EACA;EACA,MAAM0B,gBAAgB,GAAG,EAAE;EAC3B;EACA,IAAIC,aAAa,GAAGM,eAAe,CAACE,KAAK,CAAC,CAAC;EAC3C,KAAK,MAAMjB,mBAAmB,IAAIe,eAAe,EAAE;IAC/C;IACA,MAAMG,cAAc,GAAGV,gBAAgB,CAACW,IAAI,CAACC,EAAE,IAAIA,EAAE,CAAC1B,KAAK,KAAKM,mBAAmB,CAACN,KAAK,CAAC;IAC1F,IAAI,CAACwB,cAAc,EAAE;MACjB;MACAV,gBAAgB,CAAC5D,IAAI,CAAC;QAClB8C,KAAK,EAAEM,mBAAmB,CAACN,KAAK;QAChC2B,KAAK,EAAE,CAACrB,mBAAmB,CAACL,IAAI;MACpC,CAAC,CAAC;MACF,IAAI2B,UAAU,CAACd,gBAAgB,CAAC,GAAGQ,OAAO,EAAE;QACxC;QACA;QACAR,gBAAgB,CAACe,GAAG,CAAC,CAAC;QACtB;MACJ;IACJ,CAAC,MACI;MACDL,cAAc,CAACG,KAAK,CAACzE,IAAI,CAACoD,mBAAmB,CAACL,IAAI,CAAC;MACnD;MACA;MACA,IAAI2B,UAAU,CAACd,gBAAgB,CAAC,GAAGQ,OAAO,EAAE;QACxCE,cAAc,CAACG,KAAK,CAACE,GAAG,CAAC,CAAC;QAC1B;MACJ;IACJ;IACA;IACA;IACAd,aAAa,GAAGA,aAAa,CAACQ,KAAK,CAAC,CAAC,CAAC;EAC1C;EACA,OAAO;IACHT,gBAAgB;IAChBC;EACJ,CAAC;AACL;AACA,MAAMtB,oBAAoB,CAAC;EACvBvN,WAAWA,CAACsD,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACsM,uBAAuB,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACtE;EACMA,4BAA4BA,CAAA,EAAG;IAAA,OAAA1F,iBAAA;MACjC,IAAI,CAACvK,oBAAoB,CAAC,CAAC,EAAE;QACzB,OAAO,KAAK;MAChB,CAAC,MACI;QACD,OAAOC,yBAAyB,CAAC,CAAC,CAC7B6N,IAAI,CAAC,MAAM,IAAI,CAAC,CAChB5B,KAAK,CAAC,MAAM,KAAK,CAAC;MAC3B;IAAC;EACL;EACA;AACJ;AACA;EACU2B,IAAIA,CAAA,EAAG;IAAA,IAAAqC,MAAA;IAAA,OAAA3F,iBAAA;MACT,MAAM4F,eAAe,SAASD,MAAI,CAACF,uBAAuB;MAC1D,IAAI,CAACG,eAAe,EAAE;QAClB,OAAO;UAAE9B,UAAU,EAAE;QAAG,CAAC;MAC7B,CAAC,MACI;QACD,MAAM+B,kBAAkB,SAAS/D,2BAA2B,CAAC6D,MAAI,CAACxM,GAAG,CAAC;QACtE,IAAI0M,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAAC/B,UAAU,EAAE;UACvG,OAAO+B,kBAAkB;QAC7B,CAAC,MACI;UACD,OAAO;YAAE/B,UAAU,EAAE;UAAG,CAAC;QAC7B;MACJ;IAAC;EACL;EACA;EACMQ,SAASA,CAACwB,gBAAgB,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA/F,iBAAA;MAC9B,IAAIQ,EAAE;MACN,MAAMoF,eAAe,SAASG,MAAI,CAACN,uBAAuB;MAC1D,IAAI,CAACG,eAAe,EAAE;QAClB;MACJ,CAAC,MACI;QACD,MAAMI,wBAAwB,SAASD,MAAI,CAACzC,IAAI,CAAC,CAAC;QAClD,OAAOd,0BAA0B,CAACuD,MAAI,CAAC5M,GAAG,EAAE;UACxC4K,qBAAqB,EAAE,CAACvD,EAAE,GAAGsF,gBAAgB,CAAC/B,qBAAqB,MAAM,IAAI,IAAIvD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGwF,wBAAwB,CAACjC,qBAAqB;UACpJD,UAAU,EAAEgC,gBAAgB,CAAChC;QACjC,CAAC,CAAC;MACN;IAAC;EACL;EACA;EACMmC,GAAGA,CAACH,gBAAgB,EAAE;IAAA,IAAAI,MAAA;IAAA,OAAAlG,iBAAA;MACxB,IAAIQ,EAAE;MACN,MAAMoF,eAAe,SAASM,MAAI,CAACT,uBAAuB;MAC1D,IAAI,CAACG,eAAe,EAAE;QAClB;MACJ,CAAC,MACI;QACD,MAAMI,wBAAwB,SAASE,MAAI,CAAC5C,IAAI,CAAC,CAAC;QAClD,OAAOd,0BAA0B,CAAC0D,MAAI,CAAC/M,GAAG,EAAE;UACxC4K,qBAAqB,EAAE,CAACvD,EAAE,GAAGsF,gBAAgB,CAAC/B,qBAAqB,MAAM,IAAI,IAAIvD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGwF,wBAAwB,CAACjC,qBAAqB;UACpJD,UAAU,EAAE,CACR,GAAGkC,wBAAwB,CAAClC,UAAU,EACtC,GAAGgC,gBAAgB,CAAChC,UAAU;QAEtC,CAAC,CAAC;MACN;IAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASyB,UAAUA,CAACP,eAAe,EAAE;EACjC;EACA,OAAOxP,6BAA6B;EACpC;EACAiH,IAAI,CAAC8C,SAAS,CAAC;IAAE/I,OAAO,EAAE,CAAC;IAAEsN,UAAU,EAAEkB;EAAgB,CAAC,CAAC,CAAC,CAACd,MAAM;AACvE;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAACN,UAAU,EAAE;EACzC,IAAIA,UAAU,CAACI,MAAM,KAAK,CAAC,EAAE;IACzB,OAAO,CAAC,CAAC;EACb;EACA,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIgC,qBAAqB,GAAGrC,UAAU,CAAC,CAAC,CAAC,CAACF,IAAI;EAC9C,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtC,UAAU,CAACI,MAAM,EAAEkC,CAAC,EAAE,EAAE;IACxC,IAAItC,UAAU,CAACsC,CAAC,CAAC,CAACxC,IAAI,GAAGuC,qBAAqB,EAAE;MAC5CA,qBAAqB,GAAGrC,UAAU,CAACsC,CAAC,CAAC,CAACxC,IAAI;MAC1CO,oBAAoB,GAAGiC,CAAC;IAC5B;EACJ;EACA,OAAOjC,oBAAoB;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkC,sBAAsBA,CAAC9F,OAAO,EAAE;EACrC9G,kBAAkB,CAAC,IAAI9E,SAAS,CAAC,iBAAiB,EAAEmB,SAAS,IAAI,IAAIF,yBAAyB,CAACE,SAAS,CAAC,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;EAClJ2D,kBAAkB,CAAC,IAAI9E,SAAS,CAAC,WAAW,EAAEmB,SAAS,IAAI,IAAImN,oBAAoB,CAACnN,SAAS,CAAC,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;EACvI;EACA+H,eAAe,CAAC9G,MAAM,EAAEC,SAAS,EAAEuJ,OAAO,CAAC;EAC3C;EACA1C,eAAe,CAAC9G,MAAM,EAAEC,SAAS,EAAE,SAAS,CAAC;EAC7C;EACA6G,eAAe,CAAC,SAAS,EAAE,EAAE,CAAC;AAClC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAwI,sBAAsB,CAAC,EAAE,CAAC;AAE1B,SAASlI,WAAW,EAAEvF,kBAAkB,IAAI0N,mBAAmB,EAAEpN,aAAa,EAAEK,wBAAwB,EAAET,KAAK,EAAE+B,gBAAgB,EAAE5B,WAAW,EAAEc,YAAY,EAAEQ,cAAc,EAAEI,oBAAoB,EAAElB,kBAAkB,EAAEW,sBAAsB,EAAEpB,WAAW,EAAEkF,SAAS,EAAEsB,MAAM,EAAEC,OAAO,EAAErB,aAAa,EAAEM,mBAAmB,EAAEqC,KAAK,EAAElD,eAAe,EAAE9I,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}