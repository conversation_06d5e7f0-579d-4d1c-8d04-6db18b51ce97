{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nexport class AccueilComponent {\n  static {\n    this.ɵfac = function AccueilComponent_Factory(t) {\n      return new (t || AccueilComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccueilComponent,\n      selectors: [[\"app-accueil\"]],\n      decls: 29,\n      vars: 2,\n      consts: [[1, \"accueil-container\"], [1, \"navbar\"], [1, \"logo\"], [1, \"nav-links\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 3, \"routerLinkActiveOptions\"], [\"href\", \"#\"], [1, \"register-button\"], [1, \"content\"], [1, \"text-zone\"], [\"routerLink\", \"suivantacc\", 1, \"start-button\"], [1, \"bottom-illustration\"], [1, \"rectangles\"], [\"src\", \"assets/iris.png\", \"alt\", \"Iris illustration\"]],\n      template: function AccueilComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"nav\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"ul\", 3)(5, \"li\")(6, \"a\", 4);\n          i0.ɵɵtext(7, \"accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"li\")(9, \"a\", 5);\n          i0.ɵɵtext(10, \"\\u00C0 propos de nous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"li\")(12, \"a\", 5);\n          i0.ɵɵtext(13, \"Contact\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"button\", 6);\n          i0.ɵɵtext(15, \"Register\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 7)(17, \"div\", 8)(18, \"h1\");\n          i0.ɵɵtext(19, \"Iris & Identit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\");\n          i0.ɵɵtext(21, \" Chaque iris est une signature. \");\n          i0.ɵɵelement(22, \"br\");\n          i0.ɵɵtext(23, \" Notre syst\\u00E8me de profilage biom\\u00E9trique offre une s\\u00E9curit\\u00E9 in\\u00E9gal\\u00E9e, inspir\\u00E9e directement par la nature humaine. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 9);\n          i0.ɵɵtext(25, \"Commencer\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(26, \"div\", 10);\n          i0.ɵɵelement(27, \"div\", 11)(28, \"img\", 12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(1, _c0));\n        }\n      },\n      dependencies: [i1.RouterLink, i1.RouterLinkActive],\n      styles: [\".accueil-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #e1d8f1, #fbdde5);\\n  min-height: 100vh;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  font-weight: 500;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-family: \\\"Pacifico\\\", cursive;\\n  font-size: 24px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%] {\\n  list-style: none;\\n  display: flex;\\n  gap: 30px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: #000;\\n  font-size: 16px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  border: 2px solid #000;\\n  background: transparent;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  cursor: pointer;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  flex-grow: 1;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 600px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  font-weight: bold;\\n  margin-bottom: 20px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-style: italic;\\n  margin-bottom: 30px;\\n  line-height: 1.6;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   .start-button[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  border: 2px solid #000;\\n  border-radius: 30px;\\n  background-color: transparent;\\n  cursor: pointer;\\n  font-size: 16px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .bottom-illustration[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-top: 40px;\\n  text-align: center;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .bottom-illustration[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 400px;\\n  width: 90%;\\n  height: auto;\\n  position: relative;\\n  z-index: 2;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .bottom-illustration[_ngcontent-%COMP%]   .rectangles[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 10%;\\n  bottom: 20px;\\n  z-index: 1;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AccueilComponent", "selectors", "decls", "vars", "consts", "template", "AccueilComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-accueil',\n  templateUrl: './accueil.component.html',\n  styleUrls: ['./accueil.component.scss']\n})\nexport class AccueilComponent {\n\n}\n", "<div class=\"accueil-container\">\n    <nav class=\"navbar\">\n      <div class=\"logo\">IrisLock</div>\n      <ul class=\"nav-links\">\n        <li><a routerLink=\"/\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{ exact: true }\">accueil</a></li>\n        <li><a href=\"#\">À propos de nous</a></li>\n        <li><a href=\"#\">Contact</a></li>\n      </ul>\n      <button class=\"register-button\">Register</button>\n    </nav>\n  \n    <div class=\"content\">\n      <div class=\"text-zone\">\n        <h1>Iris & Identité</h1>\n        <p>\n          Chaque iris est une signature. <br />\n          Notre système de profilage biométrique offre une sécurité inégalée,\n          inspirée directement par la nature humaine.\n        </p>\n        <button class=\"start-button\" routerLink=\"suivantacc\">Commencer</button>\n\n      </div>\n    </div>\n  \n    <!-- Nouvelle section en bas -->\n    <div class=\"bottom-illustration\">\n      <div class=\"rectangles\">\n      </div>\n      <img src=\"assets/iris.png\" alt=\"Iris illustration\" />\n    </div>\n  </div>\n  "], "mappings": ";;;;;;;AAOA,OAAM,MAAOA,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BE,EAAA,CAAAC,cAAA,aAA+B;UAEPD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,YAAsB;UACwED,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvGH,EAAA,CAAAC,cAAA,SAAI;UAAYD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpCH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE7BH,EAAA,CAAAC,cAAA,iBAAgC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGnDH,EAAA,CAAAC,cAAA,cAAqB;UAEbD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,wCAA+B;UAAAF,EAAA,CAAAI,SAAA,UAAM;UACrCJ,EAAA,CAAAE,MAAA,4JAEF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,iBAAqD;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM3EH,EAAA,CAAAC,cAAA,eAAiC;UAC/BD,EAAA,CAAAI,SAAA,eACM;UAERJ,EAAA,CAAAG,YAAA,EAAM;;;UAzB8CH,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAM,UAAA,4BAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}