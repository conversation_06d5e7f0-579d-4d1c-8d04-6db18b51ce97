{"ast": null, "code": "import { mergeMap } from './mergeMap';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMapTo(innerObservable, resultSelector, concurrent = Infinity) {\n  if (isFunction(resultSelector)) {\n    return mergeMap(() => innerObservable, resultSelector, concurrent);\n  }\n  if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n  return mergeMap(() => innerObservable, concurrent);\n}", "map": {"version": 3, "names": ["mergeMap", "isFunction", "mergeMapTo", "innerObservable", "resultSelector", "concurrent", "Infinity"], "sources": ["D:/ProjetPfa/pfa/pfa/node_modules/rxjs/dist/esm/internal/operators/mergeMapTo.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMapTo(innerObservable, resultSelector, concurrent = Infinity) {\n    if (isFunction(resultSelector)) {\n        return mergeMap(() => innerObservable, resultSelector, concurrent);\n    }\n    if (typeof resultSelector === 'number') {\n        concurrent = resultSelector;\n    }\n    return mergeMap(() => innerObservable, concurrent);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AACrC,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,OAAO,SAASC,UAAUA,CAACC,eAAe,EAAEC,cAAc,EAAEC,UAAU,GAAGC,QAAQ,EAAE;EAC/E,IAAIL,UAAU,CAACG,cAAc,CAAC,EAAE;IAC5B,OAAOJ,QAAQ,CAAC,MAAMG,eAAe,EAAEC,cAAc,EAAEC,UAAU,CAAC;EACtE;EACA,IAAI,OAAOD,cAAc,KAAK,QAAQ,EAAE;IACpCC,UAAU,GAAGD,cAAc;EAC/B;EACA,OAAOJ,QAAQ,CAAC,MAAMG,eAAe,EAAEE,UAAU,CAAC;AACtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}