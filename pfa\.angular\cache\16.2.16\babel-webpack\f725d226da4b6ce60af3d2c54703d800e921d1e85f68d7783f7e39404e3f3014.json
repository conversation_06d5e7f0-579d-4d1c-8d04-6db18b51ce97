{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class TypeirisComponent {\n  static {\n    this.ɵfac = function TypeirisComponent_Factory(t) {\n      return new (t || TypeirisComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TypeirisComponent,\n      selectors: [[\"app-typeiris\"]],\n      decls: 16,\n      vars: 0,\n      consts: [[1, \"typeiris-container\"], [1, \"logo\"], [1, \"title\"], [1, \"underline\"], [1, \"content\"], [\"src\", \"assets/iris3.png\", \"alt\", \"Iris illustration\", 1, \"iris-image\"], [1, \"description\"], [\"routerLink\", \"/iris2\", 1, \"next-button\"]],\n      template: function TypeirisComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"h1\", 1);\n          i0.ɵɵtext(3, \"Iris\");\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵtext(5, \"Lock\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"main\")(7, \"h2\", 2);\n          i0.ɵɵtext(8, \"Iris Types\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"hr\", 3);\n          i0.ɵɵelementStart(10, \"div\", 4);\n          i0.ɵɵelement(11, \"img\", 5);\n          i0.ɵɵelementStart(12, \"p\", 6);\n          i0.ɵɵtext(13, \" Bien que nous soyons g\\u00E9n\\u00E9ralement domin\\u00E9s par un seul motif d\\u2019iris, ou parfois par une combinaison de deux, chacun de nous porte en lui l\\u2019essence des quatre types fondamentaux. Ces caract\\u00E9ristiques ne sont pas fig\\u00E9es : elles \\u00E9voluent et se modulent selon notre rang de naissance, notre parcours de vie et nos habitudes quotidiennes. Par exemple, la place occup\\u00E9e dans la fratrie influence la mani\\u00E8re dont s\\u2019exprime le type d\\u2019iris, tout comme notre \\u00E9tat de sant\\u00E9 et nos activit\\u00E9s personnelles. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 7);\n          i0.ɵɵtext(15, \"Suivant\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".typeiris-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(to right, #e3d4f5, #fcd9de);\\n  font-family: \\\"Segoe UI\\\", sans-serif;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-family: \\\"Georgia\\\", serif;\\n  font-weight: bold;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: normal;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-family: \\\"Georgia\\\", serif;\\n  margin-top: 40px;\\n  margin-bottom: 10px;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .underline[_ngcontent-%COMP%] {\\n  width: 320px;\\n  height: 4px;\\n  background-color: white;\\n  border: none;\\n  margin-bottom: 30px;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 30px;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 160px;\\n  height: 160px;\\n  object-fit: contain;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-family: \\\"Georgia\\\", serif;\\n  font-size: 1.2rem;\\n  line-height: 1.8rem;\\n  text-align: justify;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvdHlwZWlyaXMvdHlwZWlyaXMuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxhQUFBO0VBQ0EsdURBQUE7RUFDQSxtQ0FBQTtBQUNKO0FBRU07RUFDRSxpQkFBQTtFQUNBLDZCQUFBO0VBQ0EsaUJBQUE7QUFBUjtBQUVRO0VBQ0UsbUJBQUE7QUFBVjtBQU1NO0VBQ0UsZUFBQTtFQUNBLDZCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtBQUpSO0FBT007RUFDRSxZQUFBO0VBQ0EsV0FBQTtFQUNBLHVCQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0FBTFI7QUFRTTtFQUNFLGFBQUE7RUFDQSx1QkFBQTtFQUNBLFNBQUE7QUFOUjtBQVFRO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtBQU5WO0FBU1E7RUFDRSxPQUFBO0VBQ0EsNkJBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUFQViIsInNvdXJjZXNDb250ZW50IjpbIi50eXBlaXJpcy1jb250YWluZXIge1xyXG4gICAgcGFkZGluZzogMjBweDtcclxuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgI2UzZDRmNSwgI2ZjZDlkZSk7XHJcbiAgICBmb250LWZhbWlseTogJ1NlZ29lIFVJJywgc2Fucy1zZXJpZjtcclxuICBcclxuICAgIGhlYWRlciB7XHJcbiAgICAgIC5sb2dvIHtcclxuICAgICAgICBmb250LXNpemU6IDIuNXJlbTtcclxuICAgICAgICBmb250LWZhbWlseTogJ0dlb3JnaWEnLCBzZXJpZjtcclxuICAgICAgICBmb250LXdlaWdodDogYm9sZDtcclxuICBcclxuICAgICAgICBzcGFuIHtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiBub3JtYWw7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgXHJcbiAgICBtYWluIHtcclxuICAgICAgLnRpdGxlIHtcclxuICAgICAgICBmb250LXNpemU6IDJyZW07XHJcbiAgICAgICAgZm9udC1mYW1pbHk6ICdHZW9yZ2lhJywgc2VyaWY7XHJcbiAgICAgICAgbWFyZ2luLXRvcDogNDBweDtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xyXG4gICAgICB9XHJcbiAgXHJcbiAgICAgIC51bmRlcmxpbmUge1xyXG4gICAgICAgIHdpZHRoOiAzMjBweDtcclxuICAgICAgICBoZWlnaHQ6IDRweDtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcclxuICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDtcclxuICAgICAgfVxyXG4gIFxyXG4gICAgICAuY29udGVudCB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcclxuICAgICAgICBnYXA6IDMwcHg7XHJcbiAgXHJcbiAgICAgICAgLmlyaXMtaW1hZ2Uge1xyXG4gICAgICAgICAgd2lkdGg6IDE2MHB4O1xyXG4gICAgICAgICAgaGVpZ2h0OiAxNjBweDtcclxuICAgICAgICAgIG9iamVjdC1maXQ6IGNvbnRhaW47XHJcbiAgICAgICAgfVxyXG4gIFxyXG4gICAgICAgIC5kZXNjcmlwdGlvbiB7XHJcbiAgICAgICAgICBmbGV4OiAxO1xyXG4gICAgICAgICAgZm9udC1mYW1pbHk6ICdHZW9yZ2lhJywgc2VyaWY7XHJcbiAgICAgICAgICBmb250LXNpemU6IDEuMnJlbTtcclxuICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxLjhyZW07XHJcbiAgICAgICAgICB0ZXh0LWFsaWduOiBqdXN0aWZ5O1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["TypeirisComponent", "selectors", "decls", "vars", "consts", "template", "TypeirisComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\typeiris\\typeiris.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\typeiris\\typeiris.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-typeiris',\n  templateUrl: './typeiris.component.html',\n  styleUrls: ['./typeiris.component.scss']\n})\nexport class TypeirisComponent {\n\n}\n", "<div class=\"typeiris-container\">\n    <header>\n      <h1 class=\"logo\">Iris<span>Lock</span></h1>\n    </header>\n  \n    <main>\n      <h2 class=\"title\">Iris Types</h2>\n      <hr class=\"underline\" />\n  \n      <div class=\"content\">\n        <img src=\"assets/iris3.png\" alt=\"Iris illustration\" class=\"iris-image\" />\n  \n        <p class=\"description\">\n          Bien que nous soyons généralement dominés par un seul motif d’iris, ou parfois par une combinaison de deux,\n          chacun de nous porte en lui l’essence des quatre types fondamentaux. Ces caractéristiques ne sont pas figées :\n          elles évoluent et se modulent selon notre rang de naissance, notre parcours de vie et nos habitudes quotidiennes.\n          Par exemple, la place occupée dans la fratrie influence la manière dont s’exprime le type d’iris, tout comme notre\n          état de santé et nos activités personnelles.\n        </p>\n        <button class=\"next-button\" routerLink=\"/iris2\">Suivant</button>\n      </div>\n    </main>\n  </div>\n  "], "mappings": ";;AAOA,OAAM,MAAOA,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP9BE,EAAA,CAAAC,cAAA,aAAgC;UAETD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGxCH,EAAA,CAAAC,cAAA,WAAM;UACcD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAI,SAAA,YAAwB;UAExBJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAI,SAAA,cAAyE;UAEzEJ,EAAA,CAAAC,cAAA,YAAuB;UACrBD,EAAA,CAAAE,MAAA,ikBAKF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,iBAAgD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}