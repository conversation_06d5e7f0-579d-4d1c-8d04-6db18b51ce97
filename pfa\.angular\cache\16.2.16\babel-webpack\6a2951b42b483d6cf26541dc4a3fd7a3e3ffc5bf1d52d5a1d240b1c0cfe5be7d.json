{"ast": null, "code": "export function isValidDate(value) {\n  return value instanceof Date && !isNaN(value);\n}", "map": {"version": 3, "names": ["isValidDate", "value", "Date", "isNaN"], "sources": ["C:/Users/<USER>/Desktop/pfa/pfa/node_modules/rxjs/dist/esm/internal/util/isDate.js"], "sourcesContent": ["export function isValidDate(value) {\n    return value instanceof Date && !isNaN(value);\n}\n"], "mappings": "AAAA,OAAO,SAASA,WAAWA,CAACC,KAAK,EAAE;EAC/B,OAAOA,KAAK,YAAYC,IAAI,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}