{"ast": null, "code": "import { collection, doc, setDoc, addDoc } from '@angular/fire/firestore';\nimport { from } from 'rxjs';\nimport { PERSONALITY_QUESTIONS, PERSONALITY_DESCRIPTIONS } from '../models/personality-test.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/firestore\";\nimport * as i2 from \"./mock-firebase.service\";\nimport * as i3 from \"./firebase-data-init.service\";\nexport class PersonalityTestService {\n  constructor(firestore, mockFirebaseService, firebaseDataInitService) {\n    this.firestore = firestore;\n    this.mockFirebaseService = mockFirebaseService;\n    this.firebaseDataInitService = firebaseDataInitService;\n    this.USE_MOCK = false; // Utiliser Firebase réel maintenant\n    // Initialiser les données de base au démarrage\n    this.initializeFirebaseData();\n  }\n  /**\n   * Initialise les données de base dans Firebase\n   */\n  initializeFirebaseData() {\n    if (!this.USE_MOCK) {\n      this.firebaseDataInitService.initializeBaseData().subscribe({\n        next: success => {\n          if (success) {\n            console.log('✅ Données Firebase initialisées');\n          } else {\n            console.error('❌ Erreur lors de l\\'initialisation Firebase');\n          }\n        },\n        error: error => {\n          console.error('❌ Erreur Firebase:', error);\n        }\n      });\n    }\n  }\n  /**\n   * Calcule les scores pour chaque classe de personnalité\n   */\n  calculateScores(responses) {\n    const scores = {\n      flower: 0,\n      jewel: 0,\n      shaker: 0,\n      stream: 0,\n      flowerJewel: 0,\n      jewelShaker: 0,\n      shakerStream: 0,\n      streamFlower: 0\n    };\n    responses.forEach(response => {\n      const question = PERSONALITY_QUESTIONS.find(q => q.id === response.questionId);\n      if (!question) return;\n      const isCorrectAnswer = response.answer === question.expectedAnswer;\n      if (!isCorrectAnswer) return;\n      // Attribution des points selon les classes de la question\n      question.classes.forEach(className => {\n        switch (className) {\n          case 'Flower':\n            scores.flower += 1;\n            break;\n          case 'Jewel':\n            scores.jewel += 1;\n            break;\n          case 'Shaker':\n            scores.shaker += 1;\n            break;\n          case 'Stream':\n            scores.stream += 1;\n            break;\n          case 'Flower-Jewel':\n            scores.flowerJewel += 1;\n            // Contribue aussi aux classes de base\n            scores.flower += 0.5;\n            scores.jewel += 0.5;\n            break;\n          case 'Jewel-Shaker':\n            scores.jewelShaker += 1;\n            scores.jewel += 0.5;\n            scores.shaker += 0.5;\n            break;\n          case 'Shaker-Stream':\n            scores.shakerStream += 1;\n            scores.shaker += 0.5;\n            scores.stream += 0.5;\n            break;\n          case 'Stream-Flower':\n            scores.streamFlower += 1;\n            scores.stream += 0.5;\n            scores.flower += 0.5;\n            break;\n        }\n      });\n    });\n    return scores;\n  }\n  /**\n   * Détermine le profil de personnalité basé sur les scores\n   */\n  determineProfile(scores) {\n    // Scores des classes de base\n    const baseScores = [{\n      class: 'Flower',\n      score: scores.flower\n    }, {\n      class: 'Jewel',\n      score: scores.jewel\n    }, {\n      class: 'Shaker',\n      score: scores.shaker\n    }, {\n      class: 'Stream',\n      score: scores.stream\n    }];\n    // Scores des classes intermédiaires\n    const intermediateScores = [{\n      class: 'Flower-Jewel',\n      score: scores.flowerJewel\n    }, {\n      class: 'Jewel-Shaker',\n      score: scores.jewelShaker\n    }, {\n      class: 'Shaker-Stream',\n      score: scores.shakerStream\n    }, {\n      class: 'Stream-Flower',\n      score: scores.streamFlower\n    }];\n    // Trier par score décroissant\n    baseScores.sort((a, b) => b.score - a.score);\n    intermediateScores.sort((a, b) => b.score - a.score);\n    const maxBase = baseScores[0];\n    const secondMaxBase = baseScores[1];\n    const maxIntermediate = intermediateScores[0];\n    // Logique de détermination du profil\n    let primaryClass;\n    let secondaryClass;\n    let isIntermediate = false;\n    let confidenceScore = 0;\n    // Si une classe intermédiaire a un score élevé\n    if (maxIntermediate.score >= 2) {\n      primaryClass = maxIntermediate.class;\n      isIntermediate = true;\n      confidenceScore = maxIntermediate.score / 3 * 100; // Max 3 questions par classe intermédiaire\n    }\n    // Si deux classes de base sont proches et élevées\n    else if (maxBase.score >= 2 && secondMaxBase.score >= 2 && maxBase.score - secondMaxBase.score <= 1) {\n      // Créer un profil intermédiaire\n      const combinedClasses = [maxBase.class, secondMaxBase.class].sort();\n      primaryClass = `${combinedClasses[0]}-${combinedClasses[1]}`;\n      isIntermediate = true;\n      confidenceScore = (maxBase.score + secondMaxBase.score) / 8 * 100; // Max 4 questions par classe de base\n    }\n    // Sinon, classe dominante\n    else {\n      primaryClass = maxBase.class;\n      if (secondMaxBase.score >= 1) {\n        secondaryClass = secondMaxBase.class;\n      }\n      confidenceScore = maxBase.score / 4 * 100; // Max 4 questions par classe de base\n    }\n\n    return {\n      primaryClass,\n      secondaryClass,\n      isIntermediate,\n      confidenceScore: Math.round(confidenceScore),\n      description: PERSONALITY_DESCRIPTIONS[primaryClass]\n    };\n  }\n  /**\n   * Sauvegarde une session de test dans Firestore\n   */\n  saveTestSession(session) {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.saveTestSession(session);\n    }\n    const testSessionsCollection = collection(this.firestore, 'personality_tests');\n    const sessionData = {\n      ...session,\n      completedAt: session.completedAt?.toISOString(),\n      startedAt: session.startedAt?.toISOString(),\n      responses: session.responses.map(response => ({\n        ...response,\n        timestamp: response.timestamp.toISOString()\n      }))\n    };\n    return from(addDoc(testSessionsCollection, sessionData).then(docRef => docRef.id));\n  }\n  /**\n   * Sauvegarde une réponse individuelle en temps réel\n   */\n  saveIndividualResponse(userId, sessionId, response) {\n    if (this.USE_MOCK) {\n      return from(Promise.resolve(true));\n    }\n    const responseData = {\n      ...response,\n      timestamp: response.timestamp.toISOString(),\n      userId,\n      sessionId\n    };\n    const responseDoc = doc(this.firestore, 'user_responses', `${userId}_${sessionId}_${response.questionId}`);\n    return from(setDoc(responseDoc, responseData).then(() => true).catch(() => false));\n  }\n  /**\n   * Sauvegarde les statistiques de session\n   */\n  saveSessionStats(sessionId, stats) {\n    if (this.USE_MOCK) {\n      return from(Promise.resolve(true));\n    }\n    const statsRef = ref(this.database, `session_stats/${sessionId}`);\n    return from(set(statsRef, {\n      ...stats,\n      timestamp: new Date().toISOString()\n    }).then(() => true).catch(() => false));\n  }\n  /**\n   * Met à jour une session de test existante\n   */\n  updateTestSession(sessionId, updates) {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.updateTestSession(sessionId, updates);\n    }\n    const sessionRef = ref(this.database, `personality_tests/${sessionId}`);\n    const updateData = {\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    return from(set(sessionRef, updateData).then(() => void 0));\n  }\n  /**\n   * Récupère toutes les sessions de test\n   */\n  getAllTestSessions() {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.getAllTestSessions();\n    }\n    const testsRef = ref(this.database, 'personality_tests');\n    return from(get(testsRef).then(snapshot => {\n      if (snapshot.exists()) {\n        const data = snapshot.val();\n        return Object.keys(data).map(key => ({\n          id: key,\n          ...data[key]\n        })).sort((a, b) => new Date(b.completedAt || 0).getTime() - new Date(a.completedAt || 0).getTime());\n      }\n      return [];\n    }));\n  }\n  /**\n   * Récupère les sessions de test d'un utilisateur spécifique\n   */\n  getUserTestSessions(userEmail) {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.getUserTestSessions(userEmail);\n    }\n    const testsRef = ref(this.database, 'personality_tests');\n    return from(get(testsRef).then(snapshot => {\n      if (snapshot.exists()) {\n        const data = snapshot.val();\n        return Object.keys(data).map(key => ({\n          id: key,\n          ...data[key]\n        })).filter(session => session.userEmail === userEmail).sort((a, b) => new Date(b.completedAt || 0).getTime() - new Date(a.completedAt || 0).getTime());\n      }\n      return [];\n    }));\n  }\n  /**\n   * Crée une nouvelle session de test\n   */\n  createTestSession(userName, userEmail) {\n    return {\n      userName: userName || 'Utilisateur anonyme',\n      userEmail: userEmail || '',\n      responses: [],\n      scores: {\n        flower: 0,\n        jewel: 0,\n        shaker: 0,\n        stream: 0,\n        flowerJewel: 0,\n        jewelShaker: 0,\n        shakerStream: 0,\n        streamFlower: 0\n      },\n      finalProfile: {\n        primaryClass: 'Flower',\n        isIntermediate: false,\n        confidenceScore: 0,\n        description: ''\n      },\n      startedAt: new Date(),\n      completedAt: new Date()\n    };\n  }\n  /**\n   * Traite les réponses et calcule le profil final\n   */\n  processTestResults(responses) {\n    const scores = this.calculateScores(responses);\n    const profile = this.determineProfile(scores);\n    return {\n      scores,\n      profile\n    };\n  }\n  static {\n    this.ɵfac = function PersonalityTestService_Factory(t) {\n      return new (t || PersonalityTestService)(i0.ɵɵinject(i1.Firestore), i0.ɵɵinject(i2.MockFirebaseService), i0.ɵɵinject(i3.FirebaseDataInitService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PersonalityTestService,\n      factory: PersonalityTestService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["collection", "doc", "setDoc", "addDoc", "from", "PERSONALITY_QUESTIONS", "PERSONALITY_DESCRIPTIONS", "PersonalityTestService", "constructor", "firestore", "mockFirebaseService", "firebaseDataInitService", "USE_MOCK", "initializeFirebaseData", "initializeBaseData", "subscribe", "next", "success", "console", "log", "error", "calculateScores", "responses", "scores", "flower", "jewel", "shaker", "stream", "<PERSON><PERSON><PERSON><PERSON>", "jewelShaker", "shakerStream", "streamFlower", "for<PERSON>ach", "response", "question", "find", "q", "id", "questionId", "isCorrectAnswer", "answer", "expectedAnswer", "classes", "className", "determineProfile", "baseScores", "class", "score", "intermediateScores", "sort", "a", "b", "maxBase", "secondMaxBase", "maxIntermediate", "primaryClass", "secondaryClass", "isIntermediate", "confidenceScore", "combinedClasses", "Math", "round", "description", "saveTestSession", "session", "testSessionsCollection", "sessionData", "completedAt", "toISOString", "startedAt", "map", "timestamp", "then", "doc<PERSON>ef", "saveIndividualResponse", "userId", "sessionId", "Promise", "resolve", "responseData", "responseDoc", "catch", "saveSessionStats", "stats", "statsRef", "ref", "database", "set", "Date", "updateTestSession", "updates", "sessionRef", "updateData", "updatedAt", "getAllTestSessions", "testsRef", "get", "snapshot", "exists", "data", "val", "Object", "keys", "key", "getTime", "getUserTestSessions", "userEmail", "filter", "createTestSession", "userName", "finalProfile", "processTestResults", "profile", "i0", "ɵɵinject", "i1", "Firestore", "i2", "MockFirebaseService", "i3", "FirebaseDataInitService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\services\\personality-test.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Firestore, collection, doc, setDoc, addDoc, getDocs, query, orderBy, where } from '@angular/fire/firestore';\nimport { Observable, from } from 'rxjs';\nimport { MockFirebaseService } from './mock-firebase.service';\nimport { FirebaseDataInitService } from './firebase-data-init.service';\nimport {\n  TestSession,\n  UserResponse,\n  PersonalityScores,\n  PersonalityProfile,\n  PersonalityClass,\n  PERSONALITY_QUESTIONS,\n  PERSONALITY_DESCRIPTIONS\n} from '../models/personality-test.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PersonalityTestService {\n  private readonly USE_MOCK = false; // Utiliser Firebase réel maintenant\n\n  constructor(\n    private firestore: Firestore,\n    private mockFirebaseService: MockFirebaseService,\n    private firebaseDataInitService: FirebaseDataInitService\n  ) {\n    // Initialiser les données de base au démarrage\n    this.initializeFirebaseData();\n  }\n\n  /**\n   * Initialise les données de base dans Firebase\n   */\n  private initializeFirebaseData(): void {\n    if (!this.USE_MOCK) {\n      this.firebaseDataInitService.initializeBaseData().subscribe({\n        next: (success) => {\n          if (success) {\n            console.log('✅ Données Firebase initialisées');\n          } else {\n            console.error('❌ Erreur lors de l\\'initialisation Firebase');\n          }\n        },\n        error: (error) => {\n          console.error('❌ Erreur Firebase:', error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Calcule les scores pour chaque classe de personnalité\n   */\n  calculateScores(responses: UserResponse[]): PersonalityScores {\n    const scores: PersonalityScores = {\n      flower: 0,\n      jewel: 0,\n      shaker: 0,\n      stream: 0,\n      flowerJewel: 0,\n      jewelShaker: 0,\n      shakerStream: 0,\n      streamFlower: 0\n    };\n\n    responses.forEach(response => {\n      const question = PERSONALITY_QUESTIONS.find(q => q.id === response.questionId);\n      if (!question) return;\n\n      const isCorrectAnswer = response.answer === question.expectedAnswer;\n      if (!isCorrectAnswer) return;\n\n      // Attribution des points selon les classes de la question\n      question.classes.forEach(className => {\n        switch (className) {\n          case 'Flower':\n            scores.flower += 1;\n            break;\n          case 'Jewel':\n            scores.jewel += 1;\n            break;\n          case 'Shaker':\n            scores.shaker += 1;\n            break;\n          case 'Stream':\n            scores.stream += 1;\n            break;\n          case 'Flower-Jewel':\n            scores.flowerJewel += 1;\n            // Contribue aussi aux classes de base\n            scores.flower += 0.5;\n            scores.jewel += 0.5;\n            break;\n          case 'Jewel-Shaker':\n            scores.jewelShaker += 1;\n            scores.jewel += 0.5;\n            scores.shaker += 0.5;\n            break;\n          case 'Shaker-Stream':\n            scores.shakerStream += 1;\n            scores.shaker += 0.5;\n            scores.stream += 0.5;\n            break;\n          case 'Stream-Flower':\n            scores.streamFlower += 1;\n            scores.stream += 0.5;\n            scores.flower += 0.5;\n            break;\n        }\n      });\n    });\n\n    return scores;\n  }\n\n  /**\n   * Détermine le profil de personnalité basé sur les scores\n   */\n  determineProfile(scores: PersonalityScores): PersonalityProfile {\n    // Scores des classes de base\n    const baseScores = [\n      { class: 'Flower' as PersonalityClass, score: scores.flower },\n      { class: 'Jewel' as PersonalityClass, score: scores.jewel },\n      { class: 'Shaker' as PersonalityClass, score: scores.shaker },\n      { class: 'Stream' as PersonalityClass, score: scores.stream }\n    ];\n\n    // Scores des classes intermédiaires\n    const intermediateScores = [\n      { class: 'Flower-Jewel' as PersonalityClass, score: scores.flowerJewel },\n      { class: 'Jewel-Shaker' as PersonalityClass, score: scores.jewelShaker },\n      { class: 'Shaker-Stream' as PersonalityClass, score: scores.shakerStream },\n      { class: 'Stream-Flower' as PersonalityClass, score: scores.streamFlower }\n    ];\n\n    // Trier par score décroissant\n    baseScores.sort((a, b) => b.score - a.score);\n    intermediateScores.sort((a, b) => b.score - a.score);\n\n    const maxBase = baseScores[0];\n    const secondMaxBase = baseScores[1];\n    const maxIntermediate = intermediateScores[0];\n\n    // Logique de détermination du profil\n    let primaryClass: PersonalityClass;\n    let secondaryClass: PersonalityClass | undefined;\n    let isIntermediate = false;\n    let confidenceScore = 0;\n\n    // Si une classe intermédiaire a un score élevé\n    if (maxIntermediate.score >= 2) {\n      primaryClass = maxIntermediate.class;\n      isIntermediate = true;\n      confidenceScore = (maxIntermediate.score / 3) * 100; // Max 3 questions par classe intermédiaire\n    }\n    // Si deux classes de base sont proches et élevées\n    else if (maxBase.score >= 2 && secondMaxBase.score >= 2 && (maxBase.score - secondMaxBase.score) <= 1) {\n      // Créer un profil intermédiaire\n      const combinedClasses = [maxBase.class, secondMaxBase.class].sort();\n      primaryClass = `${combinedClasses[0]}-${combinedClasses[1]}` as PersonalityClass;\n      isIntermediate = true;\n      confidenceScore = ((maxBase.score + secondMaxBase.score) / 8) * 100; // Max 4 questions par classe de base\n    }\n    // Sinon, classe dominante\n    else {\n      primaryClass = maxBase.class;\n      if (secondMaxBase.score >= 1) {\n        secondaryClass = secondMaxBase.class;\n      }\n      confidenceScore = (maxBase.score / 4) * 100; // Max 4 questions par classe de base\n    }\n\n    return {\n      primaryClass,\n      secondaryClass,\n      isIntermediate,\n      confidenceScore: Math.round(confidenceScore),\n      description: PERSONALITY_DESCRIPTIONS[primaryClass]\n    };\n  }\n\n  /**\n   * Sauvegarde une session de test dans Firestore\n   */\n  saveTestSession(session: TestSession): Observable<string> {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.saveTestSession(session);\n    }\n\n    const testSessionsCollection = collection(this.firestore, 'personality_tests');\n\n    const sessionData = {\n      ...session,\n      completedAt: session.completedAt?.toISOString(),\n      startedAt: session.startedAt?.toISOString(),\n      responses: session.responses.map(response => ({\n        ...response,\n        timestamp: response.timestamp.toISOString()\n      }))\n    };\n\n    return from(addDoc(testSessionsCollection, sessionData).then(docRef => docRef.id));\n  }\n\n  /**\n   * Sauvegarde une réponse individuelle en temps réel\n   */\n  saveIndividualResponse(userId: string, sessionId: string, response: UserResponse): Observable<boolean> {\n    if (this.USE_MOCK) {\n      return from(Promise.resolve(true));\n    }\n\n    const responseData = {\n      ...response,\n      timestamp: response.timestamp.toISOString(),\n      userId,\n      sessionId\n    };\n\n    const responseDoc = doc(this.firestore, 'user_responses', `${userId}_${sessionId}_${response.questionId}`);\n    return from(setDoc(responseDoc, responseData).then(() => true).catch(() => false));\n  }\n\n  /**\n   * Sauvegarde les statistiques de session\n   */\n  saveSessionStats(sessionId: string, stats: any): Observable<boolean> {\n    if (this.USE_MOCK) {\n      return from(Promise.resolve(true));\n    }\n\n    const statsRef = ref(this.database, `session_stats/${sessionId}`);\n    return from(set(statsRef, {\n      ...stats,\n      timestamp: new Date().toISOString()\n    }).then(() => true).catch(() => false));\n  }\n\n  /**\n   * Met à jour une session de test existante\n   */\n  updateTestSession(sessionId: string, updates: Partial<TestSession>): Observable<void> {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.updateTestSession(sessionId, updates);\n    }\n\n    const sessionRef = ref(this.database, `personality_tests/${sessionId}`);\n    const updateData = {\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n\n    return from(set(sessionRef, updateData).then(() => void 0));\n  }\n\n  /**\n   * Récupère toutes les sessions de test\n   */\n  getAllTestSessions(): Observable<TestSession[]> {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.getAllTestSessions();\n    }\n\n    const testsRef = ref(this.database, 'personality_tests');\n    return from(get(testsRef).then(snapshot => {\n      if (snapshot.exists()) {\n        const data = snapshot.val();\n        return Object.keys(data).map(key => ({\n          id: key,\n          ...data[key]\n        } as TestSession)).sort((a, b) =>\n          new Date(b.completedAt || 0).getTime() - new Date(a.completedAt || 0).getTime()\n        );\n      }\n      return [];\n    }));\n  }\n\n  /**\n   * Récupère les sessions de test d'un utilisateur spécifique\n   */\n  getUserTestSessions(userEmail: string): Observable<TestSession[]> {\n    if (this.USE_MOCK) {\n      return this.mockFirebaseService.getUserTestSessions(userEmail);\n    }\n\n    const testsRef = ref(this.database, 'personality_tests');\n    return from(get(testsRef).then(snapshot => {\n      if (snapshot.exists()) {\n        const data = snapshot.val();\n        return Object.keys(data)\n          .map(key => ({\n            id: key,\n            ...data[key]\n          } as TestSession))\n          .filter(session => session.userEmail === userEmail)\n          .sort((a, b) =>\n            new Date(b.completedAt || 0).getTime() - new Date(a.completedAt || 0).getTime()\n          );\n      }\n      return [];\n    }));\n  }\n\n  /**\n   * Crée une nouvelle session de test\n   */\n  createTestSession(userName?: string, userEmail?: string): TestSession {\n    return {\n      userName: userName || 'Utilisateur anonyme',\n      userEmail: userEmail || '',\n      responses: [],\n      scores: {\n        flower: 0,\n        jewel: 0,\n        shaker: 0,\n        stream: 0,\n        flowerJewel: 0,\n        jewelShaker: 0,\n        shakerStream: 0,\n        streamFlower: 0\n      },\n      finalProfile: {\n        primaryClass: 'Flower',\n        isIntermediate: false,\n        confidenceScore: 0,\n        description: ''\n      },\n      startedAt: new Date(),\n      completedAt: new Date()\n    };\n  }\n\n  /**\n   * Traite les réponses et calcule le profil final\n   */\n  processTestResults(responses: UserResponse[]): { scores: PersonalityScores, profile: PersonalityProfile } {\n    const scores = this.calculateScores(responses);\n    const profile = this.determineProfile(scores);\n\n    return { scores, profile };\n  }\n}\n"], "mappings": "AACA,SAAoBA,UAAU,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,QAAwC,yBAAyB;AACpH,SAAqBC,IAAI,QAAQ,MAAM;AAGvC,SAMEC,qBAAqB,EACrBC,wBAAwB,QACnB,kCAAkC;;;;;AAKzC,OAAM,MAAOC,sBAAsB;EAGjCC,YACUC,SAAoB,EACpBC,mBAAwC,EACxCC,uBAAgD;IAFhD,KAAAF,SAAS,GAATA,SAAS;IACT,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,uBAAuB,GAAvBA,uBAAuB;IALhB,KAAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;IAOjC;IACA,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEA;;;EAGQA,sBAAsBA,CAAA;IAC5B,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;MAClB,IAAI,CAACD,uBAAuB,CAACG,kBAAkB,EAAE,CAACC,SAAS,CAAC;QAC1DC,IAAI,EAAGC,OAAO,IAAI;UAChB,IAAIA,OAAO,EAAE;YACXC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;WAC/C,MAAM;YACLD,OAAO,CAACE,KAAK,CAAC,6CAA6C,CAAC;;QAEhE,CAAC;QACDA,KAAK,EAAGA,KAAK,IAAI;UACfF,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC5C;OACD,CAAC;;EAEN;EAEA;;;EAGAC,eAAeA,CAACC,SAAyB;IACvC,MAAMC,MAAM,GAAsB;MAChCC,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC;MACfC,YAAY,EAAE;KACf;IAEDT,SAAS,CAACU,OAAO,CAACC,QAAQ,IAAG;MAC3B,MAAMC,QAAQ,GAAG7B,qBAAqB,CAAC8B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,QAAQ,CAACK,UAAU,CAAC;MAC9E,IAAI,CAACJ,QAAQ,EAAE;MAEf,MAAMK,eAAe,GAAGN,QAAQ,CAACO,MAAM,KAAKN,QAAQ,CAACO,cAAc;MACnE,IAAI,CAACF,eAAe,EAAE;MAEtB;MACAL,QAAQ,CAACQ,OAAO,CAACV,OAAO,CAACW,SAAS,IAAG;QACnC,QAAQA,SAAS;UACf,KAAK,QAAQ;YACXpB,MAAM,CAACC,MAAM,IAAI,CAAC;YAClB;UACF,KAAK,OAAO;YACVD,MAAM,CAACE,KAAK,IAAI,CAAC;YACjB;UACF,KAAK,QAAQ;YACXF,MAAM,CAACG,MAAM,IAAI,CAAC;YAClB;UACF,KAAK,QAAQ;YACXH,MAAM,CAACI,MAAM,IAAI,CAAC;YAClB;UACF,KAAK,cAAc;YACjBJ,MAAM,CAACK,WAAW,IAAI,CAAC;YACvB;YACAL,MAAM,CAACC,MAAM,IAAI,GAAG;YACpBD,MAAM,CAACE,KAAK,IAAI,GAAG;YACnB;UACF,KAAK,cAAc;YACjBF,MAAM,CAACM,WAAW,IAAI,CAAC;YACvBN,MAAM,CAACE,KAAK,IAAI,GAAG;YACnBF,MAAM,CAACG,MAAM,IAAI,GAAG;YACpB;UACF,KAAK,eAAe;YAClBH,MAAM,CAACO,YAAY,IAAI,CAAC;YACxBP,MAAM,CAACG,MAAM,IAAI,GAAG;YACpBH,MAAM,CAACI,MAAM,IAAI,GAAG;YACpB;UACF,KAAK,eAAe;YAClBJ,MAAM,CAACQ,YAAY,IAAI,CAAC;YACxBR,MAAM,CAACI,MAAM,IAAI,GAAG;YACpBJ,MAAM,CAACC,MAAM,IAAI,GAAG;YACpB;;MAEN,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,OAAOD,MAAM;EACf;EAEA;;;EAGAqB,gBAAgBA,CAACrB,MAAyB;IACxC;IACA,MAAMsB,UAAU,GAAG,CACjB;MAAEC,KAAK,EAAE,QAA4B;MAAEC,KAAK,EAAExB,MAAM,CAACC;IAAM,CAAE,EAC7D;MAAEsB,KAAK,EAAE,OAA2B;MAAEC,KAAK,EAAExB,MAAM,CAACE;IAAK,CAAE,EAC3D;MAAEqB,KAAK,EAAE,QAA4B;MAAEC,KAAK,EAAExB,MAAM,CAACG;IAAM,CAAE,EAC7D;MAAEoB,KAAK,EAAE,QAA4B;MAAEC,KAAK,EAAExB,MAAM,CAACI;IAAM,CAAE,CAC9D;IAED;IACA,MAAMqB,kBAAkB,GAAG,CACzB;MAAEF,KAAK,EAAE,cAAkC;MAAEC,KAAK,EAAExB,MAAM,CAACK;IAAW,CAAE,EACxE;MAAEkB,KAAK,EAAE,cAAkC;MAAEC,KAAK,EAAExB,MAAM,CAACM;IAAW,CAAE,EACxE;MAAEiB,KAAK,EAAE,eAAmC;MAAEC,KAAK,EAAExB,MAAM,CAACO;IAAY,CAAE,EAC1E;MAAEgB,KAAK,EAAE,eAAmC;MAAEC,KAAK,EAAExB,MAAM,CAACQ;IAAY,CAAE,CAC3E;IAED;IACAc,UAAU,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,KAAK,GAAGG,CAAC,CAACH,KAAK,CAAC;IAC5CC,kBAAkB,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,KAAK,GAAGG,CAAC,CAACH,KAAK,CAAC;IAEpD,MAAMK,OAAO,GAAGP,UAAU,CAAC,CAAC,CAAC;IAC7B,MAAMQ,aAAa,GAAGR,UAAU,CAAC,CAAC,CAAC;IACnC,MAAMS,eAAe,GAAGN,kBAAkB,CAAC,CAAC,CAAC;IAE7C;IACA,IAAIO,YAA8B;IAClC,IAAIC,cAA4C;IAChD,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAIC,eAAe,GAAG,CAAC;IAEvB;IACA,IAAIJ,eAAe,CAACP,KAAK,IAAI,CAAC,EAAE;MAC9BQ,YAAY,GAAGD,eAAe,CAACR,KAAK;MACpCW,cAAc,GAAG,IAAI;MACrBC,eAAe,GAAIJ,eAAe,CAACP,KAAK,GAAG,CAAC,GAAI,GAAG,CAAC,CAAC;;IAEvD;IAAA,KACK,IAAIK,OAAO,CAACL,KAAK,IAAI,CAAC,IAAIM,aAAa,CAACN,KAAK,IAAI,CAAC,IAAKK,OAAO,CAACL,KAAK,GAAGM,aAAa,CAACN,KAAK,IAAK,CAAC,EAAE;MACrG;MACA,MAAMY,eAAe,GAAG,CAACP,OAAO,CAACN,KAAK,EAAEO,aAAa,CAACP,KAAK,CAAC,CAACG,IAAI,EAAE;MACnEM,YAAY,GAAG,GAAGI,eAAe,CAAC,CAAC,CAAC,IAAIA,eAAe,CAAC,CAAC,CAAC,EAAsB;MAChFF,cAAc,GAAG,IAAI;MACrBC,eAAe,GAAI,CAACN,OAAO,CAACL,KAAK,GAAGM,aAAa,CAACN,KAAK,IAAI,CAAC,GAAI,GAAG,CAAC,CAAC;;IAEvE;IAAA,KACK;MACHQ,YAAY,GAAGH,OAAO,CAACN,KAAK;MAC5B,IAAIO,aAAa,CAACN,KAAK,IAAI,CAAC,EAAE;QAC5BS,cAAc,GAAGH,aAAa,CAACP,KAAK;;MAEtCY,eAAe,GAAIN,OAAO,CAACL,KAAK,GAAG,CAAC,GAAI,GAAG,CAAC,CAAC;;;IAG/C,OAAO;MACLQ,YAAY;MACZC,cAAc;MACdC,cAAc;MACdC,eAAe,EAAEE,IAAI,CAACC,KAAK,CAACH,eAAe,CAAC;MAC5CI,WAAW,EAAExD,wBAAwB,CAACiD,YAAY;KACnD;EACH;EAEA;;;EAGAQ,eAAeA,CAACC,OAAoB;IAClC,IAAI,IAAI,CAACpD,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACF,mBAAmB,CAACqD,eAAe,CAACC,OAAO,CAAC;;IAG1D,MAAMC,sBAAsB,GAAGjE,UAAU,CAAC,IAAI,CAACS,SAAS,EAAE,mBAAmB,CAAC;IAE9E,MAAMyD,WAAW,GAAG;MAClB,GAAGF,OAAO;MACVG,WAAW,EAAEH,OAAO,CAACG,WAAW,EAAEC,WAAW,EAAE;MAC/CC,SAAS,EAAEL,OAAO,CAACK,SAAS,EAAED,WAAW,EAAE;MAC3C9C,SAAS,EAAE0C,OAAO,CAAC1C,SAAS,CAACgD,GAAG,CAACrC,QAAQ,KAAK;QAC5C,GAAGA,QAAQ;QACXsC,SAAS,EAAEtC,QAAQ,CAACsC,SAAS,CAACH,WAAW;OAC1C,CAAC;KACH;IAED,OAAOhE,IAAI,CAACD,MAAM,CAAC8D,sBAAsB,EAAEC,WAAW,CAAC,CAACM,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACpC,EAAE,CAAC,CAAC;EACpF;EAEA;;;EAGAqC,sBAAsBA,CAACC,MAAc,EAAEC,SAAiB,EAAE3C,QAAsB;IAC9E,IAAI,IAAI,CAACrB,QAAQ,EAAE;MACjB,OAAOR,IAAI,CAACyE,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAAC;;IAGpC,MAAMC,YAAY,GAAG;MACnB,GAAG9C,QAAQ;MACXsC,SAAS,EAAEtC,QAAQ,CAACsC,SAAS,CAACH,WAAW,EAAE;MAC3CO,MAAM;MACNC;KACD;IAED,MAAMI,WAAW,GAAG/E,GAAG,CAAC,IAAI,CAACQ,SAAS,EAAE,gBAAgB,EAAE,GAAGkE,MAAM,IAAIC,SAAS,IAAI3C,QAAQ,CAACK,UAAU,EAAE,CAAC;IAC1G,OAAOlC,IAAI,CAACF,MAAM,CAAC8E,WAAW,EAAED,YAAY,CAAC,CAACP,IAAI,CAAC,MAAM,IAAI,CAAC,CAACS,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;EACpF;EAEA;;;EAGAC,gBAAgBA,CAACN,SAAiB,EAAEO,KAAU;IAC5C,IAAI,IAAI,CAACvE,QAAQ,EAAE;MACjB,OAAOR,IAAI,CAACyE,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC,CAAC;;IAGpC,MAAMM,QAAQ,GAAGC,GAAG,CAAC,IAAI,CAACC,QAAQ,EAAE,iBAAiBV,SAAS,EAAE,CAAC;IACjE,OAAOxE,IAAI,CAACmF,GAAG,CAACH,QAAQ,EAAE;MACxB,GAAGD,KAAK;MACRZ,SAAS,EAAE,IAAIiB,IAAI,EAAE,CAACpB,WAAW;KAClC,CAAC,CAACI,IAAI,CAAC,MAAM,IAAI,CAAC,CAACS,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;EACzC;EAEA;;;EAGAQ,iBAAiBA,CAACb,SAAiB,EAAEc,OAA6B;IAChE,IAAI,IAAI,CAAC9E,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACF,mBAAmB,CAAC+E,iBAAiB,CAACb,SAAS,EAAEc,OAAO,CAAC;;IAGvE,MAAMC,UAAU,GAAGN,GAAG,CAAC,IAAI,CAACC,QAAQ,EAAE,qBAAqBV,SAAS,EAAE,CAAC;IACvE,MAAMgB,UAAU,GAAG;MACjB,GAAGF,OAAO;MACVG,SAAS,EAAE,IAAIL,IAAI,EAAE,CAACpB,WAAW;KAClC;IAED,OAAOhE,IAAI,CAACmF,GAAG,CAACI,UAAU,EAAEC,UAAU,CAAC,CAACpB,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;EAC7D;EAEA;;;EAGAsB,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAClF,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACF,mBAAmB,CAACoF,kBAAkB,EAAE;;IAGtD,MAAMC,QAAQ,GAAGV,GAAG,CAAC,IAAI,CAACC,QAAQ,EAAE,mBAAmB,CAAC;IACxD,OAAOlF,IAAI,CAAC4F,GAAG,CAACD,QAAQ,CAAC,CAACvB,IAAI,CAACyB,QAAQ,IAAG;MACxC,IAAIA,QAAQ,CAACC,MAAM,EAAE,EAAE;QACrB,MAAMC,IAAI,GAAGF,QAAQ,CAACG,GAAG,EAAE;QAC3B,OAAOC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CAAC7B,GAAG,CAACiC,GAAG,KAAK;UACnClE,EAAE,EAAEkE,GAAG;UACP,GAAGJ,IAAI,CAACI,GAAG;SACI,EAAC,CAACtD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC3B,IAAIqC,IAAI,CAACrC,CAAC,CAACgB,WAAW,IAAI,CAAC,CAAC,CAACqC,OAAO,EAAE,GAAG,IAAIhB,IAAI,CAACtC,CAAC,CAACiB,WAAW,IAAI,CAAC,CAAC,CAACqC,OAAO,EAAE,CAChF;;MAEH,OAAO,EAAE;IACX,CAAC,CAAC,CAAC;EACL;EAEA;;;EAGAC,mBAAmBA,CAACC,SAAiB;IACnC,IAAI,IAAI,CAAC9F,QAAQ,EAAE;MACjB,OAAO,IAAI,CAACF,mBAAmB,CAAC+F,mBAAmB,CAACC,SAAS,CAAC;;IAGhE,MAAMX,QAAQ,GAAGV,GAAG,CAAC,IAAI,CAACC,QAAQ,EAAE,mBAAmB,CAAC;IACxD,OAAOlF,IAAI,CAAC4F,GAAG,CAACD,QAAQ,CAAC,CAACvB,IAAI,CAACyB,QAAQ,IAAG;MACxC,IAAIA,QAAQ,CAACC,MAAM,EAAE,EAAE;QACrB,MAAMC,IAAI,GAAGF,QAAQ,CAACG,GAAG,EAAE;QAC3B,OAAOC,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC,CACrB7B,GAAG,CAACiC,GAAG,KAAK;UACXlE,EAAE,EAAEkE,GAAG;UACP,GAAGJ,IAAI,CAACI,GAAG;SACI,EAAC,CACjBI,MAAM,CAAC3C,OAAO,IAAIA,OAAO,CAAC0C,SAAS,KAAKA,SAAS,CAAC,CAClDzD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACT,IAAIqC,IAAI,CAACrC,CAAC,CAACgB,WAAW,IAAI,CAAC,CAAC,CAACqC,OAAO,EAAE,GAAG,IAAIhB,IAAI,CAACtC,CAAC,CAACiB,WAAW,IAAI,CAAC,CAAC,CAACqC,OAAO,EAAE,CAChF;;MAEL,OAAO,EAAE;IACX,CAAC,CAAC,CAAC;EACL;EAEA;;;EAGAI,iBAAiBA,CAACC,QAAiB,EAAEH,SAAkB;IACrD,OAAO;MACLG,QAAQ,EAAEA,QAAQ,IAAI,qBAAqB;MAC3CH,SAAS,EAAEA,SAAS,IAAI,EAAE;MAC1BpF,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE;QACNC,MAAM,EAAE,CAAC;QACTC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACTC,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE;OACf;MACD+E,YAAY,EAAE;QACZvD,YAAY,EAAE,QAAQ;QACtBE,cAAc,EAAE,KAAK;QACrBC,eAAe,EAAE,CAAC;QAClBI,WAAW,EAAE;OACd;MACDO,SAAS,EAAE,IAAImB,IAAI,EAAE;MACrBrB,WAAW,EAAE,IAAIqB,IAAI;KACtB;EACH;EAEA;;;EAGAuB,kBAAkBA,CAACzF,SAAyB;IAC1C,MAAMC,MAAM,GAAG,IAAI,CAACF,eAAe,CAACC,SAAS,CAAC;IAC9C,MAAM0F,OAAO,GAAG,IAAI,CAACpE,gBAAgB,CAACrB,MAAM,CAAC;IAE7C,OAAO;MAAEA,MAAM;MAAEyF;IAAO,CAAE;EAC5B;;;uBAnUWzG,sBAAsB,EAAA0G,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,SAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,uBAAA;IAAA;EAAA;;;aAAtBjH,sBAAsB;MAAAkH,OAAA,EAAtBlH,sBAAsB,CAAAmH,IAAA;MAAAC,UAAA,EAFrB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}