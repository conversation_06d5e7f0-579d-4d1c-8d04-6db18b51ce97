{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class FooterComponent {\n  static {\n    this.ɵfac = function FooterComponent_Factory(t) {\n      return new (t || FooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FooterComponent,\n      selectors: [[\"app-footer\"]],\n      decls: 70,\n      vars: 0,\n      consts: [[1, \"footer\"], [1, \"footer-container\"], [1, \"footer-top\"], [1, \"footer-logo\"], [1, \"footer-links\"], [1, \"footer-column\"], [\"routerLink\", \"/accueil\"], [\"routerLink\", \"/iris-types\"], [\"routerLink\", \"/iris-diversity\"], [\"routerLink\", \"/dashboard\"], [\"href\", \"#\"], [1, \"footer-bottom\"], [1, \"social-icons\"], [\"href\", \"#\", 1, \"social-icon\"], [1, \"fab\", \"fa-facebook-f\"], [1, \"fab\", \"fa-twitter\"], [1, \"fab\", \"fa-instagram\"], [1, \"fab\", \"fa-linkedin-in\"], [1, \"copyright\"]],\n      template: function FooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\");\n          i0.ɵɵtext(5, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\");\n          i0.ɵɵtext(7, \"\\\"L'avenir du profilage d'iris pour une identification unique\\\"\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 4)(9, \"div\", 5)(10, \"h3\");\n          i0.ɵɵtext(11, \"Navigation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"ul\")(13, \"li\")(14, \"a\", 6);\n          i0.ɵɵtext(15, \"Accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"li\")(17, \"a\", 7);\n          i0.ɵɵtext(18, \"Types d'Iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"li\")(20, \"a\", 8);\n          i0.ɵɵtext(21, \"Diversit\\u00E9 d'Iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"li\")(23, \"a\", 9);\n          i0.ɵɵtext(24, \"Tableau de bord\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 5)(26, \"h3\");\n          i0.ɵɵtext(27, \"Ressources\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"ul\")(29, \"li\")(30, \"a\", 10);\n          i0.ɵɵtext(31, \"Documentation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"li\")(33, \"a\", 10);\n          i0.ɵɵtext(34, \"Tutoriels\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"li\")(36, \"a\", 10);\n          i0.ɵɵtext(37, \"FAQ\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"li\")(39, \"a\", 10);\n          i0.ɵɵtext(40, \"Support\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 5)(42, \"h3\");\n          i0.ɵɵtext(43, \"L\\u00E9gal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"ul\")(45, \"li\")(46, \"a\", 10);\n          i0.ɵɵtext(47, \"Conditions d'utilisation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"li\")(49, \"a\", 10);\n          i0.ɵɵtext(50, \"Politique de confidentialit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\")(52, \"a\", 10);\n          i0.ɵɵtext(53, \"Mentions l\\u00E9gales\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"li\")(55, \"a\", 10);\n          i0.ɵɵtext(56, \"Cookies\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(57, \"div\", 11)(58, \"div\", 12)(59, \"a\", 13);\n          i0.ɵɵelement(60, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"a\", 13);\n          i0.ɵɵelement(62, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"a\", 13);\n          i0.ɵɵelement(64, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"a\", 13);\n          i0.ɵɵelement(66, \"i\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 18)(68, \"p\");\n          i0.ɵɵtext(69, \"\\u00A9 2025 IrisLock. Tous droits r\\u00E9serv\\u00E9s.\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".footer[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #495057;\\n  padding: 0;\\n  margin-top: 60px;\\n  border-top: 1px solid #e9ecef;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n\\n.footer-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n.footer-top[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  padding: 30px 0 20px;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n\\n.footer-logo[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 250px;\\n  margin-bottom: 20px;\\n}\\n.footer-logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin: 0 0 5px 0;\\n  color: #2c7be5;\\n  font-weight: 700;\\n}\\n.footer-logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  margin: 0;\\n  color: #495057;\\n  max-width: 300px;\\n  font-style: italic;\\n}\\n\\n.footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  flex: 2;\\n}\\n\\n.footer-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 140px;\\n  margin-bottom: 15px;\\n  padding: 0 10px;\\n}\\n.footer-column[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n  color: #495057;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #495057;\\n  text-decoration: none;\\n  font-size: 0.8rem;\\n  transition: color 0.2s ease;\\n  position: relative;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  width: 0;\\n  height: 1px;\\n  bottom: -2px;\\n  left: 0;\\n  background-color: #2c7be5;\\n  transition: width 0.3s ease;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #2c7be5;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:before {\\n  width: 100%;\\n}\\n\\n.footer-bottom[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 0;\\n}\\n\\n.social-icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 15px;\\n}\\n.social-icons[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  background-color: #f1f3f5;\\n  color: #495057;\\n  text-decoration: none;\\n  transition: all 0.2s ease;\\n}\\n.social-icons[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.social-icons[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%]:hover {\\n  background-color: #2c7be5;\\n  color: white;\\n  transform: translateY(-3px);\\n}\\n\\n.copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.85rem;\\n  color: #495057;\\n}\\n\\n.footer.dark[_ngcontent-%COMP%] {\\n  background-color: #212529;\\n  color: #f8f9fa;\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n.footer.dark[_ngcontent-%COMP%]   .footer-logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.footer.dark[_ngcontent-%COMP%]   .footer-logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .footer.dark[_ngcontent-%COMP%]   .footer-column[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .footer.dark[_ngcontent-%COMP%]   .footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%], .footer.dark[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #f8f9fa;\\n}\\n.footer.dark[_ngcontent-%COMP%]   .footer-top[_ngcontent-%COMP%], .footer.dark[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.1);\\n}\\n.footer.dark[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: #f8f9fa;\\n}\\n\\n@media (max-width: 768px) {\\n  .footer-top[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .footer-links[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .footer-column[_ngcontent-%COMP%] {\\n    margin-bottom: 30px;\\n    padding: 0;\\n  }\\n  .footer-bottom[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .social-icons[_ngcontent-%COMP%] {\\n    margin: 0 auto 15px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .footer-logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .footer-column[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FooterComponent", "selectors", "decls", "vars", "consts", "template", "FooterComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shared\\footer\\footer.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shared\\footer\\footer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-footer',\n  templateUrl: './footer.component.html',\n  styleUrls: ['./footer.component.scss']\n})\nexport class FooterComponent {\n\n}\n", "<footer class=\"footer\">\n  <div class=\"footer-container\">\n    <div class=\"footer-top\">\n      <div class=\"footer-logo\">\n        <h2>IrisLock</h2>\n        <p>\"L'avenir du profilage d'iris pour une identification unique\"</p>\n      </div>\n\n      <div class=\"footer-links\">\n        <div class=\"footer-column\">\n          <h3>Navigation</h3>\n          <ul>\n            <li><a routerLink=\"/accueil\">Accueil</a></li>\n            <li><a routerLink=\"/iris-types\">Types d'Iris</a></li>\n            <li><a routerLink=\"/iris-diversity\">Diversité d'Iris</a></li>\n            <li><a routerLink=\"/dashboard\">Tableau de bord</a></li>\n          </ul>\n        </div>\n\n        <div class=\"footer-column\">\n          <h3>Ressources</h3>\n          <ul>\n            <li><a href=\"#\">Documentation</a></li>\n            <li><a href=\"#\">Tutoriels</a></li>\n            <li><a href=\"#\">FAQ</a></li>\n            <li><a href=\"#\">Support</a></li>\n          </ul>\n        </div>\n\n        <div class=\"footer-column\">\n          <h3>Légal</h3>\n          <ul>\n            <li><a href=\"#\">Conditions d'utilisation</a></li>\n            <li><a href=\"#\">Politique de confidentialité</a></li>\n            <li><a href=\"#\">Mentions légales</a></li>\n            <li><a href=\"#\">Cookies</a></li>\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"footer-bottom\">\n      <div class=\"social-icons\">\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-facebook-f\"></i></a>\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-twitter\"></i></a>\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-instagram\"></i></a>\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-linkedin-in\"></i></a>\n      </div>\n\n      <div class=\"copyright\">\n        <p>&copy; 2025 IrisLock. Tous droits réservés.</p>\n      </div>\n    </div>\n  </div>\n</footer>\n"], "mappings": ";;AAOA,OAAM,MAAOA,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,gBAAuB;UAIXD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,sEAA6D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGtEH,EAAA,CAAAC,cAAA,aAA0B;UAElBD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,UAAI;UAC2BD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACxCH,EAAA,CAAAC,cAAA,UAAI;UAA4BD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChDH,EAAA,CAAAC,cAAA,UAAI;UAAgCD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACxDH,EAAA,CAAAC,cAAA,UAAI;UAA2BD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAItDH,EAAA,CAAAC,cAAA,cAA2B;UACrBD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,UAAI;UACcD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjCH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7BH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvBH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI/BH,EAAA,CAAAC,cAAA,cAA2B;UACrBD,EAAA,CAAAE,MAAA,kBAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,UAAI;UACcD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,yCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChDH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpCH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMnCH,EAAA,CAAAC,cAAA,eAA2B;UAESD,EAAA,CAAAI,SAAA,aAAiC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACrEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,SAAA,aAA8B;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAClEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,SAAA,aAAgC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACpEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,SAAA,aAAkC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGxEH,EAAA,CAAAC,cAAA,eAAuB;UAClBD,EAAA,CAAAE,MAAA,6DAA2C;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}