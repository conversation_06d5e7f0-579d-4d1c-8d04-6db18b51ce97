{"ast": null, "code": "import { scanInternals } from './scanInternals';\nimport { operate } from '../util/lift';\nexport function reduce(accumulator, seed) {\n  return operate(scanInternals(accumulator, seed, arguments.length >= 2, false, true));\n}", "map": {"version": 3, "names": ["scanInternals", "operate", "reduce", "accumulator", "seed", "arguments", "length"], "sources": ["E:/aymen/pfa/pfa/node_modules/rxjs/dist/esm/internal/operators/reduce.js"], "sourcesContent": ["import { scanInternals } from './scanInternals';\nimport { operate } from '../util/lift';\nexport function reduce(accumulator, seed) {\n    return operate(scanInternals(accumulator, seed, arguments.length >= 2, false, true));\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASC,MAAMA,CAACC,WAAW,EAAEC,IAAI,EAAE;EACtC,OAAOH,OAAO,CAACD,aAAa,CAACG,WAAW,EAAEC,IAAI,EAAEC,SAAS,CAACC,MAAM,IAAI,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACxF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}