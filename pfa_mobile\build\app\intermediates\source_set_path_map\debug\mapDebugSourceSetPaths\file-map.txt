com.example.pfa_mobile.app-jetified-lifecycle-process-2.7.0-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0095a4dba85ef3c38f0a9652b34cd9be\transformed\jetified-lifecycle-process-2.7.0\res
com.example.pfa_mobile.app-jetified-appcompat-resources-1.1.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0774b2cdbbb3e8e500618a693ab62325\transformed\jetified-appcompat-resources-1.1.0\res
com.example.pfa_mobile.app-lifecycle-runtime-2.7.0-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\07d5ffcb9c6912ffd610dc84a8004380\transformed\lifecycle-runtime-2.7.0\res
com.example.pfa_mobile.app-jetified-fragment-ktx-1.7.1-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c2fe07ca1553c701c96a682f9b78a46\transformed\jetified-fragment-ktx-1.7.1\res
com.example.pfa_mobile.app-lifecycle-viewmodel-2.7.0-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0df03071d3ea5a13ca8a701a85da690b\transformed\lifecycle-viewmodel-2.7.0\res
com.example.pfa_mobile.app-jetified-core-ktx-1.13.1-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1012b3d72a26572e790cb673f126d840\transformed\jetified-core-ktx-1.13.1\res
com.example.pfa_mobile.app-lifecycle-livedata-2.7.0-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\141e4ef551e1154e2ac426f165237904\transformed\lifecycle-livedata-2.7.0\res
com.example.pfa_mobile.app-jetified-savedstate-1.2.1-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1f37faca982b225960866422b14b0e48\transformed\jetified-savedstate-1.2.1\res
com.example.pfa_mobile.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\23adfe722c8911b0c72ccd7b6c20c672\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.pfa_mobile.app-lifecycle-livedata-core-2.7.0-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\30580628b6a4c90a31a9185e5e7442f2\transformed\lifecycle-livedata-core-2.7.0\res
com.example.pfa_mobile.app-recyclerview-1.0.0-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\31710a1d11dfb3018b4d584b1dcc524f\transformed\recyclerview-1.0.0\res
com.example.pfa_mobile.app-jetified-lifecycle-livedata-core-ktx-2.7.0-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\35d43ac5b93081fc719d5206591bc8b3\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.pfa_mobile.app-jetified-datastore-preferences-release-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e70339c6033b424474d01ed8bf2ee0e\transformed\jetified-datastore-preferences-release\res
com.example.pfa_mobile.app-jetified-lifecycle-viewmodel-ktx-2.7.0-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4cf4aaf0c2a5f8e9be19976f292485c2\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.pfa_mobile.app-fragment-1.7.1-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\504a3b9bc759ca6028567aaea36c5498\transformed\fragment-1.7.1\res
com.example.pfa_mobile.app-jetified-core-1.0.0-15 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5231da36518c5740ff6ec8f636c4adaa\transformed\jetified-core-1.0.0\res
com.example.pfa_mobile.app-slidingpanelayout-1.2.0-16 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b2e1faab8b81a1d02bc31ca8ba888c2\transformed\slidingpanelayout-1.2.0\res
com.example.pfa_mobile.app-transition-1.4.1-17 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c90255c19f9c683385e871d79ee1ace\transformed\transition-1.4.1\res
com.example.pfa_mobile.app-browser-1.4.0-18 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6643c4258ae11fe541bbf5ee341bcb98\transformed\browser-1.4.0\res
com.example.pfa_mobile.app-preference-1.2.1-19 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6fec79dfcef552889e3835cc7bab572a\transformed\preference-1.2.1\res
com.example.pfa_mobile.app-jetified-profileinstaller-1.3.1-20 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\71c2a5c462ada5f663b8ca591b1907a5\transformed\jetified-profileinstaller-1.3.1\res
com.example.pfa_mobile.app-jetified-savedstate-ktx-1.2.1-21 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73983292212b7db8ee4526aa2f086e1d\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.pfa_mobile.app-jetified-window-1.2.0-22 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73db24a2829d8fb9cf5663cfbb96cfeb\transformed\jetified-window-1.2.0\res
com.example.pfa_mobile.app-coordinatorlayout-1.0.0-23 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\765cbe31314e92111ddb6ec950f59a6b\transformed\coordinatorlayout-1.0.0\res
com.example.pfa_mobile.app-jetified-annotation-experimental-1.4.0-24 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7afb1ce17e862ead92a72a9258c9afa9\transformed\jetified-annotation-experimental-1.4.0\res
com.example.pfa_mobile.app-jetified-play-services-auth-20.7.0-25 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f3cbd85e9776d1033d598bbdc03a650\transformed\jetified-play-services-auth-20.7.0\res
com.example.pfa_mobile.app-core-1.13.1-26 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f46bbd13206153774356ca6103aa890\transformed\core-1.13.1\res
com.example.pfa_mobile.app-jetified-credentials-1.2.0-rc01-27 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\847a26655a62540eb6e1b568482ff8fb\transformed\jetified-credentials-1.2.0-rc01\res
com.example.pfa_mobile.app-appcompat-1.1.0-28 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8672542fff6e2103c63e7b98b328f638\transformed\appcompat-1.1.0\res
com.example.pfa_mobile.app-jetified-activity-1.9.3-29 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\87eca1956ddb86a2a7193da8df59556f\transformed\jetified-activity-1.9.3\res
com.example.pfa_mobile.app-jetified-window-java-1.2.0-30 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bb9749c6545c229064751184c200f17\transformed\jetified-window-java-1.2.0\res
com.example.pfa_mobile.app-core-runtime-2.2.0-31 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8bc22c9aee1a6640ae9b0a38be5c9ed3\transformed\core-runtime-2.2.0\res
com.example.pfa_mobile.app-jetified-datastore-release-32 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\973a8f19afc88ef8595e829184e66215\transformed\jetified-datastore-release\res
com.example.pfa_mobile.app-jetified-credentials-play-services-auth-1.2.0-rc01-33 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a159499e04f9d5848eb9c527e821c718\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.example.pfa_mobile.app-jetified-datastore-core-release-34 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a9f3b8c08aa98c50dc3947165cdbc861\transformed\jetified-datastore-core-release\res
com.example.pfa_mobile.app-jetified-tracing-1.2.0-35 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba2395cf8fc4cc2c2d9a7b0f13aa21c0\transformed\jetified-tracing-1.2.0\res
com.example.pfa_mobile.app-jetified-startup-runtime-1.1.1-36 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c4042ec9e8cc7d9cdf61197d11f01375\transformed\jetified-startup-runtime-1.1.1\res
com.example.pfa_mobile.app-jetified-play-services-basement-18.4.0-37 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf636ef2f0738047fe8129f320ef339e\transformed\jetified-play-services-basement-18.4.0\res
com.example.pfa_mobile.app-jetified-core-common-2.0.3-38 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc78addb618cc9a88505262f8a23e42d\transformed\jetified-core-common-2.0.3\res
com.example.pfa_mobile.app-jetified-play-services-base-18.1.0-39 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb80b7c20f86db896fc82173afcd18d6\transformed\jetified-play-services-base-18.1.0\res
com.example.pfa_mobile.app-jetified-firebase-common-21.0.0-40 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ec4138de37aaf41c537b7e74e719439c\transformed\jetified-firebase-common-21.0.0\res
com.example.pfa_mobile.app-jetified-lifecycle-runtime-ktx-2.7.0-41 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ee40907df6635dadbd2abe5161721138\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.pfa_mobile.app-jetified-activity-ktx-1.9.3-42 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f63e4fb308e51274fbafb363fde41672\transformed\jetified-activity-ktx-1.9.3\res
com.example.pfa_mobile.app-debug-43 E:\aymen\pfa\pfa_mobile\android\app\src\debug\res
com.example.pfa_mobile.app-main-44 E:\aymen\pfa\pfa_mobile\android\app\src\main\res
com.example.pfa_mobile.app-google-services-45 E:\aymen\pfa\pfa_mobile\build\app\generated\res\google-services\debug
com.example.pfa_mobile.app-pngs-46 E:\aymen\pfa\pfa_mobile\build\app\generated\res\pngs\debug
com.example.pfa_mobile.app-resValues-47 E:\aymen\pfa\pfa_mobile\build\app\generated\res\resValues\debug
com.example.pfa_mobile.app-packageDebugResources-48 E:\aymen\pfa\pfa_mobile\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.pfa_mobile.app-packageDebugResources-49 E:\aymen\pfa\pfa_mobile\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.pfa_mobile.app-debug-50 E:\aymen\pfa\pfa_mobile\build\app\intermediates\merged_res\debug\mergeDebugResources
com.example.pfa_mobile.app-debug-51 E:\aymen\pfa\pfa_mobile\build\camera_android\intermediates\packaged_res\debug\packageDebugResources
com.example.pfa_mobile.app-debug-52 E:\aymen\pfa\pfa_mobile\build\cloud_firestore\intermediates\packaged_res\debug\packageDebugResources
com.example.pfa_mobile.app-debug-53 E:\aymen\pfa\pfa_mobile\build\firebase_auth\intermediates\packaged_res\debug\packageDebugResources
com.example.pfa_mobile.app-debug-54 E:\aymen\pfa\pfa_mobile\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.example.pfa_mobile.app-debug-55 E:\aymen\pfa\pfa_mobile\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.example.pfa_mobile.app-debug-56 E:\aymen\pfa\pfa_mobile\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
com.example.pfa_mobile.app-debug-57 E:\aymen\pfa\pfa_mobile\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.example.pfa_mobile.app-debug-58 E:\aymen\pfa\pfa_mobile\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
