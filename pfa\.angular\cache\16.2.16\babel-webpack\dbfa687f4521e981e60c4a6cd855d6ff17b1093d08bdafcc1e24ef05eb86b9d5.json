{"ast": null, "code": "import { PERSONALITY_QUESTIONS } from '../models/personality-test.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/personality-test.service\";\nimport * as i2 from \"../services/iris-compatibility.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction PersonalityTestComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"h1\", 8);\n    i0.ɵɵtext(4, \"Test de Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 9);\n    i0.ɵɵelementStart(6, \"p\", 10);\n    i0.ɵɵtext(7, \"D\\u00E9couvrez votre profil psychotechnique\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"div\", 12)(10, \"div\", 13)(11, \"span\", 14);\n    i0.ɵɵtext(12, \"\\uD83D\\uDCDD\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 15)(14, \"h3\");\n    i0.ɵɵtext(15, \"32 Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\");\n    i0.ɵɵtext(17, \"Questions cibl\\u00E9es pour analyser votre personnalit\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 13)(19, \"span\", 14);\n    i0.ɵɵtext(20, \"\\u23F1\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 15)(22, \"h3\");\n    i0.ɵɵtext(23, \"5-10 Minutes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\");\n    i0.ɵɵtext(25, \"Temps estim\\u00E9 pour compl\\u00E9ter le test\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 13)(27, \"span\", 14);\n    i0.ɵɵtext(28, \"\\uD83C\\uDFAF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 15)(30, \"h3\");\n    i0.ɵɵtext(31, \"4 Profils Principaux\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"Flower, Jewel, Shaker, Stream + profils interm\\u00E9diaires\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 16)(35, \"h3\");\n    i0.ɵɵtext(36, \"Informations du test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"p\")(38, \"strong\");\n    i0.ɵɵtext(39, \"Nom:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"p\")(42, \"strong\");\n    i0.ɵɵtext(43, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"p\", 17);\n    i0.ɵɵtext(46, \"Les r\\u00E9sultats seront sauvegard\\u00E9s dans la base de donn\\u00E9es PFA1\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(47, \"div\", 18)(48, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_Template_button_click_48_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.startTest());\n    });\n    i0.ɵɵelementStart(49, \"span\");\n    i0.ɵɵtext(50, \"Commencer le Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 14);\n    i0.ɵɵtext(52, \"\\u2192\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.goToHome());\n    });\n    i0.ɵɵtext(54, \" Retour \\u00E0 l'accueil \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(40);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedUser.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedUser.email, \"\");\n  }\n}\nfunction PersonalityTestComponent_div_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"h2\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 33)(5, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_2_div_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.answerQuestion(true));\n    });\n    i0.ɵɵelementStart(6, \"span\", 35);\n    i0.ɵɵtext(7, \"\\u2713\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"Oui\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_2_div_11_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.answerQuestion(false));\n    });\n    i0.ɵɵelementStart(11, \"span\", 35);\n    i0.ɵɵtext(12, \"\\u2717\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Non\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r7.currentQuestion.question);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r7.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r7.isLoading);\n  }\n}\nfunction PersonalityTestComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"div\", 23)(3, \"div\", 24)(4, \"span\", 25);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 26);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 27);\n    i0.ɵɵelement(10, \"div\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, PersonalityTestComponent_div_2_div_11_Template, 15, 3, \"div\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"Question \", ctx_r1.currentQuestionNumber, \" sur \", ctx_r1.totalQuestions, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(8, 6, ctx_r1.progressPercentage, \"1.0-0\"), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.progressPercentage, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentQuestion);\n  }\n}\nfunction PersonalityTestComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 38);\n    i0.ɵɵelement(2, \"div\", 39);\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Analyse de votre profil...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Calcul des scores et d\\u00E9termination de votre personnalit\\u00E9\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PersonalityTestComponent_div_4_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"span\", 59);\n    i0.ɵɵtext(2, \"Profil Interm\\u00E9diaire\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"span\", 60);\n    i0.ɵɵtext(2, \"Profil Principal\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"h3\");\n    i0.ɵɵtext(2, \"Scores d\\u00E9taill\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 62)(4, \"div\", 63)(5, \"span\", 47);\n    i0.ɵɵtext(6, \"Flower\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 64);\n    i0.ɵɵelement(8, \"div\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 48);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 63)(12, \"span\", 47);\n    i0.ɵɵtext(13, \"Jewel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 64);\n    i0.ɵɵelement(15, \"div\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 48);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 63)(19, \"span\", 47);\n    i0.ɵɵtext(20, \"Shaker\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 64);\n    i0.ɵɵelement(22, \"div\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 48);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 63)(26, \"span\", 47);\n    i0.ɵɵtext(27, \"Stream\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 64);\n    i0.ɵɵelement(29, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 48);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleProp(\"width\", ctx_r13.testSession.scores.flower / 4 * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.testSession.scores.flower, \"/4\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"width\", ctx_r13.testSession.scores.jewel / 4 * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.testSession.scores.jewel, \"/4\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"width\", ctx_r13.testSession.scores.shaker / 4 * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.testSession.scores.shaker, \"/4\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"width\", ctx_r13.testSession.scores.stream / 4 * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.testSession.scores.stream, \"/4\");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53)(1, \"span\", 54);\n    i0.ɵɵtext(2, \"ID de session:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 55);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r14.testSession == null ? null : ctx_r14.testSession.id);\n  }\n}\nfunction PersonalityTestComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"div\", 42)(3, \"h1\", 8);\n    i0.ɵɵtext(4, \"Votre Profil de Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 43)(7, \"div\", 44)(8, \"h2\", 45);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 46)(11, \"span\", 47);\n    i0.ɵɵtext(12, \"Score de confiance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 48);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(15, PersonalityTestComponent_div_4_div_15_Template, 3, 0, \"div\", 49);\n    i0.ɵɵtemplate(16, PersonalityTestComponent_div_4_div_16_Template, 3, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 50)(18, \"h3\");\n    i0.ɵɵtext(19, \"Description de votre profil\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, PersonalityTestComponent_div_4_div_22_Template, 32, 12, \"div\", 51);\n    i0.ɵɵelementStart(23, \"div\", 52)(24, \"div\", 53)(25, \"span\", 54);\n    i0.ɵɵtext(26, \"Test compl\\u00E9t\\u00E9 le:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 55);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(30, PersonalityTestComponent_div_4_div_30_Template, 5, 1, \"div\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 57)(32, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_Template_button_click_32_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.restartTest());\n    });\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34, \"Refaire le Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\", 14);\n    i0.ɵɵtext(36, \"\\uD83D\\uDD04\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.goToHome());\n    });\n    i0.ɵɵtext(38, \" Retour \\u00E0 l'accueil \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMap(\"profile-\" + ctx_r3.finalProfile.primaryClass.toLowerCase().replace(\"-\", \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.finalProfile.primaryClass);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.finalProfile.confidenceScore, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.finalProfile.isIntermediate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.finalProfile.isIntermediate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.finalProfile.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testSession);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(29, 10, ctx_r3.testSession == null ? null : ctx_r3.testSession.completedAt, \"dd/MM/yyyy \\u00E0 HH:mm\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testSession == null ? null : ctx_r3.testSession.id);\n  }\n}\nexport class PersonalityTestComponent {\n  constructor(personalityTestService, irisCompatibilityService, router) {\n    this.personalityTestService = personalityTestService;\n    this.irisCompatibilityService = irisCompatibilityService;\n    this.router = router;\n    // Données du test\n    this.questions = PERSONALITY_QUESTIONS;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.testSession = null;\n    // État du composant\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.isLoading = false;\n    this.showResults = false;\n    // Comptes statiques pour les tests\n    this.staticUsers = [{\n      id: 1,\n      name: 'Marie Dubois',\n      email: '<EMAIL>',\n      description: 'Profil créatif et émotionnel'\n    }, {\n      id: 2,\n      name: 'Jean Martin',\n      email: '<EMAIL>',\n      description: 'Profil analytique et structuré'\n    }, {\n      id: 3,\n      name: 'Sophie Leroy',\n      email: '<EMAIL>',\n      description: 'Profil dynamique et aventurier'\n    }, {\n      id: 4,\n      name: 'Pierre Moreau',\n      email: '<EMAIL>',\n      description: 'Profil paisible et réfléchi'\n    }, {\n      id: 5,\n      name: 'Emma Bernard',\n      email: '<EMAIL>',\n      description: 'Profil mixte créatif-analytique'\n    }];\n    this.selectedUser = this.staticUsers[0]; // Utilisateur par défaut\n    this.currentUser = null; // Utilisateur connecté\n    // Résultats\n    this.finalProfile = null;\n    this.compatibilityResult = null;\n  }\n  ngOnInit() {\n    // Récupérer l'utilisateur connecté\n    const currentUserData = localStorage.getItem('currentUser');\n    if (currentUserData) {\n      this.currentUser = JSON.parse(currentUserData);\n      // Utiliser l'utilisateur connecté pour le test\n      this.selectedUser = {\n        name: this.currentUser.name,\n        email: this.currentUser.email,\n        description: 'Utilisateur connecté'\n      };\n    }\n    this.initializeTest();\n  }\n  /**\n   * Initialise le test\n   */\n  initializeTest() {\n    this.testSession = this.personalityTestService.createTestSession(this.selectedUser.name, this.selectedUser.email);\n  }\n  /**\n   * Sélectionne un utilisateur de test\n   */\n  selectUser(user) {\n    this.selectedUser = user;\n    this.initializeTest();\n  }\n  /**\n   * Démarre le test\n   */\n  startTest() {\n    this.isTestStarted = true;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    if (this.testSession) {\n      this.testSession.startedAt = new Date();\n    }\n  }\n  /**\n   * Traite la réponse à une question\n   */\n  answerQuestion(answer) {\n    if (!this.testSession) return;\n    const currentQuestion = this.questions[this.currentQuestionIndex];\n    const response = {\n      questionId: currentQuestion.id,\n      answer: answer,\n      timestamp: new Date()\n    };\n    this.responses.push(response);\n    this.testSession.responses = this.responses;\n    // Passer à la question suivante ou terminer le test\n    if (this.currentQuestionIndex < this.questions.length - 1) {\n      this.currentQuestionIndex++;\n    } else {\n      this.completeTest();\n    }\n  }\n  /**\n   * Termine le test et calcule les résultats\n   */\n  completeTest() {\n    if (!this.testSession) return;\n    this.isLoading = true;\n    // Calculer les résultats\n    const results = this.personalityTestService.processTestResults(this.responses);\n    // Mettre à jour la session\n    this.testSession.scores = results.scores;\n    this.testSession.finalProfile = results.profile;\n    this.testSession.completedAt = new Date();\n    this.finalProfile = results.profile;\n    // Sauvegarder dans Firebase\n    this.personalityTestService.saveTestSession(this.testSession).subscribe({\n      next: sessionId => {\n        console.log('Test sauvegardé avec l\\'ID:', sessionId);\n        this.testSession.id = sessionId;\n        this.isLoading = false;\n        this.isTestCompleted = true;\n        this.showResults = true;\n      },\n      error: error => {\n        console.error('Erreur lors de la sauvegarde:', error);\n        this.isLoading = false;\n        this.isTestCompleted = true;\n        this.showResults = true;\n      }\n    });\n  }\n  /**\n   * Redémarre le test\n   */\n  restartTest() {\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.showResults = false;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.finalProfile = null;\n    this.initializeTest();\n  }\n  /**\n   * Retourne à l'accueil\n   */\n  goToHome() {\n    this.router.navigate(['/accueil']);\n  }\n  /**\n   * Obtient la question actuelle\n   */\n  get currentQuestion() {\n    return this.questions[this.currentQuestionIndex] || null;\n  }\n  /**\n   * Obtient le pourcentage de progression\n   */\n  get progressPercentage() {\n    return (this.currentQuestionIndex + 1) / this.questions.length * 100;\n  }\n  /**\n   * Obtient le numéro de la question actuelle\n   */\n  get currentQuestionNumber() {\n    return this.currentQuestionIndex + 1;\n  }\n  /**\n   * Obtient le nombre total de questions\n   */\n  get totalQuestions() {\n    return this.questions.length;\n  }\n  static {\n    this.ɵfac = function PersonalityTestComponent_Factory(t) {\n      return new (t || PersonalityTestComponent)(i0.ɵɵdirectiveInject(i1.PersonalityTestService), i0.ɵɵdirectiveInject(i2.IrisCompatibilityService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PersonalityTestComponent,\n      selectors: [[\"app-personality-test\"]],\n      decls: 5,\n      vars: 4,\n      consts: [[1, \"personality-test-container\"], [\"class\", \"test-intro\", 4, \"ngIf\"], [\"class\", \"test-interface\", 4, \"ngIf\"], [\"class\", \"loading-screen\", 4, \"ngIf\"], [\"class\", \"test-results\", 4, \"ngIf\"], [1, \"test-intro\"], [1, \"intro-card\"], [1, \"intro-header\"], [1, \"title\"], [1, \"divider\"], [1, \"subtitle\"], [1, \"intro-content\"], [1, \"test-info\"], [1, \"info-item\"], [1, \"icon\"], [1, \"info-text\"], [1, \"user-info\"], [1, \"note\"], [1, \"intro-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"test-interface\"], [1, \"test-card\"], [1, \"progress-section\"], [1, \"progress-info\"], [1, \"question-counter\"], [1, \"progress-percentage\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [\"class\", \"question-section\", 4, \"ngIf\"], [1, \"question-section\"], [1, \"question-content\"], [1, \"question-text\"], [1, \"answer-buttons\"], [1, \"btn\", \"btn-answer\", \"btn-yes\", 3, \"disabled\", \"click\"], [1, \"answer-icon\"], [1, \"btn\", \"btn-answer\", \"btn-no\", 3, \"disabled\", \"click\"], [1, \"loading-screen\"], [1, \"loading-card\"], [1, \"loading-spinner\"], [1, \"test-results\"], [1, \"results-card\"], [1, \"results-header\"], [1, \"profile-summary\"], [1, \"profile-badge\"], [1, \"profile-name\"], [1, \"confidence-score\"], [1, \"score-label\"], [1, \"score-value\"], [\"class\", \"profile-type\", 4, \"ngIf\"], [1, \"profile-description\"], [\"class\", \"detailed-scores\", 4, \"ngIf\"], [1, \"test-info-summary\"], [1, \"info-row\"], [1, \"label\"], [1, \"value\"], [\"class\", \"info-row\", 4, \"ngIf\"], [1, \"results-actions\"], [1, \"profile-type\"], [1, \"type-badge\", \"intermediate\"], [1, \"type-badge\", \"primary\"], [1, \"detailed-scores\"], [1, \"scores-grid\"], [1, \"score-item\"], [1, \"score-bar\"], [1, \"score-fill\", \"flower\"], [1, \"score-fill\", \"jewel\"], [1, \"score-fill\", \"shaker\"], [1, \"score-fill\", \"stream\"]],\n      template: function PersonalityTestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, PersonalityTestComponent_div_1_Template, 55, 2, \"div\", 1);\n          i0.ɵɵtemplate(2, PersonalityTestComponent_div_2_Template, 12, 9, \"div\", 2);\n          i0.ɵɵtemplate(3, PersonalityTestComponent_div_3_Template, 7, 0, \"div\", 3);\n          i0.ɵɵtemplate(4, PersonalityTestComponent_div_4_Template, 39, 13, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isTestStarted && !ctx.showResults);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isTestStarted && !ctx.showResults);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showResults && ctx.finalProfile);\n        }\n      },\n      dependencies: [i4.NgIf, i4.DecimalPipe, i4.DatePipe],\n      styles: [\".personality-test-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n}\\n\\n.intro-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%], .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 20px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  padding: 3rem;\\n  max-width: 800px;\\n  width: 100%;\\n  margin: 0 auto;\\n}\\n\\n.title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2d3748;\\n  text-align: center;\\n  margin-bottom: 1rem;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #718096;\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  margin: 0 auto 2rem;\\n  border-radius: 2px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 1rem 2rem;\\n  border: none;\\n  border-radius: 12px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  text-decoration: none;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: #e2e8f0;\\n  color: #4a5568;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #cbd5e0;\\n  transform: translateY(-1px);\\n}\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none !important;\\n}\\n\\n.test-intro[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.test-intro[_ngcontent-%COMP%]   .test-info[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1.5rem;\\n  margin-bottom: 2rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1.5rem;\\n  background: rgba(102, 126, 234, 0.1);\\n  border-radius: 12px;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.1);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #2d3748;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n  color: #4a5568;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   p.note[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .intro-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-top: 2rem;\\n}\\n@media (max-width: 768px) {\\n  .test-intro[_ngcontent-%COMP%]   .intro-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n.test-interface[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .question-counter[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #667eea;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  background: #e2e8f0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  transition: width 0.3s ease;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   .question-text[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  color: #2d3748;\\n  line-height: 1.4;\\n  margin: 0;\\n}\\n.test-interface[_ngcontent-%COMP%]   .answer-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n  justify-content: center;\\n}\\n@media (max-width: 768px) {\\n  .test-interface[_ngcontent-%COMP%]   .answer-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer[_ngcontent-%COMP%] {\\n  padding: 1.5rem 3rem;\\n  font-size: 1.3rem;\\n  min-width: 150px;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer[_ngcontent-%COMP%]   .answer-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-yes[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\\n  color: white;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-yes[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 15px 30px rgba(72, 187, 120, 0.4);\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-no[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\\n  color: white;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-no[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 15px 30px rgba(245, 101, 101, 0.4);\\n}\\n\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border: 4px solid #e2e8f0;\\n  border-top: 4px solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin: 0 auto 2rem;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin-bottom: 1rem;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #718096;\\n  margin: 0;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-summary[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 2rem;\\n  border-radius: 20px;\\n  margin-bottom: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-flower[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbb6ce 0%, #f687b3 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-jewel[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #90cdf4 0%, #63b3ed 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-shaker[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbd38d 0%, #f6ad55 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-stream[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9ae6b4 0%, #68d391 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-flowerjewel[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbb6ce 0%, #90cdf4 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-jewelshaker[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #90cdf4 0%, #fbd38d 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-shakerstream[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbd38d 0%, #9ae6b4 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-streamflower[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9ae6b4 0%, #fbb6ce 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin: 0 0 1rem 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge.primary[_ngcontent-%COMP%] {\\n  background: rgba(102, 126, 234, 0.2);\\n  color: #667eea;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge.intermediate[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.2);\\n  color: #764ba2;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%] {\\n  background: rgba(102, 126, 234, 0.1);\\n  padding: 2rem;\\n  border-radius: 12px;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  line-height: 1.6;\\n  margin: 0;\\n  font-size: 1.1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1.5rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .scores-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem;\\n  background: rgba(255, 255, 255, 0.5);\\n  border-radius: 8px;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  font-weight: 600;\\n  color: #4a5568;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 8px;\\n  background: #e2e8f0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: width 0.5s ease;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.flower[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #fbb6ce, #f687b3);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.jewel[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #90cdf4, #63b3ed);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.shaker[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #fbd38d, #f6ad55);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.stream[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #9ae6b4, #68d391);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  min-width: 40px;\\n  font-weight: 600;\\n  color: #2d3748;\\n  text-align: right;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.1);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.test-results[_ngcontent-%COMP%]   .results-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-top: 2rem;\\n}\\n@media (max-width: 768px) {\\n  .test-results[_ngcontent-%COMP%]   .results-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .personality-test-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .intro-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%], .loading-card[_ngcontent-%COMP%] {\\n    padding: 2rem;\\n  }\\n  .title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .question-text[_ngcontent-%COMP%] {\\n    font-size: 1.4rem !important;\\n  }\\n  .btn-answer[_ngcontent-%COMP%] {\\n    padding: 1rem 2rem !important;\\n    font-size: 1.1rem !important;\\n    min-width: 120px !important;\\n  }\\n  .profile-name[_ngcontent-%COMP%] {\\n    font-size: 1.5rem !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGVyc29uYWxpdHktdGVzdC9wZXJzb25hbGl0eS10ZXN0LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsaUJBQUE7RUFDQSw2REFBQTtFQUNBLGFBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGtDQUFBO0FBQ0Y7O0FBR0E7RUFDRSxxQ0FBQTtFQUNBLG1DQUFBO1VBQUEsMkJBQUE7RUFDQSxtQkFBQTtFQUNBLDBDQUFBO0VBQ0EsYUFBQTtFQUNBLGdCQUFBO0VBQ0EsV0FBQTtFQUNBLGNBQUE7QUFBRjs7QUFJQTtFQUNFLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtBQURGOztBQUlBO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxtQkFBQTtBQURGOztBQUlBO0VBQ0UsV0FBQTtFQUNBLFdBQUE7RUFDQSxvREFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7QUFERjs7QUFLQTtFQUNFLGtCQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSxXQUFBO0VBQ0EscUJBQUE7QUFGRjtBQUlFO0VBQ0UsNkRBQUE7RUFDQSxZQUFBO0FBRko7QUFJSTtFQUNFLDJCQUFBO0VBQ0EsZ0RBQUE7QUFGTjtBQU1FO0VBQ0UsbUJBQUE7RUFDQSxjQUFBO0FBSko7QUFNSTtFQUNFLG1CQUFBO0VBQ0EsMkJBQUE7QUFKTjtBQVFFO0VBQ0UsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsMEJBQUE7QUFOSjs7QUFZRTtFQUNFLGNBQUE7QUFUSjtBQVlFO0VBQ0UsYUFBQTtFQUNBLDJEQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0FBVko7QUFhRTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFNBQUE7RUFDQSxlQUFBO0VBQ0Esb0NBQUE7RUFDQSxtQkFBQTtBQVhKO0FBYUk7RUFDRSxlQUFBO0FBWE47QUFlTTtFQUNFLG9CQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0FBYlI7QUFnQk07RUFDRSxTQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0FBZFI7QUFtQkU7RUFDRSxtQ0FBQTtFQUNBLGVBQUE7RUFDQSxtQkFBQTtBQWpCSjtBQW1CSTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtBQWpCTjtBQW9CSTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtBQWxCTjtBQW9CTTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLGlCQUFBO0FBbEJSO0FBdUJFO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQUFBO0FBckJKO0FBdUJJO0VBTkY7SUFPSSxzQkFBQTtFQXBCSjtBQUNGOztBQTBCRTtFQUNFLG1CQUFBO0FBdkJKO0FBMEJFO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtBQXhCSjtBQTBCSTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtBQXhCTjtBQTJCSTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtBQXpCTjtBQTZCRTtFQUNFLFdBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGdCQUFBO0FBM0JKO0FBOEJFO0VBQ0UsWUFBQTtFQUNBLG9EQUFBO0VBQ0EsMkJBQUE7QUE1Qko7QUErQkU7RUFDRSxrQkFBQTtBQTdCSjtBQWdDRTtFQUNFLG1CQUFBO0FBOUJKO0FBZ0NJO0VBQ0UsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7QUE5Qk47QUFrQ0U7RUFDRSxhQUFBO0VBQ0EsU0FBQTtFQUNBLHVCQUFBO0FBaENKO0FBa0NJO0VBTEY7SUFNSSxzQkFBQTtJQUNBLG1CQUFBO0VBL0JKO0FBQ0Y7QUFrQ0U7RUFDRSxvQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFoQ0o7QUFrQ0k7RUFDRSxpQkFBQTtBQWhDTjtBQW1DSTtFQUNFLDZEQUFBO0VBQ0EsWUFBQTtBQWpDTjtBQW1DTTtFQUNFLDJCQUFBO0VBQ0EsK0NBQUE7QUFqQ1I7QUFxQ0k7RUFDRSw2REFBQTtFQUNBLFlBQUE7QUFuQ047QUFxQ007RUFDRSwyQkFBQTtFQUNBLGdEQUFBO0FBbkNSOztBQTJDRTtFQUNFLGtCQUFBO0FBeENKO0FBMENJO0VBQ0UsV0FBQTtFQUNBLFlBQUE7RUFDQSx5QkFBQTtFQUNBLDZCQUFBO0VBQ0Esa0JBQUE7RUFDQSxrQ0FBQTtFQUNBLG1CQUFBO0FBeENOO0FBMkNJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0FBekNOO0FBNENJO0VBQ0UsY0FBQTtFQUNBLFNBQUE7QUExQ047O0FBK0NBO0VBQ0U7SUFBSyx1QkFBQTtFQTNDTDtFQTRDQTtJQUFPLHlCQUFBO0VBekNQO0FBQ0Y7QUE2Q0U7RUFDRSxrQkFBQTtFQUNBLGNBQUE7QUEzQ0o7QUE4Q0U7RUFDRSxxQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBNUNKO0FBOENJO0VBQ0UsNkRBQUE7QUE1Q047QUErQ0k7RUFDRSw2REFBQTtBQTdDTjtBQWdESTtFQUNFLDZEQUFBO0FBOUNOO0FBaURJO0VBQ0UsNkRBQUE7QUEvQ047QUFrREk7RUFDRSw2REFBQTtBQWhETjtBQW1ESTtFQUNFLDZEQUFBO0FBakROO0FBb0RJO0VBQ0UsNkRBQUE7QUFsRE47QUFxREk7RUFDRSw2REFBQTtBQW5ETjtBQXNESTtFQUNFLFlBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxrQkFBQTtFQUNBLHlDQUFBO0FBcEROO0FBdURJO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsV0FBQTtBQXJETjtBQXVETTtFQUNFLCtCQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtBQXJEUjtBQXdETTtFQUNFLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0EseUNBQUE7QUF0RFI7QUEyREU7RUFDRSxtQkFBQTtBQXpESjtBQTJESTtFQUNFLHFCQUFBO0VBQ0Esb0JBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUF6RE47QUEyRE07RUFDRSxvQ0FBQTtFQUNBLGNBQUE7QUF6RFI7QUE0RE07RUFDRSxtQ0FBQTtFQUNBLGNBQUE7QUExRFI7QUErREU7RUFDRSxvQ0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7QUE3REo7QUErREk7RUFDRSxjQUFBO0VBQ0Esa0JBQUE7QUE3RE47QUFnRUk7RUFDRSxjQUFBO0VBQ0EsZ0JBQUE7RUFDQSxTQUFBO0VBQ0EsaUJBQUE7QUE5RE47QUFrRUU7RUFDRSxjQUFBO0FBaEVKO0FBa0VJO0VBQ0UsY0FBQTtFQUNBLG9CQUFBO0FBaEVOO0FBbUVJO0VBQ0UsYUFBQTtFQUNBLFNBQUE7QUFqRU47QUFvRUk7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsYUFBQTtFQUNBLG9DQUFBO0VBQ0Esa0JBQUE7QUFsRU47QUFvRU07RUFDRSxlQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0FBbEVSO0FBcUVNO0VBQ0UsT0FBQTtFQUNBLFdBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsZ0JBQUE7QUFuRVI7QUFxRVE7RUFDRSxZQUFBO0VBQ0EsMkJBQUE7QUFuRVY7QUFxRVU7RUFDRSxvREFBQTtBQW5FWjtBQXNFVTtFQUNFLG9EQUFBO0FBcEVaO0FBdUVVO0VBQ0Usb0RBQUE7QUFyRVo7QUF3RVU7RUFDRSxvREFBQTtBQXRFWjtBQTJFTTtFQUNFLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxpQkFBQTtBQXpFUjtBQThFRTtFQUNFLG1DQUFBO0VBQ0EsZUFBQTtFQUNBLG1CQUFBO0VBQ0EsY0FBQTtBQTVFSjtBQThFSTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EscUJBQUE7QUE1RU47QUE4RU07RUFDRSxnQkFBQTtBQTVFUjtBQStFTTtFQUNFLGdCQUFBO0VBQ0EsY0FBQTtBQTdFUjtBQWdGTTtFQUNFLGNBQUE7RUFDQSxxQ0FBQTtBQTlFUjtBQW1GRTtFQUNFLGFBQUE7RUFDQSxTQUFBO0VBQ0EsdUJBQUE7RUFDQSxnQkFBQTtBQWpGSjtBQW1GSTtFQU5GO0lBT0ksc0JBQUE7RUFoRko7QUFDRjs7QUFxRkE7RUFDRTtJQUNFLGFBQUE7RUFsRkY7RUFxRkE7SUFDRSxhQUFBO0VBbkZGO0VBc0ZBO0lBQ0UsZUFBQTtFQXBGRjtFQXVGQTtJQUNFLDRCQUFBO0VBckZGO0VBd0ZBO0lBQ0UsNkJBQUE7SUFDQSw0QkFBQTtJQUNBLDJCQUFBO0VBdEZGO0VBeUZBO0lBQ0UsNEJBQUE7RUF2RkY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5wZXJzb25hbGl0eS10ZXN0LWNvbnRhaW5lciB7XHJcbiAgbWluLWhlaWdodDogMTAwdmg7XHJcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTtcclxuICBwYWRkaW5nOiAycmVtO1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICBmb250LWZhbWlseTogJ1BvcHBpbnMnLCBzYW5zLXNlcmlmO1xyXG59XHJcblxyXG4vLyBTdHlsZXMgY29tbXVucyBwb3VyIGxlcyBjYXJ0ZXNcclxuLmludHJvLWNhcmQsIC50ZXN0LWNhcmQsIC5yZXN1bHRzLWNhcmQsIC5sb2FkaW5nLWNhcmQge1xyXG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45NSk7XHJcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDEwcHgpO1xyXG4gIGJvcmRlci1yYWRpdXM6IDIwcHg7XHJcbiAgYm94LXNoYWRvdzogMCAyMHB4IDQwcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gIHBhZGRpbmc6IDNyZW07XHJcbiAgbWF4LXdpZHRoOiA4MDBweDtcclxuICB3aWR0aDogMTAwJTtcclxuICBtYXJnaW46IDAgYXV0bztcclxufVxyXG5cclxuLy8gU3R5bGVzIGNvbW11bnMgcG91ciBsZXMgdGl0cmVzIGV0IGRpdmlzZXVyc1xyXG4udGl0bGUge1xyXG4gIGZvbnQtc2l6ZTogMi41cmVtO1xyXG4gIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgY29sb3I6ICMyZDM3NDg7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIG1hcmdpbi1ib3R0b206IDFyZW07XHJcbn1cclxuXHJcbi5zdWJ0aXRsZSB7XHJcbiAgZm9udC1zaXplOiAxLjJyZW07XHJcbiAgY29sb3I6ICM3MTgwOTY7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIG1hcmdpbi1ib3R0b206IDJyZW07XHJcbn1cclxuXHJcbi5kaXZpZGVyIHtcclxuICB3aWR0aDogODBweDtcclxuICBoZWlnaHQ6IDRweDtcclxuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM2NjdlZWEsICM3NjRiYTIpO1xyXG4gIG1hcmdpbjogMCBhdXRvIDJyZW07XHJcbiAgYm9yZGVyLXJhZGl1czogMnB4O1xyXG59XHJcblxyXG4vLyBTdHlsZXMgcG91ciBsZXMgYm91dG9uc1xyXG4uYnRuIHtcclxuICBwYWRkaW5nOiAxcmVtIDJyZW07XHJcbiAgYm9yZGVyOiBub25lO1xyXG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcbiAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIGdhcDogMC41cmVtO1xyXG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuXHJcbiAgJi5idG4tcHJpbWFyeSB7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpO1xyXG4gICAgY29sb3I6IHdoaXRlO1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XHJcbiAgICAgIGJveC1zaGFkb3c6IDAgMTBweCAyNXB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC40KTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gICYuYnRuLXNlY29uZGFyeSB7XHJcbiAgICBiYWNrZ3JvdW5kOiAjZTJlOGYwO1xyXG4gICAgY29sb3I6ICM0YTU1Njg7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIGJhY2tncm91bmQ6ICNjYmQ1ZTA7XHJcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gICY6ZGlzYWJsZWQge1xyXG4gICAgb3BhY2l0eTogMC42O1xyXG4gICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuICAgIHRyYW5zZm9ybTogbm9uZSAhaW1wb3J0YW50O1xyXG4gIH1cclxufVxyXG5cclxuLy8gUGFnZSBkJ2ludHJvZHVjdGlvblxyXG4udGVzdC1pbnRybyB7XHJcbiAgLmludHJvLWNvbnRlbnQge1xyXG4gICAgbWFyZ2luOiAycmVtIDA7XHJcbiAgfVxyXG5cclxuICAudGVzdC1pbmZvIHtcclxuICAgIGRpc3BsYXk6IGdyaWQ7XHJcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKTtcclxuICAgIGdhcDogMS41cmVtO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMnJlbTtcclxuICB9XHJcblxyXG4gIC5pbmZvLWl0ZW0ge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBnYXA6IDFyZW07XHJcbiAgICBwYWRkaW5nOiAxLjVyZW07XHJcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMSk7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG5cclxuICAgIC5pY29uIHtcclxuICAgICAgZm9udC1zaXplOiAycmVtO1xyXG4gICAgfVxyXG5cclxuICAgIC5pbmZvLXRleHQge1xyXG4gICAgICBoMyB7XHJcbiAgICAgICAgbWFyZ2luOiAwIDAgMC41cmVtIDA7XHJcbiAgICAgICAgY29sb3I6ICMyZDM3NDg7XHJcbiAgICAgICAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHAge1xyXG4gICAgICAgIG1hcmdpbjogMDtcclxuICAgICAgICBjb2xvcjogIzcxODA5NjtcclxuICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLnVzZXItaW5mbyB7XHJcbiAgICBiYWNrZ3JvdW5kOiByZ2JhKDExOCwgNzUsIDE2MiwgMC4xKTtcclxuICAgIHBhZGRpbmc6IDEuNXJlbTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XHJcblxyXG4gICAgaDMge1xyXG4gICAgICBtYXJnaW46IDAgMCAxcmVtIDA7XHJcbiAgICAgIGNvbG9yOiAjMmQzNzQ4O1xyXG4gICAgfVxyXG5cclxuICAgIHAge1xyXG4gICAgICBtYXJnaW46IDAuNXJlbSAwO1xyXG4gICAgICBjb2xvcjogIzRhNTU2ODtcclxuXHJcbiAgICAgICYubm90ZSB7XHJcbiAgICAgICAgZm9udC1zdHlsZTogaXRhbGljO1xyXG4gICAgICAgIGNvbG9yOiAjNzE4MDk2O1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuaW50cm8tYWN0aW9ucyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZ2FwOiAxcmVtO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBtYXJnaW4tdG9wOiAycmVtO1xyXG5cclxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gSW50ZXJmYWNlIGR1IHRlc3RcclxuLnRlc3QtaW50ZXJmYWNlIHtcclxuICAucHJvZ3Jlc3Mtc2VjdGlvbiB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzcmVtO1xyXG4gIH1cclxuXHJcbiAgLnByb2dyZXNzLWluZm8ge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG5cclxuICAgIC5xdWVzdGlvbi1jb3VudGVyIHtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICM0YTU1Njg7XHJcbiAgICB9XHJcblxyXG4gICAgLnByb2dyZXNzLXBlcmNlbnRhZ2Uge1xyXG4gICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgICBjb2xvcjogIzY2N2VlYTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5wcm9ncmVzcy1iYXIge1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBoZWlnaHQ6IDhweDtcclxuICAgIGJhY2tncm91bmQ6ICNlMmU4ZjA7XHJcbiAgICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gIH1cclxuXHJcbiAgLnByb2dyZXNzLWZpbGwge1xyXG4gICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjNjY3ZWVhLCAjNzY0YmEyKTtcclxuICAgIHRyYW5zaXRpb246IHdpZHRoIDAuM3MgZWFzZTtcclxuICB9XHJcblxyXG4gIC5xdWVzdGlvbi1zZWN0aW9uIHtcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICB9XHJcblxyXG4gIC5xdWVzdGlvbi1jb250ZW50IHtcclxuICAgIG1hcmdpbi1ib3R0b206IDNyZW07XHJcblxyXG4gICAgLnF1ZXN0aW9uLXRleHQge1xyXG4gICAgICBmb250LXNpemU6IDEuOHJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgY29sb3I6ICMyZDM3NDg7XHJcbiAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7XHJcbiAgICAgIG1hcmdpbjogMDtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5hbnN3ZXItYnV0dG9ucyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZ2FwOiAycmVtO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcblxyXG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuYnRuLWFuc3dlciB7XHJcbiAgICBwYWRkaW5nOiAxLjVyZW0gM3JlbTtcclxuICAgIGZvbnQtc2l6ZTogMS4zcmVtO1xyXG4gICAgbWluLXdpZHRoOiAxNTBweDtcclxuXHJcbiAgICAuYW5zd2VyLWljb24ge1xyXG4gICAgICBmb250LXNpemU6IDEuNXJlbTtcclxuICAgIH1cclxuXHJcbiAgICAmLmJ0bi15ZXMge1xyXG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDhiYjc4IDAlLCAjMzhhMTY5IDEwMCUpO1xyXG4gICAgICBjb2xvcjogd2hpdGU7XHJcblxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTNweCk7XHJcbiAgICAgICAgYm94LXNoYWRvdzogMCAxNXB4IDMwcHggcmdiYSg3MiwgMTg3LCAxMjAsIDAuNCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAmLmJ0bi1ubyB7XHJcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmNTY1NjUgMCUsICNlNTNlM2UgMTAwJSk7XHJcbiAgICAgIGNvbG9yOiB3aGl0ZTtcclxuXHJcbiAgICAgICY6aG92ZXIge1xyXG4gICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KTtcclxuICAgICAgICBib3gtc2hhZG93OiAwIDE1cHggMzBweCByZ2JhKDI0NSwgMTAxLCAxMDEsIDAuNCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbn1cclxuXHJcbi8vIMODwoljcmFuIGRlIGNoYXJnZW1lbnRcclxuLmxvYWRpbmctc2NyZWVuIHtcclxuICAubG9hZGluZy1jYXJkIHtcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuXHJcbiAgICAubG9hZGluZy1zcGlubmVyIHtcclxuICAgICAgd2lkdGg6IDYwcHg7XHJcbiAgICAgIGhlaWdodDogNjBweDtcclxuICAgICAgYm9yZGVyOiA0cHggc29saWQgI2UyZThmMDtcclxuICAgICAgYm9yZGVyLXRvcDogNHB4IHNvbGlkICM2NjdlZWE7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTtcclxuICAgICAgbWFyZ2luOiAwIGF1dG8gMnJlbTtcclxuICAgIH1cclxuXHJcbiAgICBoMiB7XHJcbiAgICAgIGNvbG9yOiAjMmQzNzQ4O1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAxcmVtO1xyXG4gICAgfVxyXG5cclxuICAgIHAge1xyXG4gICAgICBjb2xvcjogIzcxODA5NjtcclxuICAgICAgbWFyZ2luOiAwO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuQGtleWZyYW1lcyBzcGluIHtcclxuICAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDBkZWcpOyB9XHJcbiAgMTAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH1cclxufVxyXG5cclxuLy8gUsODwqlzdWx0YXRzIGR1IHRlc3RcclxuLnRlc3QtcmVzdWx0cyB7XHJcbiAgLnByb2ZpbGUtc3VtbWFyeSB7XHJcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICBtYXJnaW46IDJyZW0gMDtcclxuICB9XHJcblxyXG4gIC5wcm9maWxlLWJhZGdlIHtcclxuICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxuICAgIHBhZGRpbmc6IDJyZW07XHJcbiAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xyXG4gICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxuXHJcbiAgICAmLnByb2ZpbGUtZmxvd2VyIHtcclxuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZiYjZjZSAwJSwgI2Y2ODdiMyAxMDAlKTtcclxuICAgIH1cclxuXHJcbiAgICAmLnByb2ZpbGUtamV3ZWwge1xyXG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjOTBjZGY0IDAlLCAjNjNiM2VkIDEwMCUpO1xyXG4gICAgfVxyXG5cclxuICAgICYucHJvZmlsZS1zaGFrZXIge1xyXG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZmJkMzhkIDAlLCAjZjZhZDU1IDEwMCUpO1xyXG4gICAgfVxyXG5cclxuICAgICYucHJvZmlsZS1zdHJlYW0ge1xyXG4gICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjOWFlNmI0IDAlLCAjNjhkMzkxIDEwMCUpO1xyXG4gICAgfVxyXG5cclxuICAgICYucHJvZmlsZS1mbG93ZXJqZXdlbCB7XHJcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmYmI2Y2UgMCUsICM5MGNkZjQgMTAwJSk7XHJcbiAgICB9XHJcblxyXG4gICAgJi5wcm9maWxlLWpld2Vsc2hha2VyIHtcclxuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzkwY2RmNCAwJSwgI2ZiZDM4ZCAxMDAlKTtcclxuICAgIH1cclxuXHJcbiAgICAmLnByb2ZpbGUtc2hha2Vyc3RyZWFtIHtcclxuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2ZiZDM4ZCAwJSwgIzlhZTZiNCAxMDAlKTtcclxuICAgIH1cclxuXHJcbiAgICAmLnByb2ZpbGUtc3RyZWFtZmxvd2VyIHtcclxuICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzlhZTZiNCAwJSwgI2ZiYjZjZSAxMDAlKTtcclxuICAgIH1cclxuXHJcbiAgICAucHJvZmlsZS1uYW1lIHtcclxuICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgICBmb250LXNpemU6IDJyZW07XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7XHJcbiAgICAgIG1hcmdpbjogMCAwIDFyZW0gMDtcclxuICAgICAgdGV4dC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMyk7XHJcbiAgICB9XHJcblxyXG4gICAgLmNvbmZpZGVuY2Utc2NvcmUge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICBnYXA6IDAuNXJlbTtcclxuXHJcbiAgICAgIC5zY29yZS1sYWJlbCB7XHJcbiAgICAgICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTtcclxuICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuc2NvcmUtdmFsdWUge1xyXG4gICAgICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgICAgICBmb250LXNpemU6IDEuNXJlbTtcclxuICAgICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgICAgIHRleHQtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjMpO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAucHJvZmlsZS10eXBlIHtcclxuICAgIG1hcmdpbi1ib3R0b206IDJyZW07XHJcblxyXG4gICAgLnR5cGUtYmFkZ2Uge1xyXG4gICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7XHJcbiAgICAgIHBhZGRpbmc6IDAuNXJlbSAxcmVtO1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xyXG4gICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuXHJcbiAgICAgICYucHJpbWFyeSB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjIpO1xyXG4gICAgICAgIGNvbG9yOiAjNjY3ZWVhO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAmLmludGVybWVkaWF0ZSB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgxMTgsIDc1LCAxNjIsIDAuMik7XHJcbiAgICAgICAgY29sb3I6ICM3NjRiYTI7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5wcm9maWxlLWRlc2NyaXB0aW9uIHtcclxuICAgIGJhY2tncm91bmQ6IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4xKTtcclxuICAgIHBhZGRpbmc6IDJyZW07XHJcbiAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gICAgbWFyZ2luOiAycmVtIDA7XHJcblxyXG4gICAgaDMge1xyXG4gICAgICBjb2xvcjogIzJkMzc0ODtcclxuICAgICAgbWFyZ2luOiAwIDAgMXJlbSAwO1xyXG4gICAgfVxyXG5cclxuICAgIHAge1xyXG4gICAgICBjb2xvcjogIzRhNTU2ODtcclxuICAgICAgbGluZS1oZWlnaHQ6IDEuNjtcclxuICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICBmb250LXNpemU6IDEuMXJlbTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIC5kZXRhaWxlZC1zY29yZXMge1xyXG4gICAgbWFyZ2luOiAycmVtIDA7XHJcblxyXG4gICAgaDMge1xyXG4gICAgICBjb2xvcjogIzJkMzc0ODtcclxuICAgICAgbWFyZ2luOiAwIDAgMS41cmVtIDA7XHJcbiAgICB9XHJcblxyXG4gICAgLnNjb3Jlcy1ncmlkIHtcclxuICAgICAgZGlzcGxheTogZ3JpZDtcclxuICAgICAgZ2FwOiAxcmVtO1xyXG4gICAgfVxyXG5cclxuICAgIC5zY29yZS1pdGVtIHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgZ2FwOiAxcmVtO1xyXG4gICAgICBwYWRkaW5nOiAxcmVtO1xyXG4gICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuNSk7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcclxuXHJcbiAgICAgIC5zY29yZS1sYWJlbCB7XHJcbiAgICAgICAgbWluLXdpZHRoOiA4MHB4O1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgY29sb3I6ICM0YTU1Njg7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5zY29yZS1iYXIge1xyXG4gICAgICAgIGZsZXg6IDE7XHJcbiAgICAgICAgaGVpZ2h0OiA4cHg7XHJcbiAgICAgICAgYmFja2dyb3VuZDogI2UyZThmMDtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7XHJcbiAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjtcclxuXHJcbiAgICAgICAgLnNjb3JlLWZpbGwge1xyXG4gICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogd2lkdGggMC41cyBlYXNlO1xyXG5cclxuICAgICAgICAgICYuZmxvd2VyIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjZmJiNmNlLCAjZjY4N2IzKTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmLmpld2VsIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjOTBjZGY0LCAjNjNiM2VkKTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmLnNoYWtlciB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgI2ZiZDM4ZCwgI2Y2YWQ1NSk7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgJi5zdHJlYW0ge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM5YWU2YjQsICM2OGQzOTEpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnNjb3JlLXZhbHVlIHtcclxuICAgICAgICBtaW4td2lkdGg6IDQwcHg7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcclxuICAgICAgICBjb2xvcjogIzJkMzc0ODtcclxuICAgICAgICB0ZXh0LWFsaWduOiByaWdodDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLnRlc3QtaW5mby1zdW1tYXJ5IHtcclxuICAgIGJhY2tncm91bmQ6IHJnYmEoMTE4LCA3NSwgMTYyLCAwLjEpO1xyXG4gICAgcGFkZGluZzogMS41cmVtO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAgIG1hcmdpbjogMnJlbSAwO1xyXG5cclxuICAgIC5pbmZvLXJvdyB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgbWFyZ2luLWJvdHRvbTogMC41cmVtO1xyXG5cclxuICAgICAgJjpsYXN0LWNoaWxkIHtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAwO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAubGFiZWwge1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICAgICAgY29sb3I6ICM0YTU1Njg7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC52YWx1ZSB7XHJcbiAgICAgICAgY29sb3I6ICMyZDM3NDg7XHJcbiAgICAgICAgZm9udC1mYW1pbHk6ICdDb3VyaWVyIE5ldycsIG1vbm9zcGFjZTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLnJlc3VsdHMtYWN0aW9ucyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZ2FwOiAxcmVtO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBtYXJnaW4tdG9wOiAycmVtO1xyXG5cclxuICAgIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgfVxyXG4gIH1cclxufVxyXG5cclxuLy8gUmVzcG9uc2l2ZSBkZXNpZ25cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLnBlcnNvbmFsaXR5LXRlc3QtY29udGFpbmVyIHtcclxuICAgIHBhZGRpbmc6IDFyZW07XHJcbiAgfVxyXG5cclxuICAuaW50cm8tY2FyZCwgLnRlc3QtY2FyZCwgLnJlc3VsdHMtY2FyZCwgLmxvYWRpbmctY2FyZCB7XHJcbiAgICBwYWRkaW5nOiAycmVtO1xyXG4gIH1cclxuXHJcbiAgLnRpdGxlIHtcclxuICAgIGZvbnQtc2l6ZTogMnJlbTtcclxuICB9XHJcblxyXG4gIC5xdWVzdGlvbi10ZXh0IHtcclxuICAgIGZvbnQtc2l6ZTogMS40cmVtICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAuYnRuLWFuc3dlciB7XHJcbiAgICBwYWRkaW5nOiAxcmVtIDJyZW0gIWltcG9ydGFudDtcclxuICAgIGZvbnQtc2l6ZTogMS4xcmVtICFpbXBvcnRhbnQ7XHJcbiAgICBtaW4td2lkdGg6IDEyMHB4ICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAucHJvZmlsZS1uYW1lIHtcclxuICAgIGZvbnQtc2l6ZTogMS41cmVtICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["PERSONALITY_QUESTIONS", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "PersonalityTestComponent_div_1_Template_button_click_48_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "startTest", "PersonalityTestComponent_div_1_Template_button_click_53_listener", "ctx_r6", "goToHome", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "selected<PERSON>ser", "name", "email", "PersonalityTestComponent_div_2_div_11_Template_button_click_5_listener", "_r9", "ctx_r8", "answerQuestion", "PersonalityTestComponent_div_2_div_11_Template_button_click_10_listener", "ctx_r10", "ɵɵtextInterpolate", "ctx_r7", "currentQuestion", "question", "ɵɵproperty", "isLoading", "ɵɵtemplate", "PersonalityTestComponent_div_2_div_11_Template", "ɵɵtextInterpolate2", "ctx_r1", "currentQuestionNumber", "totalQuestions", "ɵɵpipeBind2", "progressPercentage", "ɵɵstyleProp", "ctx_r13", "testSession", "scores", "flower", "jewel", "shaker", "stream", "ctx_r14", "id", "PersonalityTestComponent_div_4_div_15_Template", "PersonalityTestComponent_div_4_div_16_Template", "PersonalityTestComponent_div_4_div_22_Template", "PersonalityTestComponent_div_4_div_30_Template", "PersonalityTestComponent_div_4_Template_button_click_32_listener", "_r16", "ctx_r15", "restartTest", "PersonalityTestComponent_div_4_Template_button_click_37_listener", "ctx_r17", "ɵɵclassMap", "ctx_r3", "finalProfile", "primaryClass", "toLowerCase", "replace", "confidenceScore", "isIntermediate", "description", "completedAt", "PersonalityTestComponent", "constructor", "personalityTestService", "irisCompatibilityService", "router", "questions", "currentQuestionIndex", "responses", "isTestStarted", "isTestCompleted", "showResults", "staticUsers", "currentUser", "compatibilityResult", "ngOnInit", "currentUserData", "localStorage", "getItem", "JSON", "parse", "initializeTest", "createTestSession", "selectUser", "user", "startedAt", "Date", "answer", "response", "questionId", "timestamp", "push", "length", "completeTest", "results", "processTestResults", "profile", "saveTestSession", "subscribe", "next", "sessionId", "console", "log", "error", "navigate", "ɵɵdirectiveInject", "i1", "PersonalityTestService", "i2", "IrisCompatibilityService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "PersonalityTestComponent_Template", "rf", "ctx", "PersonalityTestComponent_div_1_Template", "PersonalityTestComponent_div_2_Template", "PersonalityTestComponent_div_3_Template", "PersonalityTestComponent_div_4_Template"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\personality-test\\personality-test.component.ts", "D:\\ProjetPfa\\pfa\\pfa\\src\\app\\personality-test\\personality-test.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { PersonalityTestService } from '../services/personality-test.service';\nimport { IrisCompatibilityService, CompatibilityResult } from '../services/iris-compatibility.service';\nimport {\n  Question,\n  UserResponse,\n  TestSession,\n  PersonalityProfile,\n  PERSONALITY_QUESTIONS\n} from '../models/personality-test.model';\n\n@Component({\n  selector: 'app-personality-test',\n  templateUrl: './personality-test.component.html',\n  styleUrls: ['./personality-test.component.scss']\n})\nexport class PersonalityTestComponent implements OnInit {\n  // Données du test\n  questions: Question[] = PERSONALITY_QUESTIONS;\n  currentQuestionIndex: number = 0;\n  responses: UserResponse[] = [];\n  testSession: TestSession | null = null;\n\n  // État du composant\n  isTestStarted: boolean = false;\n  isTestCompleted: boolean = false;\n  isLoading: boolean = false;\n  showResults: boolean = false;\n\n  // Comptes statiques pour les tests\n  staticUsers = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      description: 'Profil créatif et émotionnel'\n    },\n    {\n      id: 2,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      description: 'Profil analytique et structuré'\n    },\n    {\n      id: 3,\n      name: 'Sophie Leroy',\n      email: '<EMAIL>',\n      description: 'Profil dynamique et aventurier'\n    },\n    {\n      id: 4,\n      name: 'Pierre Moreau',\n      email: '<EMAIL>',\n      description: 'Profil paisible et réfléchi'\n    },\n    {\n      id: 5,\n      name: 'Emma Bernard',\n      email: '<EMAIL>',\n      description: 'Profil mixte créatif-analytique'\n    }\n  ];\n\n  selectedUser = this.staticUsers[0]; // Utilisateur par défaut\n  currentUser: any = null; // Utilisateur connecté\n\n  // Résultats\n  finalProfile: PersonalityProfile | null = null;\n  compatibilityResult: CompatibilityResult | null = null;\n\n  constructor(\n    private personalityTestService: PersonalityTestService,\n    private irisCompatibilityService: IrisCompatibilityService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Récupérer l'utilisateur connecté\n    const currentUserData = localStorage.getItem('currentUser');\n    if (currentUserData) {\n      this.currentUser = JSON.parse(currentUserData);\n      // Utiliser l'utilisateur connecté pour le test\n      this.selectedUser = {\n        name: this.currentUser.name,\n        email: this.currentUser.email,\n        description: 'Utilisateur connecté'\n      };\n    }\n    this.initializeTest();\n  }\n\n  /**\n   * Initialise le test\n   */\n  initializeTest(): void {\n    this.testSession = this.personalityTestService.createTestSession(\n      this.selectedUser.name,\n      this.selectedUser.email\n    );\n  }\n\n  /**\n   * Sélectionne un utilisateur de test\n   */\n  selectUser(user: any): void {\n    this.selectedUser = user;\n    this.initializeTest();\n  }\n\n  /**\n   * Démarre le test\n   */\n  startTest(): void {\n    this.isTestStarted = true;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    if (this.testSession) {\n      this.testSession.startedAt = new Date();\n    }\n  }\n\n  /**\n   * Traite la réponse à une question\n   */\n  answerQuestion(answer: boolean): void {\n    if (!this.testSession) return;\n\n    const currentQuestion = this.questions[this.currentQuestionIndex];\n    const response: UserResponse = {\n      questionId: currentQuestion.id,\n      answer: answer,\n      timestamp: new Date()\n    };\n\n    this.responses.push(response);\n    this.testSession.responses = this.responses;\n\n    // Passer à la question suivante ou terminer le test\n    if (this.currentQuestionIndex < this.questions.length - 1) {\n      this.currentQuestionIndex++;\n    } else {\n      this.completeTest();\n    }\n  }\n\n  /**\n   * Termine le test et calcule les résultats\n   */\n  completeTest(): void {\n    if (!this.testSession) return;\n\n    this.isLoading = true;\n\n    // Calculer les résultats\n    const results = this.personalityTestService.processTestResults(this.responses);\n\n    // Mettre à jour la session\n    this.testSession.scores = results.scores;\n    this.testSession.finalProfile = results.profile;\n    this.testSession.completedAt = new Date();\n\n    this.finalProfile = results.profile;\n\n    // Sauvegarder dans Firebase\n    this.personalityTestService.saveTestSession(this.testSession).subscribe({\n      next: (sessionId) => {\n        console.log('Test sauvegardé avec l\\'ID:', sessionId);\n        this.testSession!.id = sessionId;\n        this.isLoading = false;\n        this.isTestCompleted = true;\n        this.showResults = true;\n      },\n      error: (error) => {\n        console.error('Erreur lors de la sauvegarde:', error);\n        this.isLoading = false;\n        this.isTestCompleted = true;\n        this.showResults = true;\n      }\n    });\n  }\n\n  /**\n   * Redémarre le test\n   */\n  restartTest(): void {\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.showResults = false;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.finalProfile = null;\n    this.initializeTest();\n  }\n\n  /**\n   * Retourne à l'accueil\n   */\n  goToHome(): void {\n    this.router.navigate(['/accueil']);\n  }\n\n  /**\n   * Obtient la question actuelle\n   */\n  get currentQuestion(): Question | null {\n    return this.questions[this.currentQuestionIndex] || null;\n  }\n\n  /**\n   * Obtient le pourcentage de progression\n   */\n  get progressPercentage(): number {\n    return ((this.currentQuestionIndex + 1) / this.questions.length) * 100;\n  }\n\n  /**\n   * Obtient le numéro de la question actuelle\n   */\n  get currentQuestionNumber(): number {\n    return this.currentQuestionIndex + 1;\n  }\n\n  /**\n   * Obtient le nombre total de questions\n   */\n  get totalQuestions(): number {\n    return this.questions.length;\n  }\n}\n", "<div class=\"personality-test-container\">\n  <!-- Page d'accueil du test -->\n  <div class=\"test-intro\" *ngIf=\"!isTestStarted && !showResults\">\n    <div class=\"intro-card\">\n      <div class=\"intro-header\">\n        <h1 class=\"title\">Test de Personnalité</h1>\n        <div class=\"divider\"></div>\n        <p class=\"subtitle\">Découvrez votre profil psychotechnique</p>\n      </div>\n\n      <div class=\"intro-content\">\n        <div class=\"test-info\">\n          <div class=\"info-item\">\n            <span class=\"icon\">📝</span>\n            <div class=\"info-text\">\n              <h3>32 Questions</h3>\n              <p>Questions ciblées pour analyser votre personnalité</p>\n            </div>\n          </div>\n\n          <div class=\"info-item\">\n            <span class=\"icon\">⏱️</span>\n            <div class=\"info-text\">\n              <h3>5-10 Minutes</h3>\n              <p>Temps estimé pour compléter le test</p>\n            </div>\n          </div>\n\n          <div class=\"info-item\">\n            <span class=\"icon\">🎯</span>\n            <div class=\"info-text\">\n              <h3>4 Profils Principaux</h3>\n              <p>Flower, Jewel, Shaker, Stream + profils intermédiaires</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"user-info\">\n          <h3>Informations du test</h3>\n          <p><strong>Nom:</strong> {{ selectedUser.name }}</p>\n          <p><strong>Email:</strong> {{ selectedUser.email }}</p>\n          <p class=\"note\">Les résultats seront sauvegardés dans la base de données PFA1</p>\n        </div>\n      </div>\n\n      <div class=\"intro-actions\">\n        <button class=\"btn btn-primary\" (click)=\"startTest()\">\n          <span>Commencer le Test</span>\n          <span class=\"icon\">→</span>\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"goToHome()\">\n          Retour à l'accueil\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Interface du test -->\n  <div class=\"test-interface\" *ngIf=\"isTestStarted && !showResults\">\n    <div class=\"test-card\">\n      <!-- Barre de progression -->\n      <div class=\"progress-section\">\n        <div class=\"progress-info\">\n          <span class=\"question-counter\">Question {{ currentQuestionNumber }} sur {{ totalQuestions }}</span>\n          <span class=\"progress-percentage\">{{ progressPercentage | number:'1.0-0' }}%</span>\n        </div>\n        <div class=\"progress-bar\">\n          <div class=\"progress-fill\" [style.width.%]=\"progressPercentage\"></div>\n        </div>\n      </div>\n\n      <!-- Question actuelle -->\n      <div class=\"question-section\" *ngIf=\"currentQuestion\">\n        <div class=\"question-content\">\n          <h2 class=\"question-text\">{{ currentQuestion.question }}</h2>\n        </div>\n\n        <div class=\"answer-buttons\">\n          <button\n            class=\"btn btn-answer btn-yes\"\n            (click)=\"answerQuestion(true)\"\n            [disabled]=\"isLoading\">\n            <span class=\"answer-icon\">✓</span>\n            <span>Oui</span>\n          </button>\n\n          <button\n            class=\"btn btn-answer btn-no\"\n            (click)=\"answerQuestion(false)\"\n            [disabled]=\"isLoading\">\n            <span class=\"answer-icon\">✗</span>\n            <span>Non</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Écran de chargement -->\n  <div class=\"loading-screen\" *ngIf=\"isLoading\">\n    <div class=\"loading-card\">\n      <div class=\"loading-spinner\"></div>\n      <h2>Analyse de votre profil...</h2>\n      <p>Calcul des scores et détermination de votre personnalité</p>\n    </div>\n  </div>\n\n  <!-- Résultats du test -->\n  <div class=\"test-results\" *ngIf=\"showResults && finalProfile\">\n    <div class=\"results-card\">\n      <div class=\"results-header\">\n        <h1 class=\"title\">Votre Profil de Personnalité</h1>\n        <div class=\"divider\"></div>\n      </div>\n\n      <div class=\"profile-summary\">\n        <div class=\"profile-badge\" [class]=\"'profile-' + finalProfile.primaryClass.toLowerCase().replace('-', '')\">\n          <h2 class=\"profile-name\">{{ finalProfile.primaryClass }}</h2>\n          <div class=\"confidence-score\">\n            <span class=\"score-label\">Score de confiance</span>\n            <span class=\"score-value\">{{ finalProfile.confidenceScore }}%</span>\n          </div>\n        </div>\n\n        <div class=\"profile-type\" *ngIf=\"finalProfile.isIntermediate\">\n          <span class=\"type-badge intermediate\">Profil Intermédiaire</span>\n        </div>\n        <div class=\"profile-type\" *ngIf=\"!finalProfile.isIntermediate\">\n          <span class=\"type-badge primary\">Profil Principal</span>\n        </div>\n      </div>\n\n      <div class=\"profile-description\">\n        <h3>Description de votre profil</h3>\n        <p>{{ finalProfile.description }}</p>\n      </div>\n\n      <div class=\"detailed-scores\" *ngIf=\"testSession\">\n        <h3>Scores détaillés</h3>\n        <div class=\"scores-grid\">\n          <div class=\"score-item\">\n            <span class=\"score-label\">Flower</span>\n            <div class=\"score-bar\">\n              <div class=\"score-fill flower\" [style.width.%]=\"(testSession.scores.flower / 4) * 100\"></div>\n            </div>\n            <span class=\"score-value\">{{ testSession.scores.flower }}/4</span>\n          </div>\n\n          <div class=\"score-item\">\n            <span class=\"score-label\">Jewel</span>\n            <div class=\"score-bar\">\n              <div class=\"score-fill jewel\" [style.width.%]=\"(testSession.scores.jewel / 4) * 100\"></div>\n            </div>\n            <span class=\"score-value\">{{ testSession.scores.jewel }}/4</span>\n          </div>\n\n          <div class=\"score-item\">\n            <span class=\"score-label\">Shaker</span>\n            <div class=\"score-bar\">\n              <div class=\"score-fill shaker\" [style.width.%]=\"(testSession.scores.shaker / 4) * 100\"></div>\n            </div>\n            <span class=\"score-value\">{{ testSession.scores.shaker }}/4</span>\n          </div>\n\n          <div class=\"score-item\">\n            <span class=\"score-label\">Stream</span>\n            <div class=\"score-bar\">\n              <div class=\"score-fill stream\" [style.width.%]=\"(testSession.scores.stream / 4) * 100\"></div>\n            </div>\n            <span class=\"score-value\">{{ testSession.scores.stream }}/4</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"test-info-summary\">\n        <div class=\"info-row\">\n          <span class=\"label\">Test complété le:</span>\n          <span class=\"value\">{{ testSession?.completedAt | date:'dd/MM/yyyy à HH:mm' }}</span>\n        </div>\n        <div class=\"info-row\" *ngIf=\"testSession?.id\">\n          <span class=\"label\">ID de session:</span>\n          <span class=\"value\">{{ testSession?.id }}</span>\n        </div>\n      </div>\n\n      <div class=\"results-actions\">\n        <button class=\"btn btn-primary\" (click)=\"restartTest()\">\n          <span>Refaire le Test</span>\n          <span class=\"icon\">🔄</span>\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"goToHome()\">\n          Retour à l'accueil\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAIA,SAKEA,qBAAqB,QAChB,kCAAkC;;;;;;;;;ICRvCC,EAAA,CAAAC,cAAA,aAA+D;IAGvCD,EAAA,CAAAE,MAAA,gCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAI,SAAA,aAA2B;IAC3BJ,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAE,MAAA,kDAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGhEH,EAAA,CAAAC,cAAA,cAA2B;IAGFD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,oEAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAI7DH,EAAA,CAAAC,cAAA,eAAuB;IACFD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,qDAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAI9CH,EAAA,CAAAC,cAAA,eAAuB;IACFD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,mEAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKnEH,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACpDH,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvDH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,oFAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIrFH,EAAA,CAAAC,cAAA,eAA2B;IACOD,EAAA,CAAAK,UAAA,mBAAAC,iEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAF,MAAA,CAAAG,SAAA,EAAW;IAAA,EAAC;IACnDZ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9BH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,cAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE7BH,EAAA,CAAAC,cAAA,kBAAuD;IAArBD,EAAA,CAAAK,UAAA,mBAAAQ,iEAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAd,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAG,MAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACpDf,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAbkBH,EAAA,CAAAgB,SAAA,IAAuB;IAAvBhB,EAAA,CAAAiB,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAC,IAAA,KAAuB;IACrBpB,EAAA,CAAAgB,SAAA,GAAwB;IAAxBhB,EAAA,CAAAiB,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAE,KAAA,KAAwB;;;;;;IAgCvDrB,EAAA,CAAAC,cAAA,cAAsD;IAExBD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG/DH,EAAA,CAAAC,cAAA,cAA4B;IAGxBD,EAAA,CAAAK,UAAA,mBAAAiB,uEAAA;MAAAtB,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAC,MAAA,GAAAxB,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAa,MAAA,CAAAC,cAAA,CAAe,IAAI,CAAC;IAAA,EAAC;IAE9BzB,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,aAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGlBH,EAAA,CAAAC,cAAA,kBAGyB;IADvBD,EAAA,CAAAK,UAAA,mBAAAqB,wEAAA;MAAA1B,EAAA,CAAAO,aAAA,CAAAgB,GAAA;MAAA,MAAAI,OAAA,GAAA3B,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAgB,OAAA,CAAAF,cAAA,CAAe,KAAK,CAAC;IAAA,EAAC;IAE/BzB,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,cAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAjBQH,EAAA,CAAAgB,SAAA,GAA8B;IAA9BhB,EAAA,CAAA4B,iBAAA,CAAAC,MAAA,CAAAC,eAAA,CAAAC,QAAA,CAA8B;IAOtD/B,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAgC,UAAA,aAAAH,MAAA,CAAAI,SAAA,CAAsB;IAQtBjC,EAAA,CAAAgB,SAAA,GAAsB;IAAtBhB,EAAA,CAAAgC,UAAA,aAAAH,MAAA,CAAAI,SAAA,CAAsB;;;;;IA/BhCjC,EAAA,CAAAC,cAAA,cAAkE;IAK3BD,EAAA,CAAAE,MAAA,GAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnGH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAErFH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAI,SAAA,eAAsE;IACxEJ,EAAA,CAAAG,YAAA,EAAM;IAIRH,EAAA,CAAAkC,UAAA,KAAAC,8CAAA,mBAsBM;IACRnC,EAAA,CAAAG,YAAA,EAAM;;;;IAhC+BH,EAAA,CAAAgB,SAAA,GAA6D;IAA7DhB,EAAA,CAAAoC,kBAAA,cAAAC,MAAA,CAAAC,qBAAA,WAAAD,MAAA,CAAAE,cAAA,KAA6D;IAC1DvC,EAAA,CAAAgB,SAAA,GAA0C;IAA1ChB,EAAA,CAAAiB,kBAAA,KAAAjB,EAAA,CAAAwC,WAAA,OAAAH,MAAA,CAAAI,kBAAA,gBAA0C;IAGjDzC,EAAA,CAAAgB,SAAA,GAAoC;IAApChB,EAAA,CAAA0C,WAAA,UAAAL,MAAA,CAAAI,kBAAA,MAAoC;IAKpCzC,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAgC,UAAA,SAAAK,MAAA,CAAAP,eAAA,CAAqB;;;;;IA2BxD9B,EAAA,CAAAC,cAAA,cAA8C;IAE1CD,EAAA,CAAAI,SAAA,cAAmC;IACnCJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yEAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAqB7DH,EAAA,CAAAC,cAAA,cAA8D;IACtBD,EAAA,CAAAE,MAAA,gCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAEnEH,EAAA,CAAAC,cAAA,cAA+D;IAC5BD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS5DH,EAAA,CAAAC,cAAA,cAAiD;IAC3CD,EAAA,CAAAE,MAAA,iCAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,cAAyB;IAEKD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAI,SAAA,cAA6F;IAC/FJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGpEH,EAAA,CAAAC,cAAA,eAAwB;IACID,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtCH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAI,SAAA,eAA2F;IAC7FJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGnEH,EAAA,CAAAC,cAAA,eAAwB;IACID,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAI,SAAA,eAA6F;IAC/FJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGpEH,EAAA,CAAAC,cAAA,eAAwB;IACID,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAI,SAAA,eAA6F;IAC/FJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IA1BjCH,EAAA,CAAAgB,SAAA,GAAuD;IAAvDhB,EAAA,CAAA0C,WAAA,UAAAC,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAC,MAAA,gBAAuD;IAE9D9C,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAiB,kBAAA,KAAA0B,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAC,MAAA,OAAiC;IAM3B9C,EAAA,CAAAgB,SAAA,GAAsD;IAAtDhB,EAAA,CAAA0C,WAAA,UAAAC,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAE,KAAA,gBAAsD;IAE5D/C,EAAA,CAAAgB,SAAA,GAAgC;IAAhChB,EAAA,CAAAiB,kBAAA,KAAA0B,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAE,KAAA,OAAgC;IAMzB/C,EAAA,CAAAgB,SAAA,GAAuD;IAAvDhB,EAAA,CAAA0C,WAAA,UAAAC,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAG,MAAA,gBAAuD;IAE9DhD,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAiB,kBAAA,KAAA0B,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAG,MAAA,OAAiC;IAM1BhD,EAAA,CAAAgB,SAAA,GAAuD;IAAvDhB,EAAA,CAAA0C,WAAA,UAAAC,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAI,MAAA,gBAAuD;IAE9DjD,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAiB,kBAAA,KAAA0B,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAI,MAAA,OAAiC;;;;;IAU/DjD,EAAA,CAAAC,cAAA,cAA8C;IACxBD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzCH,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA5BH,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAA4B,iBAAA,CAAAsB,OAAA,CAAAN,WAAA,kBAAAM,OAAA,CAAAN,WAAA,CAAAO,EAAA,CAAqB;;;;;;IAzEjDnD,EAAA,CAAAC,cAAA,cAA8D;IAGtCD,EAAA,CAAAE,MAAA,wCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAI,SAAA,aAA2B;IAC7BJ,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,cAA6B;IAEAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7DH,EAAA,CAAAC,cAAA,eAA8B;IACFD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIxEH,EAAA,CAAAkC,UAAA,KAAAkB,8CAAA,kBAEM;IACNpD,EAAA,CAAAkC,UAAA,KAAAmB,8CAAA,kBAEM;IACRrD,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAAiC;IAC3BD,EAAA,CAAAE,MAAA,mCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGvCH,EAAA,CAAAkC,UAAA,KAAAoB,8CAAA,oBAmCM;IAENtD,EAAA,CAAAC,cAAA,eAA+B;IAEPD,EAAA,CAAAE,MAAA,mCAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA0D;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEvFH,EAAA,CAAAkC,UAAA,KAAAqB,8CAAA,kBAGM;IACRvD,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,eAA6B;IACKD,EAAA,CAAAK,UAAA,mBAAAmD,iEAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAA1D,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAA+C,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACrD3D,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAE9BH,EAAA,CAAAC,cAAA,kBAAuD;IAArBD,EAAA,CAAAK,UAAA,mBAAAuD,iEAAA;MAAA5D,EAAA,CAAAO,aAAA,CAAAkD,IAAA;MAAA,MAAAI,OAAA,GAAA7D,EAAA,CAAAU,aAAA;MAAA,OAASV,EAAA,CAAAW,WAAA,CAAAkD,OAAA,CAAA9C,QAAA,EAAU;IAAA,EAAC;IACpDf,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IA5EkBH,EAAA,CAAAgB,SAAA,GAA+E;IAA/EhB,EAAA,CAAA8D,UAAA,cAAAC,MAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,WAAA,GAAAC,OAAA,UAA+E;IAC/EnE,EAAA,CAAAgB,SAAA,GAA+B;IAA/BhB,EAAA,CAAA4B,iBAAA,CAAAmC,MAAA,CAAAC,YAAA,CAAAC,YAAA,CAA+B;IAG5BjE,EAAA,CAAAgB,SAAA,GAAmC;IAAnChB,EAAA,CAAAiB,kBAAA,KAAA8C,MAAA,CAAAC,YAAA,CAAAI,eAAA,MAAmC;IAItCpE,EAAA,CAAAgB,SAAA,GAAiC;IAAjChB,EAAA,CAAAgC,UAAA,SAAA+B,MAAA,CAAAC,YAAA,CAAAK,cAAA,CAAiC;IAGjCrE,EAAA,CAAAgB,SAAA,GAAkC;IAAlChB,EAAA,CAAAgC,UAAA,UAAA+B,MAAA,CAAAC,YAAA,CAAAK,cAAA,CAAkC;IAO1DrE,EAAA,CAAAgB,SAAA,GAA8B;IAA9BhB,EAAA,CAAA4B,iBAAA,CAAAmC,MAAA,CAAAC,YAAA,CAAAM,WAAA,CAA8B;IAGLtE,EAAA,CAAAgB,SAAA,GAAiB;IAAjBhB,EAAA,CAAAgC,UAAA,SAAA+B,MAAA,CAAAnB,WAAA,CAAiB;IAwCvB5C,EAAA,CAAAgB,SAAA,GAA0D;IAA1DhB,EAAA,CAAA4B,iBAAA,CAAA5B,EAAA,CAAAwC,WAAA,SAAAuB,MAAA,CAAAnB,WAAA,kBAAAmB,MAAA,CAAAnB,WAAA,CAAA2B,WAAA,6BAA0D;IAEzDvE,EAAA,CAAAgB,SAAA,GAAqB;IAArBhB,EAAA,CAAAgC,UAAA,SAAA+B,MAAA,CAAAnB,WAAA,kBAAAmB,MAAA,CAAAnB,WAAA,CAAAO,EAAA,CAAqB;;;ADlKpD,OAAM,MAAOqB,wBAAwB;EAsDnCC,YACUC,sBAA8C,EAC9CC,wBAAkD,EAClDC,MAAc;IAFd,KAAAF,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,MAAM,GAANA,MAAM;IAxDhB;IACA,KAAAC,SAAS,GAAe9E,qBAAqB;IAC7C,KAAA+E,oBAAoB,GAAW,CAAC;IAChC,KAAAC,SAAS,GAAmB,EAAE;IAC9B,KAAAnC,WAAW,GAAuB,IAAI;IAEtC;IACA,KAAAoC,aAAa,GAAY,KAAK;IAC9B,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAhD,SAAS,GAAY,KAAK;IAC1B,KAAAiD,WAAW,GAAY,KAAK;IAE5B;IACA,KAAAC,WAAW,GAAG,CACZ;MACEhC,EAAE,EAAE,CAAC;MACL/B,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BiD,WAAW,EAAE;KACd,EACD;MACEnB,EAAE,EAAE,CAAC;MACL/B,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,sBAAsB;MAC7BiD,WAAW,EAAE;KACd,EACD;MACEnB,EAAE,EAAE,CAAC;MACL/B,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BiD,WAAW,EAAE;KACd,EACD;MACEnB,EAAE,EAAE,CAAC;MACL/B,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,wBAAwB;MAC/BiD,WAAW,EAAE;KACd,EACD;MACEnB,EAAE,EAAE,CAAC;MACL/B,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BiD,WAAW,EAAE;KACd,CACF;IAED,KAAAnD,YAAY,GAAG,IAAI,CAACgE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,KAAAC,WAAW,GAAQ,IAAI,CAAC,CAAC;IAEzB;IACA,KAAApB,YAAY,GAA8B,IAAI;IAC9C,KAAAqB,mBAAmB,GAA+B,IAAI;EAMnD;EAEHC,QAAQA,CAAA;IACN;IACA,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAC3D,IAAIF,eAAe,EAAE;MACnB,IAAI,CAACH,WAAW,GAAGM,IAAI,CAACC,KAAK,CAACJ,eAAe,CAAC;MAC9C;MACA,IAAI,CAACpE,YAAY,GAAG;QAClBC,IAAI,EAAE,IAAI,CAACgE,WAAW,CAAChE,IAAI;QAC3BC,KAAK,EAAE,IAAI,CAAC+D,WAAW,CAAC/D,KAAK;QAC7BiD,WAAW,EAAE;OACd;;IAEH,IAAI,CAACsB,cAAc,EAAE;EACvB;EAEA;;;EAGAA,cAAcA,CAAA;IACZ,IAAI,CAAChD,WAAW,GAAG,IAAI,CAAC8B,sBAAsB,CAACmB,iBAAiB,CAC9D,IAAI,CAAC1E,YAAY,CAACC,IAAI,EACtB,IAAI,CAACD,YAAY,CAACE,KAAK,CACxB;EACH;EAEA;;;EAGAyE,UAAUA,CAACC,IAAS;IAClB,IAAI,CAAC5E,YAAY,GAAG4E,IAAI;IACxB,IAAI,CAACH,cAAc,EAAE;EACvB;EAEA;;;EAGAhF,SAASA,CAAA;IACP,IAAI,CAACoE,aAAa,GAAG,IAAI;IACzB,IAAI,CAACF,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,IAAI,CAACnC,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACoD,SAAS,GAAG,IAAIC,IAAI,EAAE;;EAE3C;EAEA;;;EAGAxE,cAAcA,CAACyE,MAAe;IAC5B,IAAI,CAAC,IAAI,CAACtD,WAAW,EAAE;IAEvB,MAAMd,eAAe,GAAG,IAAI,CAAC+C,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC;IACjE,MAAMqB,QAAQ,GAAiB;MAC7BC,UAAU,EAAEtE,eAAe,CAACqB,EAAE;MAC9B+C,MAAM,EAAEA,MAAM;MACdG,SAAS,EAAE,IAAIJ,IAAI;KACpB;IAED,IAAI,CAAClB,SAAS,CAACuB,IAAI,CAACH,QAAQ,CAAC;IAC7B,IAAI,CAACvD,WAAW,CAACmC,SAAS,GAAG,IAAI,CAACA,SAAS;IAE3C;IACA,IAAI,IAAI,CAACD,oBAAoB,GAAG,IAAI,CAACD,SAAS,CAAC0B,MAAM,GAAG,CAAC,EAAE;MACzD,IAAI,CAACzB,oBAAoB,EAAE;KAC5B,MAAM;MACL,IAAI,CAAC0B,YAAY,EAAE;;EAEvB;EAEA;;;EAGAA,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC5D,WAAW,EAAE;IAEvB,IAAI,CAACX,SAAS,GAAG,IAAI;IAErB;IACA,MAAMwE,OAAO,GAAG,IAAI,CAAC/B,sBAAsB,CAACgC,kBAAkB,CAAC,IAAI,CAAC3B,SAAS,CAAC;IAE9E;IACA,IAAI,CAACnC,WAAW,CAACC,MAAM,GAAG4D,OAAO,CAAC5D,MAAM;IACxC,IAAI,CAACD,WAAW,CAACoB,YAAY,GAAGyC,OAAO,CAACE,OAAO;IAC/C,IAAI,CAAC/D,WAAW,CAAC2B,WAAW,GAAG,IAAI0B,IAAI,EAAE;IAEzC,IAAI,CAACjC,YAAY,GAAGyC,OAAO,CAACE,OAAO;IAEnC;IACA,IAAI,CAACjC,sBAAsB,CAACkC,eAAe,CAAC,IAAI,CAAChE,WAAW,CAAC,CAACiE,SAAS,CAAC;MACtEC,IAAI,EAAGC,SAAS,IAAI;QAClBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,SAAS,CAAC;QACrD,IAAI,CAACnE,WAAY,CAACO,EAAE,GAAG4D,SAAS;QAChC,IAAI,CAAC9E,SAAS,GAAG,KAAK;QACtB,IAAI,CAACgD,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACC,WAAW,GAAG,IAAI;MACzB,CAAC;MACDgC,KAAK,EAAGA,KAAK,IAAI;QACfF,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,IAAI,CAACjF,SAAS,GAAG,KAAK;QACtB,IAAI,CAACgD,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACC,WAAW,GAAG,IAAI;MACzB;KACD,CAAC;EACJ;EAEA;;;EAGAvB,WAAWA,CAAA;IACT,IAAI,CAACqB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACJ,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACf,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC4B,cAAc,EAAE;EACvB;EAEA;;;EAGA7E,QAAQA,CAAA;IACN,IAAI,CAAC6D,MAAM,CAACuC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGA,IAAIrF,eAAeA,CAAA;IACjB,OAAO,IAAI,CAAC+C,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC,IAAI,IAAI;EAC1D;EAEA;;;EAGA,IAAIrC,kBAAkBA,CAAA;IACpB,OAAQ,CAAC,IAAI,CAACqC,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAACD,SAAS,CAAC0B,MAAM,GAAI,GAAG;EACxE;EAEA;;;EAGA,IAAIjE,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACwC,oBAAoB,GAAG,CAAC;EACtC;EAEA;;;EAGA,IAAIvC,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACsC,SAAS,CAAC0B,MAAM;EAC9B;;;uBAnNW/B,wBAAwB,EAAAxE,EAAA,CAAAoH,iBAAA,CAAAC,EAAA,CAAAC,sBAAA,GAAAtH,EAAA,CAAAoH,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAAxH,EAAA,CAAAoH,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBlD,wBAAwB;MAAAmD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBrCjI,EAAA,CAAAC,cAAA,aAAwC;UAEtCD,EAAA,CAAAkC,UAAA,IAAAiG,uCAAA,kBAqDM;UAGNnI,EAAA,CAAAkC,UAAA,IAAAkG,uCAAA,kBAsCM;UAGNpI,EAAA,CAAAkC,UAAA,IAAAmG,uCAAA,iBAMM;UAGNrI,EAAA,CAAAkC,UAAA,IAAAoG,uCAAA,mBAuFM;UACRtI,EAAA,CAAAG,YAAA,EAAM;;;UAlMqBH,EAAA,CAAAgB,SAAA,GAAoC;UAApChB,EAAA,CAAAgC,UAAA,UAAAkG,GAAA,CAAAlD,aAAA,KAAAkD,GAAA,CAAAhD,WAAA,CAAoC;UAwDhClF,EAAA,CAAAgB,SAAA,GAAmC;UAAnChB,EAAA,CAAAgC,UAAA,SAAAkG,GAAA,CAAAlD,aAAA,KAAAkD,GAAA,CAAAhD,WAAA,CAAmC;UAyCnClF,EAAA,CAAAgB,SAAA,GAAe;UAAfhB,EAAA,CAAAgC,UAAA,SAAAkG,GAAA,CAAAjG,SAAA,CAAe;UASjBjC,EAAA,CAAAgB,SAAA,GAAiC;UAAjChB,EAAA,CAAAgC,UAAA,SAAAkG,GAAA,CAAAhD,WAAA,IAAAgD,GAAA,CAAAlE,YAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}