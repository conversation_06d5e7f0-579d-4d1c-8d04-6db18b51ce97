{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class DashboardComponent {\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 157,\n      vars: 1,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"welcome-card\"], [1, \"new-scan-btn\"], [1, \"fas\", \"fa-eye\"], [1, \"info-cards\"], [1, \"info-card\"], [1, \"card-icon\", \"security\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"card-content\"], [1, \"status\"], [1, \"card-icon\", \"verified\"], [1, \"fas\", \"fa-check-circle\"], [1, \"card-icon\", \"time\"], [1, \"fas\", \"fa-clock\"], [1, \"dashboard-nav\"], [1, \"nav-btn\", \"active\"], [1, \"nav-btn\"], [1, \"profile-overview\"], [1, \"section-header\"], [1, \"profile-content\"], [1, \"iris-image-container\"], [\"src\", \"assets/iris-scan.jpg\", \"alt\", \"Iris Scan\", 1, \"iris-image\"], [1, \"verification-badge\"], [1, \"iris-id\"], [1, \"iris-details\"], [1, \"detail-item\"], [1, \"detail-label\"], [1, \"detail-bar\"], [1, \"progress-bar\"], [1, \"detail-value\"], [1, \"verification-info\"], [1, \"verification-label\"], [1, \"verification-time\"], [1, \"verification-status\", \"success\"], [1, \"bottom-sections\"], [1, \"activity-section\"], [1, \"activity-list\"], [1, \"activity-item\"], [1, \"activity-icon\", \"scan\"], [1, \"activity-details\"], [1, \"activity-icon\", \"profile\"], [1, \"fas\", \"fa-user-edit\"], [1, \"activity-icon\", \"report\"], [1, \"fas\", \"fa-file-alt\"], [1, \"security-section\"], [1, \"security-list\"], [1, \"security-item\"], [1, \"security-icon\", \"data\"], [1, \"fas\", \"fa-database\"], [1, \"security-details\"], [1, \"security-icon\", \"biometric\"], [1, \"fas\", \"fa-fingerprint\"], [1, \"security-icon\", \"compliance\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Votre espace personnel de d\\u00E9tection et profilage d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"button\", 3);\n          i0.ɵɵelement(8, \"i\", 4);\n          i0.ɵɵtext(9, \" Nouveau scan d'iris \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6)(12, \"div\", 7);\n          i0.ɵɵelement(13, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"h3\");\n          i0.ɵɵtext(16, \"Niveau de s\\u00E9curit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\", 10);\n          i0.ɵɵtext(18, \"\\u00C9lev\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 6)(20, \"div\", 11);\n          i0.ɵɵelement(21, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 9)(23, \"h3\");\n          i0.ɵɵtext(24, \"Statut du profil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 10);\n          i0.ɵɵtext(26, \"V\\u00E9rifi\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 6)(28, \"div\", 13);\n          i0.ɵɵelement(29, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"h3\");\n          i0.ɵɵtext(32, \"Dernier scan\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"p\", 10);\n          i0.ɵɵtext(34, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(35, \"div\", 15)(36, \"button\", 16);\n          i0.ɵɵtext(37, \"Vue d'ensemble\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"button\", 17);\n          i0.ɵɵtext(39, \"Scan d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"button\", 17);\n          i0.ɵɵtext(41, \"Mon profil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"button\", 17);\n          i0.ɵɵtext(43, \"Historique\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 18)(45, \"div\", 19)(46, \"h2\");\n          i0.ɵɵtext(47, \"Aper\\u00E7u de votre profil d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"p\");\n          i0.ɵɵtext(49, \"Caract\\u00E9ristiques principales de votre iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 20)(51, \"div\", 21);\n          i0.ɵɵelement(52, \"img\", 22);\n          i0.ɵɵelementStart(53, \"div\", 23);\n          i0.ɵɵtext(54, \"V\\u00E9rifi\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 24);\n          i0.ɵɵtext(56, \"ID: #A12345678\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 25)(58, \"div\", 26)(59, \"div\", 27);\n          i0.ɵɵtext(60, \"Type de motif:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 28);\n          i0.ɵɵelement(62, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 30);\n          i0.ɵɵtext(64, \"Crypte Dominant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 26)(66, \"div\", 27);\n          i0.ɵɵtext(67, \"Couleur d'iris:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"div\", 28);\n          i0.ɵɵelement(69, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"div\", 30);\n          i0.ɵɵtext(71, \"Marron\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 26)(73, \"div\", 27);\n          i0.ɵɵtext(74, \"Caract\\u00E9ristiques uniques:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"div\", 28);\n          i0.ɵɵelement(76, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\", 30);\n          i0.ɵɵtext(78, \"42\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"div\", 26)(80, \"div\", 27);\n          i0.ɵɵtext(81, \"Score de confiance:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 28);\n          i0.ɵɵelement(83, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"div\", 30);\n          i0.ɵɵtext(85, \"98.7%\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 31)(87, \"div\", 32);\n          i0.ɵɵtext(88, \"Derni\\u00E8re v\\u00E9rification\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"div\", 33);\n          i0.ɵɵtext(90, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"div\", 34);\n          i0.ɵɵelement(92, \"i\", 12);\n          i0.ɵɵtext(93, \" R\\u00E9ussi \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(94, \"div\", 35)(95, \"div\", 36)(96, \"div\", 19)(97, \"h2\");\n          i0.ɵɵtext(98, \"Activit\\u00E9 r\\u00E9cente\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"p\");\n          i0.ɵɵtext(100, \"Votre activit\\u00E9 r\\u00E9cente de scan d'iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(101, \"div\", 37)(102, \"div\", 38)(103, \"div\", 39);\n          i0.ɵɵelement(104, \"i\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 40)(106, \"h4\");\n          i0.ɵɵtext(107, \"Scan d'iris compl\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"p\");\n          i0.ɵɵtext(109, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(110, \"div\", 38)(111, \"div\", 41);\n          i0.ɵɵelement(112, \"i\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"div\", 40)(114, \"h4\");\n          i0.ɵɵtext(115, \"Profil mis \\u00E0 jour\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"p\");\n          i0.ɵɵtext(117, \"Hier \\u00E0 14:15\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(118, \"div\", 38)(119, \"div\", 43);\n          i0.ɵɵelement(120, \"i\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"div\", 40)(122, \"h4\");\n          i0.ɵɵtext(123, \"Rapport g\\u00E9n\\u00E9r\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"p\");\n          i0.ɵɵtext(125, \"Il y a 3 jours \\u00E0 11:45\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(126, \"div\", 45)(127, \"div\", 19)(128, \"h2\");\n          i0.ɵɵtext(129, \"Statut de s\\u00E9curit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"p\");\n          i0.ɵɵtext(131, \"Aper\\u00E7u de la s\\u00E9curit\\u00E9 de votre compte\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(132, \"div\", 46)(133, \"div\", 47)(134, \"div\", 48);\n          i0.ɵɵelement(135, \"i\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"div\", 50)(137, \"h4\");\n          i0.ɵɵtext(138, \"Protection des donn\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(139, \"p\");\n          i0.ɵɵtext(140, \"Vos donn\\u00E9es biom\\u00E9triques sont crypt\\u00E9es\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(141, \"div\", 47)(142, \"div\", 51);\n          i0.ɵɵelement(143, \"i\", 52);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(144, \"div\", 50)(145, \"h4\");\n          i0.ɵɵtext(146, \"Authentification biom\\u00E9trique\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"p\");\n          i0.ɵɵtext(148, \"Activ\\u00E9e pour une s\\u00E9curit\\u00E9 renforc\\u00E9e\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(149, \"div\", 47)(150, \"div\", 53);\n          i0.ɵɵelement(151, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"div\", 50)(153, \"h4\");\n          i0.ɵɵtext(154, \"Conformit\\u00E9 RGPD\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"p\");\n          i0.ɵɵtext(156, \"Conforme aux r\\u00E9glementations de protection des donn\\u00E9es\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"Bienvenue, \", ctx.userName, \"\");\n        }\n      },\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n  background-color: #f8f9fe;\\n  padding: 20px;\\n  min-height: 100vh;\\n  color: #333333;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4e54ff, #8a56ff);\\n  color: white;\\n  padding: 30px;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 600;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  opacity: 0.9;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%] {\\n  background-color: white;\\n  color: #4e54ff;\\n  border: none;\\n  padding: 10px 20px;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);\\n}\\n\\n.info-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);\\n  padding: 20px;\\n  display: flex;\\n  align-items: center;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1.2rem;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.security[_ngcontent-%COMP%] {\\n  background-color: #4e54ff;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.verified[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.time[_ngcontent-%COMP%] {\\n  background-color: #ff9d00;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin: 0 0 5px 0;\\n  color: #666666;\\n  font-weight: 500;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.dashboard-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 30px;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 15px 25px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  color: #666666;\\n  cursor: pointer;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -1px;\\n  left: 0;\\n  width: 0;\\n  height: 3px;\\n  background-color: #8a56ff;\\n  transition: width 0.3s ease;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover, .dashboard-nav[_ngcontent-%COMP%]   .nav-btn.active[_ngcontent-%COMP%] {\\n  color: #8a56ff;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn.active[_ngcontent-%COMP%]:after {\\n  width: 100%;\\n}\\n\\n.profile-overview[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);\\n  padding: 25px;\\n  margin-bottom: 30px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 600;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666666;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 30px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 220px;\\n  height: 220px;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  background-color: #f0f0f0;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .verification-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 40px;\\n  left: 10px;\\n  background-color: #4caf50;\\n  color: white;\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .iris-id[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 10px;\\n  left: 10px;\\n  color: white;\\n  font-size: 0.8rem;\\n  background-color: rgba(0, 0, 0, 0.6);\\n  padding: 3px 8px;\\n  border-radius: 4px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  width: 180px;\\n  font-size: 0.9rem;\\n  color: #666666;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 6px;\\n  background-color: #f0f0f0;\\n  border-radius: 3px;\\n  margin: 0 20px;\\n  overflow: hidden;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(135deg, #4e54ff, #8a56ff);\\n  width: 100%;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  width: 120px;\\n  text-align: right;\\n  font-weight: 600;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  padding-top: 20px;\\n  border-top: 1px solid #f0f0f0;\\n  display: flex;\\n  align-items: center;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-label[_ngcontent-%COMP%] {\\n  color: #666666;\\n  font-size: 0.9rem;\\n  margin-right: 15px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-time[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  margin-right: 20px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: flex;\\n  align-items: center;\\n  font-weight: 500;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status.success[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n}\\n\\n.bottom-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 30px;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);\\n  padding: 25px;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 600;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666666;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 15px 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.scan[_ngcontent-%COMP%] {\\n  background-color: #8a56ff;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.profile[_ngcontent-%COMP%] {\\n  background-color: #ff5c8d;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.report[_ngcontent-%COMP%] {\\n  background-color: #ffb74d;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666666;\\n  font-size: 0.85rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 15px 0;\\n  border-bottom: 1px solid #f0f0f0;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.data[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.biometric[_ngcontent-%COMP%] {\\n  background-color: #8a56ff;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.compliance[_ngcontent-%COMP%] {\\n  background-color: #4e54ff;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666666;\\n  font-size: 0.85rem;\\n}\\n\\n@media (max-width: 1024px) {\\n  .profile-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%] {\\n    margin: 0 auto 20px;\\n  }\\n  .bottom-sections[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .info-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n    white-space: nowrap;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    padding: 15px 15px;\\n  }\\n  .detail-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    margin-bottom: 5px;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    margin: 5px 0 !important;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    text-align: left !important;\\n    margin-top: 5px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DashboardComponent", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "userName"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent {\n\n}\n", "<div class=\"dashboard-container\">\n  <!-- En-tête du tableau de bord -->\n  <div class=\"dashboard-header\">\n    <div class=\"welcome-card\">\n      <h1>Bienvenue, {{ userName }}</h1>\n      <p>Votre espace personnel de détection et profilage d'iris</p>\n      <button class=\"new-scan-btn\">\n        <i class=\"fas fa-eye\"></i>\n        Nouveau scan d'iris\n      </button>\n    </div>\n  </div>\n\n  <!-- Cartes d'information -->\n  <div class=\"info-cards\">\n    <div class=\"info-card\">\n      <div class=\"card-icon security\">\n        <i class=\"fas fa-shield-alt\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Niveau de sécurité</h3>\n        <p class=\"status\">Élevé</p>\n      </div>\n    </div>\n\n    <div class=\"info-card\">\n      <div class=\"card-icon verified\">\n        <i class=\"fas fa-check-circle\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Statut du profil</h3>\n        <p class=\"status\">Vérifié</p>\n      </div>\n    </div>\n\n    <div class=\"info-card\">\n      <div class=\"card-icon time\">\n        <i class=\"fas fa-clock\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Dernier scan</h3>\n        <p class=\"status\">Aujourd'hui à 10:30</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- Navigation du tableau de bord -->\n  <div class=\"dashboard-nav\">\n    <button class=\"nav-btn active\">Vue d'ensemble</button>\n    <button class=\"nav-btn\">Scan d'iris</button>\n    <button class=\"nav-btn\">Mon profil</button>\n    <button class=\"nav-btn\">Historique</button>\n  </div>\n\n  <!-- Aperçu du profil d'iris -->\n  <div class=\"profile-overview\">\n    <div class=\"section-header\">\n      <h2>Aperçu de votre profil d'iris</h2>\n      <p>Caractéristiques principales de votre iris</p>\n    </div>\n\n    <div class=\"profile-content\">\n      <div class=\"iris-image-container\">\n        <img src=\"assets/iris-scan.jpg\" alt=\"Iris Scan\" class=\"iris-image\">\n        <div class=\"verification-badge\">Vérifié</div>\n        <div class=\"iris-id\">ID: #A12345678</div>\n      </div>\n\n      <div class=\"iris-details\">\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Type de motif:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">Crypte Dominant</div>\n        </div>\n\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Couleur d'iris:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">Marron</div>\n        </div>\n\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Caractéristiques uniques:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">42</div>\n        </div>\n\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Score de confiance:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">98.7%</div>\n        </div>\n\n        <div class=\"verification-info\">\n          <div class=\"verification-label\">Dernière vérification</div>\n          <div class=\"verification-time\">Aujourd'hui à 10:30</div>\n          <div class=\"verification-status success\">\n            <i class=\"fas fa-check-circle\"></i> Réussi\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Section inférieure avec deux colonnes -->\n  <div class=\"bottom-sections\">\n    <!-- Activité récente -->\n    <div class=\"activity-section\">\n      <div class=\"section-header\">\n        <h2>Activité récente</h2>\n        <p>Votre activité récente de scan d'iris</p>\n      </div>\n\n      <div class=\"activity-list\">\n        <div class=\"activity-item\">\n          <div class=\"activity-icon scan\">\n            <i class=\"fas fa-eye\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Scan d'iris complété</h4>\n            <p>Aujourd'hui à 10:30</p>\n          </div>\n        </div>\n\n        <div class=\"activity-item\">\n          <div class=\"activity-icon profile\">\n            <i class=\"fas fa-user-edit\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Profil mis à jour</h4>\n            <p>Hier à 14:15</p>\n          </div>\n        </div>\n\n        <div class=\"activity-item\">\n          <div class=\"activity-icon report\">\n            <i class=\"fas fa-file-alt\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Rapport généré</h4>\n            <p>Il y a 3 jours à 11:45</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statut de sécurité -->\n    <div class=\"security-section\">\n      <div class=\"section-header\">\n        <h2>Statut de sécurité</h2>\n        <p>Aperçu de la sécurité de votre compte</p>\n      </div>\n\n      <div class=\"security-list\">\n        <div class=\"security-item\">\n          <div class=\"security-icon data\">\n            <i class=\"fas fa-database\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Protection des données</h4>\n            <p>Vos données biométriques sont cryptées</p>\n          </div>\n        </div>\n\n        <div class=\"security-item\">\n          <div class=\"security-icon biometric\">\n            <i class=\"fas fa-fingerprint\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Authentification biométrique</h4>\n            <p>Activée pour une sécurité renforcée</p>\n          </div>\n        </div>\n\n        <div class=\"security-item\">\n          <div class=\"security-icon compliance\">\n            <i class=\"fas fa-shield-alt\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Conformité RGPD</h4>\n            <p>Conforme aux réglementations de protection des données</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AAOA,OAAM,MAAOA,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP/BE,EAAA,CAAAC,cAAA,aAAiC;UAIvBD,EAAA,CAAAE,MAAA,GAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,mEAAuD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9DH,EAAA,CAAAC,cAAA,gBAA6B;UAC3BD,EAAA,CAAAI,SAAA,WAA0B;UAC1BJ,EAAA,CAAAE,MAAA,4BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,cAAwB;UAGlBD,EAAA,CAAAI,SAAA,YAAiC;UACnCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAA0B;UACpBD,EAAA,CAAAE,MAAA,oCAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,aAAkB;UAAAD,EAAA,CAAAE,MAAA,uBAAK;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI/BH,EAAA,CAAAC,cAAA,cAAuB;UAEnBD,EAAA,CAAAI,SAAA,aAAmC;UACrCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAA0B;UACpBD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,aAAkB;UAAAD,EAAA,CAAAE,MAAA,yBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIjCH,EAAA,CAAAC,cAAA,cAAuB;UAEnBD,EAAA,CAAAI,SAAA,aAA4B;UAC9BJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAA0B;UACpBD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,aAAkB;UAAAD,EAAA,CAAAE,MAAA,gCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAM/CH,EAAA,CAAAC,cAAA,eAA2B;UACMD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtDH,EAAA,CAAAC,cAAA,kBAAwB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5CH,EAAA,CAAAC,cAAA,kBAAwB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC3CH,EAAA,CAAAC,cAAA,kBAAwB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAI7CH,EAAA,CAAAC,cAAA,eAA8B;UAEtBD,EAAA,CAAAE,MAAA,0CAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uDAA0C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGnDH,EAAA,CAAAC,cAAA,eAA6B;UAEzBD,EAAA,CAAAI,SAAA,eAAmE;UACnEJ,EAAA,CAAAC,cAAA,eAAgC;UAAAD,EAAA,CAAAE,MAAA,yBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC7CH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAG3CH,EAAA,CAAAC,cAAA,eAA0B;UAEID,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC9CH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAI,SAAA,eAAgC;UAClCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGjDH,EAAA,CAAAC,cAAA,eAAyB;UACGD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC/CH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAI,SAAA,eAAgC;UAClCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGxCH,EAAA,CAAAC,cAAA,eAAyB;UACGD,EAAA,CAAAE,MAAA,sCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACzDH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAI,SAAA,eAAgC;UAClCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGpCH,EAAA,CAAAC,cAAA,eAAyB;UACGD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnDH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAI,SAAA,eAAgC;UAClCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGvCH,EAAA,CAAAC,cAAA,eAA+B;UACGD,EAAA,CAAAE,MAAA,uCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC3DH,EAAA,CAAAC,cAAA,eAA+B;UAAAD,EAAA,CAAAE,MAAA,gCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACxDH,EAAA,CAAAC,cAAA,eAAyC;UACvCD,EAAA,CAAAI,SAAA,aAAmC;UAACJ,EAAA,CAAAE,MAAA,qBACtC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAOdH,EAAA,CAAAC,cAAA,eAA6B;UAInBD,EAAA,CAAAE,MAAA,kCAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,wDAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG9CH,EAAA,CAAAC,cAAA,gBAA2B;UAGrBD,EAAA,CAAAI,SAAA,aAA0B;UAC5BJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA8B;UACxBD,EAAA,CAAAE,MAAA,uCAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7BH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,iCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI9BH,EAAA,CAAAC,cAAA,gBAA2B;UAEvBD,EAAA,CAAAI,SAAA,cAAgC;UAClCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA8B;UACxBD,EAAA,CAAAE,MAAA,+BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,0BAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIvBH,EAAA,CAAAC,cAAA,gBAA2B;UAEvBD,EAAA,CAAAI,SAAA,cAA+B;UACjCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA8B;UACxBD,EAAA,CAAAE,MAAA,sCAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvBH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,oCAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAOrCH,EAAA,CAAAC,cAAA,gBAA8B;UAEtBD,EAAA,CAAAE,MAAA,qCAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,6DAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG9CH,EAAA,CAAAC,cAAA,gBAA2B;UAGrBD,EAAA,CAAAI,SAAA,cAA+B;UACjCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA8B;UACxBD,EAAA,CAAAE,MAAA,oCAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,8DAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIjDH,EAAA,CAAAC,cAAA,gBAA2B;UAEvBD,EAAA,CAAAI,SAAA,cAAkC;UACpCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA8B;UACxBD,EAAA,CAAAE,MAAA,0CAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,gEAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI9CH,EAAA,CAAAC,cAAA,gBAA2B;UAEvBD,EAAA,CAAAI,SAAA,aAAiC;UACnCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAA8B;UACxBD,EAAA,CAAAE,MAAA,6BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,yEAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UAxL/DH,EAAA,CAAAK,SAAA,GAAyB;UAAzBL,EAAA,CAAAM,kBAAA,gBAAAP,GAAA,CAAAQ,QAAA,KAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}