{"ast": null, "code": "import { config } from '../config';\nlet context = null;\nexport function errorContext(cb) {\n  if (config.useDeprecatedSynchronousErrorHandling) {\n    const isRoot = !context;\n    if (isRoot) {\n      context = {\n        errorThrown: false,\n        error: null\n      };\n    }\n    cb();\n    if (isRoot) {\n      const {\n        errorThrown,\n        error\n      } = context;\n      context = null;\n      if (errorThrown) {\n        throw error;\n      }\n    }\n  } else {\n    cb();\n  }\n}\nexport function captureError(err) {\n  if (config.useDeprecatedSynchronousErrorHandling && context) {\n    context.errorThrown = true;\n    context.error = err;\n  }\n}", "map": {"version": 3, "names": ["config", "context", "errorContext", "cb", "useDeprecatedSynchronousErrorHandling", "isRoot", "errorThrown", "error", "captureError", "err"], "sources": ["D:/ProjetPfa/pfa/pfa/node_modules/rxjs/dist/esm/internal/util/errorContext.js"], "sourcesContent": ["import { config } from '../config';\nlet context = null;\nexport function errorContext(cb) {\n    if (config.useDeprecatedSynchronousErrorHandling) {\n        const isRoot = !context;\n        if (isRoot) {\n            context = { errorThrown: false, error: null };\n        }\n        cb();\n        if (isRoot) {\n            const { errorThrown, error } = context;\n            context = null;\n            if (errorThrown) {\n                throw error;\n            }\n        }\n    }\n    else {\n        cb();\n    }\n}\nexport function captureError(err) {\n    if (config.useDeprecatedSynchronousErrorHandling && context) {\n        context.errorThrown = true;\n        context.error = err;\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,WAAW;AAClC,IAAIC,OAAO,GAAG,IAAI;AAClB,OAAO,SAASC,YAAYA,CAACC,EAAE,EAAE;EAC7B,IAAIH,MAAM,CAACI,qCAAqC,EAAE;IAC9C,MAAMC,MAAM,GAAG,CAACJ,OAAO;IACvB,IAAII,MAAM,EAAE;MACRJ,OAAO,GAAG;QAAEK,WAAW,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAK,CAAC;IACjD;IACAJ,EAAE,CAAC,CAAC;IACJ,IAAIE,MAAM,EAAE;MACR,MAAM;QAAEC,WAAW;QAAEC;MAAM,CAAC,GAAGN,OAAO;MACtCA,OAAO,GAAG,IAAI;MACd,IAAIK,WAAW,EAAE;QACb,MAAMC,KAAK;MACf;IACJ;EACJ,CAAC,MACI;IACDJ,EAAE,CAAC,CAAC;EACR;AACJ;AACA,OAAO,SAASK,YAAYA,CAACC,GAAG,EAAE;EAC9B,IAAIT,MAAM,CAACI,qCAAqC,IAAIH,OAAO,EAAE;IACzDA,OAAO,CAACK,WAAW,GAAG,IAAI;IAC1BL,OAAO,CAACM,KAAK,GAAGE,GAAG;EACvB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}