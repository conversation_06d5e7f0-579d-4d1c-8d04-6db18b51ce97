{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n// Firebase imports\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\nimport { provideDatabase, getDatabase } from '@angular/fire/database';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { IrisFormComponent } from './iris-form/iris-form.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { FooterComponent } from './shared/footer/footer.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/app\";\nimport * as i2 from \"@angular/fire/database\";\n// Firebase configuration pour PFA1\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDummy\",\n  authDomain: \"pfa1-13f62.firebaseapp.com\",\n  databaseURL: \"https://pfa1-13f62-default-rtdb.firebaseio.com/\",\n  projectId: \"pfa1-13f62\",\n  storageBucket: \"pfa1-13f62.appspot.com\",\n  messagingSenderId: \"*********\",\n  appId: \"1:*********:web:dummy\"\n};\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, provideFirebaseApp(() => initializeApp(firebaseConfig)), provideDatabase(() => getDatabase())]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, AccueilComponent, SuivantaccComponent, TypeirisComponent, Iris2Component, FleurComponent, BijouComponent, FluxComponent, ShakerComponent, IrisFormComponent, LoginComponent, SignupComponent, IrisDiversityComponent, DashboardComponent, FooterComponent, PersonalityTestComponent],\n    imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, i1.FirebaseAppModule, i2.DatabaseModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "ReactiveFormsModule", "AppRoutingModule", "AppComponent", "initializeApp", "provideFirebaseApp", "provideDatabase", "getDatabase", "AccueilComponent", "SuivantaccComponent", "TypeirisComponent", "Iris2Component", "FleurComponent", "BijouComponent", "FluxComponent", "ShakerComponent", "IrisFormComponent", "LoginComponent", "SignupComponent", "IrisDiversityComponent", "DashboardComponent", "FooterComponent", "PersonalityTestComponent", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "databaseURL", "projectId", "storageBucket", "messagingSenderId", "appId", "AppModule", "bootstrap", "declarations", "imports", "i1", "FirebaseAppModule", "i2", "DatabaseModule"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n\n// Firebase imports\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\nimport { provideDatabase, getDatabase } from '@angular/fire/database';\n\n// Firebase configuration pour PFA1\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDummy\", // Remplacez par votre vraie clé API\n  authDomain: \"pfa1-13f62.firebaseapp.com\",\n  databaseURL: \"https://pfa1-13f62-default-rtdb.firebaseio.com/\",\n  projectId: \"pfa1-13f62\",\n  storageBucket: \"pfa1-13f62.appspot.com\",\n  messagingSenderId: \"*********\",\n  appId: \"1:*********:web:dummy\"\n};\n\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { IrisFormComponent } from './iris-form/iris-form.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { FooterComponent } from './shared/footer/footer.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    AccueilComponent,\n    SuivantaccComponent,\n    TypeirisComponent,\n    Iris2Component,\n    FleurComponent,\n    BijouComponent,\n    FluxComponent,\n    ShakerComponent,\n    IrisFormComponent,\n    LoginComponent,\n    SignupComponent,\n    IrisDiversityComponent,\n    DashboardComponent,\n    FooterComponent,\n    PersonalityTestComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    provideFirebaseApp(() => initializeApp(firebaseConfig)),\n    provideDatabase(() => getDatabase())\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB,CAAC,CAAC;AACnE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,eAAe,EAAEC,WAAW,QAAQ,wBAAwB;AAarE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,wBAAwB,QAAQ,+CAA+C;;;;AAzBxF;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,aAAa;EACrBC,UAAU,EAAE,4BAA4B;EACxCC,WAAW,EAAE,iDAAiD;EAC9DC,SAAS,EAAE,YAAY;EACvBC,aAAa,EAAE,wBAAwB;EACvCC,iBAAiB,EAAE,WAAW;EAC9BC,KAAK,EAAE;CACR;AAgDD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFR7B,YAAY;IAAA;EAAA;;;gBARtBJ,aAAa,EACbG,gBAAgB,EAChBF,WAAW,EACXC,mBAAmB,EACnBI,kBAAkB,CAAC,MAAMD,aAAa,CAACmB,cAAc,CAAC,CAAC,EACvDjB,eAAe,CAAC,MAAMC,WAAW,EAAE,CAAC;IAAA;EAAA;;;2EAK3BwB,SAAS;IAAAE,YAAA,GA5BlB9B,YAAY,EACZK,gBAAgB,EAChBC,mBAAmB,EACnBC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,eAAe,EACfC,sBAAsB,EACtBC,kBAAkB,EAClBC,eAAe,EACfC,wBAAwB;IAAAY,OAAA,GAGxBnC,aAAa,EACbG,gBAAgB,EAChBF,WAAW,EACXC,mBAAmB,EAAAkC,EAAA,CAAAC,iBAAA,EAAAC,EAAA,CAAAC,cAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}