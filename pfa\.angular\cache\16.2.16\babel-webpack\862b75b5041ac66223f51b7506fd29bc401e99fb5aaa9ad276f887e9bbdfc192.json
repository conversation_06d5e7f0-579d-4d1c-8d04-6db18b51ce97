{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjetPfa/pfa/pfa/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, onAuthStateChanged, updateProfile } from '@angular/fire/auth';\nimport { doc, setDoc, getDoc } from '@angular/fire/firestore';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/auth\";\nimport * as i2 from \"@angular/fire/firestore\";\nimport * as i3 from \"@angular/router\";\nexport class AuthService {\n  constructor(auth, firestore, router) {\n    var _this = this;\n    this.auth = auth;\n    this.firestore = firestore;\n    this.router = router;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.userProfileSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.userProfile$ = this.userProfileSubject.asObservable();\n    this.isAuthenticated$ = new BehaviorSubject(false);\n    // Écouter les changements d'état d'authentification\n    onAuthStateChanged(this.auth, /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (user) {\n        _this.currentUserSubject.next(user);\n        _this.isAuthenticated$.next(!!user);\n        if (user) {\n          // Charger le profil utilisateur depuis Firestore\n          yield _this.loadUserProfile(user.uid);\n        } else {\n          _this.userProfileSubject.next(null);\n        }\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n  /**\n   * Inscription avec email et mot de passe\n   */\n  signUp(email, password, firstName, lastName) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const userCredential = yield createUserWithEmailAndPassword(_this2.auth, email, password);\n        const user = userCredential.user;\n        // Mettre à jour le profil Firebase Auth\n        const displayName = `${firstName} ${lastName}`;\n        yield updateProfile(user, {\n          displayName\n        });\n        // Créer le profil utilisateur dans Firestore\n        const userProfile = {\n          uid: user.uid,\n          email: user.email,\n          firstName,\n          lastName,\n          displayName,\n          role: 'user',\n          createdAt: new Date(),\n          lastLoginAt: new Date(),\n          profileCompleted: false\n        };\n        yield _this2.createUserProfile(userProfile);\n        return {\n          success: true,\n          message: 'Compte créé avec succès !',\n          user\n        };\n      } catch (error) {\n        console.error('Erreur lors de l\\'inscription:', error);\n        return {\n          success: false,\n          message: _this2.getErrorMessage(error.code)\n        };\n      }\n    })();\n  }\n  /**\n   * Connexion avec email et mot de passe\n   */\n  signIn(email, password) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const userCredential = yield signInWithEmailAndPassword(_this3.auth, email, password);\n        const user = userCredential.user;\n        // Mettre à jour la dernière connexion\n        yield _this3.updateLastLogin(user.uid);\n        return {\n          success: true,\n          message: 'Connexion réussie !',\n          user\n        };\n      } catch (error) {\n        console.error('Erreur lors de la connexion:', error);\n        return {\n          success: false,\n          message: _this3.getErrorMessage(error.code)\n        };\n      }\n    })();\n  }\n  /**\n   * Déconnexion\n   */\n  signOut() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield signOut(_this4.auth);\n        _this4.router.navigate(['/login']);\n      } catch (error) {\n        console.error('Erreur lors de la déconnexion:', error);\n      }\n    })();\n  }\n  /**\n   * Déconnexion silencieuse (sans redirection automatique)\n   */\n  signOutSilent() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield signOut(_this5.auth);\n      } catch (error) {\n        console.error('Erreur lors de la déconnexion silencieuse:', error);\n      }\n    })();\n  }\n  /**\n   * Créer le profil utilisateur dans Firestore\n   */\n  createUserProfile(userProfile) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const userDocRef = doc(_this6.firestore, 'users', userProfile.uid);\n      yield setDoc(userDocRef, userProfile);\n      _this6.userProfileSubject.next(userProfile);\n    })();\n  }\n  /**\n   * Charger le profil utilisateur depuis Firestore\n   */\n  loadUserProfile(uid) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const userDocRef = doc(_this7.firestore, 'users', uid);\n        const userDoc = yield getDoc(userDocRef);\n        if (userDoc.exists()) {\n          const userProfile = userDoc.data();\n          _this7.userProfileSubject.next(userProfile);\n        }\n      } catch (error) {\n        console.error('Erreur lors du chargement du profil:', error);\n      }\n    })();\n  }\n  /**\n   * Mettre à jour la dernière connexion\n   */\n  updateLastLogin(uid) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const userDocRef = doc(_this8.firestore, 'users', uid);\n        const userDoc = yield getDoc(userDocRef);\n        if (userDoc.exists()) {\n          const currentProfile = userDoc.data();\n          const updatedProfile = {\n            ...currentProfile,\n            lastLoginAt: new Date()\n          };\n          yield setDoc(userDocRef, updatedProfile);\n          _this8.userProfileSubject.next(updatedProfile);\n        }\n      } catch (error) {\n        console.error('Erreur lors de la mise à jour de la dernière connexion:', error);\n      }\n    })();\n  }\n  /**\n   * Obtenir l'utilisateur actuel\n   */\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  /**\n   * Obtenir le profil utilisateur actuel\n   */\n  getCurrentUserProfile() {\n    return this.userProfileSubject.value;\n  }\n  /**\n   * Vérifier si l'utilisateur est administrateur\n   */\n  isAdmin() {\n    const profile = this.getCurrentUserProfile();\n    return profile?.role === 'admin';\n  }\n  /**\n   * Vérifier si l'utilisateur est connecté\n   */\n  isAuthenticated() {\n    return this.isAuthenticated$.value;\n  }\n  /**\n   * Convertir les codes d'erreur Firebase en messages lisibles\n   */\n  getErrorMessage(errorCode) {\n    switch (errorCode) {\n      case 'auth/email-already-in-use':\n        return 'Cette adresse email est déjà utilisée.';\n      case 'auth/weak-password':\n        return 'Le mot de passe doit contenir au moins 6 caractères.';\n      case 'auth/invalid-email':\n        return 'Adresse email invalide.';\n      case 'auth/user-not-found':\n        return 'Aucun compte trouvé avec cette adresse email.';\n      case 'auth/wrong-password':\n        return 'Mot de passe incorrect.';\n      case 'auth/too-many-requests':\n        return 'Trop de tentatives. Veuillez réessayer plus tard.';\n      case 'auth/network-request-failed':\n        return 'Erreur de connexion. Vérifiez votre connexion internet.';\n      default:\n        return 'Une erreur est survenue. Veuillez réessayer.';\n    }\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.Auth), i0.ɵɵinject(i2.Firestore), i0.ɵɵinject(i3.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["createUserWithEmailAndPassword", "signInWithEmailAndPassword", "signOut", "onAuthStateChanged", "updateProfile", "doc", "setDoc", "getDoc", "BehaviorSubject", "AuthService", "constructor", "auth", "firestore", "router", "_this", "currentUserSubject", "userProfileSubject", "currentUser$", "asObservable", "userProfile$", "isAuthenticated$", "_ref", "_asyncToGenerator", "user", "next", "loadUserProfile", "uid", "_x", "apply", "arguments", "signUp", "email", "password", "firstName", "lastName", "_this2", "userCredential", "displayName", "userProfile", "role", "createdAt", "Date", "lastLoginAt", "profileCompleted", "createUserProfile", "success", "message", "error", "console", "getErrorMessage", "code", "signIn", "_this3", "updateLastLogin", "_this4", "navigate", "signOutSilent", "_this5", "_this6", "userDocRef", "_this7", "userDoc", "exists", "data", "_this8", "currentProfile", "updatedProfile", "getCurrentUser", "value", "getCurrentUserProfile", "isAdmin", "profile", "isAuthenticated", "errorCode", "i0", "ɵɵinject", "i1", "<PERSON><PERSON>", "i2", "Firestore", "i3", "Router", "factory", "ɵfac", "providedIn"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Auth, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, User, onAuthStateChanged, updateProfile } from '@angular/fire/auth';\nimport { Firestore, doc, setDoc, getDoc } from '@angular/fire/firestore';\nimport { Observable, BehaviorSubject, from } from 'rxjs';\nimport { Router } from '@angular/router';\n\nexport interface UserProfile {\n  uid: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  displayName: string;\n  role: 'user' | 'admin';\n  createdAt: Date;\n  lastLoginAt: Date;\n  profileCompleted: boolean;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  private userProfileSubject = new BehaviorSubject<UserProfile | null>(null);\n\n  public currentUser$ = this.currentUserSubject.asObservable();\n  public userProfile$ = this.userProfileSubject.asObservable();\n  public isAuthenticated$ = new BehaviorSubject<boolean>(false);\n\n  constructor(\n    private auth: Auth,\n    private firestore: Firestore,\n    private router: Router\n  ) {\n    // Écouter les changements d'état d'authentification\n    onAuthStateChanged(this.auth, async (user) => {\n      this.currentUserSubject.next(user);\n      this.isAuthenticated$.next(!!user);\n\n      if (user) {\n        // Charger le profil utilisateur depuis Firestore\n        await this.loadUserProfile(user.uid);\n      } else {\n        this.userProfileSubject.next(null);\n      }\n    });\n  }\n\n  /**\n   * Inscription avec email et mot de passe\n   */\n  async signUp(email: string, password: string, firstName: string, lastName: string): Promise<{ success: boolean; message: string; user?: User }> {\n    try {\n      const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);\n      const user = userCredential.user;\n\n      // Mettre à jour le profil Firebase Auth\n      const displayName = `${firstName} ${lastName}`;\n      await updateProfile(user, { displayName });\n\n      // Créer le profil utilisateur dans Firestore\n      const userProfile: UserProfile = {\n        uid: user.uid,\n        email: user.email!,\n        firstName,\n        lastName,\n        displayName,\n        role: 'user', // Par défaut, les nouveaux utilisateurs sont des utilisateurs normaux\n        createdAt: new Date(),\n        lastLoginAt: new Date(),\n        profileCompleted: false\n      };\n\n      await this.createUserProfile(userProfile);\n\n      return {\n        success: true,\n        message: 'Compte créé avec succès !',\n        user\n      };\n    } catch (error: any) {\n      console.error('Erreur lors de l\\'inscription:', error);\n      return {\n        success: false,\n        message: this.getErrorMessage(error.code)\n      };\n    }\n  }\n\n  /**\n   * Connexion avec email et mot de passe\n   */\n  async signIn(email: string, password: string): Promise<{ success: boolean; message: string; user?: User }> {\n    try {\n      const userCredential = await signInWithEmailAndPassword(this.auth, email, password);\n      const user = userCredential.user;\n\n      // Mettre à jour la dernière connexion\n      await this.updateLastLogin(user.uid);\n\n      return {\n        success: true,\n        message: 'Connexion réussie !',\n        user\n      };\n    } catch (error: any) {\n      console.error('Erreur lors de la connexion:', error);\n      return {\n        success: false,\n        message: this.getErrorMessage(error.code)\n      };\n    }\n  }\n\n  /**\n   * Déconnexion\n   */\n  async signOut(): Promise<void> {\n    try {\n      await signOut(this.auth);\n      this.router.navigate(['/login']);\n    } catch (error) {\n      console.error('Erreur lors de la déconnexion:', error);\n    }\n  }\n\n  /**\n   * Déconnexion silencieuse (sans redirection automatique)\n   */\n  async signOutSilent(): Promise<void> {\n    try {\n      await signOut(this.auth);\n    } catch (error) {\n      console.error('Erreur lors de la déconnexion silencieuse:', error);\n    }\n  }\n\n  /**\n   * Créer le profil utilisateur dans Firestore\n   */\n  private async createUserProfile(userProfile: UserProfile): Promise<void> {\n    const userDocRef = doc(this.firestore, 'users', userProfile.uid);\n    await setDoc(userDocRef, userProfile);\n    this.userProfileSubject.next(userProfile);\n  }\n\n  /**\n   * Charger le profil utilisateur depuis Firestore\n   */\n  private async loadUserProfile(uid: string): Promise<void> {\n    try {\n      const userDocRef = doc(this.firestore, 'users', uid);\n      const userDoc = await getDoc(userDocRef);\n\n      if (userDoc.exists()) {\n        const userProfile = userDoc.data() as UserProfile;\n        this.userProfileSubject.next(userProfile);\n      }\n    } catch (error) {\n      console.error('Erreur lors du chargement du profil:', error);\n    }\n  }\n\n  /**\n   * Mettre à jour la dernière connexion\n   */\n  private async updateLastLogin(uid: string): Promise<void> {\n    try {\n      const userDocRef = doc(this.firestore, 'users', uid);\n      const userDoc = await getDoc(userDocRef);\n\n      if (userDoc.exists()) {\n        const currentProfile = userDoc.data() as UserProfile;\n        const updatedProfile = {\n          ...currentProfile,\n          lastLoginAt: new Date()\n        };\n\n        await setDoc(userDocRef, updatedProfile);\n        this.userProfileSubject.next(updatedProfile);\n      }\n    } catch (error) {\n      console.error('Erreur lors de la mise à jour de la dernière connexion:', error);\n    }\n  }\n\n  /**\n   * Obtenir l'utilisateur actuel\n   */\n  getCurrentUser(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  /**\n   * Obtenir le profil utilisateur actuel\n   */\n  getCurrentUserProfile(): UserProfile | null {\n    return this.userProfileSubject.value;\n  }\n\n  /**\n   * Vérifier si l'utilisateur est administrateur\n   */\n  isAdmin(): boolean {\n    const profile = this.getCurrentUserProfile();\n    return profile?.role === 'admin';\n  }\n\n  /**\n   * Vérifier si l'utilisateur est connecté\n   */\n  isAuthenticated(): boolean {\n    return this.isAuthenticated$.value;\n  }\n\n\n\n  /**\n   * Convertir les codes d'erreur Firebase en messages lisibles\n   */\n  private getErrorMessage(errorCode: string): string {\n    switch (errorCode) {\n      case 'auth/email-already-in-use':\n        return 'Cette adresse email est déjà utilisée.';\n      case 'auth/weak-password':\n        return 'Le mot de passe doit contenir au moins 6 caractères.';\n      case 'auth/invalid-email':\n        return 'Adresse email invalide.';\n      case 'auth/user-not-found':\n        return 'Aucun compte trouvé avec cette adresse email.';\n      case 'auth/wrong-password':\n        return 'Mot de passe incorrect.';\n      case 'auth/too-many-requests':\n        return 'Trop de tentatives. Veuillez réessayer plus tard.';\n      case 'auth/network-request-failed':\n        return 'Erreur de connexion. Vérifiez votre connexion internet.';\n      default:\n        return 'Une erreur est survenue. Veuillez réessayer.';\n    }\n  }\n}\n"], "mappings": ";AACA,SAAeA,8BAA8B,EAAEC,0BAA0B,EAAEC,OAAO,EAAQC,kBAAkB,EAAEC,aAAa,QAAQ,oBAAoB;AACvJ,SAAoBC,GAAG,EAAEC,MAAM,EAAEC,MAAM,QAAQ,yBAAyB;AACxE,SAAqBC,eAAe,QAAc,MAAM;;;;;AAkBxD,OAAM,MAAOC,WAAW;EAQtBC,YACUC,IAAU,EACVC,SAAoB,EACpBC,MAAc;IAAA,IAAAC,KAAA;IAFd,KAAAH,IAAI,GAAJA,IAAI;IACJ,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IAVR,KAAAE,kBAAkB,GAAG,IAAIP,eAAe,CAAc,IAAI,CAAC;IAC3D,KAAAQ,kBAAkB,GAAG,IAAIR,eAAe,CAAqB,IAAI,CAAC;IAEnE,KAAAS,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACG,YAAY,EAAE;IACrD,KAAAC,YAAY,GAAG,IAAI,CAACH,kBAAkB,CAACE,YAAY,EAAE;IACrD,KAAAE,gBAAgB,GAAG,IAAIZ,eAAe,CAAU,KAAK,CAAC;IAO3D;IACAL,kBAAkB,CAAC,IAAI,CAACQ,IAAI;MAAA,IAAAU,IAAA,GAAAC,iBAAA,CAAE,WAAOC,IAAI,EAAI;QAC3CT,KAAI,CAACC,kBAAkB,CAACS,IAAI,CAACD,IAAI,CAAC;QAClCT,KAAI,CAACM,gBAAgB,CAACI,IAAI,CAAC,CAAC,CAACD,IAAI,CAAC;QAElC,IAAIA,IAAI,EAAE;UACR;UACA,MAAMT,KAAI,CAACW,eAAe,CAACF,IAAI,CAACG,GAAG,CAAC;SACrC,MAAM;UACLZ,KAAI,CAACE,kBAAkB,CAACQ,IAAI,CAAC,IAAI,CAAC;;MAEtC,CAAC;MAAA,iBAAAG,EAAA;QAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;EACJ;EAEA;;;EAGMC,MAAMA,CAACC,KAAa,EAAEC,QAAgB,EAAEC,SAAiB,EAAEC,QAAgB;IAAA,IAAAC,MAAA;IAAA,OAAAb,iBAAA;MAC/E,IAAI;QACF,MAAMc,cAAc,SAASpC,8BAA8B,CAACmC,MAAI,CAACxB,IAAI,EAAEoB,KAAK,EAAEC,QAAQ,CAAC;QACvF,MAAMT,IAAI,GAAGa,cAAc,CAACb,IAAI;QAEhC;QACA,MAAMc,WAAW,GAAG,GAAGJ,SAAS,IAAIC,QAAQ,EAAE;QAC9C,MAAM9B,aAAa,CAACmB,IAAI,EAAE;UAAEc;QAAW,CAAE,CAAC;QAE1C;QACA,MAAMC,WAAW,GAAgB;UAC/BZ,GAAG,EAAEH,IAAI,CAACG,GAAG;UACbK,KAAK,EAAER,IAAI,CAACQ,KAAM;UAClBE,SAAS;UACTC,QAAQ;UACRG,WAAW;UACXE,IAAI,EAAE,MAAM;UACZC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,WAAW,EAAE,IAAID,IAAI,EAAE;UACvBE,gBAAgB,EAAE;SACnB;QAED,MAAMR,MAAI,CAACS,iBAAiB,CAACN,WAAW,CAAC;QAEzC,OAAO;UACLO,OAAO,EAAE,IAAI;UACbC,OAAO,EAAE,2BAA2B;UACpCvB;SACD;OACF,CAAC,OAAOwB,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,OAAO;UACLF,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEX,MAAI,CAACc,eAAe,CAACF,KAAK,CAACG,IAAI;SACzC;;IACF;EACH;EAEA;;;EAGMC,MAAMA,CAACpB,KAAa,EAAEC,QAAgB;IAAA,IAAAoB,MAAA;IAAA,OAAA9B,iBAAA;MAC1C,IAAI;QACF,MAAMc,cAAc,SAASnC,0BAA0B,CAACmD,MAAI,CAACzC,IAAI,EAAEoB,KAAK,EAAEC,QAAQ,CAAC;QACnF,MAAMT,IAAI,GAAGa,cAAc,CAACb,IAAI;QAEhC;QACA,MAAM6B,MAAI,CAACC,eAAe,CAAC9B,IAAI,CAACG,GAAG,CAAC;QAEpC,OAAO;UACLmB,OAAO,EAAE,IAAI;UACbC,OAAO,EAAE,qBAAqB;UAC9BvB;SACD;OACF,CAAC,OAAOwB,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,OAAO;UACLF,OAAO,EAAE,KAAK;UACdC,OAAO,EAAEM,MAAI,CAACH,eAAe,CAACF,KAAK,CAACG,IAAI;SACzC;;IACF;EACH;EAEA;;;EAGMhD,OAAOA,CAAA;IAAA,IAAAoD,MAAA;IAAA,OAAAhC,iBAAA;MACX,IAAI;QACF,MAAMpB,OAAO,CAACoD,MAAI,CAAC3C,IAAI,CAAC;QACxB2C,MAAI,CAACzC,MAAM,CAAC0C,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;OACjC,CAAC,OAAOR,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;IACvD;EACH;EAEA;;;EAGMS,aAAaA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAnC,iBAAA;MACjB,IAAI;QACF,MAAMpB,OAAO,CAACuD,MAAI,CAAC9C,IAAI,CAAC;OACzB,CAAC,OAAOoC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;;IACnE;EACH;EAEA;;;EAGcH,iBAAiBA,CAACN,WAAwB;IAAA,IAAAoB,MAAA;IAAA,OAAApC,iBAAA;MACtD,MAAMqC,UAAU,GAAGtD,GAAG,CAACqD,MAAI,CAAC9C,SAAS,EAAE,OAAO,EAAE0B,WAAW,CAACZ,GAAG,CAAC;MAChE,MAAMpB,MAAM,CAACqD,UAAU,EAAErB,WAAW,CAAC;MACrCoB,MAAI,CAAC1C,kBAAkB,CAACQ,IAAI,CAACc,WAAW,CAAC;IAAC;EAC5C;EAEA;;;EAGcb,eAAeA,CAACC,GAAW;IAAA,IAAAkC,MAAA;IAAA,OAAAtC,iBAAA;MACvC,IAAI;QACF,MAAMqC,UAAU,GAAGtD,GAAG,CAACuD,MAAI,CAAChD,SAAS,EAAE,OAAO,EAAEc,GAAG,CAAC;QACpD,MAAMmC,OAAO,SAAStD,MAAM,CAACoD,UAAU,CAAC;QAExC,IAAIE,OAAO,CAACC,MAAM,EAAE,EAAE;UACpB,MAAMxB,WAAW,GAAGuB,OAAO,CAACE,IAAI,EAAiB;UACjDH,MAAI,CAAC5C,kBAAkB,CAACQ,IAAI,CAACc,WAAW,CAAC;;OAE5C,CAAC,OAAOS,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;;IAC7D;EACH;EAEA;;;EAGcM,eAAeA,CAAC3B,GAAW;IAAA,IAAAsC,MAAA;IAAA,OAAA1C,iBAAA;MACvC,IAAI;QACF,MAAMqC,UAAU,GAAGtD,GAAG,CAAC2D,MAAI,CAACpD,SAAS,EAAE,OAAO,EAAEc,GAAG,CAAC;QACpD,MAAMmC,OAAO,SAAStD,MAAM,CAACoD,UAAU,CAAC;QAExC,IAAIE,OAAO,CAACC,MAAM,EAAE,EAAE;UACpB,MAAMG,cAAc,GAAGJ,OAAO,CAACE,IAAI,EAAiB;UACpD,MAAMG,cAAc,GAAG;YACrB,GAAGD,cAAc;YACjBvB,WAAW,EAAE,IAAID,IAAI;WACtB;UAED,MAAMnC,MAAM,CAACqD,UAAU,EAAEO,cAAc,CAAC;UACxCF,MAAI,CAAChD,kBAAkB,CAACQ,IAAI,CAAC0C,cAAc,CAAC;;OAE/C,CAAC,OAAOnB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;;IAChF;EACH;EAEA;;;EAGAoB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACpD,kBAAkB,CAACqD,KAAK;EACtC;EAEA;;;EAGAC,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACrD,kBAAkB,CAACoD,KAAK;EACtC;EAEA;;;EAGAE,OAAOA,CAAA;IACL,MAAMC,OAAO,GAAG,IAAI,CAACF,qBAAqB,EAAE;IAC5C,OAAOE,OAAO,EAAEhC,IAAI,KAAK,OAAO;EAClC;EAEA;;;EAGAiC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpD,gBAAgB,CAACgD,KAAK;EACpC;EAIA;;;EAGQnB,eAAeA,CAACwB,SAAiB;IACvC,QAAQA,SAAS;MACf,KAAK,2BAA2B;QAC9B,OAAO,wCAAwC;MACjD,KAAK,oBAAoB;QACvB,OAAO,sDAAsD;MAC/D,KAAK,oBAAoB;QACvB,OAAO,yBAAyB;MAClC,KAAK,qBAAqB;QACxB,OAAO,+CAA+C;MACxD,KAAK,qBAAqB;QACxB,OAAO,yBAAyB;MAClC,KAAK,wBAAwB;QAC3B,OAAO,mDAAmD;MAC5D,KAAK,6BAA6B;QAChC,OAAO,yDAAyD;MAClE;QACE,OAAO,8CAA8C;;EAE3D;;;uBA1NWhE,WAAW,EAAAiE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,IAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,SAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAXxE,WAAW;MAAAyE,OAAA,EAAXzE,WAAW,CAAA0E,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}