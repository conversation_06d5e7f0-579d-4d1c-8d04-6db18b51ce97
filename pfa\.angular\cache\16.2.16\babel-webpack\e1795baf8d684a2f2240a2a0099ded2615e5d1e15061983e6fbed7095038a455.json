{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nexport class SignupComponent {\n  constructor() {\n    this.user = {\n      name: '',\n      email: '',\n      password: '',\n      terms: false\n    };\n  }\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 51,\n      vars: 5,\n      consts: [[1, \"signup-container\"], [1, \"left-section\"], [1, \"graphic\"], [1, \"dot\"], [1, \"slogan\"], [1, \"right-section\"], [1, \"form-container\"], [1, \"logo\"], [1, \"custom-logo\"], [1, \"google-btn\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google Icon\"], [1, \"divider\"], [3, \"ngSubmit\"], [\"signupForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \"<PERSON> <PERSON>\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"placeholder\", \"<EMAIL>\", \"required\", \"\", \"email\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"placeholder\", \"At least 8 characters\", \"required\", \"\", \"minlength\", \"8\", 3, \"ngModel\", \"ngModelChange\"], [1, \"checkbox-group\"], [\"type\", \"checkbox\", \"id\", \"terms\", \"name\", \"terms\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"terms\"], [\"href\", \"#\"], [\"type\", \"submit\", 1, \"signup-btn\", 3, \"disabled\"], [1, \"login-link\"], [\"routerLink\", \"/login\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"div\", 3)(4, \"div\", 3)(5, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 4);\n          i0.ɵɵtext(7, \" Each iris is a unique story written by nature, waiting to be decoded by technology \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 5)(9, \"div\", 6)(10, \"div\", 7)(11, \"div\", 8);\n          i0.ɵɵelement(12, \"span\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"h1\");\n          i0.ɵɵtext(14, \"Sign Up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\");\n          i0.ɵɵtext(16, \"Votre identit\\u00E9, red\\u00E9finie par la technologie. Cr\\u00E9ez votre compte.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"button\", 9);\n          i0.ɵɵelement(18, \"img\", 10);\n          i0.ɵɵtext(19, \" Continue with Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 11);\n          i0.ɵɵtext(21, \"or Sign in with Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"form\", 12, 13);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_22_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(24, \"div\", 14)(25, \"label\", 15);\n          i0.ɵɵtext(26, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_27_listener($event) {\n            return ctx.user.name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 14)(29, \"label\", 17);\n          i0.ɵɵtext(30, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"input\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_31_listener($event) {\n            return ctx.user.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 14)(33, \"label\", 19);\n          i0.ɵɵtext(34, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_35_listener($event) {\n            return ctx.user.password = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 21)(37, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_37_listener($event) {\n            return ctx.user.terms = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"label\", 23);\n          i0.ɵɵtext(39, \"I agree with \");\n          i0.ɵɵelementStart(40, \"a\", 24);\n          i0.ɵɵtext(41, \"Terms\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" and \");\n          i0.ɵɵelementStart(43, \"a\", 24);\n          i0.ɵɵtext(44, \"Privacy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"button\", 25);\n          i0.ɵɵtext(46, \" Sign Up \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"p\", 26);\n          i0.ɵɵtext(48, \" Already have an account? \");\n          i0.ɵɵelementStart(49, \"a\", 27);\n          i0.ɵɵtext(50, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(23);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.terms);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", !_r0.valid);\n        }\n      },\n      dependencies: [i1.RouterLink, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MinLengthValidator, i2.CheckboxRequiredValidator, i2.EmailValidator, i2.NgModel, i2.NgForm],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.signup-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  background: #f5f5ff; \\n\\n  overflow: hidden;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  background: #e6e6ff url(\\\"./assets/iris-collage.png\\\") no-repeat center center; \\n\\n  background-size: 300px auto; \\n\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n  overflow: hidden;\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\\n}\\n\\n\\n\\n.dot[_ngcontent-%COMP%] {\\n  position: absolute;\\n  background: #ff6f61; \\n\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_pulseDot 2s infinite;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before, .left-section[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #ff6f61;\\n  border-radius: 50%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before {\\n  width: 5px;\\n  height: 5px;\\n  top: 10%;\\n  left: 20%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::after {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 15%;\\n  right: 10%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(1) {\\n  width: 4px;\\n  height: 4px;\\n  top: 5%;\\n  left: 30%;\\n  animation-delay: 0.5s;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(2) {\\n  width: 6px;\\n  height: 6px;\\n  top: 20%;\\n  right: 15%;\\n  animation-delay: 1s;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(3) {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 20%;\\n  left: 10%;\\n  animation-delay: 1.5s;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%]::before, .left-section[_ngcontent-%COMP%]::after, .left-section[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: rgba(255, 182, 193, 0.5); \\n\\n  border-radius: 50%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before {\\n  width: 80px;\\n  height: 80px;\\n  top: 10%;\\n  right: 10%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::after {\\n  width: 50px;\\n  height: 50px;\\n  bottom: 10%;\\n  left: 5%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  background: rgba(200, 200, 255, 0.3); \\n\\n}\\n\\n.slogan[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #6464ad; \\n\\n  text-align: center;\\n  margin-top: 40px;\\n  font-style: italic;\\n  font-family: \\\"Dancing Script\\\", cursive;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_fadeIn 1.5s ease-in-out forwards;\\n}\\n\\n\\n\\n.right-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background: linear-gradient(135deg, #f5f5ff, #ffffff);\\n}\\n\\n\\n\\n.form-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 550px;\\n  padding: 20px;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(106, 90, 205, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_slideIn 1s ease-in-out forwards;\\n}\\n\\n\\n\\n.custom-logo[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  margin: 0 auto;\\n  position: relative;\\n  background: transparent;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd;\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  bottom: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd;\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  left: 5px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  right: 5px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.2rem;\\n  color: #333;\\n  font-weight: 700;\\n  margin: 10px 0;\\n  letter-spacing: 1px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n  font-weight: 400;\\n}\\n\\n.google-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  padding: 10px;\\n  background: #fff;\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 0.9rem;\\n  color: #333;\\n  font-weight: 500;\\n  margin-bottom: 15px;\\n  transition: all 0.3s ease;\\n}\\n\\n.google-btn[_ngcontent-%COMP%]:hover {\\n  background: #e6e6fa;\\n  box-shadow: 0 0 15px rgba(106, 90, 205, 0.2);\\n  transform: translateY(-2px);\\n}\\n\\n.google-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 18px;\\n  margin-right: 8px;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #666;\\n  font-size: 0.8rem;\\n  margin: 15px 0;\\n  position: relative;\\n  font-weight: 500;\\n}\\n\\n.divider[_ngcontent-%COMP%]::before, .divider[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 40%;\\n  height: 1px;\\n  background: rgba(106, 90, 205, 0.2);\\n}\\n\\n.divider[_ngcontent-%COMP%]::before {\\n  left: -10px;\\n}\\n\\n.divider[_ngcontent-%COMP%]::after {\\n  right: -10px;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.8rem;\\n  color: #333;\\n  margin-bottom: 6px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  color: #333;\\n  background: rgba(255, 255, 255, 0.5);\\n  transition: all 0.3s ease;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #6a5acd;\\n  box-shadow: 0 0 10px rgba(106, 90, 205, 0.2);\\n  background: #fff;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-weight: 400;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 15px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: #5a4bbd;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px;\\n  background: linear-gradient(90deg, #6a5acd, #5a4bbd);\\n  color: #fff; \\n\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  text-transform: uppercase;\\n  transition: all 0.3s ease;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, #5a4bbd, #6a5acd);\\n  box-shadow: 0 0 15px rgba(106, 90, 205, 0.4);\\n  transform: translateY(-2px);\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n  box-shadow: none;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 15px;\\n  font-size: 0.8rem;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: #5a4bbd;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInScale {\\n  0% {\\n    opacity: 0;\\n    transform: scale(0.8);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulseDot {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  50% {\\n    transform: scale(1.5);\\n    opacity: 0.5;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  0% {\\n    opacity: 0;\\n    transform: translateX(50px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateX(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SignupComponent", "constructor", "user", "name", "email", "password", "terms", "onSubmit", "console", "log", "alert", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_22_listener", "SignupComponent_Template_input_ngModelChange_27_listener", "$event", "SignupComponent_Template_input_ngModelChange_31_listener", "SignupComponent_Template_input_ngModelChange_35_listener", "SignupComponent_Template_input_ngModelChange_37_listener", "ɵɵadvance", "ɵɵproperty", "_r0", "valid"], "sources": ["C:\\pfa\\src\\app\\signup\\signup.component.ts", "C:\\pfa\\src\\app\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.scss']\n})\nexport class SignupComponent {\n  user = {\n    name: '',\n    email: '',\n    password: '',\n    terms: false\n  };\n\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n}", "<div class=\"signup-container\">\n  <!-- Section gauche : Graphique et texte -->\n  <div class=\"left-section\">\n    <div class=\"graphic\">\n      <!-- Points décoratifs -->\n      <div class=\"dot\"></div>\n      <div class=\"dot\"></div>\n      <div class=\"dot\"></div>\n    </div>\n    <p class=\"slogan\">\n      Each iris is a unique story written by nature, waiting to be decoded by technology\n    </p>\n  </div>\n\n  <!-- Section droite : Formulaire -->\n  <div class=\"right-section\">\n    <div class=\"form-container\">\n      <div class=\"logo\">\n        <!-- Logo en forme de croix -->\n        <div class=\"custom-logo\"><span></span></div>\n        <h1>Sign Up</h1>\n        <p>Votre identité, redéfinie par la technologie. Créez votre compte.</p>\n      </div>\n\n      <!-- Bouton \"Continue with Google\" -->\n      <button class=\"google-btn\">\n        <img src=\"assets/google-icon.png\" alt=\"Google Icon\" /> Continue with Google\n      </button>\n\n      <div class=\"divider\">or Sign in with Email</div>\n\n      <!-- Formulaire -->\n      <form (ngSubmit)=\"onSubmit()\" #signupForm=\"ngForm\">\n        <div class=\"form-group\">\n          <label for=\"name\">Name</label>\n          <input\n            type=\"text\"\n            id=\"name\"\n            name=\"name\"\n            placeholder=\"Leslie Alexander\"\n            [(ngModel)]=\"user.name\"\n            required\n          />\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"email\">Email</label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            name=\"email\"\n            placeholder=\"<EMAIL>\"\n            [(ngModel)]=\"user.email\"\n            required\n            email\n          />\n        </div>\n\n        <div class=\"form-group\">\n          <label for=\"password\">Password</label>\n          <input\n            type=\"password\"\n            id=\"password\"\n            name=\"password\"\n            placeholder=\"At least 8 characters\"\n            [(ngModel)]=\"user.password\"\n            required\n            minlength=\"8\"\n          />\n        </div>\n\n        <div class=\"checkbox-group\">\n          <input\n            type=\"checkbox\"\n            id=\"terms\"\n            name=\"terms\"\n            [(ngModel)]=\"user.terms\"\n            required\n          />\n          <label for=\"terms\">I agree with <a href=\"#\">Terms</a> and <a href=\"#\">Privacy</a></label>\n        </div>\n\n        <button type=\"submit\" class=\"signup-btn\" [disabled]=\"!signupForm.valid\">\n          Sign Up\n        </button>\n      </form>\n\n      <p class=\"login-link\">\n        Already have an account? <a routerLink=\"/login\">Log in</a>\n      </p>\n    </div>\n  </div>\n</div>"], "mappings": ";;;AAOA,OAAM,MAAOA,eAAe;EAL5BC,YAAA;IAME,KAAAC,IAAI,GAAG;MACLC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;KACR;;EAEDC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,IAAI,CAACI,KAAK,EAAE;MACnBE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACP,IAAI,CAAC;MACzC;KACD,MAAM;MACLQ,KAAK,CAAC,+CAA+C,CAAC;;EAE1D;;;uBAfWV,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAW,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,aAA8B;UAKxBD,EAAA,CAAAE,SAAA,aAAuB;UAGzBF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,WAAkB;UAChBD,EAAA,CAAAI,MAAA,2FACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,aAA2B;UAIID,EAAA,CAAAE,SAAA,YAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,wFAAiE;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAI1EH,EAAA,CAAAC,cAAA,iBAA2B;UACzBD,EAAA,CAAAE,SAAA,eAAsD;UAACF,EAAA,CAAAI,MAAA,8BACzD;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAI,MAAA,6BAAqB;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAGhDH,EAAA,CAAAC,cAAA,oBAAmD;UAA7CD,EAAA,CAAAK,UAAA,sBAAAC,mDAAA;YAAA,OAAYP,GAAA,CAAAX,QAAA,EAAU;UAAA,EAAC;UAC3BY,EAAA,CAAAC,cAAA,eAAwB;UACJD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAC,cAAA,iBAOE;UAFAD,EAAA,CAAAK,UAAA,2BAAAE,yDAAAC,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAC,IAAA,GAAAwB,MAAA;UAAA,EAAuB;UALzBR,EAAA,CAAAG,YAAA,EAOE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAI,yDAAAD,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAE,KAAA,GAAAuB,MAAA;UAAA,EAAwB;UAL1BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAK,yDAAAF,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAG,QAAA,GAAAsB,MAAA;UAAA,EAA2B;UAL7BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAA4B;UAKxBD,EAAA,CAAAK,UAAA,2BAAAM,yDAAAH,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAI,KAAA,GAAAqB,MAAA;UAAA,EAAwB;UAJ1BR,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAAmB;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAI,MAAA,aAAI;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGnFH,EAAA,CAAAC,cAAA,kBAAwE;UACtED,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,aAAsB;UACpBD,EAAA,CAAAI,MAAA,kCAAyB;UAAAJ,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;UAhDtDH,EAAA,CAAAY,SAAA,IAAuB;UAAvBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAC,IAAA,CAAuB;UAYvBgB,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAE,KAAA,CAAwB;UAaxBe,EAAA,CAAAY,SAAA,GAA2B;UAA3BZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAG,QAAA,CAA2B;UAW3Bc,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAI,KAAA,CAAwB;UAMaa,EAAA,CAAAY,SAAA,GAA8B;UAA9BZ,EAAA,CAAAa,UAAA,cAAAC,GAAA,CAAAC,KAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}