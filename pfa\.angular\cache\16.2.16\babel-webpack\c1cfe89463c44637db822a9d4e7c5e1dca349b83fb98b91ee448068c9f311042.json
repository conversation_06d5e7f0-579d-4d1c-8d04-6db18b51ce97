{"ast": null, "code": "import _asyncToGenerator from \"E:/aymen/pfa/pfa/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction LoginComponent_div_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"L'email est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Veuillez entrer un email valide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, LoginComponent_div_20_span_1_Template, 2, 0, \"span\", 29);\n    i0.ɵɵtemplate(2, LoginComponent_div_20_span_2_Template, 2, 0, \"span\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r1 = i0.ɵɵreference(19);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"email\"]);\n  }\n}\nfunction LoginComponent_div_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le mot de passe est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_31_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le mot de passe doit contenir au moins 6 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, LoginComponent_div_31_span_1_Template, 2, 0, \"span\", 29);\n    i0.ɵɵtemplate(2, LoginComponent_div_31_span_2_Template, 2, 0, \"span\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(28);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r3.errors == null ? null : _r3.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r3.errors == null ? null : _r3.errors[\"minlength\"]);\n  }\n}\nfunction LoginComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.errorMessage, \" \");\n  }\n}\nfunction LoginComponent_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Connexion en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(router, route, authService) {\n    this.router = router;\n    this.route = route;\n    this.authService = authService;\n    this.loginData = {\n      email: '',\n      password: '',\n      rememberMe: false\n    };\n    this.showPassword = false;\n    this.isLoading = false;\n    this.errorMessage = '';\n    this.returnUrl = '';\n    // Récupérer l'URL de retour si elle existe\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '';\n  }\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.isLoading) return;\n      _this.isLoading = true;\n      _this.errorMessage = '';\n      try {\n        const result = yield _this.authService.signIn(_this.loginData.email, _this.loginData.password);\n        if (result.success) {\n          console.log('Connexion réussie !');\n          // Attendre que le profil utilisateur soit chargé\n          setTimeout(() => {\n            const userProfile = _this.authService.getCurrentUserProfile();\n            if (_this.returnUrl) {\n              // Rediriger vers l'URL de retour\n              _this.router.navigateByUrl(_this.returnUrl);\n            } else {\n              // Rediriger selon le rôle\n              if (userProfile?.role === 'admin') {\n                _this.router.navigate(['/dashboard']);\n              } else {\n                _this.router.navigate(['/personality-test']);\n              }\n            }\n          }, 500);\n        } else {\n          _this.errorMessage = result.message;\n        }\n      } catch (error) {\n        console.error('Erreur lors de la connexion:', error);\n        _this.errorMessage = 'Une erreur est survenue lors de la connexion.';\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  /**\n   * Connexion avec un compte de test (pour le développement)\n   */\n  loginWithTestAccount(email, password) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.loginData.email = email;\n      _this2.loginData.password = password;\n      yield _this2.onSubmit();\n    })();\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 74,\n      vars: 11,\n      consts: [[1, \"auth-container\", \"login\"], [1, \"container\"], [1, \"auth-card\"], [1, \"auth-header\"], [1, \"title\"], [1, \"subtitle\"], [1, \"divider\"], [1, \"auth-form\"], [3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"email\"], [1, \"input-container\"], [1, \"input-icon\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"Votre adresse email\", 3, \"ngModel\", \"ngModelChange\"], [\"email\", \"ngModel\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"for\", \"password\"], [\"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"minlength\", \"6\", \"placeholder\", \"Votre mot de passe\", 3, \"type\", \"ngModel\", \"ngModelChange\"], [\"password\", \"ngModel\"], [\"type\", \"button\", 1, \"toggle-password\", 3, \"click\"], [1, \"form-options\"], [1, \"remember-me\"], [\"type\", \"checkbox\", \"id\", \"remember\", \"name\", \"remember\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"remember\"], [\"href\", \"#\", 1, \"forgot-password\"], [\"class\", \"error-message global-error\", 4, \"ngIf\"], [1, \"form-actions\"], [\"type\", \"submit\", 1, \"submit-btn\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"social-login\"], [1, \"separator\"], [1, \"social-buttons\"], [1, \"social-btn\", \"google\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google\"], [1, \"social-btn\", \"facebook\"], [1, \"facebook-icon\"], [1, \"test-accounts\", 2, \"margin-top\", \"2rem\", \"padding\", \"1rem\", \"background\", \"#f8f9fa\", \"border-radius\", \"8px\", \"border\", \"1px solid #e9ecef\"], [2, \"margin\", \"0 0 1rem 0\", \"color\", \"#6c757d\", \"font-size\", \"0.9rem\"], [2, \"display\", \"flex\", \"gap\", \"1rem\", \"flex-wrap\", \"wrap\"], [\"type\", \"button\", 1, \"test-btn\", 2, \"padding\", \"0.5rem 1rem\", \"background\", \"#007bff\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.8rem\", 3, \"click\"], [\"type\", \"button\", 1, \"test-btn\", 2, \"padding\", \"0.5rem 1rem\", \"background\", \"#28a745\", \"color\", \"white\", \"border\", \"none\", \"border-radius\", \"4px\", \"cursor\", \"pointer\", \"font-size\", \"0.8rem\", 3, \"click\"], [1, \"auth-footer\"], [\"routerLink\", \"/signup\"], [1, \"navigation\"], [\"routerLink\", \"/accueil\", 1, \"back-btn\"], [1, \"icon\"], [1, \"error-message\"], [1, \"error-message\", \"global-error\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"Connexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Acc\\u00E9dez \\u00E0 votre compte IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"form\", 8, 9);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"label\", 11);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"span\", 13);\n          i0.ɵɵtext(17, \"\\u2709\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 14, 15);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.loginData.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(20, LoginComponent_div_20_Template, 3, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"label\", 17);\n          i0.ɵɵtext(23, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"span\", 13);\n          i0.ɵɵtext(26, \"\\uD83D\\uDD12\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 18, 19);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_27_listener($event) {\n            return ctx.loginData.password = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_29_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, LoginComponent_div_31_Template, 3, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 21)(33, \"div\", 22)(34, \"input\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_34_listener($event) {\n            return ctx.loginData.rememberMe = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"label\", 24);\n          i0.ɵɵtext(36, \"Se souvenir de moi\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"a\", 25);\n          i0.ɵɵtext(38, \"Mot de passe oubli\\u00E9?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(39, LoginComponent_div_39_Template, 2, 1, \"div\", 26);\n          i0.ɵɵelementStart(40, \"div\", 27)(41, \"button\", 28);\n          i0.ɵɵtemplate(42, LoginComponent_span_42_Template, 2, 0, \"span\", 29);\n          i0.ɵɵtemplate(43, LoginComponent_span_43_Template, 2, 0, \"span\", 29);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 30)(45, \"p\", 31);\n          i0.ɵɵtext(46, \"Ou connectez-vous avec\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 32)(48, \"button\", 33);\n          i0.ɵɵelement(49, \"img\", 34);\n          i0.ɵɵtext(50, \" Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"button\", 35)(52, \"span\", 36);\n          i0.ɵɵtext(53, \"f\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(54, \" Facebook \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"div\", 37)(56, \"h4\", 38);\n          i0.ɵɵtext(57, \"Comptes de test (d\\u00E9veloppement)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"div\", 39)(59, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_59_listener() {\n            return ctx.loginWithTestAccount(\"<EMAIL>\", \"admin123\");\n          });\n          i0.ɵɵtext(60, \" Admin Test \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_61_listener() {\n            return ctx.loginWithTestAccount(\"<EMAIL>\", \"user123\");\n          });\n          i0.ɵɵtext(62, \" User Test \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(63, \"div\", 42)(64, \"p\");\n          i0.ɵɵtext(65, \"Vous n'avez pas de compte? \");\n          i0.ɵɵelementStart(66, \"a\", 43);\n          i0.ɵɵtext(67, \"Inscrivez-vous\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(68, \"div\", 44)(69, \"a\", 45)(70, \"span\", 46);\n          i0.ɵɵtext(71, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"span\");\n          i0.ɵɵtext(73, \"Retour \\u00E0 l'accueil\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(11);\n          const _r1 = i0.ɵɵreference(19);\n          const _r3 = i0.ɵɵreference(28);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngModel\", ctx.loginData.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && (_r1.dirty || _r1.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\")(\"ngModel\", ctx.loginData.password);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.showPassword ? \"\\uD83D\\uDC41\\uFE0F\" : \"\\uD83D\\uDC41\\uFE0F\\u200D\\uD83D\\uDDE8\\uFE0F\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r3.invalid && (_r3.dirty || _r3.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.loginData.rememberMe);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", _r0.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i3.NgIf, i1.RouterLink, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.CheckboxControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.MinLengthValidator, i4.EmailValidator, i4.NgModel, i4.NgForm],\n      styles: [\".auth-container.login[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\\n  padding: 40px 0;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 20px;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  max-width: 500px;\\n  margin: 0 auto 40px;\\n  padding: 40px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2.2rem;\\n  color: #333;\\n  margin-bottom: 10px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1rem;\\n  margin-bottom: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  margin: 0 auto;\\n  border-radius: 3px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  color: #555;\\n  margin-bottom: 8px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 15px;\\n  font-size: 1.1rem;\\n  color: #aaa;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 15px 12px 45px;\\n  border: 1px solid #ddd;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--fleur-primary);\\n  box-shadow: 0 0 0 3px rgba(138, 79, 255, 0.1);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .toggle-password[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  background: none;\\n  border: none;\\n  font-size: 1.1rem;\\n  cursor: pointer;\\n  color: #aaa;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .toggle-password[_ngcontent-%COMP%]:hover {\\n  color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #e74c3c;\\n  margin-top: 5px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 3px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 25px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n  cursor: pointer;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--fleur-primary);\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 25px rgba(138, 79, 255, 0.4);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%] {\\n  text-align: center;\\n  position: relative;\\n  color: #999;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:before, .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 40%;\\n  height: 1px;\\n  background-color: #ddd;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:before {\\n  left: 0;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:after {\\n  right: 0;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  padding: 12px;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 1px solid #ddd;\\n  background-color: white;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]   .facebook-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background-color: #1877f2;\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn.google[_ngcontent-%COMP%]:hover {\\n  border-color: #ea4335;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn.facebook[_ngcontent-%COMP%]:hover {\\n  border-color: #1877f2;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--fleur-primary);\\n  text-decoration: none;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 12px 25px;\\n  background-color: white;\\n  color: #555;\\n  border-radius: 50px;\\n  font-weight: 500;\\n  text-decoration: none;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\\n  color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n@media (max-width: 576px) {\\n  .auth-container.login[_ngcontent-%COMP%] {\\n    padding: 20px 0;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%] {\\n    padding: 25px;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbG9naW4vbG9naW4uY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxpQkFBQTtFQUNBLDZEQUFBO0VBQ0EsZUFBQTtFQUNBLHFDQUFBO0FBQ0Y7QUFDRTtFQUNFLGlCQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7QUFDSjtBQUVFO0VBQ0UsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLDBDQUFBO0VBQ0EsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7QUFBSjtBQUVJO0VBQ0Usa0JBQUE7RUFDQSxtQkFBQTtBQUFOO0FBRU07RUFDRSxzQ0FBQTtFQUNBLGlCQUFBO0VBQ0EsV0FBQTtFQUNBLG1CQUFBO0FBQVI7QUFHTTtFQUNFLFdBQUE7RUFDQSxlQUFBO0VBQ0EsbUJBQUE7QUFEUjtBQUlNO0VBQ0UsV0FBQTtFQUNBLFdBQUE7RUFDQSxpRkFBQTtFQUNBLGNBQUE7RUFDQSxrQkFBQTtBQUZSO0FBT007RUFDRSxtQkFBQTtBQUxSO0FBT1E7RUFDRSxjQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtBQUxWO0FBUVE7RUFDRSxrQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtBQU5WO0FBUVU7RUFDRSxrQkFBQTtFQUNBLFVBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7QUFOWjtBQVNVO0VBQ0UsV0FBQTtFQUNBLDRCQUFBO0VBQ0Esc0JBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtBQVBaO0FBU1k7RUFDRSxhQUFBO0VBQ0Esa0NBQUE7RUFDQSw2Q0FBQTtBQVBkO0FBV1U7RUFDRSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0VBQ0EseUJBQUE7QUFUWjtBQVdZO0VBQ0UsMkJBQUE7QUFUZDtBQWNRO0VBQ0UsaUJBQUE7RUFDQSxjQUFBO0VBQ0EsZUFBQTtBQVpWO0FBY1U7RUFDRSxjQUFBO0VBQ0Esa0JBQUE7QUFaWjtBQWlCTTtFQUNFLGFBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBQ0EsbUJBQUE7QUFmUjtBQWlCUTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtBQWZWO0FBaUJVO0VBQ0UsaUJBQUE7QUFmWjtBQWtCVTtFQUNFLGlCQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7QUFoQlo7QUFvQlE7RUFDRSxpQkFBQTtFQUNBLDJCQUFBO0VBQ0EscUJBQUE7RUFDQSx5QkFBQTtBQWxCVjtBQW9CVTtFQUNFLDBCQUFBO0FBbEJaO0FBdUJNO0VBQ0UsbUJBQUE7QUFyQlI7QUF1QlE7RUFDRSxXQUFBO0VBQ0EsYUFBQTtFQUNBLGlGQUFBO0VBQ0EsWUFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtFQUNBLGVBQUE7RUFDQSx5QkFBQTtFQUNBLDhDQUFBO0FBckJWO0FBdUJVO0VBQ0UsMkJBQUE7RUFDQSw4Q0FBQTtBQXJCWjtBQXdCVTtFQUNFLFlBQUE7RUFDQSxtQkFBQTtBQXRCWjtBQTJCTTtFQUNFLG1CQUFBO0FBekJSO0FBMkJRO0VBQ0Usa0JBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxpQkFBQTtFQUNBLG1CQUFBO0FBekJWO0FBMkJVO0VBQ0UsV0FBQTtFQUNBLGtCQUFBO0VBQ0EsUUFBQTtFQUNBLFVBQUE7RUFDQSxXQUFBO0VBQ0Esc0JBQUE7QUF6Qlo7QUE0QlU7RUFDRSxPQUFBO0FBMUJaO0FBNkJVO0VBQ0UsUUFBQTtBQTNCWjtBQStCUTtFQUNFLGFBQUE7RUFDQSxTQUFBO0FBN0JWO0FBK0JVO0VBQ0UsT0FBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsU0FBQTtFQUNBLGFBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EseUJBQUE7RUFDQSxzQkFBQTtFQUNBLHVCQUFBO0FBN0JaO0FBK0JZO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUE3QmQ7QUFnQ1k7RUFDRSxXQUFBO0VBQ0EsWUFBQTtFQUNBLHlCQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxpQkFBQTtBQTlCZDtBQWlDWTtFQUNFLDJCQUFBO0VBQ0EsMENBQUE7QUEvQmQ7QUFrQ1k7RUFDRSxxQkFBQTtBQWhDZDtBQW1DWTtFQUNFLHFCQUFBO0FBakNkO0FBMkNNO0VBQ0Usa0JBQUE7RUFDQSxpQkFBQTtFQUNBLFdBQUE7QUF6Q1I7QUEyQ1E7RUFDRSwyQkFBQTtFQUNBLHFCQUFBO0VBQ0EsZ0JBQUE7RUFDQSx5QkFBQTtBQXpDVjtBQTJDVTtFQUNFLDBCQUFBO0FBekNaO0FBZ0RFO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0FBOUNKO0FBZ0RJO0VBQ0UsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsU0FBQTtFQUNBLGtCQUFBO0VBQ0EsdUJBQUE7RUFDQSxXQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtFQUNBLHFCQUFBO0VBQ0EsMENBQUE7RUFDQSx5QkFBQTtBQTlDTjtBQWdETTtFQUNFLDJCQUFBO0VBQ0EseUNBQUE7RUFDQSwyQkFBQTtBQTlDUjtBQWlETTtFQUNFLGlCQUFBO0FBL0NSOztBQXNEQTtFQUNFO0lBQ0UsZUFBQTtFQW5ERjtFQXFERTtJQUNFLGFBQUE7RUFuREo7RUFzRE07SUFDRSxpQkFBQTtFQXBEUjtFQXlETTtJQUNFLHNCQUFBO0lBQ0EsdUJBQUE7SUFDQSxTQUFBO0VBdkRSO0VBMkRRO0lBQ0Usc0JBQUE7RUF6RFY7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5hdXRoLWNvbnRhaW5lci5sb2dpbiB7XHJcbiAgbWluLWhlaWdodDogMTAwdmg7XHJcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y1ZjdmYSAwJSwgI2U0ZThmMCAxMDAlKTtcclxuICBwYWRkaW5nOiA0MHB4IDA7XHJcbiAgZm9udC1mYW1pbHk6ICdNb250c2VycmF0Jywgc2Fucy1zZXJpZjtcclxuXHJcbiAgLmNvbnRhaW5lciB7XHJcbiAgICBtYXgtd2lkdGg6IDEyMDBweDtcclxuICAgIG1hcmdpbjogMCBhdXRvO1xyXG4gICAgcGFkZGluZzogMCAyMHB4O1xyXG4gIH1cclxuXHJcbiAgLmF1dGgtY2FyZCB7XHJcbiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDIwcHg7XHJcbiAgICBib3gtc2hhZG93OiAwIDE1cHggMzVweCByZ2JhKDAsIDAsIDAsIDAuMSk7XHJcbiAgICBtYXgtd2lkdGg6IDUwMHB4O1xyXG4gICAgbWFyZ2luOiAwIGF1dG8gNDBweDtcclxuICAgIHBhZGRpbmc6IDQwcHg7XHJcblxyXG4gICAgLmF1dGgtaGVhZGVyIHtcclxuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xyXG5cclxuICAgICAgLnRpdGxlIHtcclxuICAgICAgICBmb250LWZhbWlseTogJ1BsYXlmYWlyIERpc3BsYXknLCBzZXJpZjtcclxuICAgICAgICBmb250LXNpemU6IDIuMnJlbTtcclxuICAgICAgICBjb2xvcjogIzMzMztcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuc3VidGl0bGUge1xyXG4gICAgICAgIGNvbG9yOiAjNjY2O1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAxNXB4O1xyXG4gICAgICB9XHJcblxyXG4gICAgICAuZGl2aWRlciB7XHJcbiAgICAgICAgd2lkdGg6IDYwcHg7XHJcbiAgICAgICAgaGVpZ2h0OiAzcHg7XHJcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCB2YXIoLS1mbGV1ci1wcmltYXJ5KSwgdmFyKC0tYmlqb3UtcHJpbWFyeSkpO1xyXG4gICAgICAgIG1hcmdpbjogMCBhdXRvO1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDNweDtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5hdXRoLWZvcm0ge1xyXG4gICAgICAuZm9ybS1ncm91cCB7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuXHJcbiAgICAgICAgbGFiZWwge1xyXG4gICAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgICBmb250LXNpemU6IDAuOXJlbTtcclxuICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgICAgICBjb2xvcjogIzU1NTtcclxuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDhweDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5pbnB1dC1jb250YWluZXIge1xyXG4gICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcblxyXG4gICAgICAgICAgLmlucHV0LWljb24ge1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIGxlZnQ6IDE1cHg7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgICAgICAgICBjb2xvcjogI2FhYTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBpbnB1dCB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAxMnB4IDE1cHggMTJweCA0NXB4O1xyXG4gICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGRkO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMXJlbTtcclxuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAgICAgICAgICY6Zm9jdXMge1xyXG4gICAgICAgICAgICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiB2YXIoLS1mbGV1ci1wcmltYXJ5KTtcclxuICAgICAgICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAzcHggcmdiYSgxMzgsIDc5LCAyNTUsIDAuMSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAudG9nZ2xlLXBhc3N3b3JkIHtcclxuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICByaWdodDogMTVweDtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogbm9uZTtcclxuICAgICAgICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDEuMXJlbTtcclxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICBjb2xvcjogI2FhYTtcclxuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAgICAgICAgICY6aG92ZXIge1xyXG4gICAgICAgICAgICAgIGNvbG9yOiB2YXIoLS1mbGV1ci1wcmltYXJ5KTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLmVycm9yLW1lc3NhZ2Uge1xyXG4gICAgICAgICAgZm9udC1zaXplOiAwLjhyZW07XHJcbiAgICAgICAgICBjb2xvcjogI2U3NGMzYztcclxuICAgICAgICAgIG1hcmdpbi10b3A6IDVweDtcclxuXHJcbiAgICAgICAgICBzcGFuIHtcclxuICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDNweDtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5mb3JtLW9wdGlvbnMge1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMjVweDtcclxuXHJcbiAgICAgICAgLnJlbWVtYmVyLW1lIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG5cclxuICAgICAgICAgIGlucHV0W3R5cGU9XCJjaGVja2JveFwiXSB7XHJcbiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogOHB4O1xyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIGxhYmVsIHtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgICAgICAgIGNvbG9yOiAjNjY2O1xyXG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuZm9yZ290LXBhc3N3b3JkIHtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgICAgY29sb3I6IHZhcigtLWZsZXVyLXByaW1hcnkpO1xyXG4gICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuXHJcbiAgICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiB1bmRlcmxpbmU7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG4gICAgICAuZm9ybS1hY3Rpb25zIHtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xyXG5cclxuICAgICAgICAuc3VibWl0LWJ0biB7XHJcbiAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgIHBhZGRpbmc6IDE0cHg7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gcmlnaHQsIHZhcigtLWZsZXVyLXByaW1hcnkpLCB2YXIoLS1iaWpvdS1wcmltYXJ5KSk7XHJcbiAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICBib3JkZXI6IG5vbmU7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxuICAgICAgICAgIGJveC1zaGFkb3c6IDAgNXB4IDE1cHggcmdiYSgxMzgsIDc5LCAyNTUsIDAuMyk7XHJcblxyXG4gICAgICAgICAgJjpob3Zlcjpub3QoOmRpc2FibGVkKSB7XHJcbiAgICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtM3B4KTtcclxuICAgICAgICAgICAgYm94LXNoYWRvdzogMCA4cHggMjVweCByZ2JhKDEzOCwgNzksIDI1NSwgMC40KTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmOmRpc2FibGVkIHtcclxuICAgICAgICAgICAgb3BhY2l0eTogMC43O1xyXG4gICAgICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG5cclxuICAgICAgLnNvY2lhbC1sb2dpbiB7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDtcclxuXHJcbiAgICAgICAgLnNlcGFyYXRvciB7XHJcbiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgICAgICBjb2xvcjogIzk5OTtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuXHJcbiAgICAgICAgICAmOmJlZm9yZSwgJjphZnRlciB7XHJcbiAgICAgICAgICAgIGNvbnRlbnQ6ICcnO1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIHRvcDogNTAlO1xyXG4gICAgICAgICAgICB3aWR0aDogNDAlO1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDFweDtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2RkZDtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAmOmJlZm9yZSB7XHJcbiAgICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgJjphZnRlciB7XHJcbiAgICAgICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnNvY2lhbC1idXR0b25zIHtcclxuICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICBnYXA6IDE1cHg7XHJcblxyXG4gICAgICAgICAgLnNvY2lhbC1idG4ge1xyXG4gICAgICAgICAgICBmbGV4OiAxO1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgICAgICAgZ2FwOiAxMHB4O1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAxMnB4O1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45cmVtO1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcbiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xyXG5cclxuICAgICAgICAgICAgaW1nIHtcclxuICAgICAgICAgICAgICB3aWR0aDogMjBweDtcclxuICAgICAgICAgICAgICBoZWlnaHQ6IDIwcHg7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC5mYWNlYm9vay1pY29uIHtcclxuICAgICAgICAgICAgICB3aWR0aDogMjBweDtcclxuICAgICAgICAgICAgICBoZWlnaHQ6IDIwcHg7XHJcbiAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzE4NzdmMjtcclxuICAgICAgICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0zcHgpO1xyXG4gICAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgNXB4IDE1cHggcmdiYSgwLCAwLCAwLCAwLjA1KTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJi5nb29nbGU6aG92ZXIge1xyXG4gICAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2VhNDMzNTtcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgJi5mYWNlYm9vazpob3ZlciB7XHJcbiAgICAgICAgICAgICAgYm9yZGVyLWNvbG9yOiAjMTg3N2YyO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcblxyXG5cclxuXHJcblxyXG5cclxuICAgICAgLmF1dGgtZm9vdGVyIHtcclxuICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgZm9udC1zaXplOiAwLjlyZW07XHJcbiAgICAgICAgY29sb3I6ICM2NjY7XHJcblxyXG4gICAgICAgIGEge1xyXG4gICAgICAgICAgY29sb3I6IHZhcigtLWZsZXVyLXByaW1hcnkpO1xyXG4gICAgICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XHJcblxyXG4gICAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLm5hdmlnYXRpb24ge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG5cclxuICAgIC5iYWNrLWJ0biB7XHJcbiAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGdhcDogMTBweDtcclxuICAgICAgcGFkZGluZzogMTJweCAyNXB4O1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTtcclxuICAgICAgY29sb3I6ICM1NTU7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDUwcHg7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICAgICAgYm94LXNoYWRvdzogMCA1cHggMTVweCByZ2JhKDAsIDAsIDAsIDAuMDUpO1xyXG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xyXG5cclxuICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0zcHgpO1xyXG4gICAgICAgIGJveC1zaGFkb3c6IDAgOHB4IDIwcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xyXG4gICAgICAgIGNvbG9yOiB2YXIoLS1mbGV1ci1wcmltYXJ5KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLmljb24ge1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMS4xcmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vLyBNZWRpYSBxdWVyaWVzIHBvdXIgbGEgcmVzcG9uc2l2aXTDg8KpXHJcbkBtZWRpYSAobWF4LXdpZHRoOiA1NzZweCkge1xyXG4gIC5hdXRoLWNvbnRhaW5lci5sb2dpbiB7XHJcbiAgICBwYWRkaW5nOiAyMHB4IDA7XHJcblxyXG4gICAgLmF1dGgtY2FyZCB7XHJcbiAgICAgIHBhZGRpbmc6IDI1cHg7XHJcblxyXG4gICAgICAuYXV0aC1oZWFkZXIge1xyXG4gICAgICAgIC50aXRsZSB7XHJcbiAgICAgICAgICBmb250LXNpemU6IDEuOHJlbTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIC5hdXRoLWZvcm0ge1xyXG4gICAgICAgIC5mb3JtLW9wdGlvbnMge1xyXG4gICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xyXG4gICAgICAgICAgZ2FwOiAxNXB4O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnNvY2lhbC1sb2dpbiB7XHJcbiAgICAgICAgICAuc29jaWFsLWJ1dHRvbnMge1xyXG4gICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "LoginComponent_div_20_span_1_Template", "LoginComponent_div_20_span_2_Template", "ɵɵadvance", "ɵɵproperty", "_r1", "errors", "LoginComponent_div_31_span_1_Template", "LoginComponent_div_31_span_2_Template", "_r3", "ɵɵtextInterpolate1", "ctx_r5", "errorMessage", "LoginComponent", "constructor", "router", "route", "authService", "loginData", "email", "password", "rememberMe", "showPassword", "isLoading", "returnUrl", "snapshot", "queryParams", "togglePasswordVisibility", "onSubmit", "_this", "_asyncToGenerator", "result", "signIn", "success", "console", "log", "setTimeout", "userProfile", "getCurrentUserProfile", "navigateByUrl", "role", "navigate", "message", "error", "loginWithTestAccount", "_this2", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "AuthService", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_10_listener", "LoginComponent_Template_input_ngModelChange_18_listener", "$event", "LoginComponent_div_20_Template", "LoginComponent_Template_input_ngModelChange_27_listener", "LoginComponent_Template_button_click_29_listener", "LoginComponent_div_31_Template", "LoginComponent_Template_input_ngModelChange_34_listener", "LoginComponent_div_39_Template", "LoginComponent_span_42_Template", "LoginComponent_span_43_Template", "LoginComponent_Template_button_click_59_listener", "LoginComponent_Template_button_click_61_listener", "invalid", "dirty", "touched", "_r0"], "sources": ["E:\\aymen\\pfa\\pfa\\src\\app\\login\\login.component.ts", "E:\\aymen\\pfa\\pfa\\src\\app\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\n\ninterface LoginData {\n  email: string;\n  password: string;\n  rememberMe: boolean;\n}\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n})\nexport class LoginComponent {\n  loginData: LoginData = {\n    email: '',\n    password: '',\n    rememberMe: false\n  };\n\n  showPassword: boolean = false;\n  isLoading: boolean = false;\n  errorMessage: string = '';\n  returnUrl: string = '';\n\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute,\n    private authService: AuthService\n  ) {\n    // Récupérer l'URL de retour si elle existe\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '';\n  }\n\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  async onSubmit(): Promise<void> {\n    if (this.isLoading) return;\n\n    this.isLoading = true;\n    this.errorMessage = '';\n\n    try {\n      const result = await this.authService.signIn(this.loginData.email, this.loginData.password);\n\n      if (result.success) {\n        console.log('Connexion réussie !');\n\n        // Attendre que le profil utilisateur soit chargé\n        setTimeout(() => {\n          const userProfile = this.authService.getCurrentUserProfile();\n\n          if (this.returnUrl) {\n            // Rediriger vers l'URL de retour\n            this.router.navigateByUrl(this.returnUrl);\n          } else {\n            // Rediriger selon le rôle\n            if (userProfile?.role === 'admin') {\n              this.router.navigate(['/dashboard']);\n            } else {\n              this.router.navigate(['/personality-test']);\n            }\n          }\n        }, 500);\n      } else {\n        this.errorMessage = result.message;\n      }\n    } catch (error) {\n      console.error('Erreur lors de la connexion:', error);\n      this.errorMessage = 'Une erreur est survenue lors de la connexion.';\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  /**\n   * Connexion avec un compte de test (pour le développement)\n   */\n  async loginWithTestAccount(email: string, password: string): Promise<void> {\n    this.loginData.email = email;\n    this.loginData.password = password;\n    await this.onSubmit();\n  }\n}\n", "<div class=\"auth-container login\">\n  <div class=\"container\">\n    <div class=\"auth-card\">\n      <div class=\"auth-header\">\n        <h1 class=\"title\">Connexion</h1>\n        <p class=\"subtitle\">Accédez à votre compte IrisLock</p>\n        <div class=\"divider\"></div>\n      </div>\n\n      <div class=\"auth-form\">\n        <form (ngSubmit)=\"onSubmit()\" #loginForm=\"ngForm\">\n          <div class=\"form-group\">\n            <label for=\"email\">Email</label>\n            <div class=\"input-container\">\n              <span class=\"input-icon\">✉️</span>\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                [(ngModel)]=\"loginData.email\"\n                required\n                email\n                #email=\"ngModel\"\n                placeholder=\"Votre adresse email\"\n              >\n            </div>\n            <div class=\"error-message\" *ngIf=\"email.invalid && (email.dirty || email.touched)\">\n              <span *ngIf=\"email.errors?.['required']\">L'email est requis</span>\n              <span *ngIf=\"email.errors?.['email']\">Veuillez entrer un email valide</span>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"password\">Mot de passe</label>\n            <div class=\"input-container\">\n              <span class=\"input-icon\">🔒</span>\n              <input\n                [type]=\"showPassword ? 'text' : 'password'\"\n                id=\"password\"\n                name=\"password\"\n                [(ngModel)]=\"loginData.password\"\n                required\n                minlength=\"6\"\n                #password=\"ngModel\"\n                placeholder=\"Votre mot de passe\"\n              >\n              <button\n                type=\"button\"\n                class=\"toggle-password\"\n                (click)=\"togglePasswordVisibility()\"\n              >\n                {{ showPassword ? '👁️' : '👁️‍🗨️' }}\n              </button>\n            </div>\n            <div class=\"error-message\" *ngIf=\"password.invalid && (password.dirty || password.touched)\">\n              <span *ngIf=\"password.errors?.['required']\">Le mot de passe est requis</span>\n              <span *ngIf=\"password.errors?.['minlength']\">Le mot de passe doit contenir au moins 6 caractères</span>\n            </div>\n          </div>\n\n          <div class=\"form-options\">\n            <div class=\"remember-me\">\n              <input\n                type=\"checkbox\"\n                id=\"remember\"\n                name=\"remember\"\n                [(ngModel)]=\"loginData.rememberMe\"\n              >\n              <label for=\"remember\">Se souvenir de moi</label>\n            </div>\n            <a href=\"#\" class=\"forgot-password\">Mot de passe oublié?</a>\n          </div>\n\n\n\n          <!-- Message d'erreur global -->\n          <div class=\"error-message global-error\" *ngIf=\"errorMessage\">\n            {{ errorMessage }}\n          </div>\n\n          <div class=\"form-actions\">\n            <button\n              type=\"submit\"\n              class=\"submit-btn\"\n              [disabled]=\"loginForm.invalid || isLoading\"\n            >\n              <span *ngIf=\"!isLoading\">Se connecter</span>\n              <span *ngIf=\"isLoading\">Connexion en cours...</span>\n            </button>\n          </div>\n        </form>\n\n        <div class=\"social-login\">\n          <p class=\"separator\">Ou connectez-vous avec</p>\n          <div class=\"social-buttons\">\n            <button class=\"social-btn google\">\n              <img src=\"assets/google-icon.png\" alt=\"Google\">\n              Google\n            </button>\n            <button class=\"social-btn facebook\">\n              <span class=\"facebook-icon\">f</span>\n              Facebook\n            </button>\n          </div>\n        </div>\n\n        <!-- Comptes de test pour le développement -->\n        <div class=\"test-accounts\" style=\"margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;\">\n          <h4 style=\"margin: 0 0 1rem 0; color: #6c757d; font-size: 0.9rem;\">Comptes de test (développement)</h4>\n          <div style=\"display: flex; gap: 1rem; flex-wrap: wrap;\">\n            <button\n              type=\"button\"\n              class=\"test-btn\"\n              (click)=\"loginWithTestAccount('<EMAIL>', 'admin123')\"\n              style=\"padding: 0.5rem 1rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;\"\n            >\n              Admin Test\n            </button>\n            <button\n              type=\"button\"\n              class=\"test-btn\"\n              (click)=\"loginWithTestAccount('<EMAIL>', 'user123')\"\n              style=\"padding: 0.5rem 1rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;\"\n            >\n              User Test\n            </button>\n          </div>\n        </div>\n\n        <div class=\"auth-footer\">\n          <p>Vous n'avez pas de compte? <a routerLink=\"/signup\">Inscrivez-vous</a></p>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"navigation\">\n      <a routerLink=\"/accueil\" class=\"back-btn\">\n        <span class=\"icon\">←</span>\n        <span>Retour à l'accueil</span>\n      </a>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;IC2BcA,EAAA,CAAAC,cAAA,WAAyC;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAClEH,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF9EH,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAI,UAAA,IAAAC,qCAAA,mBAAkE;IAClEL,EAAA,CAAAI,UAAA,IAAAE,qCAAA,mBAA4E;IAC9EN,EAAA,CAAAG,YAAA,EAAM;;;;;IAFGH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAgC;IAChCV,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,UAA6B;;;;;IA2BpCV,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7EH,EAAA,CAAAC,cAAA,WAA6C;IAAAD,EAAA,CAAAE,MAAA,+DAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFzGH,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAI,UAAA,IAAAO,qCAAA,mBAA6E;IAC7EX,EAAA,CAAAI,UAAA,IAAAQ,qCAAA,mBAAuG;IACzGZ,EAAA,CAAAG,YAAA,EAAM;;;;;IAFGH,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAAH,MAAA,kBAAAG,GAAA,CAAAH,MAAA,aAAmC;IACnCV,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAAH,MAAA,kBAAAG,GAAA,CAAAH,MAAA,cAAoC;;;;;IAoB/CV,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAC,MAAA,CAAAC,YAAA,MACF;;;;;IAQIhB,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5CH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADxElE,OAAM,MAAOc,cAAc;EAYzBC,YACUC,MAAc,EACdC,KAAqB,EACrBC,WAAwB;IAFxB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IAdrB,KAAAC,SAAS,GAAc;MACrBC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;KACb;IAED,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAX,YAAY,GAAW,EAAE;IACzB,KAAAY,SAAS,GAAW,EAAE;IAOpB;IACA,IAAI,CAACA,SAAS,GAAG,IAAI,CAACR,KAAK,CAACS,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE;EACrE;EAEAC,wBAAwBA,CAAA;IACtB,IAAI,CAACL,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEMM,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,IAAID,KAAI,CAACN,SAAS,EAAE;MAEpBM,KAAI,CAACN,SAAS,GAAG,IAAI;MACrBM,KAAI,CAACjB,YAAY,GAAG,EAAE;MAEtB,IAAI;QACF,MAAMmB,MAAM,SAASF,KAAI,CAACZ,WAAW,CAACe,MAAM,CAACH,KAAI,CAACX,SAAS,CAACC,KAAK,EAAEU,KAAI,CAACX,SAAS,CAACE,QAAQ,CAAC;QAE3F,IAAIW,MAAM,CAACE,OAAO,EAAE;UAClBC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;UAElC;UACAC,UAAU,CAAC,MAAK;YACd,MAAMC,WAAW,GAAGR,KAAI,CAACZ,WAAW,CAACqB,qBAAqB,EAAE;YAE5D,IAAIT,KAAI,CAACL,SAAS,EAAE;cAClB;cACAK,KAAI,CAACd,MAAM,CAACwB,aAAa,CAACV,KAAI,CAACL,SAAS,CAAC;aAC1C,MAAM;cACL;cACA,IAAIa,WAAW,EAAEG,IAAI,KAAK,OAAO,EAAE;gBACjCX,KAAI,CAACd,MAAM,CAAC0B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;eACrC,MAAM;gBACLZ,KAAI,CAACd,MAAM,CAAC0B,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;;;UAGjD,CAAC,EAAE,GAAG,CAAC;SACR,MAAM;UACLZ,KAAI,CAACjB,YAAY,GAAGmB,MAAM,CAACW,OAAO;;OAErC,CAAC,OAAOC,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDd,KAAI,CAACjB,YAAY,GAAG,+CAA+C;OACpE,SAAS;QACRiB,KAAI,CAACN,SAAS,GAAG,KAAK;;IACvB;EACH;EAEA;;;EAGMqB,oBAAoBA,CAACzB,KAAa,EAAEC,QAAgB;IAAA,IAAAyB,MAAA;IAAA,OAAAf,iBAAA;MACxDe,MAAI,CAAC3B,SAAS,CAACC,KAAK,GAAGA,KAAK;MAC5B0B,MAAI,CAAC3B,SAAS,CAACE,QAAQ,GAAGA,QAAQ;MAClC,MAAMyB,MAAI,CAACjB,QAAQ,EAAE;IAAC;EACxB;;;uBAvEWf,cAAc,EAAAjB,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAApD,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAArD,EAAA,CAAAkD,iBAAA,CAAAI,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAdtC,cAAc;MAAAuC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf3B9D,EAAA,CAAAC,cAAA,aAAkC;UAIRD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,WAAoB;UAAAD,EAAA,CAAAE,MAAA,gDAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvDH,EAAA,CAAAgE,SAAA,aAA2B;UAC7BhE,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAAuB;UACfD,EAAA,CAAAiE,UAAA,sBAAAC,kDAAA;YAAA,OAAYH,GAAA,CAAA/B,QAAA,EAAU;UAAA,EAAC;UAC3BhC,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,eAA6B;UACFD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,qBASC;UALCD,EAAA,CAAAiE,UAAA,2BAAAE,wDAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAzC,SAAA,CAAAC,KAAA,GAAA6C,MAAA;UAAA,EAA6B;UAJ/BpE,EAAA,CAAAG,YAAA,EASC;UAEHH,EAAA,CAAAI,UAAA,KAAAiE,8BAAA,kBAGM;UACRrE,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,eAA6B;UACFD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,qBASC;UALCD,EAAA,CAAAiE,UAAA,2BAAAK,wDAAAF,MAAA;YAAA,OAAAL,GAAA,CAAAzC,SAAA,CAAAE,QAAA,GAAA4C,MAAA;UAAA,EAAgC;UAJlCpE,EAAA,CAAAG,YAAA,EASC;UACDH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAiE,UAAA,mBAAAM,iDAAA;YAAA,OAASR,GAAA,CAAAhC,wBAAA,EAA0B;UAAA,EAAC;UAEpC/B,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAI,UAAA,KAAAoE,8BAAA,kBAGM;UACRxE,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA0B;UAMpBD,EAAA,CAAAiE,UAAA,2BAAAQ,wDAAAL,MAAA;YAAA,OAAAL,GAAA,CAAAzC,SAAA,CAAAG,UAAA,GAAA2C,MAAA;UAAA,EAAkC;UAJpCpE,EAAA,CAAAG,YAAA,EAKC;UACDH,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAElDH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,iCAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAM9DH,EAAA,CAAAI,UAAA,KAAAsE,8BAAA,kBAEM;UAEN1E,EAAA,CAAAC,cAAA,eAA0B;UAMtBD,EAAA,CAAAI,UAAA,KAAAuE,+BAAA,mBAA4C;UAC5C3E,EAAA,CAAAI,UAAA,KAAAwE,+BAAA,mBAAoD;UACtD5E,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA0B;UACHD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC/CH,EAAA,CAAAC,cAAA,eAA4B;UAExBD,EAAA,CAAAgE,SAAA,eAA+C;UAC/ChE,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAoC;UACND,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpCH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,eAAwI;UACnED,EAAA,CAAAE,MAAA,4CAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvGH,EAAA,CAAAC,cAAA,eAAwD;UAIpDD,EAAA,CAAAiE,UAAA,mBAAAY,iDAAA;YAAA,OAASd,GAAA,CAAAf,oBAAA,CAAqB,gBAAgB,EAAE,UAAU,CAAC;UAAA,EAAC;UAG5DhD,EAAA,CAAAE,MAAA,oBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAKC;UAFCD,EAAA,CAAAiE,UAAA,mBAAAa,iDAAA;YAAA,OAASf,GAAA,CAAAf,oBAAA,CAAqB,eAAe,EAAE,SAAS,CAAC;UAAA,EAAC;UAG1DhD,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAAyB;UACpBD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAC,cAAA,aAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK9EH,EAAA,CAAAC,cAAA,eAAwB;UAEDD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,+BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;UAvHvBH,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAQ,UAAA,YAAAuD,GAAA,CAAAzC,SAAA,CAAAC,KAAA,CAA6B;UAOLvB,EAAA,CAAAO,SAAA,GAAqD;UAArDP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAsE,OAAA,KAAAtE,GAAA,CAAAuE,KAAA,IAAAvE,GAAA,CAAAwE,OAAA,EAAqD;UAW7EjF,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,UAAA,SAAAuD,GAAA,CAAArC,YAAA,uBAA2C,YAAAqC,GAAA,CAAAzC,SAAA,CAAAE,QAAA;UAc3CxB,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAc,kBAAA,MAAAiD,GAAA,CAAArC,YAAA,4EACF;UAE0B1B,EAAA,CAAAO,SAAA,GAA8D;UAA9DP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAAkE,OAAA,KAAAlE,GAAA,CAAAmE,KAAA,IAAAnE,GAAA,CAAAoE,OAAA,EAA8D;UAYtFjF,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,YAAAuD,GAAA,CAAAzC,SAAA,CAAAG,UAAA,CAAkC;UAUCzB,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAAQ,UAAA,SAAAuD,GAAA,CAAA/C,YAAA,CAAkB;UAQvDhB,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,UAAA,aAAA0E,GAAA,CAAAH,OAAA,IAAAhB,GAAA,CAAApC,SAAA,CAA2C;UAEpC3B,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAQ,UAAA,UAAAuD,GAAA,CAAApC,SAAA,CAAgB;UAChB3B,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAAuD,GAAA,CAAApC,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}