{"ast": null, "code": "// Modèles pour le test de personnalité\nexport const PERSONALITY_DESCRIPTIONS = {\n  'Flower': 'Vous êtes une personne émotionnelle, créative et empathique. Vous vous laissez guider par vos sentiments et appréciez les environnements artistiques.',\n  'Jewel': 'Vous êtes structuré, analytique et méthodique. Vous préférez la logique à la spontanéité et aimez planifier vos actions.',\n  'Shaker': 'Vous êtes dynamique, aventurier et spontané. Vous aimez l\\'action, la nouveauté et vous vous lassez facilement de la routine.',\n  'Stream': 'Vous êtes paisible, réfléchi et diplomate. Vous préférez la tranquillité aux conflits et prenez le temps avant d\\'agir.',\n  'Flower-Jewel': 'Vous combinez sensibilité et organisation. Vous êtes créatif mais aimez suivre une méthode structurée.',\n  'Jewel-Shaker': 'Vous alliez méthode et dynamisme. Vous êtes à la fois analytique et énergique, agissant vite mais de façon calculée.',\n  'Shaker-Stream': 'Vous équilibrez action et réflexion. Vous êtes dynamique sans être impulsif, adaptable et calme.',\n  'Stream-Flower': 'Vous mélangez paix et émotion. Vous êtes empathique mais discret, intuitif mais paisible.'\n};\n// Questions du test psychotechnique\nexport const PERSONALITY_QUESTIONS = [\n// Questions Flower (1-4)\n{\n  id: 1,\n  question: \"Vous vous laissez guider par vos émotions ?\",\n  expectedAnswer: true,\n  classes: ['Flower']\n}, {\n  id: 2,\n  question: \"Vous aimez aider les autres spontanément ?\",\n  expectedAnswer: true,\n  classes: ['Flower']\n}, {\n  id: 3,\n  question: \"Vous êtes souvent dans l'imaginaire ?\",\n  expectedAnswer: true,\n  classes: ['Flower']\n}, {\n  id: 4,\n  question: \"Vous appréciez les environnements créatifs ?\",\n  expectedAnswer: true,\n  classes: ['Flower']\n},\n// Questions Jewel (5-8)\n{\n  id: 5,\n  question: \"Vous êtes très structuré dans votre quotidien ?\",\n  expectedAnswer: true,\n  classes: ['Jewel']\n}, {\n  id: 6,\n  question: \"Vous analysez beaucoup avant d'agir ?\",\n  expectedAnswer: true,\n  classes: ['Jewel']\n}, {\n  id: 7,\n  question: \"Vous aimez les listes, les tableaux, les plans ?\",\n  expectedAnswer: true,\n  classes: ['Jewel']\n}, {\n  id: 8,\n  question: \"Vous êtes plus logique que spontané ?\",\n  expectedAnswer: true,\n  classes: ['Jewel']\n},\n// Questions Shaker (9-12)\n{\n  id: 9,\n  question: \"Vous aimez l'aventure et la nouveauté ?\",\n  expectedAnswer: true,\n  classes: ['Shaker']\n}, {\n  id: 10,\n  question: \"Vous agissez souvent sans réfléchir longtemps ?\",\n  expectedAnswer: true,\n  classes: ['Shaker']\n}, {\n  id: 11,\n  question: \"Vous aimez être dans l'action rapide ?\",\n  expectedAnswer: true,\n  classes: ['Shaker']\n}, {\n  id: 12,\n  question: \"Vous vous lassez vite de la routine ?\",\n  expectedAnswer: true,\n  classes: ['Shaker']\n},\n// Questions Stream (13-16)\n{\n  id: 13,\n  question: \"Vous préférez la paix aux conflits ?\",\n  expectedAnswer: true,\n  classes: ['Stream']\n}, {\n  id: 14,\n  question: \"Vous prenez le temps avant de donner une réponse ?\",\n  expectedAnswer: true,\n  classes: ['Stream']\n}, {\n  id: 15,\n  question: \"Vous avez besoin d'un environnement calme ?\",\n  expectedAnswer: true,\n  classes: ['Stream']\n}, {\n  id: 16,\n  question: \"Vous évitez les confrontations ?\",\n  expectedAnswer: true,\n  classes: ['Stream']\n},\n// Questions intermédiaires Flower-Jewel (17-19)\n{\n  id: 17,\n  question: \"Vous êtes sensible et organisé ?\",\n  expectedAnswer: true,\n  classes: ['Flower-Jewel']\n}, {\n  id: 18,\n  question: \"Vous êtes créatif mais aimez suivre une méthode ?\",\n  expectedAnswer: true,\n  classes: ['Flower-Jewel']\n}, {\n  id: 19,\n  question: \"Vous ressentez le besoin d'exprimer vos émotions dans un cadre structuré ?\",\n  expectedAnswer: true,\n  classes: ['Flower-Jewel']\n},\n// Questions intermédiaires Jewel-Shaker (20-22)\n{\n  id: 20,\n  question: \"Vous aimez résoudre des problèmes en étant rapide ?\",\n  expectedAnswer: true,\n  classes: ['Jewel-Shaker']\n}, {\n  id: 21,\n  question: \"Vous êtes à la fois méthodique et énergique ?\",\n  expectedAnswer: true,\n  classes: ['Jewel-Shaker']\n}, {\n  id: 22,\n  question: \"Vous agissez vite mais de façon calculée ?\",\n  expectedAnswer: true,\n  classes: ['Jewel-Shaker']\n},\n// Questions intermédiaires Shaker-Stream (23-25)\n{\n  id: 23,\n  question: \"Vous aimez improviser tout en restant adaptable ?\",\n  expectedAnswer: true,\n  classes: ['Shaker-Stream']\n}, {\n  id: 24,\n  question: \"Vous êtes dynamique sans être trop impulsif ?\",\n  expectedAnswer: true,\n  classes: ['Shaker-Stream']\n}, {\n  id: 25,\n  question: \"Vous aimez passer à l'action quand l'ambiance est calme ?\",\n  expectedAnswer: true,\n  classes: ['Shaker-Stream']\n},\n// Questions intermédiaires Stream-Flower (26-28)\n{\n  id: 26,\n  question: \"Vous êtes émotif(ve) mais discret(ète) ?\",\n  expectedAnswer: true,\n  classes: ['Stream-Flower']\n}, {\n  id: 27,\n  question: \"Vous ressentez beaucoup sans forcément l'exprimer ?\",\n  expectedAnswer: true,\n  classes: ['Stream-Flower']\n}, {\n  id: 28,\n  question: \"Vous êtes paisible mais intuitif(ve) ?\",\n  expectedAnswer: true,\n  classes: ['Stream-Flower']\n},\n// Questions mixtes (29-32)\n{\n  id: 29,\n  question: \"Vous vous retrouvez autant dans la logique que dans la sensibilité ?\",\n  expectedAnswer: true,\n  classes: ['Flower-Jewel']\n}, {\n  id: 30,\n  question: \"Vous êtes réactif(ve) mais posé(e) ?\",\n  expectedAnswer: true,\n  classes: ['Shaker-Stream']\n}, {\n  id: 31,\n  question: \"Vous êtes réfléchi(é) tout en appréciant la nouveauté ?\",\n  expectedAnswer: true,\n  classes: ['Jewel-Shaker']\n}, {\n  id: 32,\n  question: \"Vous êtes calme, empathique, et adaptable ?\",\n  expectedAnswer: true,\n  classes: ['Stream-Flower']\n}];", "map": {"version": 3, "names": ["PERSONALITY_DESCRIPTIONS", "PERSONALITY_QUESTIONS", "id", "question", "expectedAnswer", "classes"], "sources": ["E:\\aymen\\pfa\\pfa\\src\\app\\models\\personality-test.model.ts"], "sourcesContent": ["// Modèles pour le test de personnalité\n\nexport interface Question {\n  id: number;\n  question: string;\n  expectedAnswer: boolean;\n  classes: PersonalityClass[];\n  weight?: number; // Poids de la question (par défaut 1)\n}\n\nexport interface UserResponse {\n  questionId: number;\n  answer: boolean;\n  timestamp: Date;\n}\n\nexport interface TestSession {\n  id?: string;\n  userId?: string;\n  userName?: string;\n  userEmail?: string;\n  responses: UserResponse[];\n  scores: PersonalityScores;\n  finalProfile: PersonalityProfile;\n  completedAt: Date;\n  startedAt: Date;\n}\n\nexport interface PersonalityScores {\n  flower: number;\n  jewel: number;\n  shaker: number;\n  stream: number;\n  // Classes intermédiaires\n  flowerJewel: number;\n  jewelShaker: number;\n  shakerStream: number;\n  streamFlower: number;\n}\n\nexport interface PersonalityProfile {\n  primaryClass: PersonalityClass;\n  secondaryClass?: PersonalityClass;\n  isIntermediate: boolean;\n  confidenceScore: number;\n  description: string;\n}\n\nexport type PersonalityClass = \n  | 'Flower' \n  | 'Jewel' \n  | 'Shaker' \n  | 'Stream' \n  | 'Flower-Jewel' \n  | 'Jewel-Shaker' \n  | 'Shaker-Stream' \n  | 'Stream-Flower';\n\nexport const PERSONALITY_DESCRIPTIONS: Record<PersonalityClass, string> = {\n  'Flower': 'Vous êtes une personne émotionnelle, créative et empathique. Vous vous laissez guider par vos sentiments et appréciez les environnements artistiques.',\n  'Jewel': 'Vous êtes structuré, analytique et méthodique. Vous préférez la logique à la spontanéité et aimez planifier vos actions.',\n  'Shaker': 'Vous êtes dynamique, aventurier et spontané. Vous aimez l\\'action, la nouveauté et vous vous lassez facilement de la routine.',\n  'Stream': 'Vous êtes paisible, réfléchi et diplomate. Vous préférez la tranquillité aux conflits et prenez le temps avant d\\'agir.',\n  'Flower-Jewel': 'Vous combinez sensibilité et organisation. Vous êtes créatif mais aimez suivre une méthode structurée.',\n  'Jewel-Shaker': 'Vous alliez méthode et dynamisme. Vous êtes à la fois analytique et énergique, agissant vite mais de façon calculée.',\n  'Shaker-Stream': 'Vous équilibrez action et réflexion. Vous êtes dynamique sans être impulsif, adaptable et calme.',\n  'Stream-Flower': 'Vous mélangez paix et émotion. Vous êtes empathique mais discret, intuitif mais paisible.'\n};\n\n// Questions du test psychotechnique\nexport const PERSONALITY_QUESTIONS: Question[] = [\n  // Questions Flower (1-4)\n  { id: 1, question: \"Vous vous laissez guider par vos émotions ?\", expectedAnswer: true, classes: ['Flower'] },\n  { id: 2, question: \"Vous aimez aider les autres spontanément ?\", expectedAnswer: true, classes: ['Flower'] },\n  { id: 3, question: \"Vous êtes souvent dans l'imaginaire ?\", expectedAnswer: true, classes: ['Flower'] },\n  { id: 4, question: \"Vous appréciez les environnements créatifs ?\", expectedAnswer: true, classes: ['Flower'] },\n  \n  // Questions Jewel (5-8)\n  { id: 5, question: \"Vous êtes très structuré dans votre quotidien ?\", expectedAnswer: true, classes: ['Jewel'] },\n  { id: 6, question: \"Vous analysez beaucoup avant d'agir ?\", expectedAnswer: true, classes: ['Jewel'] },\n  { id: 7, question: \"Vous aimez les listes, les tableaux, les plans ?\", expectedAnswer: true, classes: ['Jewel'] },\n  { id: 8, question: \"Vous êtes plus logique que spontané ?\", expectedAnswer: true, classes: ['Jewel'] },\n  \n  // Questions Shaker (9-12)\n  { id: 9, question: \"Vous aimez l'aventure et la nouveauté ?\", expectedAnswer: true, classes: ['Shaker'] },\n  { id: 10, question: \"Vous agissez souvent sans réfléchir longtemps ?\", expectedAnswer: true, classes: ['Shaker'] },\n  { id: 11, question: \"Vous aimez être dans l'action rapide ?\", expectedAnswer: true, classes: ['Shaker'] },\n  { id: 12, question: \"Vous vous lassez vite de la routine ?\", expectedAnswer: true, classes: ['Shaker'] },\n  \n  // Questions Stream (13-16)\n  { id: 13, question: \"Vous préférez la paix aux conflits ?\", expectedAnswer: true, classes: ['Stream'] },\n  { id: 14, question: \"Vous prenez le temps avant de donner une réponse ?\", expectedAnswer: true, classes: ['Stream'] },\n  { id: 15, question: \"Vous avez besoin d'un environnement calme ?\", expectedAnswer: true, classes: ['Stream'] },\n  { id: 16, question: \"Vous évitez les confrontations ?\", expectedAnswer: true, classes: ['Stream'] },\n  \n  // Questions intermédiaires Flower-Jewel (17-19)\n  { id: 17, question: \"Vous êtes sensible et organisé ?\", expectedAnswer: true, classes: ['Flower-Jewel'] },\n  { id: 18, question: \"Vous êtes créatif mais aimez suivre une méthode ?\", expectedAnswer: true, classes: ['Flower-Jewel'] },\n  { id: 19, question: \"Vous ressentez le besoin d'exprimer vos émotions dans un cadre structuré ?\", expectedAnswer: true, classes: ['Flower-Jewel'] },\n  \n  // Questions intermédiaires Jewel-Shaker (20-22)\n  { id: 20, question: \"Vous aimez résoudre des problèmes en étant rapide ?\", expectedAnswer: true, classes: ['Jewel-Shaker'] },\n  { id: 21, question: \"Vous êtes à la fois méthodique et énergique ?\", expectedAnswer: true, classes: ['Jewel-Shaker'] },\n  { id: 22, question: \"Vous agissez vite mais de façon calculée ?\", expectedAnswer: true, classes: ['Jewel-Shaker'] },\n  \n  // Questions intermédiaires Shaker-Stream (23-25)\n  { id: 23, question: \"Vous aimez improviser tout en restant adaptable ?\", expectedAnswer: true, classes: ['Shaker-Stream'] },\n  { id: 24, question: \"Vous êtes dynamique sans être trop impulsif ?\", expectedAnswer: true, classes: ['Shaker-Stream'] },\n  { id: 25, question: \"Vous aimez passer à l'action quand l'ambiance est calme ?\", expectedAnswer: true, classes: ['Shaker-Stream'] },\n  \n  // Questions intermédiaires Stream-Flower (26-28)\n  { id: 26, question: \"Vous êtes émotif(ve) mais discret(ète) ?\", expectedAnswer: true, classes: ['Stream-Flower'] },\n  { id: 27, question: \"Vous ressentez beaucoup sans forcément l'exprimer ?\", expectedAnswer: true, classes: ['Stream-Flower'] },\n  { id: 28, question: \"Vous êtes paisible mais intuitif(ve) ?\", expectedAnswer: true, classes: ['Stream-Flower'] },\n  \n  // Questions mixtes (29-32)\n  { id: 29, question: \"Vous vous retrouvez autant dans la logique que dans la sensibilité ?\", expectedAnswer: true, classes: ['Flower-Jewel'] },\n  { id: 30, question: \"Vous êtes réactif(ve) mais posé(e) ?\", expectedAnswer: true, classes: ['Shaker-Stream'] },\n  { id: 31, question: \"Vous êtes réfléchi(é) tout en appréciant la nouveauté ?\", expectedAnswer: true, classes: ['Jewel-Shaker'] },\n  { id: 32, question: \"Vous êtes calme, empathique, et adaptable ?\", expectedAnswer: true, classes: ['Stream-Flower'] }\n];\n"], "mappings": "AAAA;AA0DA,OAAO,MAAMA,wBAAwB,GAAqC;EACxE,QAAQ,EAAE,uJAAuJ;EACjK,OAAO,EAAE,0HAA0H;EACnI,QAAQ,EAAE,+HAA+H;EACzI,QAAQ,EAAE,yHAAyH;EACnI,cAAc,EAAE,wGAAwG;EACxH,cAAc,EAAE,sHAAsH;EACtI,eAAe,EAAE,kGAAkG;EACnH,eAAe,EAAE;CAClB;AAED;AACA,OAAO,MAAMC,qBAAqB,GAAe;AAC/C;AACA;EAAEC,EAAE,EAAE,CAAC;EAAEC,QAAQ,EAAE,6CAA6C;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE,EAC7G;EAAEH,EAAE,EAAE,CAAC;EAAEC,QAAQ,EAAE,4CAA4C;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE,EAC5G;EAAEH,EAAE,EAAE,CAAC;EAAEC,QAAQ,EAAE,uCAAuC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE,EACvG;EAAEH,EAAE,EAAE,CAAC;EAAEC,QAAQ,EAAE,8CAA8C;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE;AAE9G;AACA;EAAEH,EAAE,EAAE,CAAC;EAAEC,QAAQ,EAAE,iDAAiD;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,OAAO;AAAC,CAAE,EAChH;EAAEH,EAAE,EAAE,CAAC;EAAEC,QAAQ,EAAE,uCAAuC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,OAAO;AAAC,CAAE,EACtG;EAAEH,EAAE,EAAE,CAAC;EAAEC,QAAQ,EAAE,kDAAkD;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,OAAO;AAAC,CAAE,EACjH;EAAEH,EAAE,EAAE,CAAC;EAAEC,QAAQ,EAAE,uCAAuC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,OAAO;AAAC,CAAE;AAEtG;AACA;EAAEH,EAAE,EAAE,CAAC;EAAEC,QAAQ,EAAE,yCAAyC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE,EACzG;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,iDAAiD;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE,EAClH;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,wCAAwC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE,EACzG;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,uCAAuC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE;AAExG;AACA;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,sCAAsC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE,EACvG;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,oDAAoD;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE,EACrH;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,6CAA6C;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE,EAC9G;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,kCAAkC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,QAAQ;AAAC,CAAE;AAEnG;AACA;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,kCAAkC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,cAAc;AAAC,CAAE,EACzG;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,mDAAmD;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,cAAc;AAAC,CAAE,EAC1H;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,4EAA4E;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,cAAc;AAAC,CAAE;AAEnJ;AACA;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,qDAAqD;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,cAAc;AAAC,CAAE,EAC5H;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,+CAA+C;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,cAAc;AAAC,CAAE,EACtH;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,4CAA4C;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,cAAc;AAAC,CAAE;AAEnH;AACA;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,mDAAmD;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,eAAe;AAAC,CAAE,EAC3H;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,+CAA+C;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,eAAe;AAAC,CAAE,EACvH;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,2DAA2D;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,eAAe;AAAC,CAAE;AAEnI;AACA;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,0CAA0C;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,eAAe;AAAC,CAAE,EAClH;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,qDAAqD;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,eAAe;AAAC,CAAE,EAC7H;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,wCAAwC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,eAAe;AAAC,CAAE;AAEhH;AACA;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,sEAAsE;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,cAAc;AAAC,CAAE,EAC7I;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,sCAAsC;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,eAAe;AAAC,CAAE,EAC9G;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,yDAAyD;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,cAAc;AAAC,CAAE,EAChI;EAAEH,EAAE,EAAE,EAAE;EAAEC,QAAQ,EAAE,6CAA6C;EAAEC,cAAc,EAAE,IAAI;EAAEC,OAAO,EAAE,CAAC,eAAe;AAAC,CAAE,CACtH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}