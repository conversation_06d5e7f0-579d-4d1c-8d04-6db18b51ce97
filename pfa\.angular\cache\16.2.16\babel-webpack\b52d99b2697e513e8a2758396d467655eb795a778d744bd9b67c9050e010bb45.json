{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FluxComponent {\n  static {\n    this.ɵfac = function FluxComponent_Factory(t) {\n      return new (t || FluxComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FluxComponent,\n      selectors: [[\"app-flux\"]],\n      decls: 26,\n      vars: 0,\n      consts: [[1, \"iris-details\"], [\"src\", \"assets/3.png\", \"alt\", \"Flux\", 1, \"iris-img\"], [1, \"iris-description\"], [1, \"iris-subtitle\"]],\n      template: function FluxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4, \"Flux - Le Sensitif\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 3);\n          i0.ɵɵtext(6, \"Type intuitif, physique et empathique\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"ul\")(8, \"li\");\n          i0.ɵɵtext(9, \"Type intuitif, physique et empathique par nature\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"li\");\n          i0.ɵɵtext(11, \"Int\\u00E8gre la vie via l\\u2019exp\\u00E9rience sensorielle et corporelle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"li\");\n          i0.ɵɵtext(13, \"Apprentissage kinesth\\u00E9sique : bouger, pratiquer, ressentir\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"li\");\n          i0.ɵɵtext(15, \"Calme, pos\\u00E9, attentionn\\u00E9, \\u00E9quilibre les autres\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"li\");\n          i0.ɵɵtext(17, \"Adapt\\u00E9 aux soins, sport, et services humains\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"li\");\n          i0.ɵɵtext(19, \"Communication physique : posture, gestes, toucher contr\\u00F4l\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"li\");\n          i0.ɵɵtext(21, \"Apporte stabilit\\u00E9, empathie et soutien\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"li\");\n          i0.ɵɵtext(23, \"Peut se sentir impuissant ou d\\u00E9bord\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"li\");\n          i0.ɵɵtext(25, \"Le\\u00E7on de vie : faire confiance, l\\u00E2cher prise et trouver sa mission\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\"@charset \\\"UTF-8\\\";\\n.iris-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  background: linear-gradient(to right, #e8fff8, #f1fff6);\\n  min-height: 100vh;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-img[_ngcontent-%COMP%] {\\n  width: 180px;\\n  height: 180px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\\n  margin-bottom: 25px;\\n  transition: transform 0.3s ease;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n  max-width: 700px;\\n  background: rgba(255, 255, 255, 0.8);\\n  padding: 20px;\\n  border-radius: 20px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  text-align: center;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: bold;\\n  color: #4a8e6e;\\n  margin-bottom: 10px;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   .iris-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #6a937a;\\n  margin-bottom: 20px;\\n  font-style: italic;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style-type: none;\\n  padding: 0;\\n  text-align: left;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  line-height: 1.7;\\n  color: #3d3d3d;\\n  margin-bottom: 10px;\\n  position: relative;\\n  padding-left: 20px;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before {\\n  content: \\\"\\u2022\\\";\\n  color: #4a8e6e;\\n  font-weight: bold;\\n  position: absolute;\\n  left: 0;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%] {\\n  margin-top: 25px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 10px 20px;\\n  background-color: #4a8e6e;\\n  color: white;\\n  text-decoration: none;\\n  border-radius: 30px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: #3a7e5e;\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n@media (min-width: 768px) {\\n  .iris-details[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    align-items: flex-start;\\n    gap: 30px;\\n  }\\n  .iris-details[_ngcontent-%COMP%]   .iris-img[_ngcontent-%COMP%] {\\n    width: 220px;\\n    height: 220px;\\n  }\\n  .iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n    text-align: left;\\n  }\\n  .iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%] {\\n    justify-content: flex-start;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FluxComponent", "selectors", "decls", "vars", "consts", "template", "FluxComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\flux\\flux.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\flux\\flux.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-flux',\n  templateUrl: './flux.component.html',\n  styleUrls: ['./flux.component.scss']\n})\nexport class FluxComponent {\n\n}\n", "<div class=\"iris-details\">\n    <img src=\"assets/3.png\" alt=\"Flux\" class=\"iris-img\" />\n    <div class=\"iris-description\">\n      <h2>Flux - Le Sensitif</h2>\n      <p class=\"iris-subtitle\">Type intuitif, physique et empathique</p>\n\n      <ul>\n        <li>Type intuitif, physique et empathique par nature</li>\n        <li>Intègre la vie via l’expérience sensorielle et corporelle</li>\n        <li>Apprentissage kinesthésique : bouger, pratiquer, ressentir</li>\n        <li><PERSON><PERSON>, posé, attentionné, équilibre les autres</li>\n        <li>Adapté aux soins, sport, et services humains</li>\n        <li>Communication physique : posture, gestes, toucher contrôlé</li>\n        <li>Apporte stabilité, empathie et soutien</li>\n        <li>Peut se sentir impuissant ou débordé</li>\n        <li>Leçon de vie : faire confiance, lâcher prise et trouver sa mission</li>\n      </ul>\n    </div>\n  </div>"], "mappings": ";AAOA,OAAM,MAAOA,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BE,EAAA,CAAAC,cAAA,aAA0B;UACtBD,EAAA,CAAAE,SAAA,aAAsD;UACtDF,EAAA,CAAAC,cAAA,aAA8B;UACxBD,EAAA,CAAAG,MAAA,yBAAkB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC3BJ,EAAA,CAAAC,cAAA,WAAyB;UAAAD,EAAA,CAAAG,MAAA,4CAAqC;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAElEJ,EAAA,CAAAC,cAAA,SAAI;UACED,EAAA,CAAAG,MAAA,uDAAgD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,gFAAyD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,uEAA0D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,qEAA8C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,yDAA4C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACrDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,4EAA0D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,mDAAsC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/CJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,sDAAoC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC7CJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,oFAAkE;UAAAH,EAAA,CAAAI,YAAA,EAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}