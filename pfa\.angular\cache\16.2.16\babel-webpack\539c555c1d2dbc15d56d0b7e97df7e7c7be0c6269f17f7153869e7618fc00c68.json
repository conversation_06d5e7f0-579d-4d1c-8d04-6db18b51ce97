{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class IrisFormComponent {\n  static {\n    this.ɵfac = function IrisFormComponent_Factory(t) {\n      return new (t || IrisFormComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IrisFormComponent,\n      selectors: [[\"app-iris-form\"]],\n      decls: 2,\n      vars: 0,\n      template: function IrisFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"iris-form works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["IrisFormComponent", "selectors", "decls", "vars", "template", "IrisFormComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris-form\\iris-form.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris-form\\iris-form.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-iris-form',\n  templateUrl: './iris-form.component.html',\n  styleUrls: ['./iris-form.component.scss']\n})\nexport class IrisFormComponent {\n\n}\n", "<p>iris-form works!</p>\n"], "mappings": ";AAOA,OAAM,MAAOA,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP9BE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,uBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}