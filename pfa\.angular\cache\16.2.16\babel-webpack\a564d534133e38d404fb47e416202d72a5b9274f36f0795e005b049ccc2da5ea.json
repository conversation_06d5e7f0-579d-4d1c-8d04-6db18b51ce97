{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../iris-form/iris-form.component\";\nexport class FluxComponent {\n  static {\n    this.ɵfac = function FluxComponent_Factory(t) {\n      return new (t || FluxComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FluxComponent,\n      selectors: [[\"app-flux\"]],\n      decls: 68,\n      vars: 0,\n      consts: [[1, \"iris-profile\", \"flux\"], [1, \"container\"], [1, \"header\"], [1, \"title\"], [1, \"subtitle\"], [1, \"content\"], [1, \"image-container\"], [\"src\", \"assets/3.png\", \"alt\", \"Flux\", 1, \"iris-image\"], [1, \"image-decoration\"], [1, \"description\"], [1, \"card\", \"traits-card\"], [1, \"traits-list\"], [1, \"icon\"], [1, \"text\"], [1, \"navigation\"], [\"routerLink\", \"/iris2\", 1, \"btn\", \"back-btn\"]],\n      template: function FluxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Flux - Le Sensitif\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Type intuitif, physique et empathique\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6);\n          i0.ɵɵelement(9, \"img\", 7)(10, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"h2\");\n          i0.ɵɵtext(14, \"Caract\\u00E9ristiques\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"ul\", 11)(16, \"li\")(17, \"span\", 12);\n          i0.ɵɵtext(18, \"\\uD83C\\uDF3F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 13);\n          i0.ɵɵtext(20, \"Type intuitif, physique et empathique par nature\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"li\")(22, \"span\", 12);\n          i0.ɵɵtext(23, \"\\uD83D\\uDC50\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 13);\n          i0.ɵɵtext(25, \"Int\\u00E8gre la vie via l'exp\\u00E9rience sensorielle et corporelle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"li\")(27, \"span\", 12);\n          i0.ɵɵtext(28, \"\\uD83C\\uDFC3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"span\", 13);\n          i0.ɵɵtext(30, \"Apprentissage kinesth\\u00E9sique : bouger, pratiquer, ressentir\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"li\")(32, \"span\", 12);\n          i0.ɵɵtext(33, \"\\uD83E\\uDDD8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"span\", 13);\n          i0.ɵɵtext(35, \"Calme, pos\\u00E9, attentionn\\u00E9, \\u00E9quilibre les autres\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"li\")(37, \"span\", 12);\n          i0.ɵɵtext(38, \"\\uD83E\\uDD32\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"span\", 13);\n          i0.ɵɵtext(40, \"Adapt\\u00E9 aux soins, sport, et services humains\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\")(42, \"span\", 12);\n          i0.ɵɵtext(43, \"\\uD83D\\uDC4B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 13);\n          i0.ɵɵtext(45, \"Communication physique : posture, gestes, toucher contr\\u00F4l\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"li\")(47, \"span\", 12);\n          i0.ɵɵtext(48, \"\\uD83C\\uDFD4\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 13);\n          i0.ɵɵtext(50, \"Apporte stabilit\\u00E9, empathie et soutien\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\")(52, \"span\", 12);\n          i0.ɵɵtext(53, \"\\uD83C\\uDF0A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"span\", 13);\n          i0.ɵɵtext(55, \"Peut se sentir impuissant ou d\\u00E9bord\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"li\")(57, \"span\", 12);\n          i0.ɵɵtext(58, \"\\uD83C\\uDF31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"span\", 13);\n          i0.ɵɵtext(60, \"Le\\u00E7on de vie : faire confiance, l\\u00E2cher prise et trouver sa mission\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(61, \"div\", 14)(62, \"a\", 15)(63, \"span\", 12);\n          i0.ɵɵtext(64, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"span\");\n          i0.ɵɵtext(66, \"Retour aux types d'iris\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(67, \"app-iris-form\");\n        }\n      },\n      dependencies: [i1.RouterLink, i2.IrisFormComponent],\n      styles: [\".iris-profile.flux[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: var(--flux-gradient);\\n  padding: 40px 0;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  color: var(--flux-primary);\\n  font-size: 2.5rem;\\n  margin-bottom: 10px;\\n  position: relative;\\n  display: inline-block;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 3px;\\n  background-color: var(--flux-secondary);\\n  border-radius: 3px;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.2rem;\\n  font-weight: 300;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  margin-top: 20px;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 30px;\\n  margin-bottom: 40px;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);\\n  border: 5px solid white;\\n  z-index: 2;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 15px 40px rgba(52, 152, 219, 0.4);\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 220px;\\n  height: 220px;\\n  border-radius: 50%;\\n  border: 2px dashed var(--flux-secondary);\\n  top: -10px;\\n  left: -10px;\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_rotate 20s linear infinite;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 800px;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: var(--flux-primary);\\n  font-size: 1.8rem;\\n  margin-bottom: 25px;\\n  text-align: center;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 15px;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 15px;\\n  border-radius: 8px;\\n  background-color: rgba(255, 255, 255, 0.7);\\n  transition: all 0.3s ease;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover {\\n  background-color: white;\\n  transform: translateX(5px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin-right: 15px;\\n  min-width: 30px;\\n  text-align: center;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #444;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 30px;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background-color: var(--flux-primary);\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 12px 25px;\\n  border-radius: 50px;\\n  font-weight: 500;\\n  box-shadow: 0 5px 15px rgba(79, 255, 138, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1cff68;\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 20px rgba(79, 255, 138, 0.4);\\n}\\n.iris-profile.flux[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (min-width: 768px) {\\n  .iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    align-items: flex-start;\\n  }\\n  .iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n    flex: 0 0 auto;\\n  }\\n  .iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n  .iris-profile.flux[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FluxComponent", "selectors", "decls", "vars", "consts", "template", "FluxComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\flux\\flux.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\flux\\flux.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-flux',\n  templateUrl: './flux.component.html',\n  styleUrls: ['./flux.component.scss']\n})\nexport class FluxComponent {\n\n}\n", "<div class=\"iris-profile flux\">\n  <div class=\"container\">\n    <div class=\"header\">\n      <h1 class=\"title\">Flux - Le Sensitif</h1>\n      <p class=\"subtitle\">Type intuitif, physique et empathique</p>\n    </div>\n\n    <div class=\"content\">\n      <div class=\"image-container\">\n        <img src=\"assets/3.png\" alt=\"Flux\" class=\"iris-image\" />\n        <div class=\"image-decoration\"></div>\n      </div>\n\n      <div class=\"description\">\n        <div class=\"card traits-card\">\n          <h2>Caractéristiques</h2>\n          <ul class=\"traits-list\">\n            <li>\n              <span class=\"icon\">🌿</span>\n              <span class=\"text\">Type intuitif, physique et empathique par nature</span>\n            </li>\n            <li>\n              <span class=\"icon\">👐</span>\n              <span class=\"text\">Intègre la vie via l'expérience sensorielle et corporelle</span>\n            </li>\n            <li>\n              <span class=\"icon\">🏃</span>\n              <span class=\"text\">Apprentissage kinesthésique : bouger, pratiquer, ressentir</span>\n            </li>\n            <li>\n              <span class=\"icon\">🧘</span>\n              <span class=\"text\">Calme, posé, attentionné, équilibre les autres</span>\n            </li>\n            <li>\n              <span class=\"icon\">🤲</span>\n              <span class=\"text\">Adapté aux soins, sport, et services humains</span>\n            </li>\n            <li>\n              <span class=\"icon\">👋</span>\n              <span class=\"text\">Communication physique : posture, gestes, toucher contrôlé</span>\n            </li>\n            <li>\n              <span class=\"icon\">🏔️</span>\n              <span class=\"text\">Apporte stabilité, empathie et soutien</span>\n            </li>\n            <li>\n              <span class=\"icon\">🌊</span>\n              <span class=\"text\">Peut se sentir impuissant ou débordé</span>\n            </li>\n            <li>\n              <span class=\"icon\">🌱</span>\n              <span class=\"text\">Leçon de vie : faire confiance, lâcher prise et trouver sa mission</span>\n            </li>\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"navigation\">\n      <a routerLink=\"/iris2\" class=\"btn back-btn\">\n        <span class=\"icon\">←</span>\n        <span>Retour aux types d'iris</span>\n      </a>\n    </div>\n  </div>\n</div>\n\n<!-- Formulaire d'analyse d'iris -->\n<app-iris-form></app-iris-form>\n"], "mappings": ";;;AAOA,OAAM,MAAOA,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BE,EAAA,CAAAC,cAAA,aAA+B;UAGPD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzCH,EAAA,CAAAC,cAAA,WAAoB;UAAAD,EAAA,CAAAE,MAAA,4CAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG/DH,EAAA,CAAAC,cAAA,aAAqB;UAEjBD,EAAA,CAAAI,SAAA,aAAwD;UAE1DJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAyB;UAEjBD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,cAAwB;UAEDD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,wDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5EH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,2EAAyD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAErFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,uEAA0D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEtFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,qEAA8C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1EH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,yDAA4C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAExEH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,4EAA0D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEtFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,0BAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,mDAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAElEH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,sDAAoC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEhEH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,oFAAkE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAOtGH,EAAA,CAAAC,cAAA,eAAwB;UAEDD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAO5CH,EAAA,CAAAI,SAAA,qBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}