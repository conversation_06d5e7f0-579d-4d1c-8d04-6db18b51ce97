{"ast": null, "code": "import { PERSONALITY_QUESTIONS } from '../models/personality-test.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/personality-test.service\";\nimport * as i2 from \"../services/iris-compatibility.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction PersonalityTestComponent_div_1_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_div_47_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.logout());\n    });\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Se d\\u00E9connecter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 14);\n    i0.ɵɵtext(5, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PersonalityTestComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"h1\", 8);\n    i0.ɵɵtext(4, \"Test de Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 9);\n    i0.ɵɵelementStart(6, \"p\", 10);\n    i0.ɵɵtext(7, \"D\\u00E9couvrez votre profil psychotechnique\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"div\", 12)(10, \"div\", 13)(11, \"span\", 14);\n    i0.ɵɵtext(12, \"\\uD83D\\uDCDD\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 15)(14, \"h3\");\n    i0.ɵɵtext(15, \"32 Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\");\n    i0.ɵɵtext(17, \"Questions cibl\\u00E9es pour analyser votre personnalit\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 13)(19, \"span\", 14);\n    i0.ɵɵtext(20, \"\\u23F1\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 15)(22, \"h3\");\n    i0.ɵɵtext(23, \"5-10 Minutes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\");\n    i0.ɵɵtext(25, \"Temps estim\\u00E9 pour compl\\u00E9ter le test\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 13)(27, \"span\", 14);\n    i0.ɵɵtext(28, \"\\uD83C\\uDFAF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 15)(30, \"h3\");\n    i0.ɵɵtext(31, \"4 Profils Principaux\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"Flower, Jewel, Shaker, Stream + profils interm\\u00E9diaires\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 16)(35, \"h3\");\n    i0.ɵɵtext(36, \"Informations du test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"p\")(38, \"strong\");\n    i0.ɵɵtext(39, \"Nom:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"p\")(42, \"strong\");\n    i0.ɵɵtext(43, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"p\", 17);\n    i0.ɵɵtext(46, \"Les r\\u00E9sultats seront sauvegard\\u00E9s dans la base de donn\\u00E9es PFA1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, PersonalityTestComponent_div_1_div_47_Template, 6, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 19)(49, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.startTest());\n    });\n    i0.ɵɵelementStart(50, \"span\");\n    i0.ɵɵtext(51, \"Commencer le Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"span\", 14);\n    i0.ɵɵtext(53, \"\\u2192\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_Template_button_click_54_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.goToHome());\n    });\n    i0.ɵɵtext(55, \" Retour \\u00E0 l'accueil \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(40);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedUser.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedUser.email, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser);\n  }\n}\nfunction PersonalityTestComponent_div_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"h2\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 36)(5, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_2_div_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.answerQuestion(true));\n    });\n    i0.ɵɵelementStart(6, \"span\", 38);\n    i0.ɵɵtext(7, \"\\u2713\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"Oui\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_2_div_11_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.answerQuestion(false));\n    });\n    i0.ɵɵelementStart(11, \"span\", 38);\n    i0.ɵɵtext(12, \"\\u2717\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Non\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r10.currentQuestion.question);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.isLoading);\n  }\n}\nfunction PersonalityTestComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"div\", 27)(4, \"span\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 30);\n    i0.ɵɵelement(10, \"div\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, PersonalityTestComponent_div_2_div_11_Template, 15, 3, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"Question \", ctx_r1.currentQuestionNumber, \" sur \", ctx_r1.totalQuestions, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(8, 6, ctx_r1.progressPercentage, \"1.0-0\"), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.progressPercentage, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentQuestion);\n  }\n}\nfunction PersonalityTestComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"div\", 42);\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Analyse de votre profil...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Calcul des scores et d\\u00E9termination de votre personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 43)(8, \"div\", 44);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 44);\n    i0.ɵɵtext(11, \"\\uD83D\\uDD04 Calcul des scores par famille\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 44);\n    i0.ɵɵtext(13, \"\\uD83C\\uDFAF D\\u00E9termination du profil principal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 44);\n    i0.ɵɵtext(15, \"\\uD83D\\uDD0D V\\u00E9rification compatibilit\\u00E9 iris\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\\u2713 R\\u00E9ponses collect\\u00E9es (\", ctx_r2.responses.length, \"/32)\");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"span\", 66);\n    i0.ɵɵtext(2, \"Profil Interm\\u00E9diaire\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"span\", 67);\n    i0.ɵɵtext(2, \"Profil Principal\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const characteristic_r23 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", characteristic_r23, \" \");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_li_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const recommendation_r24 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", recommendation_r24, \" \");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_div_29_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const correction_r26 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", correction_r26, \" \");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"h4\");\n    i0.ɵɵtext(2, \"Corrections sugg\\u00E9r\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 85);\n    i0.ɵɵtemplate(4, PersonalityTestComponent_div_4_div_22_div_29_li_4_Template, 2, 1, \"li\", 81);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r22.compatibilityResult.corrections);\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"h3\");\n    i0.ɵɵtext(2, \"Compatibilit\\u00E9 avec votre Iris\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 69)(4, \"div\", 70)(5, \"div\", 71)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 72)(11, \"div\", 73)(12, \"span\", 53);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 52);\n    i0.ɵɵtext(15, \"Compatibilit\\u00E9\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 74)(17, \"span\", 75);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 76)(20, \"h4\");\n    i0.ɵɵtext(21, \"Caract\\u00E9ristiques de votre iris\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 77);\n    i0.ɵɵtemplate(23, PersonalityTestComponent_div_4_div_22_span_23_Template, 2, 1, \"span\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 79)(25, \"h4\");\n    i0.ɵɵtext(26, \"Recommandations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"ul\", 80);\n    i0.ɵɵtemplate(28, PersonalityTestComponent_div_4_div_22_li_28_Template, 2, 1, \"li\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, PersonalityTestComponent_div_4_div_22_div_29_Template, 5, 1, \"div\", 82);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r16.compatibilityResult.isCompatible ? \"compatible\" : \"incompatible\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r16.compatibilityResult.irisType.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r16.compatibilityResult.irisType.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r16.compatibilityResult.isCompatible ? \"high-score\" : \"low-score\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r16.compatibilityResult.compatibilityScore, \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r16.compatibilityResult.isCompatible ? \"compatible\" : \"incompatible\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.compatibilityResult.isCompatible ? \"\\u2713 Compatible\" : \"\\u26A0 Incompatible\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.compatibilityResult.irisType.characteristics);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.compatibilityResult.recommendations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.compatibilityResult.isCompatible);\n  }\n}\nfunction PersonalityTestComponent_div_4_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"h3\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDCCA Scores d\\u00E9taill\\u00E9s par famille\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 87);\n    i0.ɵɵtext(4, \"Voici la r\\u00E9partition de vos r\\u00E9ponses selon les 4 familles de personnalit\\u00E9 :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 88)(6, \"div\", 89)(7, \"div\", 90)(8, \"span\", 52);\n    i0.ɵɵtext(9, \"\\uD83C\\uDF38 Flower\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\", 91);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 92);\n    i0.ɵɵelement(13, \"div\", 93);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 94)(15, \"span\", 53);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 95);\n    i0.ɵɵtext(18, \"\\u00C9motionnel, Cr\\u00E9atif, Empathique\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 89)(20, \"div\", 90)(21, \"span\", 52);\n    i0.ɵɵtext(22, \"\\uD83D\\uDC8E Jewel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 91);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 92);\n    i0.ɵɵelement(26, \"div\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 94)(28, \"span\", 53);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 95);\n    i0.ɵɵtext(31, \"Structur\\u00E9, Analytique, M\\u00E9thodique\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(32, \"div\", 89)(33, \"div\", 90)(34, \"span\", 52);\n    i0.ɵɵtext(35, \"\\u26A1 Shaker\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 91);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 92);\n    i0.ɵɵelement(39, \"div\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 94)(41, \"span\", 53);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 95);\n    i0.ɵɵtext(44, \"Dynamique, Aventurier, Spontan\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(45, \"div\", 89)(46, \"div\", 90)(47, \"span\", 52);\n    i0.ɵɵtext(48, \"\\uD83C\\uDF0A Stream\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"span\", 91);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 92);\n    i0.ɵɵelement(52, \"div\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 94)(54, \"span\", 53);\n    i0.ɵɵtext(55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"span\", 95);\n    i0.ɵɵtext(57, \"Paisible, R\\u00E9fl\\u00E9chi, Diplomate\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(58, \"div\", 99)(59, \"div\", 100)(60, \"span\", 60);\n    i0.ɵɵtext(61, \"Famille dominante :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"span\", 101);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 100)(65, \"span\", 60);\n    i0.ɵɵtext(66, \"Score de confiance :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"span\", 102);\n    i0.ɵɵtext(68);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵclassProp(\"highest-score\", ctx_r17.isHighestScore(\"flower\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.getScorePercentage(\"flower\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.flower / 4 * 100, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.flower, \"/4 r\\u00E9ponses\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"highest-score\", ctx_r17.isHighestScore(\"jewel\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.getScorePercentage(\"jewel\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.jewel / 4 * 100, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.jewel, \"/4 r\\u00E9ponses\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"highest-score\", ctx_r17.isHighestScore(\"shaker\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.getScorePercentage(\"shaker\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.shaker / 4 * 100, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.shaker, \"/4 r\\u00E9ponses\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"highest-score\", ctx_r17.isHighestScore(\"stream\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.getScorePercentage(\"stream\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.stream / 4 * 100, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.stream, \"/4 r\\u00E9ponses\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r17.getDominantFamily());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.finalProfile == null ? null : ctx_r17.finalProfile.confidenceScore, \"%\");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"span\", 60);\n    i0.ɵɵtext(2, \"ID de session:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r18.testSession == null ? null : ctx_r18.testSession.id);\n  }\n}\nfunction PersonalityTestComponent_div_4_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.logout());\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2, \"Se d\\u00E9connecter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 14);\n    i0.ɵɵtext(4, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"div\", 47)(3, \"h1\", 8);\n    i0.ɵɵtext(4, \"Votre Profil de Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 48)(7, \"div\", 49)(8, \"h2\", 50);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 51)(11, \"span\", 52);\n    i0.ɵɵtext(12, \"Score de confiance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 53);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(15, PersonalityTestComponent_div_4_div_15_Template, 3, 0, \"div\", 54);\n    i0.ɵɵtemplate(16, PersonalityTestComponent_div_4_div_16_Template, 3, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 55)(18, \"h3\");\n    i0.ɵɵtext(19, \"Description de votre profil\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, PersonalityTestComponent_div_4_div_22_Template, 30, 13, \"div\", 56);\n    i0.ɵɵtemplate(23, PersonalityTestComponent_div_4_div_23_Template, 69, 26, \"div\", 57);\n    i0.ɵɵelementStart(24, \"div\", 58)(25, \"div\", 59)(26, \"span\", 60);\n    i0.ɵɵtext(27, \"Test compl\\u00E9t\\u00E9 le:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 61);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, PersonalityTestComponent_div_4_div_31_Template, 5, 1, \"div\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 63)(33, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.restartTest());\n    });\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35, \"Refaire le Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 14);\n    i0.ɵɵtext(37, \"\\uD83D\\uDD04\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.goToHome());\n    });\n    i0.ɵɵtext(39, \" Retour \\u00E0 l'accueil \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(40, PersonalityTestComponent_div_4_button_40_Template, 5, 0, \"button\", 64);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMap(\"profile-\" + ctx_r3.finalProfile.primaryClass.toLowerCase().replace(\"-\", \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.finalProfile.primaryClass);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.finalProfile.confidenceScore, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.finalProfile.isIntermediate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.finalProfile.isIntermediate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.finalProfile.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.compatibilityResult);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testSession);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(30, 12, ctx_r3.testSession == null ? null : ctx_r3.testSession.completedAt, \"dd/MM/yyyy \\u00E0 HH:mm\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testSession == null ? null : ctx_r3.testSession.id);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentUser);\n  }\n}\nexport class PersonalityTestComponent {\n  constructor(personalityTestService, irisCompatibilityService, router) {\n    this.personalityTestService = personalityTestService;\n    this.irisCompatibilityService = irisCompatibilityService;\n    this.router = router;\n    // Données du test\n    this.questions = PERSONALITY_QUESTIONS;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.testSession = null;\n    // État du composant\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.isLoading = false;\n    this.showResults = false;\n    this.currentSessionId = '';\n    // Comptes statiques pour les tests\n    this.staticUsers = [{\n      id: 1,\n      name: 'Marie Dubois',\n      email: '<EMAIL>',\n      description: 'Profil créatif et émotionnel'\n    }, {\n      id: 2,\n      name: 'Jean Martin',\n      email: '<EMAIL>',\n      description: 'Profil analytique et structuré'\n    }, {\n      id: 3,\n      name: 'Sophie Leroy',\n      email: '<EMAIL>',\n      description: 'Profil dynamique et aventurier'\n    }, {\n      id: 4,\n      name: 'Pierre Moreau',\n      email: '<EMAIL>',\n      description: 'Profil paisible et réfléchi'\n    }, {\n      id: 5,\n      name: 'Emma Bernard',\n      email: '<EMAIL>',\n      description: 'Profil mixte créatif-analytique'\n    }];\n    this.selectedUser = this.staticUsers[0]; // Utilisateur par défaut\n    this.currentUser = null; // Utilisateur connecté\n    // Résultats\n    this.finalProfile = null;\n    this.compatibilityResult = null;\n  }\n  ngOnInit() {\n    // Récupérer l'utilisateur connecté\n    const currentUserData = localStorage.getItem('currentUser');\n    if (currentUserData) {\n      this.currentUser = JSON.parse(currentUserData);\n      // Utiliser l'utilisateur connecté pour le test\n      this.selectedUser = {\n        id: 0,\n        name: this.currentUser.name,\n        email: this.currentUser.email,\n        description: 'Utilisateur connecté'\n      };\n    }\n    this.initializeTest();\n  }\n  /**\n   * Initialise le test\n   */\n  initializeTest() {\n    this.testSession = this.personalityTestService.createTestSession(this.selectedUser.name, this.selectedUser.email);\n  }\n  /**\n   * Sélectionne un utilisateur de test\n   */\n  selectUser(user) {\n    this.selectedUser = user;\n    this.initializeTest();\n  }\n  /**\n   * Démarre le test\n   */\n  startTest() {\n    this.isTestStarted = true;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n    if (this.testSession) {\n      this.testSession.startedAt = new Date();\n      this.testSession.id = this.currentSessionId;\n    }\n    console.log('🚀 Test démarré - Session ID:', this.currentSessionId);\n  }\n  /**\n   * Traite la réponse à une question\n   */\n  answerQuestion(answer) {\n    if (!this.testSession) return;\n    const currentQuestion = this.questions[this.currentQuestionIndex];\n    const response = {\n      questionId: currentQuestion.id,\n      answer: answer,\n      timestamp: new Date()\n    };\n    this.responses.push(response);\n    this.testSession.responses = this.responses;\n    // Sauvegarder la réponse individuelle en temps réel\n    if (this.currentUser && this.currentSessionId) {\n      this.personalityTestService.saveIndividualResponse(this.currentUser.email, this.currentSessionId, response).subscribe({\n        next: success => {\n          if (success) {\n            console.log(`✅ Réponse ${currentQuestion.id} sauvegardée`);\n          } else {\n            console.warn(`⚠️ Erreur sauvegarde réponse ${currentQuestion.id}`);\n          }\n        },\n        error: error => {\n          console.error(`❌ Erreur réponse ${currentQuestion.id}:`, error);\n        }\n      });\n    }\n    // Passer à la question suivante ou terminer le test\n    if (this.currentQuestionIndex < this.questions.length - 1) {\n      this.currentQuestionIndex++;\n    } else {\n      this.completeTest();\n    }\n  }\n  /**\n   * Termine le test et calcule les résultats\n   */\n  completeTest() {\n    if (!this.testSession) return;\n    this.isLoading = true;\n    console.log('🔄 Début de l\\'analyse des résultats...');\n    // Simuler un délai d'analyse réaliste (2-3 secondes)\n    setTimeout(() => {\n      this.processTestResults();\n    }, 2500);\n  }\n  /**\n   * Traite les résultats du test\n   */\n  processTestResults() {\n    if (!this.testSession) return;\n    try {\n      console.log('📊 Calcul des scores avec', this.responses.length, 'réponses');\n      // Calculer les résultats\n      const results = this.personalityTestService.processTestResults(this.responses);\n      console.log('✅ Résultats calculés:', results);\n      // Mettre à jour la session\n      this.testSession.scores = results.scores;\n      this.testSession.finalProfile = results.profile;\n      this.testSession.completedAt = new Date();\n      this.finalProfile = results.profile;\n      // Vérifier la compatibilité avec l'iris\n      this.compatibilityResult = this.irisCompatibilityService.checkCompatibility(results.profile, this.selectedUser.email);\n      console.log('🔍 Compatibilité calculée:', this.compatibilityResult);\n      // Sauvegarder les statistiques de session\n      const sessionStats = {\n        totalQuestions: this.questions.length,\n        totalResponses: this.responses.length,\n        completionRate: this.responses.length / this.questions.length * 100,\n        averageResponseTime: this.calculateAverageResponseTime(),\n        scores: results.scores,\n        profile: results.profile,\n        userId: this.currentUser?.email,\n        completedAt: new Date().toISOString()\n      };\n      // Sauvegarder les statistiques\n      if (this.currentSessionId) {\n        this.personalityTestService.saveSessionStats(this.currentSessionId, sessionStats).subscribe({\n          next: success => {\n            console.log('📊 Statistiques de session sauvegardées:', success);\n          },\n          error: error => {\n            console.error('❌ Erreur sauvegarde stats:', error);\n          }\n        });\n      }\n      // Sauvegarder dans Firebase\n      this.personalityTestService.saveTestSession(this.testSession).subscribe({\n        next: sessionId => {\n          console.log('✅ Test sauvegardé avec l\\'ID:', sessionId);\n          this.testSession.id = sessionId;\n          this.showResultsWithDelay();\n        },\n        error: error => {\n          console.error('❌ Erreur lors de la sauvegarde:', error);\n          this.showResultsWithDelay();\n        }\n      });\n    } catch (error) {\n      console.error('❌ Erreur lors du traitement des résultats:', error);\n      this.showResultsWithDelay();\n    }\n  }\n  /**\n   * Affiche les résultats avec un délai pour une transition fluide\n   */\n  showResultsWithDelay() {\n    setTimeout(() => {\n      this.isLoading = false;\n      this.isTestCompleted = true;\n      this.showResults = true;\n      console.log('🎉 Résultats affichés !');\n    }, 500);\n  }\n  /**\n   * Calcule le temps de réponse moyen\n   */\n  calculateAverageResponseTime() {\n    if (this.responses.length === 0) return 0;\n    let totalTime = 0;\n    for (let i = 1; i < this.responses.length; i++) {\n      const timeDiff = this.responses[i].timestamp.getTime() - this.responses[i - 1].timestamp.getTime();\n      totalTime += timeDiff;\n    }\n    return totalTime / (this.responses.length - 1);\n  }\n  /**\n   * Redémarre le test\n   */\n  restartTest() {\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.showResults = false;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.finalProfile = null;\n    this.compatibilityResult = null;\n    this.currentSessionId = '';\n    this.initializeTest();\n  }\n  /**\n   * Retourne à l'accueil\n   */\n  goToHome() {\n    this.router.navigate(['/accueil']);\n  }\n  /**\n   * Déconnexion de l'utilisateur\n   */\n  logout() {\n    localStorage.removeItem('currentUser');\n    this.router.navigate(['/login']);\n  }\n  /**\n   * Obtient la question actuelle\n   */\n  get currentQuestion() {\n    return this.questions[this.currentQuestionIndex] || null;\n  }\n  /**\n   * Obtient le pourcentage de progression\n   */\n  get progressPercentage() {\n    return (this.currentQuestionIndex + 1) / this.questions.length * 100;\n  }\n  /**\n   * Obtient le numéro de la question actuelle\n   */\n  get currentQuestionNumber() {\n    return this.currentQuestionIndex + 1;\n  }\n  /**\n   * Obtient le nombre total de questions\n   */\n  get totalQuestions() {\n    return this.questions.length;\n  }\n  /**\n   * Vérifie si un score est le plus élevé\n   */\n  isHighestScore(family) {\n    if (!this.testSession?.scores) return false;\n    const scores = this.testSession.scores;\n    const currentScore = scores[family];\n    const maxScore = Math.max(scores.flower, scores.jewel, scores.shaker, scores.stream);\n    return currentScore === maxScore && maxScore > 0;\n  }\n  /**\n   * Calcule le pourcentage d'un score\n   */\n  getScorePercentage(family) {\n    if (!this.testSession?.scores) return 0;\n    const scores = this.testSession.scores;\n    const score = scores[family];\n    return Math.round(score / 4 * 100);\n  }\n  /**\n   * Obtient la famille dominante\n   */\n  getDominantFamily() {\n    if (!this.testSession?.scores) return 'Aucune';\n    const scores = this.testSession.scores;\n    const families = [{\n      name: '🌸 Flower',\n      score: scores.flower\n    }, {\n      name: '💎 Jewel',\n      score: scores.jewel\n    }, {\n      name: '⚡ Shaker',\n      score: scores.shaker\n    }, {\n      name: '🌊 Stream',\n      score: scores.stream\n    }];\n    const dominant = families.reduce((prev, current) => current.score > prev.score ? current : prev);\n    return dominant.name;\n  }\n  static {\n    this.ɵfac = function PersonalityTestComponent_Factory(t) {\n      return new (t || PersonalityTestComponent)(i0.ɵɵdirectiveInject(i1.PersonalityTestService), i0.ɵɵdirectiveInject(i2.IrisCompatibilityService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PersonalityTestComponent,\n      selectors: [[\"app-personality-test\"]],\n      decls: 5,\n      vars: 4,\n      consts: [[1, \"personality-test-container\"], [\"class\", \"test-intro\", 4, \"ngIf\"], [\"class\", \"test-interface\", 4, \"ngIf\"], [\"class\", \"loading-screen\", 4, \"ngIf\"], [\"class\", \"test-results\", 4, \"ngIf\"], [1, \"test-intro\"], [1, \"intro-card\"], [1, \"intro-header\"], [1, \"title\"], [1, \"divider\"], [1, \"subtitle\"], [1, \"intro-content\"], [1, \"test-info\"], [1, \"info-item\"], [1, \"icon\"], [1, \"info-text\"], [1, \"user-info\"], [1, \"note\"], [\"class\", \"user-actions\", 4, \"ngIf\"], [1, \"intro-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"user-actions\"], [1, \"btn\", \"btn-logout\", 3, \"click\"], [1, \"test-interface\"], [1, \"test-card\"], [1, \"progress-section\"], [1, \"progress-info\"], [1, \"question-counter\"], [1, \"progress-percentage\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [\"class\", \"question-section\", 4, \"ngIf\"], [1, \"question-section\"], [1, \"question-content\"], [1, \"question-text\"], [1, \"answer-buttons\"], [1, \"btn\", \"btn-answer\", \"btn-yes\", 3, \"disabled\", \"click\"], [1, \"answer-icon\"], [1, \"btn\", \"btn-answer\", \"btn-no\", 3, \"disabled\", \"click\"], [1, \"loading-screen\"], [1, \"loading-card\"], [1, \"loading-spinner\"], [1, \"loading-steps\"], [1, \"step\", \"active\"], [1, \"test-results\"], [1, \"results-card\"], [1, \"results-header\"], [1, \"profile-summary\"], [1, \"profile-badge\"], [1, \"profile-name\"], [1, \"confidence-score\"], [1, \"score-label\"], [1, \"score-value\"], [\"class\", \"profile-type\", 4, \"ngIf\"], [1, \"profile-description\"], [\"class\", \"iris-compatibility\", 4, \"ngIf\"], [\"class\", \"detailed-scores\", 4, \"ngIf\"], [1, \"test-info-summary\"], [1, \"info-row\"], [1, \"label\"], [1, \"value\"], [\"class\", \"info-row\", 4, \"ngIf\"], [1, \"results-actions\"], [\"class\", \"btn btn-logout\", 3, \"click\", 4, \"ngIf\"], [1, \"profile-type\"], [1, \"type-badge\", \"intermediate\"], [1, \"type-badge\", \"primary\"], [1, \"iris-compatibility\"], [1, \"compatibility-summary\"], [1, \"compatibility-header\"], [1, \"iris-info\"], [1, \"compatibility-score\"], [1, \"score-circle\"], [1, \"compatibility-status\"], [1, \"status-badge\"], [1, \"iris-characteristics\"], [1, \"characteristics-list\"], [\"class\", \"characteristic-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"recommendations\"], [1, \"recommendations-list\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"corrections\", 4, \"ngIf\"], [1, \"characteristic-tag\"], [1, \"corrections\"], [1, \"corrections-list\"], [1, \"detailed-scores\"], [1, \"scores-description\"], [1, \"scores-grid\"], [1, \"score-item\"], [1, \"score-header\"], [1, \"score-percentage\"], [1, \"score-bar\"], [1, \"score-fill\", \"flower\"], [1, \"score-details\"], [1, \"score-description\"], [1, \"score-fill\", \"jewel\"], [1, \"score-fill\", \"shaker\"], [1, \"score-fill\", \"stream\"], [1, \"score-summary\"], [1, \"summary-item\"], [1, \"value\", \"dominant\"], [1, \"value\", \"confidence\"]],\n      template: function PersonalityTestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, PersonalityTestComponent_div_1_Template, 56, 3, \"div\", 1);\n          i0.ɵɵtemplate(2, PersonalityTestComponent_div_2_Template, 12, 9, \"div\", 2);\n          i0.ɵɵtemplate(3, PersonalityTestComponent_div_3_Template, 16, 1, \"div\", 3);\n          i0.ɵɵtemplate(4, PersonalityTestComponent_div_4_Template, 41, 15, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isTestStarted && !ctx.showResults);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isTestStarted && !ctx.showResults);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showResults && ctx.finalProfile);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i4.DecimalPipe, i4.DatePipe],\n      styles: [\"[_ngcontent-%COMP%]:root {\\n  --primary-color: #2563eb;\\n  --secondary-color: #64748b;\\n  --accent-color: #0f172a;\\n  --success-color: #059669;\\n  --warning-color: #d97706;\\n  --danger-color: #dc2626;\\n  --text-primary: #0f172a;\\n  --text-secondary: #64748b;\\n  --text-muted: #94a3b8;\\n  --background-light: #f8fafc;\\n  --background-white: #ffffff;\\n  --border-color: #e2e8f0;\\n  --border-light: #f1f5f9;\\n  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);\\n  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);\\n}\\n\\n.personality-test-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: var(--background-light);\\n  padding: 2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-family: \\\"Inter\\\", -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", sans-serif;\\n}\\n\\n.intro-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%], .loading-card[_ngcontent-%COMP%] {\\n  background: var(--background-white);\\n  border: 1px solid var(--border-color);\\n  border-radius: 16px;\\n  box-shadow: var(--shadow-lg);\\n  padding: 3rem;\\n  max-width: 800px;\\n  width: 100%;\\n  margin: 0 auto;\\n}\\n\\n.title[_ngcontent-%COMP%] {\\n  font-size: 2.25rem;\\n  font-weight: 700;\\n  color: var(--text-primary);\\n  text-align: center;\\n  margin-bottom: 1rem;\\n  letter-spacing: -0.025em;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.125rem;\\n  color: var(--text-secondary);\\n  text-align: center;\\n  margin-bottom: 2rem;\\n  font-weight: 400;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 3px;\\n  background: var(--primary-color);\\n  margin: 0 auto 2rem;\\n  border-radius: 2px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 0.875rem 1.75rem;\\n  border: 1px solid transparent;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  text-decoration: none;\\n  font-family: inherit;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n  border-color: var(--primary-color);\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: #1d4ed8;\\n  border-color: #1d4ed8;\\n  box-shadow: var(--shadow-md);\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: var(--background-white);\\n  color: var(--text-secondary);\\n  border-color: var(--border-color);\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: var(--background-light);\\n  border-color: var(--secondary-color);\\n  color: var(--text-primary);\\n}\\n.btn.btn-logout[_ngcontent-%COMP%] {\\n  background: var(--danger-color);\\n  color: white;\\n  border-color: var(--danger-color);\\n}\\n.btn.btn-logout[_ngcontent-%COMP%]:hover {\\n  background: #b91c1c;\\n  border-color: #b91c1c;\\n  box-shadow: var(--shadow-md);\\n}\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n  transform: none !important;\\n}\\n\\n.test-intro[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.test-intro[_ngcontent-%COMP%]   .test-info[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1.5rem;\\n  margin-bottom: 2rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1.5rem;\\n  background: var(--background-light);\\n  border: 1px solid var(--border-light);\\n  border-radius: 12px;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: var(--primary-color);\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: var(--text-primary);\\n  font-size: 1rem;\\n  font-weight: 600;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--text-secondary);\\n  font-size: 0.875rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  background: var(--background-light);\\n  border: 1px solid var(--border-color);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: var(--text-primary);\\n  font-weight: 600;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n  color: var(--text-secondary);\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   p.note[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: var(--text-muted);\\n  font-size: 0.875rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .intro-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-top: 2rem;\\n}\\n@media (max-width: 768px) {\\n  .test-intro[_ngcontent-%COMP%]   .intro-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n.test-interface[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .question-counter[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--text-secondary);\\n  font-size: 0.875rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  font-size: 0.875rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 6px;\\n  background: var(--border-light);\\n  border-radius: 3px;\\n  overflow: hidden;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--primary-color);\\n  transition: width 0.3s ease;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   .question-text[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  color: #2d3748;\\n  line-height: 1.4;\\n  margin: 0;\\n}\\n.test-interface[_ngcontent-%COMP%]   .answer-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n  justify-content: center;\\n}\\n@media (max-width: 768px) {\\n  .test-interface[_ngcontent-%COMP%]   .answer-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer[_ngcontent-%COMP%] {\\n  padding: 1.5rem 3rem;\\n  font-size: 1.3rem;\\n  min-width: 150px;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer[_ngcontent-%COMP%]   .answer-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-yes[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\\n  color: white;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-yes[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 15px 30px rgba(72, 187, 120, 0.4);\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-no[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\\n  color: white;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-no[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 15px 30px rgba(245, 101, 101, 0.4);\\n}\\n\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border: 4px solid #e2e8f0;\\n  border-top: 4px solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin: 0 auto 2rem;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin-bottom: 1rem;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #718096;\\n  margin: 0 0 1.5rem 0;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-steps[_ngcontent-%COMP%] {\\n  text-align: left;\\n  margin-top: 1.5rem;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  color: #667eea;\\n  font-size: 0.9rem;\\n  opacity: 0.8;\\n  transition: opacity 0.3s ease;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  font-weight: 500;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-summary[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 2rem;\\n  border-radius: 20px;\\n  margin-bottom: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-flower[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbb6ce 0%, #f687b3 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-jewel[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #90cdf4 0%, #63b3ed 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-shaker[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbd38d 0%, #f6ad55 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-stream[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9ae6b4 0%, #68d391 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-flowerjewel[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbb6ce 0%, #90cdf4 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-jewelshaker[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #90cdf4 0%, #fbd38d 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-shakerstream[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbd38d 0%, #9ae6b4 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-streamflower[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9ae6b4 0%, #fbb6ce 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin: 0 0 1rem 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge.primary[_ngcontent-%COMP%] {\\n  background: rgba(102, 126, 234, 0.2);\\n  color: #667eea;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge.intermediate[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.2);\\n  color: #764ba2;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%] {\\n  background: rgba(102, 126, 234, 0.1);\\n  padding: 2rem;\\n  border-radius: 12px;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  line-height: 1.6;\\n  margin: 0;\\n  font-size: 1.1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1.5rem 0;\\n  text-align: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  border-radius: 12px;\\n  margin-bottom: 2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary.compatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(56, 161, 105, 0.1) 100%);\\n  border: 2px solid rgba(72, 187, 120, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary.incompatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(229, 62, 62, 0.1) 100%);\\n  border: 2px solid rgba(245, 101, 101, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .iris-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .iris-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .iris-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle.high-score[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle.low-score[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n  font-size: 0.7rem;\\n  font-weight: 500;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.5rem 1.5rem;\\n  border-radius: 20px;\\n  font-weight: 600;\\n  font-size: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%]   .status-badge.compatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\\n  color: white;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%]   .status-badge.incompatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\\n  color: white;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.1);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  margin-bottom: 1.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%]   .characteristics-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%]   .characteristics-list[_ngcontent-%COMP%]   .characteristic-tag[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.3rem 0.8rem;\\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\\n  color: white;\\n  border-radius: 15px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.5);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  margin-bottom: 1.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 1.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  line-height: 1.6;\\n  margin-bottom: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child, .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%] {\\n  border-left: 4px solid #f56565;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #e53e3e;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n  background: rgba(255, 255, 255, 0.8);\\n  padding: 2rem;\\n  border-radius: 16px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.4rem;\\n  text-align: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .scores-description[_ngcontent-%COMP%] {\\n  color: #718096;\\n  text-align: center;\\n  margin-bottom: 2rem;\\n  font-size: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .scores-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1.5rem;\\n  margin-bottom: 2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  background: rgba(255, 255, 255, 0.9);\\n  border-radius: 12px;\\n  border: 2px solid transparent;\\n  transition: all 0.3s ease;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item.highest-score[_ngcontent-%COMP%] {\\n  border-color: #667eea;\\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);\\n  transform: translateY(-2px);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-header[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-header[_ngcontent-%COMP%]   .score-percentage[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #667eea;\\n  font-size: 1.2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%] {\\n  height: 12px;\\n  background: #e2e8f0;\\n  border-radius: 6px;\\n  overflow: hidden;\\n  margin-bottom: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: width 1s ease;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.flower[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #fbb6ce, #f687b3);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.jewel[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #90cdf4, #63b3ed);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.shaker[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #fbd38d, #f6ad55);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.stream[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #9ae6b4, #68d391);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-details[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-details[_ngcontent-%COMP%]   .score-description[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 0.9rem;\\n  font-style: italic;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  border: 1px solid rgba(102, 126, 234, 0.2);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.8rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n  font-size: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  font-size: 1.1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .value.dominant[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .value.confidence[_ngcontent-%COMP%] {\\n  color: #38a169;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.1);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.test-results[_ngcontent-%COMP%]   .results-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-top: 2rem;\\n}\\n@media (max-width: 768px) {\\n  .test-results[_ngcontent-%COMP%]   .results-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .personality-test-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .intro-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%], .loading-card[_ngcontent-%COMP%] {\\n    padding: 2rem;\\n  }\\n  .title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .question-text[_ngcontent-%COMP%] {\\n    font-size: 1.4rem !important;\\n  }\\n  .btn-answer[_ngcontent-%COMP%] {\\n    padding: 1rem 2rem !important;\\n    font-size: 1.1rem !important;\\n    min-width: 120px !important;\\n  }\\n  .profile-name[_ngcontent-%COMP%] {\\n    font-size: 1.5rem !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["PERSONALITY_QUESTIONS", "i0", "ɵɵelementStart", "ɵɵlistener", "PersonalityTestComponent_div_1_div_47_Template_button_click_1_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "PersonalityTestComponent_div_1_div_47_Template", "PersonalityTestComponent_div_1_Template_button_click_49_listener", "_r8", "ctx_r7", "startTest", "PersonalityTestComponent_div_1_Template_button_click_54_listener", "ctx_r9", "goToHome", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "selected<PERSON>ser", "name", "email", "ɵɵproperty", "currentUser", "PersonalityTestComponent_div_2_div_11_Template_button_click_5_listener", "_r12", "ctx_r11", "answerQuestion", "PersonalityTestComponent_div_2_div_11_Template_button_click_10_listener", "ctx_r13", "ɵɵtextInterpolate", "ctx_r10", "currentQuestion", "question", "isLoading", "PersonalityTestComponent_div_2_div_11_Template", "ɵɵtextInterpolate2", "ctx_r1", "currentQuestionNumber", "totalQuestions", "ɵɵpipeBind2", "progressPercentage", "ɵɵstyleProp", "ctx_r2", "responses", "length", "characteristic_r23", "recommendation_r24", "correction_r26", "PersonalityTestComponent_div_4_div_22_div_29_li_4_Template", "ctx_r22", "compatibilityResult", "corrections", "PersonalityTestComponent_div_4_div_22_span_23_Template", "PersonalityTestComponent_div_4_div_22_li_28_Template", "PersonalityTestComponent_div_4_div_22_div_29_Template", "ɵɵclassMap", "ctx_r16", "isCompatible", "irisType", "description", "compatibilityScore", "characteristics", "recommendations", "ɵɵclassProp", "ctx_r17", "isHighestScore", "getScorePercentage", "testSession", "scores", "flower", "jewel", "shaker", "stream", "getDominantFamily", "finalProfile", "confidenceScore", "ctx_r18", "id", "PersonalityTestComponent_div_4_button_40_Template_button_click_0_listener", "_r28", "ctx_r27", "PersonalityTestComponent_div_4_div_15_Template", "PersonalityTestComponent_div_4_div_16_Template", "PersonalityTestComponent_div_4_div_22_Template", "PersonalityTestComponent_div_4_div_23_Template", "PersonalityTestComponent_div_4_div_31_Template", "PersonalityTestComponent_div_4_Template_button_click_33_listener", "_r30", "ctx_r29", "restartTest", "PersonalityTestComponent_div_4_Template_button_click_38_listener", "ctx_r31", "PersonalityTestComponent_div_4_button_40_Template", "ctx_r3", "primaryClass", "toLowerCase", "replace", "isIntermediate", "completedAt", "PersonalityTestComponent", "constructor", "personalityTestService", "irisCompatibilityService", "router", "questions", "currentQuestionIndex", "isTestStarted", "isTestCompleted", "showResults", "currentSessionId", "staticUsers", "ngOnInit", "currentUserData", "localStorage", "getItem", "JSON", "parse", "initializeTest", "createTestSession", "selectUser", "user", "Date", "now", "Math", "random", "toString", "substr", "startedAt", "console", "log", "answer", "response", "questionId", "timestamp", "push", "saveIndividualResponse", "subscribe", "next", "success", "warn", "error", "completeTest", "setTimeout", "processTestResults", "results", "profile", "checkCompatibility", "sessionStats", "totalResponses", "completionRate", "averageResponseTime", "calculateAverageResponseTime", "userId", "toISOString", "saveSessionStats", "saveTestSession", "sessionId", "showResultsWithDelay", "totalTime", "i", "timeDiff", "getTime", "navigate", "removeItem", "family", "currentScore", "maxScore", "max", "score", "round", "families", "dominant", "reduce", "prev", "current", "ɵɵdirectiveInject", "i1", "PersonalityTestService", "i2", "IrisCompatibilityService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "PersonalityTestComponent_Template", "rf", "ctx", "PersonalityTestComponent_div_1_Template", "PersonalityTestComponent_div_2_Template", "PersonalityTestComponent_div_3_Template", "PersonalityTestComponent_div_4_Template"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\personality-test\\personality-test.component.ts", "D:\\ProjetPfa\\pfa\\pfa\\src\\app\\personality-test\\personality-test.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { PersonalityTestService } from '../services/personality-test.service';\nimport { IrisCompatibilityService, CompatibilityResult } from '../services/iris-compatibility.service';\nimport {\n  Question,\n  UserResponse,\n  TestSession,\n  PersonalityProfile,\n  PERSONALITY_QUESTIONS\n} from '../models/personality-test.model';\n\n@Component({\n  selector: 'app-personality-test',\n  templateUrl: './personality-test.component.html',\n  styleUrls: ['./personality-test.component.scss']\n})\nexport class PersonalityTestComponent implements OnInit {\n  // Données du test\n  questions: Question[] = PERSONALITY_QUESTIONS;\n  currentQuestionIndex: number = 0;\n  responses: UserResponse[] = [];\n  testSession: TestSession | null = null;\n\n  // État du composant\n  isTestStarted: boolean = false;\n  isTestCompleted: boolean = false;\n  isLoading: boolean = false;\n  showResults: boolean = false;\n  currentSessionId: string = '';\n\n  // Comptes statiques pour les tests\n  staticUsers = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      description: 'Profil créatif et émotionnel'\n    },\n    {\n      id: 2,\n      name: 'Jean Martin',\n      email: '<EMAIL>',\n      description: 'Profil analytique et structuré'\n    },\n    {\n      id: 3,\n      name: 'Sophie Leroy',\n      email: '<EMAIL>',\n      description: 'Profil dynamique et aventurier'\n    },\n    {\n      id: 4,\n      name: 'Pierre Moreau',\n      email: '<EMAIL>',\n      description: 'Profil paisible et réfléchi'\n    },\n    {\n      id: 5,\n      name: 'Emma Bernard',\n      email: '<EMAIL>',\n      description: 'Profil mixte créatif-analytique'\n    }\n  ];\n\n  selectedUser = this.staticUsers[0]; // Utilisateur par défaut\n  currentUser: any = null; // Utilisateur connecté\n\n  // Résultats\n  finalProfile: PersonalityProfile | null = null;\n  compatibilityResult: CompatibilityResult | null = null;\n\n  constructor(\n    private personalityTestService: PersonalityTestService,\n    private irisCompatibilityService: IrisCompatibilityService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Récupérer l'utilisateur connecté\n    const currentUserData = localStorage.getItem('currentUser');\n    if (currentUserData) {\n      this.currentUser = JSON.parse(currentUserData);\n      // Utiliser l'utilisateur connecté pour le test\n      this.selectedUser = {\n        id: 0,\n        name: this.currentUser.name,\n        email: this.currentUser.email,\n        description: 'Utilisateur connecté'\n      };\n    }\n    this.initializeTest();\n  }\n\n  /**\n   * Initialise le test\n   */\n  initializeTest(): void {\n    this.testSession = this.personalityTestService.createTestSession(\n      this.selectedUser.name,\n      this.selectedUser.email\n    );\n  }\n\n  /**\n   * Sélectionne un utilisateur de test\n   */\n  selectUser(user: any): void {\n    this.selectedUser = user;\n    this.initializeTest();\n  }\n\n  /**\n   * Démarre le test\n   */\n  startTest(): void {\n    this.isTestStarted = true;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n    if (this.testSession) {\n      this.testSession.startedAt = new Date();\n      this.testSession.id = this.currentSessionId;\n    }\n\n    console.log('🚀 Test démarré - Session ID:', this.currentSessionId);\n  }\n\n  /**\n   * Traite la réponse à une question\n   */\n  answerQuestion(answer: boolean): void {\n    if (!this.testSession) return;\n\n    const currentQuestion = this.questions[this.currentQuestionIndex];\n    const response: UserResponse = {\n      questionId: currentQuestion.id,\n      answer: answer,\n      timestamp: new Date()\n    };\n\n    this.responses.push(response);\n    this.testSession.responses = this.responses;\n\n    // Sauvegarder la réponse individuelle en temps réel\n    if (this.currentUser && this.currentSessionId) {\n      this.personalityTestService.saveIndividualResponse(\n        this.currentUser.email,\n        this.currentSessionId,\n        response\n      ).subscribe({\n        next: (success) => {\n          if (success) {\n            console.log(`✅ Réponse ${currentQuestion.id} sauvegardée`);\n          } else {\n            console.warn(`⚠️ Erreur sauvegarde réponse ${currentQuestion.id}`);\n          }\n        },\n        error: (error) => {\n          console.error(`❌ Erreur réponse ${currentQuestion.id}:`, error);\n        }\n      });\n    }\n\n    // Passer à la question suivante ou terminer le test\n    if (this.currentQuestionIndex < this.questions.length - 1) {\n      this.currentQuestionIndex++;\n    } else {\n      this.completeTest();\n    }\n  }\n\n  /**\n   * Termine le test et calcule les résultats\n   */\n  completeTest(): void {\n    if (!this.testSession) return;\n\n    this.isLoading = true;\n    console.log('🔄 Début de l\\'analyse des résultats...');\n\n    // Simuler un délai d'analyse réaliste (2-3 secondes)\n    setTimeout(() => {\n      this.processTestResults();\n    }, 2500);\n  }\n\n  /**\n   * Traite les résultats du test\n   */\n  private processTestResults(): void {\n    if (!this.testSession) return;\n\n    try {\n      console.log('📊 Calcul des scores avec', this.responses.length, 'réponses');\n\n      // Calculer les résultats\n      const results = this.personalityTestService.processTestResults(this.responses);\n      console.log('✅ Résultats calculés:', results);\n\n      // Mettre à jour la session\n      this.testSession.scores = results.scores;\n      this.testSession.finalProfile = results.profile;\n      this.testSession.completedAt = new Date();\n\n      this.finalProfile = results.profile;\n\n      // Vérifier la compatibilité avec l'iris\n      this.compatibilityResult = this.irisCompatibilityService.checkCompatibility(\n        results.profile,\n        this.selectedUser.email\n      );\n      console.log('🔍 Compatibilité calculée:', this.compatibilityResult);\n\n      // Sauvegarder les statistiques de session\n      const sessionStats = {\n        totalQuestions: this.questions.length,\n        totalResponses: this.responses.length,\n        completionRate: (this.responses.length / this.questions.length) * 100,\n        averageResponseTime: this.calculateAverageResponseTime(),\n        scores: results.scores,\n        profile: results.profile,\n        userId: this.currentUser?.email,\n        completedAt: new Date().toISOString()\n      };\n\n      // Sauvegarder les statistiques\n      if (this.currentSessionId) {\n        this.personalityTestService.saveSessionStats(this.currentSessionId, sessionStats).subscribe({\n          next: (success) => {\n            console.log('📊 Statistiques de session sauvegardées:', success);\n          },\n          error: (error) => {\n            console.error('❌ Erreur sauvegarde stats:', error);\n          }\n        });\n      }\n\n      // Sauvegarder dans Firebase\n      this.personalityTestService.saveTestSession(this.testSession).subscribe({\n        next: (sessionId) => {\n          console.log('✅ Test sauvegardé avec l\\'ID:', sessionId);\n          this.testSession!.id = sessionId;\n          this.showResultsWithDelay();\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors de la sauvegarde:', error);\n          this.showResultsWithDelay();\n        }\n      });\n\n    } catch (error) {\n      console.error('❌ Erreur lors du traitement des résultats:', error);\n      this.showResultsWithDelay();\n    }\n  }\n\n  /**\n   * Affiche les résultats avec un délai pour une transition fluide\n   */\n  private showResultsWithDelay(): void {\n    setTimeout(() => {\n      this.isLoading = false;\n      this.isTestCompleted = true;\n      this.showResults = true;\n      console.log('🎉 Résultats affichés !');\n    }, 500);\n  }\n\n  /**\n   * Calcule le temps de réponse moyen\n   */\n  private calculateAverageResponseTime(): number {\n    if (this.responses.length === 0) return 0;\n\n    let totalTime = 0;\n    for (let i = 1; i < this.responses.length; i++) {\n      const timeDiff = this.responses[i].timestamp.getTime() - this.responses[i-1].timestamp.getTime();\n      totalTime += timeDiff;\n    }\n\n    return totalTime / (this.responses.length - 1);\n  }\n\n  /**\n   * Redémarre le test\n   */\n  restartTest(): void {\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.showResults = false;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.finalProfile = null;\n    this.compatibilityResult = null;\n    this.currentSessionId = '';\n    this.initializeTest();\n  }\n\n  /**\n   * Retourne à l'accueil\n   */\n  goToHome(): void {\n    this.router.navigate(['/accueil']);\n  }\n\n  /**\n   * Déconnexion de l'utilisateur\n   */\n  logout(): void {\n    localStorage.removeItem('currentUser');\n    this.router.navigate(['/login']);\n  }\n\n  /**\n   * Obtient la question actuelle\n   */\n  get currentQuestion(): Question | null {\n    return this.questions[this.currentQuestionIndex] || null;\n  }\n\n  /**\n   * Obtient le pourcentage de progression\n   */\n  get progressPercentage(): number {\n    return ((this.currentQuestionIndex + 1) / this.questions.length) * 100;\n  }\n\n  /**\n   * Obtient le numéro de la question actuelle\n   */\n  get currentQuestionNumber(): number {\n    return this.currentQuestionIndex + 1;\n  }\n\n  /**\n   * Obtient le nombre total de questions\n   */\n  get totalQuestions(): number {\n    return this.questions.length;\n  }\n\n  /**\n   * Vérifie si un score est le plus élevé\n   */\n  isHighestScore(family: string): boolean {\n    if (!this.testSession?.scores) return false;\n\n    const scores = this.testSession.scores;\n    const currentScore = scores[family as keyof typeof scores] as number;\n    const maxScore = Math.max(scores.flower, scores.jewel, scores.shaker, scores.stream);\n\n    return currentScore === maxScore && maxScore > 0;\n  }\n\n  /**\n   * Calcule le pourcentage d'un score\n   */\n  getScorePercentage(family: string): number {\n    if (!this.testSession?.scores) return 0;\n\n    const scores = this.testSession.scores;\n    const score = scores[family as keyof typeof scores] as number;\n\n    return Math.round((score / 4) * 100);\n  }\n\n  /**\n   * Obtient la famille dominante\n   */\n  getDominantFamily(): string {\n    if (!this.testSession?.scores) return 'Aucune';\n\n    const scores = this.testSession.scores;\n    const families = [\n      { name: '🌸 Flower', score: scores.flower },\n      { name: '💎 Jewel', score: scores.jewel },\n      { name: '⚡ Shaker', score: scores.shaker },\n      { name: '🌊 Stream', score: scores.stream }\n    ];\n\n    const dominant = families.reduce((prev, current) =>\n      current.score > prev.score ? current : prev\n    );\n\n    return dominant.name;\n  }\n}\n", "<div class=\"personality-test-container\">\n  <!-- Page d'accueil du test -->\n  <div class=\"test-intro\" *ngIf=\"!isTestStarted && !showResults\">\n    <div class=\"intro-card\">\n      <div class=\"intro-header\">\n        <h1 class=\"title\">Test de Personnalité</h1>\n        <div class=\"divider\"></div>\n        <p class=\"subtitle\">Découvrez votre profil psychotechnique</p>\n      </div>\n\n      <div class=\"intro-content\">\n        <div class=\"test-info\">\n          <div class=\"info-item\">\n            <span class=\"icon\">📝</span>\n            <div class=\"info-text\">\n              <h3>32 Questions</h3>\n              <p>Questions ciblées pour analyser votre personnalité</p>\n            </div>\n          </div>\n\n          <div class=\"info-item\">\n            <span class=\"icon\">⏱️</span>\n            <div class=\"info-text\">\n              <h3>5-10 Minutes</h3>\n              <p>Temps estimé pour compléter le test</p>\n            </div>\n          </div>\n\n          <div class=\"info-item\">\n            <span class=\"icon\">🎯</span>\n            <div class=\"info-text\">\n              <h3>4 Profils Principaux</h3>\n              <p>Flower, Jewel, Shaker, Stream + profils intermédiaires</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"user-info\">\n          <h3>Informations du test</h3>\n          <p><strong>Nom:</strong> {{ selectedUser.name }}</p>\n          <p><strong>Email:</strong> {{ selectedUser.email }}</p>\n          <p class=\"note\">Les résultats seront sauvegardés dans la base de données PFA1</p>\n\n          <div class=\"user-actions\" *ngIf=\"currentUser\">\n            <button class=\"btn btn-logout\" (click)=\"logout()\">\n              <span>Se déconnecter</span>\n              <span class=\"icon\">🚪</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"intro-actions\">\n        <button class=\"btn btn-primary\" (click)=\"startTest()\">\n          <span>Commencer le Test</span>\n          <span class=\"icon\">→</span>\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"goToHome()\">\n          Retour à l'accueil\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Interface du test -->\n  <div class=\"test-interface\" *ngIf=\"isTestStarted && !showResults\">\n    <div class=\"test-card\">\n      <!-- Barre de progression -->\n      <div class=\"progress-section\">\n        <div class=\"progress-info\">\n          <span class=\"question-counter\">Question {{ currentQuestionNumber }} sur {{ totalQuestions }}</span>\n          <span class=\"progress-percentage\">{{ progressPercentage | number:'1.0-0' }}%</span>\n        </div>\n        <div class=\"progress-bar\">\n          <div class=\"progress-fill\" [style.width.%]=\"progressPercentage\"></div>\n        </div>\n      </div>\n\n      <!-- Question actuelle -->\n      <div class=\"question-section\" *ngIf=\"currentQuestion\">\n        <div class=\"question-content\">\n          <h2 class=\"question-text\">{{ currentQuestion.question }}</h2>\n        </div>\n\n        <div class=\"answer-buttons\">\n          <button\n            class=\"btn btn-answer btn-yes\"\n            (click)=\"answerQuestion(true)\"\n            [disabled]=\"isLoading\">\n            <span class=\"answer-icon\">✓</span>\n            <span>Oui</span>\n          </button>\n\n          <button\n            class=\"btn btn-answer btn-no\"\n            (click)=\"answerQuestion(false)\"\n            [disabled]=\"isLoading\">\n            <span class=\"answer-icon\">✗</span>\n            <span>Non</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Écran de chargement -->\n  <div class=\"loading-screen\" *ngIf=\"isLoading\">\n    <div class=\"loading-card\">\n      <div class=\"loading-spinner\"></div>\n      <h2>Analyse de votre profil...</h2>\n      <p>Calcul des scores et détermination de votre personnalité</p>\n      <div class=\"loading-steps\">\n        <div class=\"step active\">✓ Réponses collectées ({{ responses.length }}/32)</div>\n        <div class=\"step active\">🔄 Calcul des scores par famille</div>\n        <div class=\"step active\">🎯 Détermination du profil principal</div>\n        <div class=\"step active\">🔍 Vérification compatibilité iris</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Résultats du test -->\n  <div class=\"test-results\" *ngIf=\"showResults && finalProfile\">\n    <div class=\"results-card\">\n      <div class=\"results-header\">\n        <h1 class=\"title\">Votre Profil de Personnalité</h1>\n        <div class=\"divider\"></div>\n      </div>\n\n      <div class=\"profile-summary\">\n        <div class=\"profile-badge\" [class]=\"'profile-' + finalProfile.primaryClass.toLowerCase().replace('-', '')\">\n          <h2 class=\"profile-name\">{{ finalProfile.primaryClass }}</h2>\n          <div class=\"confidence-score\">\n            <span class=\"score-label\">Score de confiance</span>\n            <span class=\"score-value\">{{ finalProfile.confidenceScore }}%</span>\n          </div>\n        </div>\n\n        <div class=\"profile-type\" *ngIf=\"finalProfile.isIntermediate\">\n          <span class=\"type-badge intermediate\">Profil Intermédiaire</span>\n        </div>\n        <div class=\"profile-type\" *ngIf=\"!finalProfile.isIntermediate\">\n          <span class=\"type-badge primary\">Profil Principal</span>\n        </div>\n      </div>\n\n      <div class=\"profile-description\">\n        <h3>Description de votre profil</h3>\n        <p>{{ finalProfile.description }}</p>\n      </div>\n\n      <!-- Compatibilité avec l'iris -->\n      <div class=\"iris-compatibility\" *ngIf=\"compatibilityResult\">\n        <h3>Compatibilité avec votre Iris</h3>\n\n        <div class=\"compatibility-summary\" [class]=\"compatibilityResult.isCompatible ? 'compatible' : 'incompatible'\">\n          <div class=\"compatibility-header\">\n            <div class=\"iris-info\">\n              <h4>{{ compatibilityResult.irisType.name }}</h4>\n              <p>{{ compatibilityResult.irisType.description }}</p>\n            </div>\n            <div class=\"compatibility-score\">\n              <div class=\"score-circle\" [class]=\"compatibilityResult.isCompatible ? 'high-score' : 'low-score'\">\n                <span class=\"score-value\">{{ compatibilityResult.compatibilityScore }}%</span>\n                <span class=\"score-label\">Compatibilité</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"compatibility-status\">\n            <span class=\"status-badge\" [class]=\"compatibilityResult.isCompatible ? 'compatible' : 'incompatible'\">\n              {{ compatibilityResult.isCompatible ? '✓ Compatible' : '⚠ Incompatible' }}\n            </span>\n          </div>\n        </div>\n\n        <div class=\"iris-characteristics\">\n          <h4>Caractéristiques de votre iris</h4>\n          <div class=\"characteristics-list\">\n            <span *ngFor=\"let characteristic of compatibilityResult.irisType.characteristics\"\n                  class=\"characteristic-tag\">\n              {{ characteristic }}\n            </span>\n          </div>\n        </div>\n\n        <div class=\"recommendations\">\n          <h4>Recommandations</h4>\n          <ul class=\"recommendations-list\">\n            <li *ngFor=\"let recommendation of compatibilityResult.recommendations\">\n              {{ recommendation }}\n            </li>\n          </ul>\n        </div>\n\n        <div class=\"corrections\" *ngIf=\"!compatibilityResult.isCompatible\">\n          <h4>Corrections suggérées</h4>\n          <ul class=\"corrections-list\">\n            <li *ngFor=\"let correction of compatibilityResult.corrections\">\n              {{ correction }}\n            </li>\n          </ul>\n        </div>\n      </div>\n\n      <div class=\"detailed-scores\" *ngIf=\"testSession\">\n        <h3>📊 Scores détaillés par famille</h3>\n        <p class=\"scores-description\">Voici la répartition de vos réponses selon les 4 familles de personnalité :</p>\n\n        <div class=\"scores-grid\">\n          <div class=\"score-item\" [class.highest-score]=\"isHighestScore('flower')\">\n            <div class=\"score-header\">\n              <span class=\"score-label\">🌸 Flower</span>\n              <span class=\"score-percentage\">{{ getScorePercentage('flower') }}%</span>\n            </div>\n            <div class=\"score-bar\">\n              <div class=\"score-fill flower\" [style.width.%]=\"(testSession.scores.flower / 4) * 100\"></div>\n            </div>\n            <div class=\"score-details\">\n              <span class=\"score-value\">{{ testSession.scores.flower }}/4 réponses</span>\n              <span class=\"score-description\">Émotionnel, Créatif, Empathique</span>\n            </div>\n          </div>\n\n          <div class=\"score-item\" [class.highest-score]=\"isHighestScore('jewel')\">\n            <div class=\"score-header\">\n              <span class=\"score-label\">💎 Jewel</span>\n              <span class=\"score-percentage\">{{ getScorePercentage('jewel') }}%</span>\n            </div>\n            <div class=\"score-bar\">\n              <div class=\"score-fill jewel\" [style.width.%]=\"(testSession.scores.jewel / 4) * 100\"></div>\n            </div>\n            <div class=\"score-details\">\n              <span class=\"score-value\">{{ testSession.scores.jewel }}/4 réponses</span>\n              <span class=\"score-description\">Structuré, Analytique, Méthodique</span>\n            </div>\n          </div>\n\n          <div class=\"score-item\" [class.highest-score]=\"isHighestScore('shaker')\">\n            <div class=\"score-header\">\n              <span class=\"score-label\">⚡ Shaker</span>\n              <span class=\"score-percentage\">{{ getScorePercentage('shaker') }}%</span>\n            </div>\n            <div class=\"score-bar\">\n              <div class=\"score-fill shaker\" [style.width.%]=\"(testSession.scores.shaker / 4) * 100\"></div>\n            </div>\n            <div class=\"score-details\">\n              <span class=\"score-value\">{{ testSession.scores.shaker }}/4 réponses</span>\n              <span class=\"score-description\">Dynamique, Aventurier, Spontané</span>\n            </div>\n          </div>\n\n          <div class=\"score-item\" [class.highest-score]=\"isHighestScore('stream')\">\n            <div class=\"score-header\">\n              <span class=\"score-label\">🌊 Stream</span>\n              <span class=\"score-percentage\">{{ getScorePercentage('stream') }}%</span>\n            </div>\n            <div class=\"score-bar\">\n              <div class=\"score-fill stream\" [style.width.%]=\"(testSession.scores.stream / 4) * 100\"></div>\n            </div>\n            <div class=\"score-details\">\n              <span class=\"score-value\">{{ testSession.scores.stream }}/4 réponses</span>\n              <span class=\"score-description\">Paisible, Réfléchi, Diplomate</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"score-summary\">\n          <div class=\"summary-item\">\n            <span class=\"label\">Famille dominante :</span>\n            <span class=\"value dominant\">{{ getDominantFamily() }}</span>\n          </div>\n          <div class=\"summary-item\">\n            <span class=\"label\">Score de confiance :</span>\n            <span class=\"value confidence\">{{ finalProfile?.confidenceScore }}%</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"test-info-summary\">\n        <div class=\"info-row\">\n          <span class=\"label\">Test complété le:</span>\n          <span class=\"value\">{{ testSession?.completedAt | date:'dd/MM/yyyy à HH:mm' }}</span>\n        </div>\n        <div class=\"info-row\" *ngIf=\"testSession?.id\">\n          <span class=\"label\">ID de session:</span>\n          <span class=\"value\">{{ testSession?.id }}</span>\n        </div>\n      </div>\n\n      <div class=\"results-actions\">\n        <button class=\"btn btn-primary\" (click)=\"restartTest()\">\n          <span>Refaire le Test</span>\n          <span class=\"icon\">🔄</span>\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"goToHome()\">\n          Retour à l'accueil\n        </button>\n        <button class=\"btn btn-logout\" (click)=\"logout()\" *ngIf=\"currentUser\">\n          <span>Se déconnecter</span>\n          <span class=\"icon\">🚪</span>\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAIA,SAKEA,qBAAqB,QAChB,kCAAkC;;;;;;;;;ICiC/BC,EAAA,CAAAC,cAAA,cAA8C;IACbD,EAAA,CAAAE,UAAA,mBAAAC,uEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IAC/CT,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,0BAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC3BX,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAU,MAAA,mBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IA5CxCX,EAAA,CAAAC,cAAA,aAA+D;IAGvCD,EAAA,CAAAU,MAAA,gCAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC3CX,EAAA,CAAAY,SAAA,aAA2B;IAC3BZ,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAU,MAAA,kDAAsC;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAGhEX,EAAA,CAAAC,cAAA,cAA2B;IAGFD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACrBX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,oEAAkD;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAI7DX,EAAA,CAAAC,cAAA,eAAuB;IACFD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACrBX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,qDAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAI9CX,EAAA,CAAAC,cAAA,eAAuB;IACFD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,4BAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC7BX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,mEAAsD;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAKnEX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,4BAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC7BX,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAU,MAAA,YAAI;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAACX,EAAA,CAAAU,MAAA,IAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACpDX,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAACX,EAAA,CAAAU,MAAA,IAAwB;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACvDX,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAU,MAAA,oFAA6D;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEjFX,EAAA,CAAAa,UAAA,KAAAC,8CAAA,kBAKM;IACRd,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,eAA2B;IACOD,EAAA,CAAAE,UAAA,mBAAAa,iEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAS,MAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IACnDlB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,yBAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC9BX,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAU,MAAA,cAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE7BX,EAAA,CAAAC,cAAA,kBAAuD;IAArBD,EAAA,CAAAE,UAAA,mBAAAiB,iEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAI,MAAA,GAAApB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAY,MAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACpDrB,EAAA,CAAAU,MAAA,iCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IApBkBX,EAAA,CAAAsB,SAAA,IAAuB;IAAvBtB,EAAA,CAAAuB,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAC,IAAA,KAAuB;IACrB1B,EAAA,CAAAsB,SAAA,GAAwB;IAAxBtB,EAAA,CAAAuB,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAE,KAAA,KAAwB;IAGxB3B,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA4B,UAAA,SAAAJ,MAAA,CAAAK,WAAA,CAAiB;;;;;;IAoChD7B,EAAA,CAAAC,cAAA,cAAsD;IAExBD,EAAA,CAAAU,MAAA,GAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAG/DX,EAAA,CAAAC,cAAA,cAA4B;IAGxBD,EAAA,CAAAE,UAAA,mBAAA4B,uEAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwB,OAAA,CAAAC,cAAA,CAAe,IAAI,CAAC;IAAA,EAAC;IAE9BjC,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAU,MAAA,aAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAClCX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,UAAG;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGlBX,EAAA,CAAAC,cAAA,kBAGyB;IADvBD,EAAA,CAAAE,UAAA,mBAAAgC,wEAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAI,OAAA,GAAAnC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA2B,OAAA,CAAAF,cAAA,CAAe,KAAK,CAAC;IAAA,EAAC;IAE/BjC,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,cAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAClCX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,WAAG;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAjBQX,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAoC,iBAAA,CAAAC,OAAA,CAAAC,eAAA,CAAAC,QAAA,CAA8B;IAOtDvC,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAA4B,UAAA,aAAAS,OAAA,CAAAG,SAAA,CAAsB;IAQtBxC,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAA4B,UAAA,aAAAS,OAAA,CAAAG,SAAA,CAAsB;;;;;IA/BhCxC,EAAA,CAAAC,cAAA,cAAkE;IAK3BD,EAAA,CAAAU,MAAA,GAA6D;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACnGX,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAU,MAAA,GAA0C;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAErFX,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAY,SAAA,eAAsE;IACxEZ,EAAA,CAAAW,YAAA,EAAM;IAIRX,EAAA,CAAAa,UAAA,KAAA4B,8CAAA,mBAsBM;IACRzC,EAAA,CAAAW,YAAA,EAAM;;;;IAhC+BX,EAAA,CAAAsB,SAAA,GAA6D;IAA7DtB,EAAA,CAAA0C,kBAAA,cAAAC,MAAA,CAAAC,qBAAA,WAAAD,MAAA,CAAAE,cAAA,KAA6D;IAC1D7C,EAAA,CAAAsB,SAAA,GAA0C;IAA1CtB,EAAA,CAAAuB,kBAAA,KAAAvB,EAAA,CAAA8C,WAAA,OAAAH,MAAA,CAAAI,kBAAA,gBAA0C;IAGjD/C,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAgD,WAAA,UAAAL,MAAA,CAAAI,kBAAA,MAAoC;IAKpC/C,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAA4B,UAAA,SAAAe,MAAA,CAAAL,eAAA,CAAqB;;;;;IA2BxDtC,EAAA,CAAAC,cAAA,cAA8C;IAE1CD,EAAA,CAAAY,SAAA,cAAmC;IACnCZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,iCAA0B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACnCX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,yEAAwD;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAC/DX,EAAA,CAAAC,cAAA,cAA2B;IACAD,EAAA,CAAAU,MAAA,GAAiD;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAChFX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,kDAAgC;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC/DX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,2DAAoC;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACnEX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,8DAAkC;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IAHxCX,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAuB,kBAAA,2CAAA0B,MAAA,CAAAC,SAAA,CAAAC,MAAA,SAAiD;;;;;IAyB1EnD,EAAA,CAAAC,cAAA,cAA8D;IACtBD,EAAA,CAAAU,MAAA,gCAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAEnEX,EAAA,CAAAC,cAAA,cAA+D;IAC5BD,EAAA,CAAAU,MAAA,uBAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAqCtDX,EAAA,CAAAC,cAAA,eACiC;IAC/BD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAA6B,kBAAA,MACF;;;;;IAOApD,EAAA,CAAAC,cAAA,SAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAA8B,kBAAA,MACF;;;;;IAOArD,EAAA,CAAAC,cAAA,SAA+D;IAC7DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAA+B,cAAA,MACF;;;;;IALJtD,EAAA,CAAAC,cAAA,cAAmE;IAC7DD,EAAA,CAAAU,MAAA,sCAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC9BX,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAa,UAAA,IAAA0C,0DAAA,iBAEK;IACPvD,EAAA,CAAAW,YAAA,EAAK;;;;IAHwBX,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA4B,UAAA,YAAA4B,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAkC;;;;;IA9CnE1D,EAAA,CAAAC,cAAA,cAA4D;IACtDD,EAAA,CAAAU,MAAA,yCAA6B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAEtCX,EAAA,CAAAC,cAAA,cAA8G;IAGpGD,EAAA,CAAAU,MAAA,GAAuC;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAChDX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAA8C;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEvDX,EAAA,CAAAC,cAAA,eAAiC;IAEHD,EAAA,CAAAU,MAAA,IAA6C;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC9EX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,0BAAa;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAKpDX,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAIXX,EAAA,CAAAC,cAAA,eAAkC;IAC5BD,EAAA,CAAAU,MAAA,2CAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACvCX,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAa,UAAA,KAAA8C,sDAAA,mBAGO;IACT3D,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACxBX,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAa,UAAA,KAAA+C,oDAAA,iBAEK;IACP5D,EAAA,CAAAW,YAAA,EAAK;IAGPX,EAAA,CAAAa,UAAA,KAAAgD,qDAAA,kBAOM;IACR7D,EAAA,CAAAW,YAAA,EAAM;;;;IAhD+BX,EAAA,CAAAsB,SAAA,GAA0E;IAA1EtB,EAAA,CAAA8D,UAAA,CAAAC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,iCAA0E;IAGnGhE,EAAA,CAAAsB,SAAA,GAAuC;IAAvCtB,EAAA,CAAAoC,iBAAA,CAAA2B,OAAA,CAAAN,mBAAA,CAAAQ,QAAA,CAAAvC,IAAA,CAAuC;IACxC1B,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAoC,iBAAA,CAAA2B,OAAA,CAAAN,mBAAA,CAAAQ,QAAA,CAAAC,WAAA,CAA8C;IAGvBlE,EAAA,CAAAsB,SAAA,GAAuE;IAAvEtB,EAAA,CAAA8D,UAAA,CAAAC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,8BAAuE;IACrEhE,EAAA,CAAAsB,SAAA,GAA6C;IAA7CtB,EAAA,CAAAuB,kBAAA,KAAAwC,OAAA,CAAAN,mBAAA,CAAAU,kBAAA,MAA6C;IAOhDnE,EAAA,CAAAsB,SAAA,GAA0E;IAA1EtB,EAAA,CAAA8D,UAAA,CAAAC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,iCAA0E;IACnGhE,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAAwC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,oDACF;IAOiChE,EAAA,CAAAsB,SAAA,GAA+C;IAA/CtB,EAAA,CAAA4B,UAAA,YAAAmC,OAAA,CAAAN,mBAAA,CAAAQ,QAAA,CAAAG,eAAA,CAA+C;IAUjDpE,EAAA,CAAAsB,SAAA,GAAsC;IAAtCtB,EAAA,CAAA4B,UAAA,YAAAmC,OAAA,CAAAN,mBAAA,CAAAY,eAAA,CAAsC;IAM/CrE,EAAA,CAAAsB,SAAA,GAAuC;IAAvCtB,EAAA,CAAA4B,UAAA,UAAAmC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,CAAuC;;;;;IAUnEhE,EAAA,CAAAC,cAAA,cAAiD;IAC3CD,EAAA,CAAAU,MAAA,0DAA+B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACxCX,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAU,MAAA,iGAA2E;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAE7GX,EAAA,CAAAC,cAAA,cAAyB;IAGOD,EAAA,CAAAU,MAAA,0BAAS;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC1CX,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE3EX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA6F;IAC/FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,IAA0C;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC3EX,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAU,MAAA,iDAA+B;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAI1EX,EAAA,CAAAC,cAAA,eAAwE;IAE1CD,EAAA,CAAAU,MAAA,0BAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACzCX,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAU,MAAA,IAAkC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE1EX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA2F;IAC7FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,IAAyC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC1EX,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAU,MAAA,mDAAiC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAI5EX,EAAA,CAAAC,cAAA,eAAyE;IAE3CD,EAAA,CAAAU,MAAA,qBAAQ;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACzCX,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE3EX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA6F;IAC/FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,IAA0C;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC3EX,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAU,MAAA,4CAA+B;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAI1EX,EAAA,CAAAC,cAAA,eAAyE;IAE3CD,EAAA,CAAAU,MAAA,2BAAS;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC1CX,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE3EX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA6F;IAC/FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,IAA0C;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC3EX,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAU,MAAA,+CAA6B;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAK1EX,EAAA,CAAAC,cAAA,eAA2B;IAEHD,EAAA,CAAAU,MAAA,2BAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC9CX,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAU,MAAA,IAAyB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE/DX,EAAA,CAAAC,cAAA,gBAA0B;IACJD,EAAA,CAAAU,MAAA,4BAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC/CX,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAU,MAAA,IAAoC;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAhEpDX,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAsE,WAAA,kBAAAC,OAAA,CAAAC,cAAA,WAAgD;IAGrCxE,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAE,kBAAA,gBAAmC;IAGnCzE,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAgD,WAAA,UAAAuB,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAC,MAAA,gBAAuD;IAG5D5E,EAAA,CAAAsB,SAAA,GAA0C;IAA1CtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAC,MAAA,qBAA0C;IAKhD5E,EAAA,CAAAsB,SAAA,GAA+C;IAA/CtB,EAAA,CAAAsE,WAAA,kBAAAC,OAAA,CAAAC,cAAA,UAA+C;IAGpCxE,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAE,kBAAA,eAAkC;IAGnCzE,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAgD,WAAA,UAAAuB,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAE,KAAA,gBAAsD;IAG1D7E,EAAA,CAAAsB,SAAA,GAAyC;IAAzCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAE,KAAA,qBAAyC;IAK/C7E,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAsE,WAAA,kBAAAC,OAAA,CAAAC,cAAA,WAAgD;IAGrCxE,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAE,kBAAA,gBAAmC;IAGnCzE,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAgD,WAAA,UAAAuB,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAG,MAAA,gBAAuD;IAG5D9E,EAAA,CAAAsB,SAAA,GAA0C;IAA1CtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAG,MAAA,qBAA0C;IAKhD9E,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAsE,WAAA,kBAAAC,OAAA,CAAAC,cAAA,WAAgD;IAGrCxE,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAE,kBAAA,gBAAmC;IAGnCzE,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAgD,WAAA,UAAAuB,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAI,MAAA,gBAAuD;IAG5D/E,EAAA,CAAAsB,SAAA,GAA0C;IAA1CtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAI,MAAA,qBAA0C;IASzC/E,EAAA,CAAAsB,SAAA,GAAyB;IAAzBtB,EAAA,CAAAoC,iBAAA,CAAAmC,OAAA,CAAAS,iBAAA,GAAyB;IAIvBhF,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAU,YAAA,kBAAAV,OAAA,CAAAU,YAAA,CAAAC,eAAA,MAAoC;;;;;IAUvElF,EAAA,CAAAC,cAAA,cAA8C;IACxBD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACzCX,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA5BX,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAAoC,iBAAA,CAAA+C,OAAA,CAAAT,WAAA,kBAAAS,OAAA,CAAAT,WAAA,CAAAU,EAAA,CAAqB;;;;;;IAY3CpF,EAAA,CAAAC,cAAA,iBAAsE;IAAvCD,EAAA,CAAAE,UAAA,mBAAAmF,0EAAA;MAAArF,EAAA,CAAAI,aAAA,CAAAkF,IAAA;MAAA,MAAAC,OAAA,GAAAvF,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+E,OAAA,CAAA9E,MAAA,EAAQ;IAAA,EAAC;IAC/CT,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,0BAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC3BX,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAU,MAAA,mBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAlLpCX,EAAA,CAAAC,cAAA,cAA8D;IAGtCD,EAAA,CAAAU,MAAA,wCAA4B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACnDX,EAAA,CAAAY,SAAA,aAA2B;IAC7BZ,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,cAA6B;IAEAD,EAAA,CAAAU,MAAA,GAA+B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC7DX,EAAA,CAAAC,cAAA,eAA8B;IACFD,EAAA,CAAAU,MAAA,0BAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACnDX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAIxEX,EAAA,CAAAa,UAAA,KAAA2E,8CAAA,kBAEM;IACNxF,EAAA,CAAAa,UAAA,KAAA4E,8CAAA,kBAEM;IACRzF,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,eAAiC;IAC3BD,EAAA,CAAAU,MAAA,mCAA2B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACpCX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,IAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAIvCX,EAAA,CAAAa,UAAA,KAAA6E,8CAAA,oBAmDM;IAEN1F,EAAA,CAAAa,UAAA,KAAA8E,8CAAA,oBAwEM;IAEN3F,EAAA,CAAAC,cAAA,eAA+B;IAEPD,EAAA,CAAAU,MAAA,mCAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5CX,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAU,MAAA,IAA0D;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAEvFX,EAAA,CAAAa,UAAA,KAAA+E,8CAAA,kBAGM;IACR5F,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,eAA6B;IACKD,EAAA,CAAAE,UAAA,mBAAA2F,iEAAA;MAAA7F,EAAA,CAAAI,aAAA,CAAA0F,IAAA;MAAA,MAAAC,OAAA,GAAA/F,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAuF,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACrDhG,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE9BX,EAAA,CAAAC,cAAA,kBAAuD;IAArBD,EAAA,CAAAE,UAAA,mBAAA+F,iEAAA;MAAAjG,EAAA,CAAAI,aAAA,CAAA0F,IAAA;MAAA,MAAAI,OAAA,GAAAlG,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA0F,OAAA,CAAA7E,QAAA,EAAU;IAAA,EAAC;IACpDrB,EAAA,CAAAU,MAAA,iCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAa,UAAA,KAAAsF,iDAAA,qBAGS;IACXnG,EAAA,CAAAW,YAAA,EAAM;;;;IA5KuBX,EAAA,CAAAsB,SAAA,GAA+E;IAA/EtB,EAAA,CAAA8D,UAAA,cAAAsC,MAAA,CAAAnB,YAAA,CAAAoB,YAAA,CAAAC,WAAA,GAAAC,OAAA,UAA+E;IAC/EvG,EAAA,CAAAsB,SAAA,GAA+B;IAA/BtB,EAAA,CAAAoC,iBAAA,CAAAgE,MAAA,CAAAnB,YAAA,CAAAoB,YAAA,CAA+B;IAG5BrG,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAAuB,kBAAA,KAAA6E,MAAA,CAAAnB,YAAA,CAAAC,eAAA,MAAmC;IAItClF,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAA4B,UAAA,SAAAwE,MAAA,CAAAnB,YAAA,CAAAuB,cAAA,CAAiC;IAGjCxG,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA4B,UAAA,UAAAwE,MAAA,CAAAnB,YAAA,CAAAuB,cAAA,CAAkC;IAO1DxG,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAoC,iBAAA,CAAAgE,MAAA,CAAAnB,YAAA,CAAAf,WAAA,CAA8B;IAIFlE,EAAA,CAAAsB,SAAA,GAAyB;IAAzBtB,EAAA,CAAA4B,UAAA,SAAAwE,MAAA,CAAA3C,mBAAA,CAAyB;IAqD5BzD,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA4B,UAAA,SAAAwE,MAAA,CAAA1B,WAAA,CAAiB;IA6EvB1E,EAAA,CAAAsB,SAAA,GAA0D;IAA1DtB,EAAA,CAAAoC,iBAAA,CAAApC,EAAA,CAAA8C,WAAA,SAAAsD,MAAA,CAAA1B,WAAA,kBAAA0B,MAAA,CAAA1B,WAAA,CAAA+B,WAAA,6BAA0D;IAEzDzG,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAA4B,UAAA,SAAAwE,MAAA,CAAA1B,WAAA,kBAAA0B,MAAA,CAAA1B,WAAA,CAAAU,EAAA,CAAqB;IAcOpF,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA4B,UAAA,SAAAwE,MAAA,CAAAvE,WAAA,CAAiB;;;ADxR5E,OAAM,MAAO6E,wBAAwB;EAuDnCC,YACUC,sBAA8C,EAC9CC,wBAAkD,EAClDC,MAAc;IAFd,KAAAF,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,MAAM,GAANA,MAAM;IAzDhB;IACA,KAAAC,SAAS,GAAehH,qBAAqB;IAC7C,KAAAiH,oBAAoB,GAAW,CAAC;IAChC,KAAA9D,SAAS,GAAmB,EAAE;IAC9B,KAAAwB,WAAW,GAAuB,IAAI;IAEtC;IACA,KAAAuC,aAAa,GAAY,KAAK;IAC9B,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAA1E,SAAS,GAAY,KAAK;IAC1B,KAAA2E,WAAW,GAAY,KAAK;IAC5B,KAAAC,gBAAgB,GAAW,EAAE;IAE7B;IACA,KAAAC,WAAW,GAAG,CACZ;MACEjC,EAAE,EAAE,CAAC;MACL1D,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BuC,WAAW,EAAE;KACd,EACD;MACEkB,EAAE,EAAE,CAAC;MACL1D,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,sBAAsB;MAC7BuC,WAAW,EAAE;KACd,EACD;MACEkB,EAAE,EAAE,CAAC;MACL1D,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BuC,WAAW,EAAE;KACd,EACD;MACEkB,EAAE,EAAE,CAAC;MACL1D,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,wBAAwB;MAC/BuC,WAAW,EAAE;KACd,EACD;MACEkB,EAAE,EAAE,CAAC;MACL1D,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BuC,WAAW,EAAE;KACd,CACF;IAED,KAAAzC,YAAY,GAAG,IAAI,CAAC4F,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,KAAAxF,WAAW,GAAQ,IAAI,CAAC,CAAC;IAEzB;IACA,KAAAoD,YAAY,GAA8B,IAAI;IAC9C,KAAAxB,mBAAmB,GAA+B,IAAI;EAMnD;EAEH6D,QAAQA,CAAA;IACN;IACA,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAC3D,IAAIF,eAAe,EAAE;MACnB,IAAI,CAAC1F,WAAW,GAAG6F,IAAI,CAACC,KAAK,CAACJ,eAAe,CAAC;MAC9C;MACA,IAAI,CAAC9F,YAAY,GAAG;QAClB2D,EAAE,EAAE,CAAC;QACL1D,IAAI,EAAE,IAAI,CAACG,WAAW,CAACH,IAAI;QAC3BC,KAAK,EAAE,IAAI,CAACE,WAAW,CAACF,KAAK;QAC7BuC,WAAW,EAAE;OACd;;IAEH,IAAI,CAAC0D,cAAc,EAAE;EACvB;EAEA;;;EAGAA,cAAcA,CAAA;IACZ,IAAI,CAAClD,WAAW,GAAG,IAAI,CAACkC,sBAAsB,CAACiB,iBAAiB,CAC9D,IAAI,CAACpG,YAAY,CAACC,IAAI,EACtB,IAAI,CAACD,YAAY,CAACE,KAAK,CACxB;EACH;EAEA;;;EAGAmG,UAAUA,CAACC,IAAS;IAClB,IAAI,CAACtG,YAAY,GAAGsG,IAAI;IACxB,IAAI,CAACH,cAAc,EAAE;EACvB;EAEA;;;EAGA1G,SAASA,CAAA;IACP,IAAI,CAAC+F,aAAa,GAAG,IAAI;IACzB,IAAI,CAACD,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAAC9D,SAAS,GAAG,EAAE;IACnB,IAAI,CAACkE,gBAAgB,GAAG,UAAU,GAAGY,IAAI,CAACC,GAAG,EAAE,GAAG,GAAG,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAE/F,IAAI,IAAI,CAAC3D,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC4D,SAAS,GAAG,IAAIN,IAAI,EAAE;MACvC,IAAI,CAACtD,WAAW,CAACU,EAAE,GAAG,IAAI,CAACgC,gBAAgB;;IAG7CmB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACpB,gBAAgB,CAAC;EACrE;EAEA;;;EAGAnF,cAAcA,CAACwG,MAAe;IAC5B,IAAI,CAAC,IAAI,CAAC/D,WAAW,EAAE;IAEvB,MAAMpC,eAAe,GAAG,IAAI,CAACyE,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC;IACjE,MAAM0B,QAAQ,GAAiB;MAC7BC,UAAU,EAAErG,eAAe,CAAC8C,EAAE;MAC9BqD,MAAM,EAAEA,MAAM;MACdG,SAAS,EAAE,IAAIZ,IAAI;KACpB;IAED,IAAI,CAAC9E,SAAS,CAAC2F,IAAI,CAACH,QAAQ,CAAC;IAC7B,IAAI,CAAChE,WAAW,CAACxB,SAAS,GAAG,IAAI,CAACA,SAAS;IAE3C;IACA,IAAI,IAAI,CAACrB,WAAW,IAAI,IAAI,CAACuF,gBAAgB,EAAE;MAC7C,IAAI,CAACR,sBAAsB,CAACkC,sBAAsB,CAChD,IAAI,CAACjH,WAAW,CAACF,KAAK,EACtB,IAAI,CAACyF,gBAAgB,EACrBsB,QAAQ,CACT,CAACK,SAAS,CAAC;QACVC,IAAI,EAAGC,OAAO,IAAI;UAChB,IAAIA,OAAO,EAAE;YACXV,OAAO,CAACC,GAAG,CAAC,aAAalG,eAAe,CAAC8C,EAAE,cAAc,CAAC;WAC3D,MAAM;YACLmD,OAAO,CAACW,IAAI,CAAC,gCAAgC5G,eAAe,CAAC8C,EAAE,EAAE,CAAC;;QAEtE,CAAC;QACD+D,KAAK,EAAGA,KAAK,IAAI;UACfZ,OAAO,CAACY,KAAK,CAAC,oBAAoB7G,eAAe,CAAC8C,EAAE,GAAG,EAAE+D,KAAK,CAAC;QACjE;OACD,CAAC;;IAGJ;IACA,IAAI,IAAI,CAACnC,oBAAoB,GAAG,IAAI,CAACD,SAAS,CAAC5D,MAAM,GAAG,CAAC,EAAE;MACzD,IAAI,CAAC6D,oBAAoB,EAAE;KAC5B,MAAM;MACL,IAAI,CAACoC,YAAY,EAAE;;EAEvB;EAEA;;;EAGAA,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC1E,WAAW,EAAE;IAEvB,IAAI,CAAClC,SAAS,GAAG,IAAI;IACrB+F,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACAa,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQA,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC5E,WAAW,EAAE;IAEvB,IAAI;MACF6D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACtF,SAAS,CAACC,MAAM,EAAE,UAAU,CAAC;MAE3E;MACA,MAAMoG,OAAO,GAAG,IAAI,CAAC3C,sBAAsB,CAAC0C,kBAAkB,CAAC,IAAI,CAACpG,SAAS,CAAC;MAC9EqF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEe,OAAO,CAAC;MAE7C;MACA,IAAI,CAAC7E,WAAW,CAACC,MAAM,GAAG4E,OAAO,CAAC5E,MAAM;MACxC,IAAI,CAACD,WAAW,CAACO,YAAY,GAAGsE,OAAO,CAACC,OAAO;MAC/C,IAAI,CAAC9E,WAAW,CAAC+B,WAAW,GAAG,IAAIuB,IAAI,EAAE;MAEzC,IAAI,CAAC/C,YAAY,GAAGsE,OAAO,CAACC,OAAO;MAEnC;MACA,IAAI,CAAC/F,mBAAmB,GAAG,IAAI,CAACoD,wBAAwB,CAAC4C,kBAAkB,CACzEF,OAAO,CAACC,OAAO,EACf,IAAI,CAAC/H,YAAY,CAACE,KAAK,CACxB;MACD4G,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC/E,mBAAmB,CAAC;MAEnE;MACA,MAAMiG,YAAY,GAAG;QACnB7G,cAAc,EAAE,IAAI,CAACkE,SAAS,CAAC5D,MAAM;QACrCwG,cAAc,EAAE,IAAI,CAACzG,SAAS,CAACC,MAAM;QACrCyG,cAAc,EAAG,IAAI,CAAC1G,SAAS,CAACC,MAAM,GAAG,IAAI,CAAC4D,SAAS,CAAC5D,MAAM,GAAI,GAAG;QACrE0G,mBAAmB,EAAE,IAAI,CAACC,4BAA4B,EAAE;QACxDnF,MAAM,EAAE4E,OAAO,CAAC5E,MAAM;QACtB6E,OAAO,EAAED,OAAO,CAACC,OAAO;QACxBO,MAAM,EAAE,IAAI,CAAClI,WAAW,EAAEF,KAAK;QAC/B8E,WAAW,EAAE,IAAIuB,IAAI,EAAE,CAACgC,WAAW;OACpC;MAED;MACA,IAAI,IAAI,CAAC5C,gBAAgB,EAAE;QACzB,IAAI,CAACR,sBAAsB,CAACqD,gBAAgB,CAAC,IAAI,CAAC7C,gBAAgB,EAAEsC,YAAY,CAAC,CAACX,SAAS,CAAC;UAC1FC,IAAI,EAAGC,OAAO,IAAI;YAChBV,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAES,OAAO,CAAC;UAClE,CAAC;UACDE,KAAK,EAAGA,KAAK,IAAI;YACfZ,OAAO,CAACY,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UACpD;SACD,CAAC;;MAGJ;MACA,IAAI,CAACvC,sBAAsB,CAACsD,eAAe,CAAC,IAAI,CAACxF,WAAW,CAAC,CAACqE,SAAS,CAAC;QACtEC,IAAI,EAAGmB,SAAS,IAAI;UAClB5B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2B,SAAS,CAAC;UACvD,IAAI,CAACzF,WAAY,CAACU,EAAE,GAAG+E,SAAS;UAChC,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QACDjB,KAAK,EAAGA,KAAK,IAAI;UACfZ,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,IAAI,CAACiB,oBAAoB,EAAE;QAC7B;OACD,CAAC;KAEH,CAAC,OAAOjB,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,IAAI,CAACiB,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGQA,oBAAoBA,CAAA;IAC1Bf,UAAU,CAAC,MAAK;MACd,IAAI,CAAC7G,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC0E,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACC,WAAW,GAAG,IAAI;MACvBoB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACxC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQsB,4BAA4BA,CAAA;IAClC,IAAI,IAAI,CAAC5G,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAEzC,IAAIkH,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpH,SAAS,CAACC,MAAM,EAAEmH,CAAC,EAAE,EAAE;MAC9C,MAAMC,QAAQ,GAAG,IAAI,CAACrH,SAAS,CAACoH,CAAC,CAAC,CAAC1B,SAAS,CAAC4B,OAAO,EAAE,GAAG,IAAI,CAACtH,SAAS,CAACoH,CAAC,GAAC,CAAC,CAAC,CAAC1B,SAAS,CAAC4B,OAAO,EAAE;MAChGH,SAAS,IAAIE,QAAQ;;IAGvB,OAAOF,SAAS,IAAI,IAAI,CAACnH,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC;EAChD;EAEA;;;EAGA6C,WAAWA,CAAA;IACT,IAAI,CAACiB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACH,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAAC9D,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC+B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACxB,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC2D,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACQ,cAAc,EAAE;EACvB;EAEA;;;EAGAvG,QAAQA,CAAA;IACN,IAAI,CAACyF,MAAM,CAAC2D,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGAhK,MAAMA,CAAA;IACJ+G,YAAY,CAACkD,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAAC5D,MAAM,CAAC2D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEA;;;EAGA,IAAInI,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACyE,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC,IAAI,IAAI;EAC1D;EAEA;;;EAGA,IAAIjE,kBAAkBA,CAAA;IACpB,OAAQ,CAAC,IAAI,CAACiE,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAACD,SAAS,CAAC5D,MAAM,GAAI,GAAG;EACxE;EAEA;;;EAGA,IAAIP,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACoE,oBAAoB,GAAG,CAAC;EACtC;EAEA;;;EAGA,IAAInE,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACkE,SAAS,CAAC5D,MAAM;EAC9B;EAEA;;;EAGAqB,cAAcA,CAACmG,MAAc;IAC3B,IAAI,CAAC,IAAI,CAACjG,WAAW,EAAEC,MAAM,EAAE,OAAO,KAAK;IAE3C,MAAMA,MAAM,GAAG,IAAI,CAACD,WAAW,CAACC,MAAM;IACtC,MAAMiG,YAAY,GAAGjG,MAAM,CAACgG,MAA6B,CAAW;IACpE,MAAME,QAAQ,GAAG3C,IAAI,CAAC4C,GAAG,CAACnG,MAAM,CAACC,MAAM,EAAED,MAAM,CAACE,KAAK,EAAEF,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,MAAM,CAAC;IAEpF,OAAO6F,YAAY,KAAKC,QAAQ,IAAIA,QAAQ,GAAG,CAAC;EAClD;EAEA;;;EAGApG,kBAAkBA,CAACkG,MAAc;IAC/B,IAAI,CAAC,IAAI,CAACjG,WAAW,EAAEC,MAAM,EAAE,OAAO,CAAC;IAEvC,MAAMA,MAAM,GAAG,IAAI,CAACD,WAAW,CAACC,MAAM;IACtC,MAAMoG,KAAK,GAAGpG,MAAM,CAACgG,MAA6B,CAAW;IAE7D,OAAOzC,IAAI,CAAC8C,KAAK,CAAED,KAAK,GAAG,CAAC,GAAI,GAAG,CAAC;EACtC;EAEA;;;EAGA/F,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACN,WAAW,EAAEC,MAAM,EAAE,OAAO,QAAQ;IAE9C,MAAMA,MAAM,GAAG,IAAI,CAACD,WAAW,CAACC,MAAM;IACtC,MAAMsG,QAAQ,GAAG,CACf;MAAEvJ,IAAI,EAAE,WAAW;MAAEqJ,KAAK,EAAEpG,MAAM,CAACC;IAAM,CAAE,EAC3C;MAAElD,IAAI,EAAE,UAAU;MAAEqJ,KAAK,EAAEpG,MAAM,CAACE;IAAK,CAAE,EACzC;MAAEnD,IAAI,EAAE,UAAU;MAAEqJ,KAAK,EAAEpG,MAAM,CAACG;IAAM,CAAE,EAC1C;MAAEpD,IAAI,EAAE,WAAW;MAAEqJ,KAAK,EAAEpG,MAAM,CAACI;IAAM,CAAE,CAC5C;IAED,MAAMmG,QAAQ,GAAGD,QAAQ,CAACE,MAAM,CAAC,CAACC,IAAI,EAAEC,OAAO,KAC7CA,OAAO,CAACN,KAAK,GAAGK,IAAI,CAACL,KAAK,GAAGM,OAAO,GAAGD,IAAI,CAC5C;IAED,OAAOF,QAAQ,CAACxJ,IAAI;EACtB;;;uBAlXWgF,wBAAwB,EAAA1G,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAC,sBAAA,GAAAxL,EAAA,CAAAsL,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAA1L,EAAA,CAAAsL,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBlF,wBAAwB;MAAAmF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBrCnM,EAAA,CAAAC,cAAA,aAAwC;UAEtCD,EAAA,CAAAa,UAAA,IAAAwL,uCAAA,kBA4DM;UAGNrM,EAAA,CAAAa,UAAA,IAAAyL,uCAAA,kBAsCM;UAGNtM,EAAA,CAAAa,UAAA,IAAA0L,uCAAA,kBAYM;UAGNvM,EAAA,CAAAa,UAAA,IAAA2L,uCAAA,mBAsLM;UACRxM,EAAA,CAAAW,YAAA,EAAM;;;UA9SqBX,EAAA,CAAAsB,SAAA,GAAoC;UAApCtB,EAAA,CAAA4B,UAAA,UAAAwK,GAAA,CAAAnF,aAAA,KAAAmF,GAAA,CAAAjF,WAAA,CAAoC;UA+DhCnH,EAAA,CAAAsB,SAAA,GAAmC;UAAnCtB,EAAA,CAAA4B,UAAA,SAAAwK,GAAA,CAAAnF,aAAA,KAAAmF,GAAA,CAAAjF,WAAA,CAAmC;UAyCnCnH,EAAA,CAAAsB,SAAA,GAAe;UAAftB,EAAA,CAAA4B,UAAA,SAAAwK,GAAA,CAAA5J,SAAA,CAAe;UAejBxC,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAA4B,UAAA,SAAAwK,GAAA,CAAAjF,WAAA,IAAAiF,GAAA,CAAAnH,YAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}