{"ast": null, "code": "import { doc, setDoc, getDoc, collection, addDoc } from '@angular/fire/firestore';\nimport { from } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/firestore\";\nexport class IrisImageService {\n  constructor(firestore) {\n    this.firestore = firestore;\n  }\n  /**\n   * Convertit un fichier image en base64\n   */\n  convertImageToBase64(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = () => {\n        const result = reader.result;\n        resolve(result);\n      };\n      reader.onerror = () => reject(reader.error);\n      reader.readAsDataURL(file);\n    });\n  }\n  /**\n   * Valide le fichier image\n   */\n  validateImageFile(file) {\n    // Vérifier le type de fichier\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        isValid: false,\n        error: 'Format non supporté. Utilisez JPG, PNG ou WebP.'\n      };\n    }\n    // Vérifier la taille (max 5MB)\n    const maxSize = 5 * 1024 * 1024; // 5MB\n    if (file.size > maxSize) {\n      return {\n        isValid: false,\n        error: 'L\\'image est trop volumineuse. Maximum 5MB.'\n      };\n    }\n    return {\n      isValid: true\n    };\n  }\n  /**\n   * Obtient les dimensions de l'image\n   */\n  getImageDimensions(file) {\n    return new Promise((resolve, reject) => {\n      const img = new Image();\n      const url = URL.createObjectURL(file);\n      img.onload = () => {\n        URL.revokeObjectURL(url);\n        resolve({\n          width: img.naturalWidth,\n          height: img.naturalHeight\n        });\n      };\n      img.onerror = () => {\n        URL.revokeObjectURL(url);\n        reject(new Error('Impossible de charger l\\'image'));\n      };\n      img.src = url;\n    });\n  }\n  /**\n   * Sauvegarde l'image d'iris dans Firestore\n   */\n  saveIrisImage(irisData) {\n    const irisCollection = collection(this.firestore, 'iris_images');\n    const dataToSave = {\n      ...irisData,\n      uploadedAt: irisData.uploadedAt.toISOString()\n    };\n    return from(addDoc(irisCollection, dataToSave).then(docRef => docRef.id));\n  }\n  /**\n   * Récupère l'image d'iris d'un utilisateur\n   */\n  getUserIrisImage(userEmail) {\n    const irisDoc = doc(this.firestore, 'iris_images', userEmail);\n    return from(getDoc(irisDoc).then(docSnap => {\n      if (docSnap.exists()) {\n        const data = docSnap.data();\n        return {\n          id: docSnap.id,\n          ...data,\n          uploadedAt: new Date(data['uploadedAt'])\n        };\n      }\n      return null;\n    }));\n  }\n  /**\n   * Met à jour l'image d'iris d'un utilisateur\n   */\n  updateUserIrisImage(userEmail, irisData) {\n    const irisDoc = doc(this.firestore, 'iris_images', userEmail);\n    const dataToUpdate = {\n      ...irisData,\n      updatedAt: new Date().toISOString()\n    };\n    return from(setDoc(irisDoc, dataToUpdate, {\n      merge: true\n    }).then(() => true).catch(() => false));\n  }\n  /**\n   * Analyse simulée de l'iris (à remplacer par une vraie IA)\n   */\n  analyzeIrisImage(imageBase64) {\n    return new Promise(resolve => {\n      // Simulation d'analyse avec délai\n      setTimeout(() => {\n        // Analyse simulée basée sur des patterns dans l'image\n        const mockAnalysis = {\n          irisType: this.detectIrisType(imageBase64),\n          characteristics: this.detectCharacteristics(imageBase64),\n          dominantColors: this.detectColors(imageBase64),\n          patterns: this.detectPatterns(imageBase64),\n          confidence: Math.floor(Math.random() * 30) + 70,\n          compatibilityScore: Math.floor(Math.random() * 40) + 60,\n          recommendations: this.generateRecommendations()\n        };\n        resolve(mockAnalysis);\n      }, 2000); // Simulation de 2 secondes d'analyse\n    });\n  }\n  /**\n   * Détection simulée du type d'iris\n   */\n  detectIrisType(imageBase64) {\n    const types = ['Flower', 'Jewel', 'Shaker', 'Stream'];\n    // Simulation basée sur la longueur de l'image\n    const index = imageBase64.length % types.length;\n    return types[index];\n  }\n  /**\n   * Détection simulée des caractéristiques\n   */\n  detectCharacteristics(imageBase64) {\n    const allCharacteristics = ['Fibres radiales prononcées', 'Anneaux concentriques', 'Pigmentation uniforme', 'Cryptes visibles', 'Lacunes périphériques', 'Collerette bien définie', 'Pupille régulière', 'Texture fine'];\n    // Retourner 3-5 caractéristiques aléatoires\n    const count = Math.floor(Math.random() * 3) + 3;\n    const shuffled = allCharacteristics.sort(() => 0.5 - Math.random());\n    return shuffled.slice(0, count);\n  }\n  /**\n   * Détection simulée des couleurs\n   */\n  detectColors(imageBase64) {\n    const colors = [['Bleu', 'Gris'], ['Marron', 'Noisette'], ['Vert', 'Doré'], ['Gris', 'Bleu clair'], ['Marron foncé', 'Ambré']];\n    const index = imageBase64.length % colors.length;\n    return colors[index];\n  }\n  /**\n   * Détection simulée des patterns\n   */\n  detectPatterns(imageBase64) {\n    const patterns = ['Rayons solaires', 'Anneaux de croissance', 'Taches pigmentaires', 'Fibres en éventail', 'Cryptes profondes'];\n    const count = Math.floor(Math.random() * 3) + 2;\n    const shuffled = patterns.sort(() => 0.5 - Math.random());\n    return shuffled.slice(0, count);\n  }\n  /**\n   * Génération de recommandations\n   */\n  generateRecommendations() {\n    const recommendations = ['Privilégiez les activités créatives pour stimuler votre potentiel', 'Développez votre capacité d\\'analyse et de réflexion', 'Explorez des environnements dynamiques et stimulants', 'Cultivez la paix intérieure et la méditation', 'Renforcez vos relations interpersonnelles', 'Travaillez sur votre confiance en vous', 'Explorez de nouveaux défis intellectuels'];\n    const count = Math.floor(Math.random() * 3) + 2;\n    const shuffled = recommendations.sort(() => 0.5 - Math.random());\n    return shuffled.slice(0, count);\n  }\n  static {\n    this.ɵfac = function IrisImageService_Factory(t) {\n      return new (t || IrisImageService)(i0.ɵɵinject(i1.Firestore));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: IrisImageService,\n      factory: IrisImageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["doc", "setDoc", "getDoc", "collection", "addDoc", "from", "IrisImageService", "constructor", "firestore", "convertImageToBase64", "file", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "result", "onerror", "error", "readAsDataURL", "validateImageFile", "allowedTypes", "includes", "type", "<PERSON><PERSON><PERSON><PERSON>", "maxSize", "size", "getImageDimensions", "img", "Image", "url", "URL", "createObjectURL", "revokeObjectURL", "width", "naturalWidth", "height", "naturalHeight", "Error", "src", "saveIrisImage", "irisData", "irisCollection", "dataToSave", "uploadedAt", "toISOString", "then", "doc<PERSON>ef", "id", "getUserIrisImage", "userEmail", "irisDoc", "docSnap", "exists", "data", "Date", "updateUserIrisImage", "dataToUpdate", "updatedAt", "merge", "catch", "analyzeIrisImage", "imageBase64", "setTimeout", "mockAnalysis", "irisType", "detectIrisType", "characteristics", "detectCharacteristics", "dominantColors", "detectColors", "patterns", "detectPatterns", "confidence", "Math", "floor", "random", "compatibilityScore", "recommendations", "generateRecommendations", "types", "index", "length", "allCharacteristics", "count", "shuffled", "sort", "slice", "colors", "i0", "ɵɵinject", "i1", "Firestore", "factory", "ɵfac", "providedIn"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\services\\iris-image.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Firestore, doc, setDoc, getDoc, collection, addDoc } from '@angular/fire/firestore';\nimport { Observable, from } from 'rxjs';\n\nexport interface IrisImageData {\n  id?: string;\n  userEmail: string;\n  userName: string;\n  imageUrl: string;\n  imageBase64: string;\n  uploadedAt: Date;\n  analysisResult?: IrisAnalysisResult;\n  metadata: {\n    fileName: string;\n    fileSize: number;\n    fileType: string;\n    imageWidth?: number;\n    imageHeight?: number;\n  };\n}\n\nexport interface IrisAnalysisResult {\n  irisType: string;\n  characteristics: string[];\n  dominantColors: string[];\n  patterns: string[];\n  confidence: number;\n  compatibilityScore: number;\n  recommendations: string[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class IrisImageService {\n\n  constructor(private firestore: Firestore) {}\n\n  /**\n   * Convertit un fichier image en base64\n   */\n  convertImageToBase64(file: File): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      reader.onload = () => {\n        const result = reader.result as string;\n        resolve(result);\n      };\n      reader.onerror = () => reject(reader.error);\n      reader.readAsDataURL(file);\n    });\n  }\n\n  /**\n   * Valide le fichier image\n   */\n  validateImageFile(file: File): { isValid: boolean; error?: string } {\n    // Vérifier le type de fichier\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return {\n        isValid: false,\n        error: 'Format non supporté. Utilisez JPG, PNG ou WebP.'\n      };\n    }\n\n    // Vérifier la taille (max 5MB)\n    const maxSize = 5 * 1024 * 1024; // 5MB\n    if (file.size > maxSize) {\n      return {\n        isValid: false,\n        error: 'L\\'image est trop volumineuse. Maximum 5MB.'\n      };\n    }\n\n    return { isValid: true };\n  }\n\n  /**\n   * Obtient les dimensions de l'image\n   */\n  getImageDimensions(file: File): Promise<{ width: number; height: number }> {\n    return new Promise((resolve, reject) => {\n      const img = new Image();\n      const url = URL.createObjectURL(file);\n      \n      img.onload = () => {\n        URL.revokeObjectURL(url);\n        resolve({\n          width: img.naturalWidth,\n          height: img.naturalHeight\n        });\n      };\n      \n      img.onerror = () => {\n        URL.revokeObjectURL(url);\n        reject(new Error('Impossible de charger l\\'image'));\n      };\n      \n      img.src = url;\n    });\n  }\n\n  /**\n   * Sauvegarde l'image d'iris dans Firestore\n   */\n  saveIrisImage(irisData: IrisImageData): Observable<string> {\n    const irisCollection = collection(this.firestore, 'iris_images');\n    \n    const dataToSave = {\n      ...irisData,\n      uploadedAt: irisData.uploadedAt.toISOString()\n    };\n\n    return from(addDoc(irisCollection, dataToSave).then(docRef => docRef.id));\n  }\n\n  /**\n   * Récupère l'image d'iris d'un utilisateur\n   */\n  getUserIrisImage(userEmail: string): Observable<IrisImageData | null> {\n    const irisDoc = doc(this.firestore, 'iris_images', userEmail);\n    \n    return from(getDoc(irisDoc).then(docSnap => {\n      if (docSnap.exists()) {\n        const data = docSnap.data();\n        return {\n          id: docSnap.id,\n          ...data,\n          uploadedAt: new Date(data['uploadedAt'])\n        } as IrisImageData;\n      }\n      return null;\n    }));\n  }\n\n  /**\n   * Met à jour l'image d'iris d'un utilisateur\n   */\n  updateUserIrisImage(userEmail: string, irisData: Partial<IrisImageData>): Observable<boolean> {\n    const irisDoc = doc(this.firestore, 'iris_images', userEmail);\n    \n    const dataToUpdate = {\n      ...irisData,\n      updatedAt: new Date().toISOString()\n    };\n\n    return from(setDoc(irisDoc, dataToUpdate, { merge: true }).then(() => true).catch(() => false));\n  }\n\n  /**\n   * Analyse simulée de l'iris (à remplacer par une vraie IA)\n   */\n  analyzeIrisImage(imageBase64: string): Promise<IrisAnalysisResult> {\n    return new Promise((resolve) => {\n      // Simulation d'analyse avec délai\n      setTimeout(() => {\n        // Analyse simulée basée sur des patterns dans l'image\n        const mockAnalysis: IrisAnalysisResult = {\n          irisType: this.detectIrisType(imageBase64),\n          characteristics: this.detectCharacteristics(imageBase64),\n          dominantColors: this.detectColors(imageBase64),\n          patterns: this.detectPatterns(imageBase64),\n          confidence: Math.floor(Math.random() * 30) + 70, // 70-100%\n          compatibilityScore: Math.floor(Math.random() * 40) + 60, // 60-100%\n          recommendations: this.generateRecommendations()\n        };\n        \n        resolve(mockAnalysis);\n      }, 2000); // Simulation de 2 secondes d'analyse\n    });\n  }\n\n  /**\n   * Détection simulée du type d'iris\n   */\n  private detectIrisType(imageBase64: string): string {\n    const types = ['Flower', 'Jewel', 'Shaker', 'Stream'];\n    // Simulation basée sur la longueur de l'image\n    const index = imageBase64.length % types.length;\n    return types[index];\n  }\n\n  /**\n   * Détection simulée des caractéristiques\n   */\n  private detectCharacteristics(imageBase64: string): string[] {\n    const allCharacteristics = [\n      'Fibres radiales prononcées',\n      'Anneaux concentriques',\n      'Pigmentation uniforme',\n      'Cryptes visibles',\n      'Lacunes périphériques',\n      'Collerette bien définie',\n      'Pupille régulière',\n      'Texture fine'\n    ];\n    \n    // Retourner 3-5 caractéristiques aléatoires\n    const count = Math.floor(Math.random() * 3) + 3;\n    const shuffled = allCharacteristics.sort(() => 0.5 - Math.random());\n    return shuffled.slice(0, count);\n  }\n\n  /**\n   * Détection simulée des couleurs\n   */\n  private detectColors(imageBase64: string): string[] {\n    const colors = [\n      ['Bleu', 'Gris'],\n      ['Marron', 'Noisette'],\n      ['Vert', 'Doré'],\n      ['Gris', 'Bleu clair'],\n      ['Marron foncé', 'Ambré']\n    ];\n    \n    const index = imageBase64.length % colors.length;\n    return colors[index];\n  }\n\n  /**\n   * Détection simulée des patterns\n   */\n  private detectPatterns(imageBase64: string): string[] {\n    const patterns = [\n      'Rayons solaires',\n      'Anneaux de croissance',\n      'Taches pigmentaires',\n      'Fibres en éventail',\n      'Cryptes profondes'\n    ];\n    \n    const count = Math.floor(Math.random() * 3) + 2;\n    const shuffled = patterns.sort(() => 0.5 - Math.random());\n    return shuffled.slice(0, count);\n  }\n\n  /**\n   * Génération de recommandations\n   */\n  private generateRecommendations(): string[] {\n    const recommendations = [\n      'Privilégiez les activités créatives pour stimuler votre potentiel',\n      'Développez votre capacité d\\'analyse et de réflexion',\n      'Explorez des environnements dynamiques et stimulants',\n      'Cultivez la paix intérieure et la méditation',\n      'Renforcez vos relations interpersonnelles',\n      'Travaillez sur votre confiance en vous',\n      'Explorez de nouveaux défis intellectuels'\n    ];\n    \n    const count = Math.floor(Math.random() * 3) + 2;\n    const shuffled = recommendations.sort(() => 0.5 - Math.random());\n    return shuffled.slice(0, count);\n  }\n}\n"], "mappings": "AACA,SAAoBA,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,QAAQ,yBAAyB;AAC5F,SAAqBC,IAAI,QAAQ,MAAM;;;AAgCvC,OAAM,MAAOC,gBAAgB;EAE3BC,YAAoBC,SAAoB;IAApB,KAAAA,SAAS,GAATA,SAAS;EAAc;EAE3C;;;EAGAC,oBAAoBA,CAACC,IAAU;IAC7B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,MAAMC,MAAM,GAAGH,MAAM,CAACG,MAAgB;QACtCL,OAAO,CAACK,MAAM,CAAC;MACjB,CAAC;MACDH,MAAM,CAACI,OAAO,GAAG,MAAML,MAAM,CAACC,MAAM,CAACK,KAAK,CAAC;MAC3CL,MAAM,CAACM,aAAa,CAACV,IAAI,CAAC;IAC5B,CAAC,CAAC;EACJ;EAEA;;;EAGAW,iBAAiBA,CAACX,IAAU;IAC1B;IACA,MAAMY,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;IAC3E,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACb,IAAI,CAACc,IAAI,CAAC,EAAE;MACrC,OAAO;QACLC,OAAO,EAAE,KAAK;QACdN,KAAK,EAAE;OACR;;IAGH;IACA,MAAMO,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACjC,IAAIhB,IAAI,CAACiB,IAAI,GAAGD,OAAO,EAAE;MACvB,OAAO;QACLD,OAAO,EAAE,KAAK;QACdN,KAAK,EAAE;OACR;;IAGH,OAAO;MAAEM,OAAO,EAAE;IAAI,CAAE;EAC1B;EAEA;;;EAGAG,kBAAkBA,CAAClB,IAAU;IAC3B,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMgB,GAAG,GAAG,IAAIC,KAAK,EAAE;MACvB,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACvB,IAAI,CAAC;MAErCmB,GAAG,CAACb,MAAM,GAAG,MAAK;QAChBgB,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;QACxBnB,OAAO,CAAC;UACNuB,KAAK,EAAEN,GAAG,CAACO,YAAY;UACvBC,MAAM,EAAER,GAAG,CAACS;SACb,CAAC;MACJ,CAAC;MAEDT,GAAG,CAACX,OAAO,GAAG,MAAK;QACjBc,GAAG,CAACE,eAAe,CAACH,GAAG,CAAC;QACxBlB,MAAM,CAAC,IAAI0B,KAAK,CAAC,gCAAgC,CAAC,CAAC;MACrD,CAAC;MAEDV,GAAG,CAACW,GAAG,GAAGT,GAAG;IACf,CAAC,CAAC;EACJ;EAEA;;;EAGAU,aAAaA,CAACC,QAAuB;IACnC,MAAMC,cAAc,GAAGxC,UAAU,CAAC,IAAI,CAACK,SAAS,EAAE,aAAa,CAAC;IAEhE,MAAMoC,UAAU,GAAG;MACjB,GAAGF,QAAQ;MACXG,UAAU,EAAEH,QAAQ,CAACG,UAAU,CAACC,WAAW;KAC5C;IAED,OAAOzC,IAAI,CAACD,MAAM,CAACuC,cAAc,EAAEC,UAAU,CAAC,CAACG,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,EAAE,CAAC,CAAC;EAC3E;EAEA;;;EAGAC,gBAAgBA,CAACC,SAAiB;IAChC,MAAMC,OAAO,GAAGpD,GAAG,CAAC,IAAI,CAACQ,SAAS,EAAE,aAAa,EAAE2C,SAAS,CAAC;IAE7D,OAAO9C,IAAI,CAACH,MAAM,CAACkD,OAAO,CAAC,CAACL,IAAI,CAACM,OAAO,IAAG;MACzC,IAAIA,OAAO,CAACC,MAAM,EAAE,EAAE;QACpB,MAAMC,IAAI,GAAGF,OAAO,CAACE,IAAI,EAAE;QAC3B,OAAO;UACLN,EAAE,EAAEI,OAAO,CAACJ,EAAE;UACd,GAAGM,IAAI;UACPV,UAAU,EAAE,IAAIW,IAAI,CAACD,IAAI,CAAC,YAAY,CAAC;SACvB;;MAEpB,OAAO,IAAI;IACb,CAAC,CAAC,CAAC;EACL;EAEA;;;EAGAE,mBAAmBA,CAACN,SAAiB,EAAET,QAAgC;IACrE,MAAMU,OAAO,GAAGpD,GAAG,CAAC,IAAI,CAACQ,SAAS,EAAE,aAAa,EAAE2C,SAAS,CAAC;IAE7D,MAAMO,YAAY,GAAG;MACnB,GAAGhB,QAAQ;MACXiB,SAAS,EAAE,IAAIH,IAAI,EAAE,CAACV,WAAW;KAClC;IAED,OAAOzC,IAAI,CAACJ,MAAM,CAACmD,OAAO,EAAEM,YAAY,EAAE;MAAEE,KAAK,EAAE;IAAI,CAAE,CAAC,CAACb,IAAI,CAAC,MAAM,IAAI,CAAC,CAACc,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;EACjG;EAEA;;;EAGAC,gBAAgBA,CAACC,WAAmB;IAClC,OAAO,IAAIpD,OAAO,CAAEC,OAAO,IAAI;MAC7B;MACAoD,UAAU,CAAC,MAAK;QACd;QACA,MAAMC,YAAY,GAAuB;UACvCC,QAAQ,EAAE,IAAI,CAACC,cAAc,CAACJ,WAAW,CAAC;UAC1CK,eAAe,EAAE,IAAI,CAACC,qBAAqB,CAACN,WAAW,CAAC;UACxDO,cAAc,EAAE,IAAI,CAACC,YAAY,CAACR,WAAW,CAAC;UAC9CS,QAAQ,EAAE,IAAI,CAACC,cAAc,CAACV,WAAW,CAAC;UAC1CW,UAAU,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;UAC/CC,kBAAkB,EAAEH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;UACvDE,eAAe,EAAE,IAAI,CAACC,uBAAuB;SAC9C;QAEDpE,OAAO,CAACqD,YAAY,CAAC;MACvB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ;EAEA;;;EAGQE,cAAcA,CAACJ,WAAmB;IACxC,MAAMkB,KAAK,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;IACrD;IACA,MAAMC,KAAK,GAAGnB,WAAW,CAACoB,MAAM,GAAGF,KAAK,CAACE,MAAM;IAC/C,OAAOF,KAAK,CAACC,KAAK,CAAC;EACrB;EAEA;;;EAGQb,qBAAqBA,CAACN,WAAmB;IAC/C,MAAMqB,kBAAkB,GAAG,CACzB,4BAA4B,EAC5B,uBAAuB,EACvB,uBAAuB,EACvB,kBAAkB,EAClB,uBAAuB,EACvB,yBAAyB,EACzB,mBAAmB,EACnB,cAAc,CACf;IAED;IACA,MAAMC,KAAK,GAAGV,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/C,MAAMS,QAAQ,GAAGF,kBAAkB,CAACG,IAAI,CAAC,MAAM,GAAG,GAAGZ,IAAI,CAACE,MAAM,EAAE,CAAC;IACnE,OAAOS,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;EACjC;EAEA;;;EAGQd,YAAYA,CAACR,WAAmB;IACtC,MAAM0B,MAAM,GAAG,CACb,CAAC,MAAM,EAAE,MAAM,CAAC,EAChB,CAAC,QAAQ,EAAE,UAAU,CAAC,EACtB,CAAC,MAAM,EAAE,MAAM,CAAC,EAChB,CAAC,MAAM,EAAE,YAAY,CAAC,EACtB,CAAC,cAAc,EAAE,OAAO,CAAC,CAC1B;IAED,MAAMP,KAAK,GAAGnB,WAAW,CAACoB,MAAM,GAAGM,MAAM,CAACN,MAAM;IAChD,OAAOM,MAAM,CAACP,KAAK,CAAC;EACtB;EAEA;;;EAGQT,cAAcA,CAACV,WAAmB;IACxC,MAAMS,QAAQ,GAAG,CACf,iBAAiB,EACjB,uBAAuB,EACvB,qBAAqB,EACrB,oBAAoB,EACpB,mBAAmB,CACpB;IAED,MAAMa,KAAK,GAAGV,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/C,MAAMS,QAAQ,GAAGd,QAAQ,CAACe,IAAI,CAAC,MAAM,GAAG,GAAGZ,IAAI,CAACE,MAAM,EAAE,CAAC;IACzD,OAAOS,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;EACjC;EAEA;;;EAGQL,uBAAuBA,CAAA;IAC7B,MAAMD,eAAe,GAAG,CACtB,mEAAmE,EACnE,sDAAsD,EACtD,sDAAsD,EACtD,8CAA8C,EAC9C,2CAA2C,EAC3C,wCAAwC,EACxC,0CAA0C,CAC3C;IAED,MAAMM,KAAK,GAAGV,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/C,MAAMS,QAAQ,GAAGP,eAAe,CAACQ,IAAI,CAAC,MAAM,GAAG,GAAGZ,IAAI,CAACE,MAAM,EAAE,CAAC;IAChE,OAAOS,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAEH,KAAK,CAAC;EACjC;;;uBA5NW/E,gBAAgB,EAAAoF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,SAAA;IAAA;EAAA;;;aAAhBvF,gBAAgB;MAAAwF,OAAA,EAAhBxF,gBAAgB,CAAAyF,IAAA;MAAAC,UAAA,EAFf;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}