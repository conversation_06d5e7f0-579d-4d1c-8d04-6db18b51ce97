{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FooterComponent {\n  static {\n    this.ɵfac = function FooterComponent_Factory(t) {\n      return new (t || FooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FooterComponent,\n      selectors: [[\"app-footer\"]],\n      decls: 70,\n      vars: 0,\n      consts: [[1, \"footer\"], [1, \"footer-container\"], [1, \"footer-content\"], [1, \"footer-logo\"], [1, \"footer-sections\"], [1, \"footer-section\"], [\"routerLink\", \"/accueil\"], [\"routerLink\", \"/iris-types\"], [\"routerLink\", \"/iris-diversity\"], [\"routerLink\", \"/dashboard\"], [\"href\", \"#\"], [1, \"footer-bottom\"], [1, \"social-icons\"], [\"href\", \"#\", 1, \"social-icon\"], [1, \"fab\", \"fa-facebook-f\"], [1, \"fab\", \"fa-twitter\"], [1, \"fab\", \"fa-instagram\"], [1, \"fab\", \"fa-linkedin-in\"], [1, \"copyright\"]],\n      template: function FooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\");\n          i0.ɵɵtext(5, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\");\n          i0.ɵɵtext(7, \"\\\"L'avenir du profilage d'iris pour une identification unique\\\"\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 4)(9, \"div\", 5)(10, \"h3\");\n          i0.ɵɵtext(11, \"NAVIGATION\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"ul\")(13, \"li\")(14, \"a\", 6);\n          i0.ɵɵtext(15, \"Accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"li\")(17, \"a\", 7);\n          i0.ɵɵtext(18, \"Types d'Iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"li\")(20, \"a\", 8);\n          i0.ɵɵtext(21, \"Diversit\\u00E9 d'iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"li\")(23, \"a\", 9);\n          i0.ɵɵtext(24, \"Tableau de bord\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 5)(26, \"h3\");\n          i0.ɵɵtext(27, \"RESSOURCES\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"ul\")(29, \"li\")(30, \"a\", 10);\n          i0.ɵɵtext(31, \"Documentation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"li\")(33, \"a\", 10);\n          i0.ɵɵtext(34, \"Tutoriels\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"li\")(36, \"a\", 10);\n          i0.ɵɵtext(37, \"FAQ\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"li\")(39, \"a\", 10);\n          i0.ɵɵtext(40, \"Support\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 5)(42, \"h3\");\n          i0.ɵɵtext(43, \"L\\u00C9GAL\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"ul\")(45, \"li\")(46, \"a\", 10);\n          i0.ɵɵtext(47, \"Conditions d'utilisation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"li\")(49, \"a\", 10);\n          i0.ɵɵtext(50, \"Politique de confidentialit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\")(52, \"a\", 10);\n          i0.ɵɵtext(53, \"Mentions l\\u00E9gales\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"li\")(55, \"a\", 10);\n          i0.ɵɵtext(56, \"Cookies\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(57, \"div\", 11)(58, \"div\", 12)(59, \"a\", 13);\n          i0.ɵɵelement(60, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"a\", 13);\n          i0.ɵɵelement(62, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"a\", 13);\n          i0.ɵɵelement(64, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"a\", 13);\n          i0.ɵɵelement(66, \"i\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 18)(68, \"p\");\n          i0.ɵɵtext(69, \"\\u00A9 2025 IrisLock. Tous droits r\\u00E9serv\\u00E9s.\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      styles: [\".footer[_ngcontent-%COMP%] {\\n  background-color: #f9fafb;\\n  color: #6c757d;\\n  padding: 0;\\n  margin-top: 30px;\\n  border-top: 1px solid #e9ecef;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n  font-size: 0.8rem;\\n}\\n\\n.footer-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n.footer-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  padding: 20px 0 10px;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n\\n.footer-logo[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 200px;\\n  margin-bottom: 10px;\\n}\\n.footer-logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  margin: 0 0 3px 0;\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.footer-logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  margin: 0;\\n  color: #6c757d;\\n  max-width: 250px;\\n  font-style: italic;\\n}\\n\\n.footer-sections[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  flex: 2;\\n  justify-content: flex-end;\\n}\\n\\n.footer-section[_ngcontent-%COMP%] {\\n  margin-left: 40px;\\n  margin-bottom: 10px;\\n}\\n.footer-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n  color: #495057;\\n  letter-spacing: 0.5px;\\n}\\n.footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 5px;\\n}\\n.footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  text-decoration: none;\\n  font-size: 0.75rem;\\n  transition: color 0.2s ease;\\n}\\n.footer-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #007bff;\\n}\\n\\n.footer-bottom[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px 0;\\n}\\n\\n.social-icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.social-icons[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 24px;\\n  height: 24px;\\n  color: #6c757d;\\n  text-decoration: none;\\n  transition: color 0.2s ease;\\n}\\n.social-icons[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.social-icons[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%]:hover {\\n  color: #007bff;\\n}\\n\\n.copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.7rem;\\n  color: #6c757d;\\n}\\n\\n@media (max-width: 768px) {\\n  .footer-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .footer-sections[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: space-between;\\n    margin-top: 15px;\\n  }\\n  .footer-section[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n    margin-right: 15px;\\n  }\\n  .footer-bottom[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .social-icons[_ngcontent-%COMP%] {\\n    margin: 0 auto 10px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .footer-sections[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .footer-section[_ngcontent-%COMP%] {\\n    margin-bottom: 15px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FooterComponent", "selectors", "decls", "vars", "consts", "template", "FooterComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\shared\\footer\\footer.component.ts", "D:\\ProjetPfa\\pfa\\pfa\\src\\app\\shared\\footer\\footer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-footer',\n  templateUrl: './footer.component.html',\n  styleUrls: ['./footer.component.scss']\n})\nexport class FooterComponent {\n\n}\n", "<footer class=\"footer\">\n  <div class=\"footer-container\">\n    <div class=\"footer-content\">\n      <div class=\"footer-logo\">\n        <h2>IrisLock</h2>\n        <p>\"L'avenir du profilage d'iris pour une identification unique\"</p>\n      </div>\n\n      <div class=\"footer-sections\">\n        <div class=\"footer-section\">\n          <h3>NAVIGATION</h3>\n          <ul>\n            <li><a routerLink=\"/accueil\">Accueil</a></li>\n            <li><a routerLink=\"/iris-types\">Types d'Iris</a></li>\n            <li><a routerLink=\"/iris-diversity\">Diversité d'iris</a></li>\n            <li><a routerLink=\"/dashboard\">Tableau de bord</a></li>\n          </ul>\n        </div>\n\n        <div class=\"footer-section\">\n          <h3>RESSOURCES</h3>\n          <ul>\n            <li><a href=\"#\">Documentation</a></li>\n            <li><a href=\"#\">Tutoriels</a></li>\n            <li><a href=\"#\">FAQ</a></li>\n            <li><a href=\"#\">Support</a></li>\n          </ul>\n        </div>\n\n        <div class=\"footer-section\">\n          <h3>LÉGAL</h3>\n          <ul>\n            <li><a href=\"#\">Conditions d'utilisation</a></li>\n            <li><a href=\"#\">Politique de confidentialité</a></li>\n            <li><a href=\"#\">Mentions légales</a></li>\n            <li><a href=\"#\">Cookies</a></li>\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"footer-bottom\">\n      <div class=\"social-icons\">\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-facebook-f\"></i></a>\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-twitter\"></i></a>\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-instagram\"></i></a>\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-linkedin-in\"></i></a>\n      </div>\n\n      <div class=\"copyright\">\n        <p>&copy; 2025 IrisLock. Tous droits réservés.</p>\n      </div>\n    </div>\n  </div>\n</footer>\n"], "mappings": ";AAOA,OAAM,MAAOA,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,gBAAuB;UAIXD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,sEAA6D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGtEH,EAAA,CAAAC,cAAA,aAA6B;UAErBD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,UAAI;UAC2BD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACxCH,EAAA,CAAAC,cAAA,UAAI;UAA4BD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChDH,EAAA,CAAAC,cAAA,UAAI;UAAgCD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACxDH,EAAA,CAAAC,cAAA,UAAI;UAA2BD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAItDH,EAAA,CAAAC,cAAA,cAA4B;UACtBD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,UAAI;UACcD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjCH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7BH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvBH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI/BH,EAAA,CAAAC,cAAA,cAA4B;UACtBD,EAAA,CAAAE,MAAA,kBAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,UAAI;UACcD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,yCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChDH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpCH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMnCH,EAAA,CAAAC,cAAA,eAA2B;UAESD,EAAA,CAAAI,SAAA,aAAiC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACrEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,SAAA,aAA8B;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAClEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,SAAA,aAAgC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACpEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,SAAA,aAAkC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGxEH,EAAA,CAAAC,cAAA,eAAuB;UAClBD,EAAA,CAAAE,MAAA,6DAA2C;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}