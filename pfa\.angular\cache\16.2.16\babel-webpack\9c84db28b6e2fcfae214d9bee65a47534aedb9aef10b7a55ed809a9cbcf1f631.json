{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DashboardComponent {\n  constructor(router) {\n    this.router = router;\n    // Données utilisateur\n    this.userName = '<PERSON>';\n    this.lastScanTime = '10:30';\n    this.securityLevel = 'Élevé';\n    this.profileStatus = 'Vérifié';\n    // Données de l'iris\n    this.irisType = 'Crypte Dominant';\n    this.irisColor = 'Marron';\n    this.uniqueFeatures = 42;\n    this.confidenceScore = '98.7%';\n    // Activités récentes\n    this.recentActivities = [{\n      type: 'scan',\n      title: 'Scan d\\'iris complété',\n      time: 'Aujourd\\'hui à 10:30'\n    }, {\n      type: 'profile',\n      title: 'Profil mis à jour',\n      time: 'Hier à 14:15'\n    }, {\n      type: 'report',\n      title: 'Rapport généré',\n      time: 'Il y a 3 jours à 11:45'\n    }];\n    // Statuts de sécurité\n    this.securityStatuses = [{\n      type: 'data',\n      title: 'Protection des données',\n      description: 'Vos données biométriques sont cryptées'\n    }, {\n      type: 'biometric',\n      title: 'Authentification biométrique',\n      description: 'Activée pour une sécurité renforcée'\n    }, {\n      type: 'compliance',\n      title: 'Conformité RGPD',\n      description: 'Conforme aux réglementations de protection des données'\n    }];\n  }\n  ngOnInit() {\n    // Vérifier si l'utilisateur est connecté (à implémenter avec un service d'authentification)\n    // Si non connecté, rediriger vers la page de connexion\n    // this.checkAuthentication();\n  }\n  // Méthode pour naviguer vers la page de scan d'iris\n  startNewScan() {\n    this.router.navigate(['/scan-iris']);\n  }\n  // Méthode pour changer d'onglet dans le tableau de bord\n  changeTab(tab) {\n    console.log(`Changement vers l'onglet: ${tab}`);\n    // Implémenter la logique de changement d'onglet\n  }\n  // Méthode pour vérifier l'authentification (à implémenter)\n  checkAuthentication() {\n    const isAuthenticated = false; // À remplacer par la vérification réelle\n    if (!isAuthenticated) {\n      this.router.navigate(['/login']);\n    }\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 157,\n      vars: 1,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"welcome-card\"], [1, \"new-scan-btn\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"info-cards\"], [1, \"info-card\", 3, \"click\"], [1, \"card-icon\", \"security\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"card-content\"], [1, \"status\"], [1, \"card-icon\", \"verified\"], [1, \"fas\", \"fa-check-circle\"], [1, \"card-icon\", \"time\"], [1, \"fas\", \"fa-clock\"], [1, \"dashboard-nav\"], [1, \"nav-btn\", \"active\", 3, \"click\"], [1, \"nav-btn\", 3, \"click\"], [1, \"profile-overview\"], [1, \"section-header\"], [1, \"profile-content\"], [1, \"iris-image-container\"], [\"src\", \"assets/iris.png\", \"alt\", \"Iris Scan\", 1, \"iris-image\"], [1, \"verification-badge\"], [1, \"iris-id\"], [1, \"iris-details\"], [1, \"detail-item\"], [1, \"detail-label\"], [1, \"detail-bar\"], [1, \"progress-bar\"], [1, \"detail-value\"], [1, \"verification-info\"], [1, \"verification-label\"], [1, \"verification-time\"], [1, \"verification-status\", \"success\"], [1, \"bottom-sections\"], [1, \"activity-section\"], [1, \"activity-list\"], [1, \"activity-item\"], [1, \"activity-icon\", \"scan\"], [1, \"activity-details\"], [1, \"activity-icon\", \"profile\"], [1, \"fas\", \"fa-user-edit\"], [1, \"activity-icon\", \"report\"], [1, \"fas\", \"fa-file-alt\"], [1, \"security-section\"], [1, \"security-list\"], [1, \"security-item\"], [1, \"security-icon\", \"data\"], [1, \"fas\", \"fa-database\"], [1, \"security-details\"], [1, \"security-icon\", \"biometric\"], [1, \"fas\", \"fa-fingerprint\"], [1, \"security-icon\", \"compliance\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Votre espace personnel de d\\u00E9tection et profilage d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_7_listener() {\n            return ctx.startNewScan();\n          });\n          i0.ɵɵelement(8, \"i\", 4);\n          i0.ɵɵtext(9, \" Nouveau scan d'iris \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_11_listener() {\n            return ctx.showSecurityDetails();\n          });\n          i0.ɵɵelementStart(12, \"div\", 7);\n          i0.ɵɵelement(13, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"h3\");\n          i0.ɵɵtext(16, \"Niveau de s\\u00E9curit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\", 10);\n          i0.ɵɵtext(18, \"\\u00C9lev\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_19_listener() {\n            return ctx.showProfileStatus();\n          });\n          i0.ɵɵelementStart(20, \"div\", 11);\n          i0.ɵɵelement(21, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 9)(23, \"h3\");\n          i0.ɵɵtext(24, \"Statut du profil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 10);\n          i0.ɵɵtext(26, \"V\\u00E9rifi\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_27_listener() {\n            return ctx.showScanHistory();\n          });\n          i0.ɵɵelementStart(28, \"div\", 13);\n          i0.ɵɵelement(29, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"h3\");\n          i0.ɵɵtext(32, \"Dernier scan\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"p\", 10);\n          i0.ɵɵtext(34, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(35, \"div\", 15)(36, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_36_listener() {\n            return ctx.changeTab(\"overview\");\n          });\n          i0.ɵɵtext(37, \"Vue d'ensemble\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_38_listener() {\n            return ctx.changeTab(\"scan\");\n          });\n          i0.ɵɵtext(39, \"Scan d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_40_listener() {\n            return ctx.changeTab(\"profile\");\n          });\n          i0.ɵɵtext(41, \"Mon profil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_42_listener() {\n            return ctx.changeTab(\"history\");\n          });\n          i0.ɵɵtext(43, \"Historique\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 18)(45, \"div\", 19)(46, \"h2\");\n          i0.ɵɵtext(47, \"Aper\\u00E7u de votre profil d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"p\");\n          i0.ɵɵtext(49, \"Caract\\u00E9ristiques principales de votre iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 20)(51, \"div\", 21);\n          i0.ɵɵelement(52, \"img\", 22);\n          i0.ɵɵelementStart(53, \"div\", 23);\n          i0.ɵɵtext(54, \"V\\u00E9rifi\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 24);\n          i0.ɵɵtext(56, \"ID: #A12345678\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 25)(58, \"div\", 26)(59, \"div\", 27);\n          i0.ɵɵtext(60, \"Type de motif:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 28);\n          i0.ɵɵelement(62, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 30);\n          i0.ɵɵtext(64, \"Crypte Dominant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 26)(66, \"div\", 27);\n          i0.ɵɵtext(67, \"Couleur d'iris:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"div\", 28);\n          i0.ɵɵelement(69, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"div\", 30);\n          i0.ɵɵtext(71, \"Marron\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 26)(73, \"div\", 27);\n          i0.ɵɵtext(74, \"Caract\\u00E9ristiques uniques:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"div\", 28);\n          i0.ɵɵelement(76, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\", 30);\n          i0.ɵɵtext(78, \"42\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"div\", 26)(80, \"div\", 27);\n          i0.ɵɵtext(81, \"Score de confiance:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 28);\n          i0.ɵɵelement(83, \"div\", 29);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"div\", 30);\n          i0.ɵɵtext(85, \"98.7%\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"div\", 31)(87, \"div\", 32);\n          i0.ɵɵtext(88, \"Derni\\u00E8re v\\u00E9rification\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"div\", 33);\n          i0.ɵɵtext(90, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"div\", 34);\n          i0.ɵɵelement(92, \"i\", 12);\n          i0.ɵɵtext(93, \" R\\u00E9ussi \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(94, \"div\", 35)(95, \"div\", 36)(96, \"div\", 19)(97, \"h2\");\n          i0.ɵɵtext(98, \"Activit\\u00E9 r\\u00E9cente\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"p\");\n          i0.ɵɵtext(100, \"Votre activit\\u00E9 r\\u00E9cente de scan d'iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(101, \"div\", 37)(102, \"div\", 38)(103, \"div\", 39);\n          i0.ɵɵelement(104, \"i\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"div\", 40)(106, \"h4\");\n          i0.ɵɵtext(107, \"Scan d'iris compl\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"p\");\n          i0.ɵɵtext(109, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(110, \"div\", 38)(111, \"div\", 41);\n          i0.ɵɵelement(112, \"i\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"div\", 40)(114, \"h4\");\n          i0.ɵɵtext(115, \"Profil mis \\u00E0 jour\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"p\");\n          i0.ɵɵtext(117, \"Hier \\u00E0 14:15\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(118, \"div\", 38)(119, \"div\", 43);\n          i0.ɵɵelement(120, \"i\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"div\", 40)(122, \"h4\");\n          i0.ɵɵtext(123, \"Rapport g\\u00E9n\\u00E9r\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"p\");\n          i0.ɵɵtext(125, \"Il y a 3 jours \\u00E0 11:45\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(126, \"div\", 45)(127, \"div\", 19)(128, \"h2\");\n          i0.ɵɵtext(129, \"Statut de s\\u00E9curit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"p\");\n          i0.ɵɵtext(131, \"Aper\\u00E7u de la s\\u00E9curit\\u00E9 de votre compte\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(132, \"div\", 46)(133, \"div\", 47)(134, \"div\", 48);\n          i0.ɵɵelement(135, \"i\", 49);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"div\", 50)(137, \"h4\");\n          i0.ɵɵtext(138, \"Protection des donn\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(139, \"p\");\n          i0.ɵɵtext(140, \"Vos donn\\u00E9es biom\\u00E9triques sont crypt\\u00E9es\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(141, \"div\", 47)(142, \"div\", 51);\n          i0.ɵɵelement(143, \"i\", 52);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(144, \"div\", 50)(145, \"h4\");\n          i0.ɵɵtext(146, \"Authentification biom\\u00E9trique\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"p\");\n          i0.ɵɵtext(148, \"Activ\\u00E9e pour une s\\u00E9curit\\u00E9 renforc\\u00E9e\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(149, \"div\", 47)(150, \"div\", 53);\n          i0.ɵɵelement(151, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"div\", 50)(153, \"h4\");\n          i0.ɵɵtext(154, \"Conformit\\u00E9 RGPD\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"p\");\n          i0.ɵɵtext(156, \"Conforme aux r\\u00E9glementations de protection des donn\\u00E9es\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"Bienvenue, \", ctx.userName, \"\");\n        }\n      },\n      styles: [\"@charset \\\"UTF-8\\\";\\n.dashboard-container[_ngcontent-%COMP%] {\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n  background-color: #f9fbfd;\\n  padding: 20px;\\n  min-height: 100vh;\\n  color: #12263f;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  color: #12263f;\\n  padding: 30px;\\n  border-radius: 8px;\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  border-left: 4px solid #2c7be5;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  width: 30%;\\n  height: 100%;\\n  background: linear-gradient(to right, rgba(44, 123, 229, 0), rgba(44, 123, 229, 0.1));\\n  z-index: 0;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 700;\\n  color: #12263f;\\n  position: relative;\\n  z-index: 1;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #95aac9;\\n  position: relative;\\n  z-index: 1;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%] {\\n  background-color: #2c7be5;\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 6px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  position: relative;\\n  z-index: 1;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  font-size: 1.1rem;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1862c6;\\n  transform: translateY(-2px);\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n\\n.info-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 8px;\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\\n  padding: 20px;\\n  display: flex;\\n  align-items: center;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n  border-bottom: 3px solid transparent;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 1rem 2rem rgba(18, 38, 63, 0.075);\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:nth-child(1) {\\n  border-bottom-color: #2c7be5;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:nth-child(2) {\\n  border-bottom-color: #00d97e;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:nth-child(3) {\\n  border-bottom-color: #f6c343;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1.2rem;\\n  transition: transform 0.2s ease;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.security[_ngcontent-%COMP%] {\\n  background-color: #2c7be5;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.verified[_ngcontent-%COMP%] {\\n  background-color: #00d97e;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.time[_ngcontent-%COMP%] {\\n  background-color: #f6c343;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:hover   .card-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  margin: 0 0 5px 0;\\n  color: #95aac9;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  margin: 0;\\n  color: #12263f;\\n}\\n\\n.dashboard-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 30px;\\n  background-color: #ffffff;\\n  border-radius: 8px;\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\\n  overflow: hidden;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 16px 25px;\\n  font-size: 0.95rem;\\n  font-weight: 600;\\n  color: #95aac9;\\n  cursor: pointer;\\n  position: relative;\\n  transition: all 0.2s ease;\\n  flex: 1;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 1rem;\\n  opacity: 0.7;\\n  transition: opacity 0.2s ease;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 3px;\\n  background-color: #2c7be5;\\n  transform: scaleX(0);\\n  transition: transform 0.2s ease;\\n  transform-origin: center;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover {\\n  color: #12263f;\\n  background-color: rgba(44, 123, 229, 0.05);\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn.active[_ngcontent-%COMP%] {\\n  color: #2c7be5;\\n  background-color: rgba(44, 123, 229, 0.1);\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn.active[_ngcontent-%COMP%]:after {\\n  transform: scaleX(1);\\n}\\n\\n.profile-overview[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 8px;\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\\n  padding: 25px;\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.profile-overview[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  width: 120px;\\n  height: 120px;\\n  background: radial-gradient(circle, rgba(44, 123, 229, 0.1) 0%, rgba(44, 123, 229, 0) 70%);\\n  border-radius: 0 0 0 100%;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n  position: relative;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 700;\\n  color: #12263f;\\n  display: flex;\\n  align-items: center;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 4px;\\n  height: 20px;\\n  background-color: #2c7be5;\\n  margin-right: 10px;\\n  border-radius: 2px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #95aac9;\\n  margin: 0 0 0 14px;\\n  font-size: 0.9rem;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 30px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 220px;\\n  height: 220px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  background-color: #f0f0f0;\\n  box-shadow: 0 0.5rem 1rem rgba(18, 38, 63, 0.1);\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n  cursor: pointer;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.15);\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.5s ease;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]:hover   .iris-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .verification-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 10px;\\n  background-color: #00d97e;\\n  color: white;\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  box-shadow: 0 2px 5px rgba(0, 217, 126, 0.3);\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .verification-badge[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u2713\\\";\\n  margin-right: 5px;\\n  font-weight: bold;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .iris-id[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 10px;\\n  left: 10px;\\n  color: white;\\n  font-size: 0.8rem;\\n  background-color: rgba(18, 38, 63, 0.8);\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  cursor: pointer;\\n  padding: 5px;\\n  border-radius: 6px;\\n  transition: background-color 0.2s ease;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(44, 123, 229, 0.05);\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  width: 180px;\\n  font-size: 0.9rem;\\n  color: #95aac9;\\n  font-weight: 500;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 8px;\\n  background-color: #edf2f9;\\n  border-radius: 4px;\\n  margin: 0 20px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(135deg, #2c7be5, #1a68d1);\\n  width: 100%;\\n  border-radius: 4px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  width: 120px;\\n  text-align: right;\\n  font-weight: 700;\\n  color: #12263f;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  padding: 15px;\\n  border-radius: 6px;\\n  background-color: #edf2f9;\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]:hover {\\n  background-color: #e5ecf6;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-label[_ngcontent-%COMP%] {\\n  color: #95aac9;\\n  font-size: 0.9rem;\\n  margin-right: 15px;\\n  font-weight: 500;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-time[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-right: 20px;\\n  color: #12263f;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: flex;\\n  align-items: center;\\n  font-weight: 600;\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status.success[_ngcontent-%COMP%] {\\n  color: white;\\n  background-color: #00d97e;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n.bottom-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 30px;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 8px;\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\\n  padding: 25px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n  position: relative;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 700;\\n  color: #12263f;\\n  display: flex;\\n  align-items: center;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before, .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 4px;\\n  height: 20px;\\n  background-color: #2c7be5;\\n  margin-right: 10px;\\n  border-radius: 2px;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #95aac9;\\n  margin: 0 0 0 14px;\\n  font-size: 0.9rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 15px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  margin-bottom: 10px;\\n  border-left: 3px solid transparent;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f9fbfd;\\n  transform: translateX(5px);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(1) {\\n  border-left-color: #6e00ff;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(1):hover {\\n  background-color: rgba(110, 0, 255, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(2) {\\n  border-left-color: #ff5c8d;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(2):hover {\\n  background-color: rgba(255, 92, 141, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(3) {\\n  border-left-color: #f6c343;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(3):hover {\\n  background-color: rgba(246, 195, 67, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%] {\\n  width: 42px;\\n  height: 42px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1rem;\\n  transition: transform 0.2s ease;\\n  box-shadow: 0 0.25rem 0.5rem rgba(18, 38, 63, 0.1);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.scan[_ngcontent-%COMP%] {\\n  background-color: #6e00ff;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.profile[_ngcontent-%COMP%] {\\n  background-color: #ff5c8d;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.report[_ngcontent-%COMP%] {\\n  background-color: #f6c343;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:hover   .activity-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #12263f;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #95aac9;\\n  font-size: 0.85rem;\\n  display: flex;\\n  align-items: center;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDD52\\\";\\n  margin-right: 5px;\\n  font-size: 0.8rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 15px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  margin-bottom: 10px;\\n  border-left: 3px solid transparent;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f9fbfd;\\n  transform: translateX(5px);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(1) {\\n  border-left-color: #00d97e;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(1):hover {\\n  background-color: rgba(0, 217, 126, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(2) {\\n  border-left-color: #6e00ff;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(2):hover {\\n  background-color: rgba(110, 0, 255, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(3) {\\n  border-left-color: #2c7be5;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(3):hover {\\n  background-color: rgba(44, 123, 229, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon[_ngcontent-%COMP%] {\\n  width: 42px;\\n  height: 42px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1rem;\\n  transition: transform 0.2s ease;\\n  box-shadow: 0 0.25rem 0.5rem rgba(18, 38, 63, 0.1);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.data[_ngcontent-%COMP%] {\\n  background-color: #00d97e;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.biometric[_ngcontent-%COMP%] {\\n  background-color: #6e00ff;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.compliance[_ngcontent-%COMP%] {\\n  background-color: #2c7be5;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:hover   .security-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #12263f;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #95aac9;\\n  font-size: 0.85rem;\\n}\\n\\n@media (max-width: 1200px) {\\n  .profile-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%] {\\n    margin: 0 auto 30px;\\n    width: 280px;\\n    height: 280px;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .bottom-sections[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 20px;\\n  }\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .info-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n    white-space: nowrap;\\n    padding: 5px;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    padding: 12px 15px;\\n    font-size: 0.85rem;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 5px;\\n  }\\n  .profile-overview[_ngcontent-%COMP%], .activity-section[_ngcontent-%COMP%], .security-section[_ngcontent-%COMP%] {\\n    padding: 20px 15px;\\n  }\\n  .detail-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    margin-bottom: 8px;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    margin: 8px 0 !important;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    text-align: left !important;\\n    margin-top: 8px;\\n  }\\n  .verification-info[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .verification-info[_ngcontent-%COMP%]   .verification-label[_ngcontent-%COMP%], .verification-info[_ngcontent-%COMP%]   .verification-time[_ngcontent-%COMP%] {\\n    margin-bottom: 10px;\\n    margin-right: 0 !important;\\n  }\\n  .verification-info[_ngcontent-%COMP%]   .verification-status[_ngcontent-%COMP%] {\\n    margin-left: 0 !important;\\n    align-self: flex-start;\\n  }\\n  .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.2rem !important;\\n  }\\n  .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.85rem !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .welcome-card[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem !important;\\n  }\\n  .welcome-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.9rem !important;\\n  }\\n  .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n    padding: 12px 15px !important;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    padding: 10px !important;\\n    font-size: 0.8rem !important;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 3px !important;\\n  }\\n  .iris-image-container[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    height: 220px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DashboardComponent", "constructor", "router", "userName", "lastScanTime", "securityLevel", "profileStatus", "irisType", "irisColor", "uniqueFeatures", "confidenceScore", "recentActivities", "type", "title", "time", "securityStatuses", "description", "ngOnInit", "startNewScan", "navigate", "changeTab", "tab", "console", "log", "checkAuthentication", "isAuthenticated", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DashboardComponent_Template_button_click_7_listener", "ɵɵelement", "DashboardComponent_Template_div_click_11_listener", "showSecurityDetails", "DashboardComponent_Template_div_click_19_listener", "showProfileStatus", "DashboardComponent_Template_div_click_27_listener", "showScanHistory", "DashboardComponent_Template_button_click_36_listener", "DashboardComponent_Template_button_click_38_listener", "DashboardComponent_Template_button_click_40_listener", "DashboardComponent_Template_button_click_42_listener", "ɵɵadvance", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  // Données utilisateur\n  userName: string = '<PERSON>';\n  lastScanTime: string = '10:30';\n  securityLevel: string = 'Élevé';\n  profileStatus: string = 'Vérifié';\n\n  // Données de l'iris\n  irisType: string = 'Crypte Dominant';\n  irisColor: string = 'Marron';\n  uniqueFeatures: number = 42;\n  confidenceScore: string = '98.7%';\n\n  // Activités récentes\n  recentActivities = [\n    { type: 'scan', title: 'Scan d\\'iris complété', time: 'Aujourd\\'hui à 10:30' },\n    { type: 'profile', title: 'Profil mis à jour', time: 'Hier à 14:15' },\n    { type: 'report', title: 'Rapport généré', time: 'Il y a 3 jours à 11:45' }\n  ];\n\n  // Statuts de sécurité\n  securityStatuses = [\n    { type: 'data', title: 'Protection des données', description: 'Vos données biométriques sont cryptées' },\n    { type: 'biometric', title: 'Authentification biométrique', description: 'Activée pour une sécurité renforcée' },\n    { type: 'compliance', title: 'Conformité RGPD', description: 'Conforme aux réglementations de protection des données' }\n  ];\n\n  constructor(private router: Router) { }\n\n  ngOnInit(): void {\n    // Vérifier si l'utilisateur est connecté (à implémenter avec un service d'authentification)\n    // Si non connecté, rediriger vers la page de connexion\n    // this.checkAuthentication();\n  }\n\n  // Méthode pour naviguer vers la page de scan d'iris\n  startNewScan(): void {\n    this.router.navigate(['/scan-iris']);\n  }\n\n  // Méthode pour changer d'onglet dans le tableau de bord\n  changeTab(tab: string): void {\n    console.log(`Changement vers l'onglet: ${tab}`);\n    // Implémenter la logique de changement d'onglet\n  }\n\n  // Méthode pour vérifier l'authentification (à implémenter)\n  private checkAuthentication(): void {\n    const isAuthenticated = false; // À remplacer par la vérification réelle\n\n    if (!isAuthenticated) {\n      this.router.navigate(['/login']);\n    }\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- En-tête du tableau de bord -->\n  <div class=\"dashboard-header\">\n    <div class=\"welcome-card\">\n      <h1>Bienvenue, {{ userName }}</h1>\n      <p>Votre espace personnel de détection et profilage d'iris</p>\n      <button class=\"new-scan-btn\" (click)=\"startNewScan()\">\n        <i class=\"fas fa-eye\"></i>\n        Nouveau scan d'iris\n      </button>\n    </div>\n  </div>\n\n  <!-- Cartes d'information -->\n  <div class=\"info-cards\">\n    <div class=\"info-card\" (click)=\"showSecurityDetails()\">\n      <div class=\"card-icon security\">\n        <i class=\"fas fa-shield-alt\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Niveau de sécurité</h3>\n        <p class=\"status\">Élevé</p>\n      </div>\n    </div>\n\n    <div class=\"info-card\" (click)=\"showProfileStatus()\">\n      <div class=\"card-icon verified\">\n        <i class=\"fas fa-check-circle\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Statut du profil</h3>\n        <p class=\"status\">Vérifié</p>\n      </div>\n    </div>\n\n    <div class=\"info-card\" (click)=\"showScanHistory()\">\n      <div class=\"card-icon time\">\n        <i class=\"fas fa-clock\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Dernier scan</h3>\n        <p class=\"status\">Aujourd'hui à 10:30</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- Navigation du tableau de bord -->\n  <div class=\"dashboard-nav\">\n    <button class=\"nav-btn active\" (click)=\"changeTab('overview')\">Vue d'ensemble</button>\n    <button class=\"nav-btn\" (click)=\"changeTab('scan')\">Scan d'iris</button>\n    <button class=\"nav-btn\" (click)=\"changeTab('profile')\">Mon profil</button>\n    <button class=\"nav-btn\" (click)=\"changeTab('history')\">Historique</button>\n  </div>\n\n  <!-- Aperçu du profil d'iris -->\n  <div class=\"profile-overview\">\n    <div class=\"section-header\">\n      <h2>Aperçu de votre profil d'iris</h2>\n      <p>Caractéristiques principales de votre iris</p>\n    </div>\n\n    <div class=\"profile-content\">\n      <div class=\"iris-image-container\">\n        <img src=\"assets/iris.png\" alt=\"Iris Scan\" class=\"iris-image\">\n        <div class=\"verification-badge\">Vérifié</div>\n        <div class=\"iris-id\">ID: #A12345678</div>\n      </div>\n\n      <div class=\"iris-details\">\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Type de motif:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">Crypte Dominant</div>\n        </div>\n\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Couleur d'iris:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">Marron</div>\n        </div>\n\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Caractéristiques uniques:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">42</div>\n        </div>\n\n        <div class=\"detail-item\">\n          <div class=\"detail-label\">Score de confiance:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">98.7%</div>\n        </div>\n\n        <div class=\"verification-info\">\n          <div class=\"verification-label\">Dernière vérification</div>\n          <div class=\"verification-time\">Aujourd'hui à 10:30</div>\n          <div class=\"verification-status success\">\n            <i class=\"fas fa-check-circle\"></i> Réussi\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Section inférieure avec deux colonnes -->\n  <div class=\"bottom-sections\">\n    <!-- Activité récente -->\n    <div class=\"activity-section\">\n      <div class=\"section-header\">\n        <h2>Activité récente</h2>\n        <p>Votre activité récente de scan d'iris</p>\n      </div>\n\n      <div class=\"activity-list\">\n        <div class=\"activity-item\">\n          <div class=\"activity-icon scan\">\n            <i class=\"fas fa-eye\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Scan d'iris complété</h4>\n            <p>Aujourd'hui à 10:30</p>\n          </div>\n        </div>\n\n        <div class=\"activity-item\">\n          <div class=\"activity-icon profile\">\n            <i class=\"fas fa-user-edit\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Profil mis à jour</h4>\n            <p>Hier à 14:15</p>\n          </div>\n        </div>\n\n        <div class=\"activity-item\">\n          <div class=\"activity-icon report\">\n            <i class=\"fas fa-file-alt\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Rapport généré</h4>\n            <p>Il y a 3 jours à 11:45</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statut de sécurité -->\n    <div class=\"security-section\">\n      <div class=\"section-header\">\n        <h2>Statut de sécurité</h2>\n        <p>Aperçu de la sécurité de votre compte</p>\n      </div>\n\n      <div class=\"security-list\">\n        <div class=\"security-item\">\n          <div class=\"security-icon data\">\n            <i class=\"fas fa-database\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Protection des données</h4>\n            <p>Vos données biométriques sont cryptées</p>\n          </div>\n        </div>\n\n        <div class=\"security-item\">\n          <div class=\"security-icon biometric\">\n            <i class=\"fas fa-fingerprint\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Authentification biométrique</h4>\n            <p>Activée pour une sécurité renforcée</p>\n          </div>\n        </div>\n\n        <div class=\"security-item\">\n          <div class=\"security-icon compliance\">\n            <i class=\"fas fa-shield-alt\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Conformité RGPD</h4>\n            <p>Conforme aux réglementations de protection des données</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAQA,OAAM,MAAOA,kBAAkB;EA2B7BC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IA1B1B;IACA,KAAAC,QAAQ,GAAW,aAAa;IAChC,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAC,aAAa,GAAW,OAAO;IAC/B,KAAAC,aAAa,GAAW,SAAS;IAEjC;IACA,KAAAC,QAAQ,GAAW,iBAAiB;IACpC,KAAAC,SAAS,GAAW,QAAQ;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,eAAe,GAAW,OAAO;IAEjC;IACA,KAAAC,gBAAgB,GAAG,CACjB;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,uBAAuB;MAAEC,IAAI,EAAE;IAAsB,CAAE,EAC9E;MAAEF,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,IAAI,EAAE;IAAc,CAAE,EACrE;MAAEF,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAwB,CAAE,CAC5E;IAED;IACA,KAAAC,gBAAgB,GAAG,CACjB;MAAEH,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,wBAAwB;MAAEG,WAAW,EAAE;IAAwC,CAAE,EACxG;MAAEJ,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,8BAA8B;MAAEG,WAAW,EAAE;IAAqC,CAAE,EAChH;MAAEJ,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,iBAAiB;MAAEG,WAAW,EAAE;IAAwD,CAAE,CACxH;EAEqC;EAEtCC,QAAQA,CAAA;IACN;IACA;IACA;EAAA;EAGF;EACAC,YAAYA,CAAA;IACV,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEA;EACAC,SAASA,CAACC,GAAW;IACnBC,OAAO,CAACC,GAAG,CAAC,6BAA6BF,GAAG,EAAE,CAAC;IAC/C;EACF;EAEA;EACQG,mBAAmBA,CAAA;IACzB,MAAMC,eAAe,GAAG,KAAK,CAAC,CAAC;IAE/B,IAAI,CAACA,eAAe,EAAE;MACpB,IAAI,CAACvB,MAAM,CAACiB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;EAEpC;;;uBArDWnB,kBAAkB,EAAA0B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlB7B,kBAAkB;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR/BV,EAAA,CAAAY,cAAA,aAAiC;UAIvBZ,EAAA,CAAAa,MAAA,GAAyB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAClCd,EAAA,CAAAY,cAAA,QAAG;UAAAZ,EAAA,CAAAa,MAAA,mEAAuD;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAC9Dd,EAAA,CAAAY,cAAA,gBAAsD;UAAzBZ,EAAA,CAAAe,UAAA,mBAAAC,oDAAA;YAAA,OAASL,GAAA,CAAAnB,YAAA,EAAc;UAAA,EAAC;UACnDQ,EAAA,CAAAiB,SAAA,WAA0B;UAC1BjB,EAAA,CAAAa,MAAA,4BACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAKbd,EAAA,CAAAY,cAAA,cAAwB;UACCZ,EAAA,CAAAe,UAAA,mBAAAG,kDAAA;YAAA,OAASP,GAAA,CAAAQ,mBAAA,EAAqB;UAAA,EAAC;UACpDnB,EAAA,CAAAY,cAAA,cAAgC;UAC9BZ,EAAA,CAAAiB,SAAA,YAAiC;UACnCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,cAA0B;UACpBZ,EAAA,CAAAa,MAAA,oCAAkB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC3Bd,EAAA,CAAAY,cAAA,aAAkB;UAAAZ,EAAA,CAAAa,MAAA,uBAAK;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI/Bd,EAAA,CAAAY,cAAA,cAAqD;UAA9BZ,EAAA,CAAAe,UAAA,mBAAAK,kDAAA;YAAA,OAAST,GAAA,CAAAU,iBAAA,EAAmB;UAAA,EAAC;UAClDrB,EAAA,CAAAY,cAAA,eAAgC;UAC9BZ,EAAA,CAAAiB,SAAA,aAAmC;UACrCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,cAA0B;UACpBZ,EAAA,CAAAa,MAAA,wBAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACzBd,EAAA,CAAAY,cAAA,aAAkB;UAAAZ,EAAA,CAAAa,MAAA,yBAAO;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAIjCd,EAAA,CAAAY,cAAA,cAAmD;UAA5BZ,EAAA,CAAAe,UAAA,mBAAAO,kDAAA;YAAA,OAASX,GAAA,CAAAY,eAAA,EAAiB;UAAA,EAAC;UAChDvB,EAAA,CAAAY,cAAA,eAA4B;UAC1BZ,EAAA,CAAAiB,SAAA,aAA4B;UAC9BjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,cAA0B;UACpBZ,EAAA,CAAAa,MAAA,oBAAY;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrBd,EAAA,CAAAY,cAAA,aAAkB;UAAAZ,EAAA,CAAAa,MAAA,gCAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAM/Cd,EAAA,CAAAY,cAAA,eAA2B;UACMZ,EAAA,CAAAe,UAAA,mBAAAS,qDAAA;YAAA,OAASb,GAAA,CAAAjB,SAAA,CAAU,UAAU,CAAC;UAAA,EAAC;UAACM,EAAA,CAAAa,MAAA,sBAAc;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACtFd,EAAA,CAAAY,cAAA,kBAAoD;UAA5BZ,EAAA,CAAAe,UAAA,mBAAAU,qDAAA;YAAA,OAASd,GAAA,CAAAjB,SAAA,CAAU,MAAM,CAAC;UAAA,EAAC;UAACM,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACxEd,EAAA,CAAAY,cAAA,kBAAuD;UAA/BZ,EAAA,CAAAe,UAAA,mBAAAW,qDAAA;YAAA,OAASf,GAAA,CAAAjB,SAAA,CAAU,SAAS,CAAC;UAAA,EAAC;UAACM,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAC1Ed,EAAA,CAAAY,cAAA,kBAAuD;UAA/BZ,EAAA,CAAAe,UAAA,mBAAAY,qDAAA;YAAA,OAAShB,GAAA,CAAAjB,SAAA,CAAU,SAAS,CAAC;UAAA,EAAC;UAACM,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAI5Ed,EAAA,CAAAY,cAAA,eAA8B;UAEtBZ,EAAA,CAAAa,MAAA,0CAA6B;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtCd,EAAA,CAAAY,cAAA,SAAG;UAAAZ,EAAA,CAAAa,MAAA,uDAA0C;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAGnDd,EAAA,CAAAY,cAAA,eAA6B;UAEzBZ,EAAA,CAAAiB,SAAA,eAA8D;UAC9DjB,EAAA,CAAAY,cAAA,eAAgC;UAAAZ,EAAA,CAAAa,MAAA,yBAAO;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC7Cd,EAAA,CAAAY,cAAA,eAAqB;UAAAZ,EAAA,CAAAa,MAAA,sBAAc;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAG3Cd,EAAA,CAAAY,cAAA,eAA0B;UAEIZ,EAAA,CAAAa,MAAA,sBAAc;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC9Cd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,uBAAe;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGjDd,EAAA,CAAAY,cAAA,eAAyB;UACGZ,EAAA,CAAAa,MAAA,uBAAe;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC/Cd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,cAAM;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGxCd,EAAA,CAAAY,cAAA,eAAyB;UACGZ,EAAA,CAAAa,MAAA,sCAAyB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACzDd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,UAAE;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGpCd,EAAA,CAAAY,cAAA,eAAyB;UACGZ,EAAA,CAAAa,MAAA,2BAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACnDd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGvCd,EAAA,CAAAY,cAAA,eAA+B;UACGZ,EAAA,CAAAa,MAAA,uCAAqB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC3Dd,EAAA,CAAAY,cAAA,eAA+B;UAAAZ,EAAA,CAAAa,MAAA,gCAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACxDd,EAAA,CAAAY,cAAA,eAAyC;UACvCZ,EAAA,CAAAiB,SAAA,aAAmC;UAACjB,EAAA,CAAAa,MAAA,qBACtC;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAOdd,EAAA,CAAAY,cAAA,eAA6B;UAInBZ,EAAA,CAAAa,MAAA,kCAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACzBd,EAAA,CAAAY,cAAA,SAAG;UAAAZ,EAAA,CAAAa,MAAA,wDAAqC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAG9Cd,EAAA,CAAAY,cAAA,gBAA2B;UAGrBZ,EAAA,CAAAiB,SAAA,aAA0B;UAC5BjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,uCAAoB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC7Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,iCAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI9Bd,EAAA,CAAAY,cAAA,gBAA2B;UAEvBZ,EAAA,CAAAiB,SAAA,cAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,+BAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC1Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,0BAAY;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAIvBd,EAAA,CAAAY,cAAA,gBAA2B;UAEvBZ,EAAA,CAAAiB,SAAA,cAA+B;UACjCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,sCAAc;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,oCAAsB;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAOrCd,EAAA,CAAAY,cAAA,gBAA8B;UAEtBZ,EAAA,CAAAa,MAAA,qCAAkB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC3Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,6DAAqC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAG9Cd,EAAA,CAAAY,cAAA,gBAA2B;UAGrBZ,EAAA,CAAAiB,SAAA,cAA+B;UACjCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,oCAAsB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,8DAAsC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAIjDd,EAAA,CAAAY,cAAA,gBAA2B;UAEvBZ,EAAA,CAAAiB,SAAA,cAAkC;UACpCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,0CAA4B;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,gEAAmC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI9Cd,EAAA,CAAAY,cAAA,gBAA2B;UAEvBZ,EAAA,CAAAiB,SAAA,aAAiC;UACnCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,6BAAe;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACxBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,yEAAsD;UAAAb,EAAA,CAAAc,YAAA,EAAI;;;UAxL/Dd,EAAA,CAAA4B,SAAA,GAAyB;UAAzB5B,EAAA,CAAA6B,kBAAA,gBAAAlB,GAAA,CAAAlC,QAAA,KAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}