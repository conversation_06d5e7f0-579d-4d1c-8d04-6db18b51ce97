{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class IrisDiversityComponent {\n  static {\n    this.ɵfac = function IrisDiversityComponent_Factory(t) {\n      return new (t || IrisDiversityComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IrisDiversityComponent,\n      selectors: [[\"app-iris-diversity\"]],\n      decls: 71,\n      vars: 0,\n      consts: [[1, \"page-container\", \"iris-diversity\"], [1, \"container\"], [1, \"navbar\"], [1, \"logo\"], [1, \"nav-links\"], [\"routerLink\", \"/accueil\", \"routerLinkActive\", \"active\"], [\"href\", \"#\"], [1, \"auth-buttons\"], [\"routerLink\", \"/login\", 1, \"login-button\"], [\"routerLink\", \"/signup\", 1, \"register-button\"], [1, \"content\"], [1, \"card\", \"main-card\"], [1, \"card-header\"], [1, \"divider\"], [1, \"iris-diversity-content\"], [1, \"iris-circle-container\"], [1, \"iris-circle\"], [1, \"iris-image\", 2, \"transform\", \"rotate(0deg) translateX(180px) rotate(0deg)\"], [\"src\", \"assets/iris-samples/iris1.png\", \"alt\", \"Iris type 1\"], [1, \"iris-image\", 2, \"transform\", \"rotate(30deg) translateX(180px) rotate(-30deg)\"], [\"src\", \"assets/iris-samples/iris2.png\", \"alt\", \"Iris type 2\"], [1, \"iris-image\", 2, \"transform\", \"rotate(60deg) translateX(180px) rotate(-60deg)\"], [\"src\", \"assets/iris-samples/iris3.png\", \"alt\", \"Iris type 3\"], [1, \"iris-image\", 2, \"transform\", \"rotate(90deg) translateX(180px) rotate(-90deg)\"], [\"src\", \"assets/iris-samples/iris4.png\", \"alt\", \"Iris type 4\"], [1, \"iris-image\", 2, \"transform\", \"rotate(120deg) translateX(180px) rotate(-120deg)\"], [\"src\", \"assets/iris-samples/iris5.png\", \"alt\", \"Iris type 5\"], [1, \"iris-image\", 2, \"transform\", \"rotate(150deg) translateX(180px) rotate(-150deg)\"], [\"src\", \"assets/iris-samples/iris6.png\", \"alt\", \"Iris type 6\"], [1, \"iris-image\", 2, \"transform\", \"rotate(180deg) translateX(180px) rotate(-180deg)\"], [\"src\", \"assets/iris-samples/iris7.png\", \"alt\", \"Iris type 7\"], [1, \"iris-image\", 2, \"transform\", \"rotate(210deg) translateX(180px) rotate(-210deg)\"], [\"src\", \"assets/iris-samples/iris8.png\", \"alt\", \"Iris type 8\"], [1, \"iris-image\", 2, \"transform\", \"rotate(240deg) translateX(180px) rotate(-240deg)\"], [\"src\", \"assets/iris-samples/iris9.png\", \"alt\", \"Iris type 9\"], [1, \"iris-image\", 2, \"transform\", \"rotate(270deg) translateX(180px) rotate(-270deg)\"], [\"src\", \"assets/iris-samples/iris10.png\", \"alt\", \"Iris type 10\"], [1, \"iris-image\", 2, \"transform\", \"rotate(300deg) translateX(180px) rotate(-300deg)\"], [\"src\", \"assets/iris-samples/iris11.png\", \"alt\", \"Iris type 11\"], [1, \"iris-image\", 2, \"transform\", \"rotate(330deg) translateX(180px) rotate(-330deg)\"], [\"src\", \"assets/iris-samples/iris12.png\", \"alt\", \"Iris type 12\"], [1, \"text-sections\"], [1, \"text-section\", \"left\"], [1, \"text-section\", \"right\"], [1, \"navigation-buttons\"], [\"routerLink\", \"/typeiris\", 1, \"nav-button\", \"prev\"], [1, \"icon\"], [\"routerLink\", \"/fleur\", 1, \"nav-button\", \"next\"]],\n      template: function IrisDiversityComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"nav\", 2)(3, \"div\", 3);\n          i0.ɵɵtext(4, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"ul\", 4)(6, \"li\")(7, \"a\", 5);\n          i0.ɵɵtext(8, \"Accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"li\")(10, \"a\", 6);\n          i0.ɵɵtext(11, \"\\u00C0 propos\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"li\")(13, \"a\", 6);\n          i0.ɵɵtext(14, \"Contact\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"a\", 8);\n          i0.ɵɵtext(17, \"Connexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"a\", 9);\n          i0.ɵɵtext(19, \"Inscription\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 10)(21, \"div\", 11)(22, \"div\", 12)(23, \"h1\");\n          i0.ɵɵtext(24, \"La Diversit\\u00E9 des Iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"div\", 15)(28, \"div\", 16)(29, \"div\", 17);\n          i0.ɵɵelement(30, \"img\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 19);\n          i0.ɵɵelement(32, \"img\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 21);\n          i0.ɵɵelement(34, \"img\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 23);\n          i0.ɵɵelement(36, \"img\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 25);\n          i0.ɵɵelement(38, \"img\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 27);\n          i0.ɵɵelement(40, \"img\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 29);\n          i0.ɵɵelement(42, \"img\", 30);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 31);\n          i0.ɵɵelement(44, \"img\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 33);\n          i0.ɵɵelement(46, \"img\", 34);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 35);\n          i0.ɵɵelement(48, \"img\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 37);\n          i0.ɵɵelement(50, \"img\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 39);\n          i0.ɵɵelement(52, \"img\", 40);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"div\", 41)(54, \"div\", 42)(55, \"p\");\n          i0.ɵɵtext(56, \" Bien que certains types d'iris soient class\\u00E9s selon des cat\\u00E9gories principales, il est rare qu'un individu corresponde exactement \\u00E0 un seul type. En r\\u00E9alit\\u00E9, de nombreuses personnes pr\\u00E9sentent des formes interm\\u00E9diaires, avec des caract\\u00E9ristiques issues de plusieurs cat\\u00E9gories. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 43)(58, \"p\");\n          i0.ɵɵtext(59, \" M\\u00EAme si certains types d'iris sont regroup\\u00E9s en cat\\u00E9gories principales, il est rare qu'un individu corresponde parfaitement \\u00E0 un seul profil. En r\\u00E9alit\\u00E9, la majorit\\u00E9 des personnes pr\\u00E9sentent des formes interm\\u00E9diaires, m\\u00EAlant des caract\\u00E9ristiques issues de plusieurs types. Cette diversit\\u00E9 refl\\u00E8te la richesse et la complexit\\u00E9 unique de chaque \\u00EAtre humain. \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(60, \"div\", 44)(61, \"a\", 45)(62, \"span\", 46);\n          i0.ɵɵtext(63, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"span\");\n          i0.ɵɵtext(65, \"Pr\\u00E9c\\u00E9dent\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"a\", 47)(67, \"span\");\n          i0.ɵɵtext(68, \"Suivant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"span\", 46);\n          i0.ɵɵtext(70, \"\\u2192\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink, i1.RouterLinkActive],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["IrisDiversityComponent", "selectors", "decls", "vars", "consts", "template", "IrisDiversityComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris-diversity\\iris-diversity.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris-diversity\\iris-diversity.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-iris-diversity',\n  templateUrl: './iris-diversity.component.html',\n  styleUrls: ['./iris-diversity.component.scss']\n})\nexport class IrisDiversityComponent {\n\n}\n", "<div class=\"page-container iris-diversity\">\n  <div class=\"container\">\n    <nav class=\"navbar\">\n      <div class=\"logo\">IrisLock</div>\n      <ul class=\"nav-links\">\n        <li><a routerLink=\"/accueil\" routerLinkActive=\"active\">Accueil</a></li>\n        <li><a href=\"#\">À propos</a></li>\n        <li><a href=\"#\">Contact</a></li>\n      </ul>\n      <div class=\"auth-buttons\">\n        <a routerLink=\"/login\" class=\"login-button\">Connexion</a>\n        <a routerLink=\"/signup\" class=\"register-button\">Inscription</a>\n      </div>\n    </nav>\n\n    <div class=\"content\">\n      <div class=\"card main-card\">\n        <div class=\"card-header\">\n          <h1>La Diversité des Iris</h1>\n          <div class=\"divider\"></div>\n        </div>\n\n        <div class=\"iris-diversity-content\">\n          <div class=\"iris-circle-container\">\n            <div class=\"iris-circle\">\n              <!-- Les images d'iris disposées en cercle -->\n              <div class=\"iris-image\" style=\"transform: rotate(0deg) translateX(180px) rotate(0deg)\">\n                <img src=\"assets/iris-samples/iris1.png\" alt=\"Iris type 1\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(30deg) translateX(180px) rotate(-30deg)\">\n                <img src=\"assets/iris-samples/iris2.png\" alt=\"Iris type 2\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(60deg) translateX(180px) rotate(-60deg)\">\n                <img src=\"assets/iris-samples/iris3.png\" alt=\"Iris type 3\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(90deg) translateX(180px) rotate(-90deg)\">\n                <img src=\"assets/iris-samples/iris4.png\" alt=\"Iris type 4\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(120deg) translateX(180px) rotate(-120deg)\">\n                <img src=\"assets/iris-samples/iris5.png\" alt=\"Iris type 5\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(150deg) translateX(180px) rotate(-150deg)\">\n                <img src=\"assets/iris-samples/iris6.png\" alt=\"Iris type 6\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(180deg) translateX(180px) rotate(-180deg)\">\n                <img src=\"assets/iris-samples/iris7.png\" alt=\"Iris type 7\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(210deg) translateX(180px) rotate(-210deg)\">\n                <img src=\"assets/iris-samples/iris8.png\" alt=\"Iris type 8\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(240deg) translateX(180px) rotate(-240deg)\">\n                <img src=\"assets/iris-samples/iris9.png\" alt=\"Iris type 9\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(270deg) translateX(180px) rotate(-270deg)\">\n                <img src=\"assets/iris-samples/iris10.png\" alt=\"Iris type 10\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(300deg) translateX(180px) rotate(-300deg)\">\n                <img src=\"assets/iris-samples/iris11.png\" alt=\"Iris type 11\">\n              </div>\n              <div class=\"iris-image\" style=\"transform: rotate(330deg) translateX(180px) rotate(-330deg)\">\n                <img src=\"assets/iris-samples/iris12.png\" alt=\"Iris type 12\">\n              </div>\n            </div>\n          </div>\n\n          <div class=\"text-sections\">\n            <div class=\"text-section left\">\n              <p>\n                Bien que certains types d'iris soient classés selon des catégories principales, il est rare qu'un individu\n                corresponde exactement à un seul type. En réalité, de nombreuses personnes présentent des formes\n                intermédiaires, avec des caractéristiques issues de plusieurs catégories.\n              </p>\n            </div>\n\n            <div class=\"text-section right\">\n              <p>\n                Même si certains types d'iris sont regroupés en catégories principales, il est rare qu'un individu\n                corresponde parfaitement à un seul profil. En réalité, la majorité des personnes présentent des formes\n                intermédiaires, mêlant des caractéristiques issues de plusieurs types. Cette diversité reflète la richesse et la\n                complexité unique de chaque être humain.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"navigation-buttons\">\n          <a routerLink=\"/typeiris\" class=\"nav-button prev\">\n            <span class=\"icon\">←</span>\n            <span>Précédent</span>\n          </a>\n          <a routerLink=\"/fleur\" class=\"nav-button next\">\n            <span>Suivant</span>\n            <span class=\"icon\">→</span>\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPnCE,EAAA,CAAAC,cAAA,aAA2C;UAGnBD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,YAAsB;UACmCD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClEH,EAAA,CAAAC,cAAA,SAAI;UAAYD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5BH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE7BH,EAAA,CAAAC,cAAA,cAA0B;UACoBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACzDH,EAAA,CAAAC,cAAA,YAAgD;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAInEH,EAAA,CAAAC,cAAA,eAAqB;UAGXD,EAAA,CAAAE,MAAA,kCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAI,SAAA,eAA2B;UAC7BJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAoC;UAK5BD,EAAA,CAAAI,SAAA,eAA2D;UAC7DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0F;UACxFD,EAAA,CAAAI,SAAA,eAA2D;UAC7DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0F;UACxFD,EAAA,CAAAI,SAAA,eAA2D;UAC7DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0F;UACxFD,EAAA,CAAAI,SAAA,eAA2D;UAC7DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA4F;UAC1FD,EAAA,CAAAI,SAAA,eAA2D;UAC7DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA4F;UAC1FD,EAAA,CAAAI,SAAA,eAA2D;UAC7DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA4F;UAC1FD,EAAA,CAAAI,SAAA,eAA2D;UAC7DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA4F;UAC1FD,EAAA,CAAAI,SAAA,eAA2D;UAC7DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA4F;UAC1FD,EAAA,CAAAI,SAAA,eAA2D;UAC7DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA4F;UAC1FD,EAAA,CAAAI,SAAA,eAA6D;UAC/DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA4F;UAC1FD,EAAA,CAAAI,SAAA,eAA6D;UAC/DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA4F;UAC1FD,EAAA,CAAAI,SAAA,eAA6D;UAC/DJ,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,eAA2B;UAGrBD,EAAA,CAAAE,MAAA,4UAGF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,eAAgC;UAE5BD,EAAA,CAAAE,MAAA,wbAIF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKVH,EAAA,CAAAC,cAAA,eAAgC;UAETD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAExBH,EAAA,CAAAC,cAAA,aAA+C;UACvCD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpBH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}