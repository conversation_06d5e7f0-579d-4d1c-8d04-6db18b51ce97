{"ast": null, "code": "// Configuration Firebase pour ProfilingIris\n// IMPORTANT: Remplacez ces valeurs par vos vraies clés Firebase\nexport const firebaseConfig = {\n  apiKey: \"VOTRE_API_KEY_ICI\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"VOTRE_SENDER_ID\",\n  appId: \"VOTRE_APP_ID\" // Remplacez par votre app ID\n};\n// Pour obtenir vos vraies clés Firebase :\n// 1. Allez sur https://console.firebase.google.com/project/profilingiris/settings/general\n// 2. Cliquez sur \"Ajouter une application\" ou sélectionnez votre app web existante\n// 3. Copiez les valeurs de configuration et remplacez-les ci-dessus\n// Exemple de configuration complète :\n/*\nexport const firebaseConfig = {\n  apiKey: \"AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"123456789012\",\n  appId: \"1:123456789012:web:abcdef1234567890\"\n};\n*/", "map": {"version": 3, "names": ["firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\environments\\firebase.config.ts"], "sourcesContent": ["// Configuration Firebase pour ProfilingIris\n// IMPORTANT: Remplacez ces valeurs par vos vraies clés Firebase\n\nexport const firebaseConfig = {\n  apiKey: \"VOTRE_API_KEY_ICI\", // Remplacez par votre vraie clé API\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"VOTRE_SENDER_ID\", // Remplacez par votre sender ID\n  appId: \"VOTRE_APP_ID\" // Remplacez par votre app ID\n};\n\n// Pour obtenir vos vraies clés Firebase :\n// 1. Allez sur https://console.firebase.google.com/project/profilingiris/settings/general\n// 2. Cliquez sur \"Ajouter une application\" ou sélectionnez votre app web existante\n// 3. Copiez les valeurs de configuration et remplacez-les ci-dessus\n\n// Exemple de configuration complète :\n/*\nexport const firebaseConfig = {\n  apiKey: \"AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"123456789012\",\n  appId: \"1:123456789012:web:abcdef1234567890\"\n};\n*/\n"], "mappings": "AAAA;AACA;AAEA,OAAO,MAAMA,cAAc,GAAG;EAC5BC,MAAM,EAAE,mBAAmB;EAC3BC,UAAU,EAAE,+BAA+B;EAC3CC,SAAS,EAAE,eAAe;EAC1BC,aAAa,EAAE,2BAA2B;EAC1CC,iBAAiB,EAAE,iBAAiB;EACpCC,KAAK,EAAE,cAAc,CAAC;CACvB;AAED;AACA;AACA;AACA;AAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}