{"ast": null, "code": "import { of, delay } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class MockFirebaseService {\n  constructor() {\n    this.testSessions = [];\n    this.nextId = 1;\n    // Charger les données depuis localStorage si disponibles\n    this.loadFromLocalStorage();\n  }\n  /**\n   * Simule la sauvegarde d'une session de test\n   */\n  saveTestSession(session) {\n    const sessionId = `test_${this.nextId++}`;\n    const sessionWithId = {\n      ...session,\n      id: sessionId\n    };\n    this.testSessions.push(sessionWithId);\n    this.saveToLocalStorage();\n    console.log('Session sauvegardée (Mock):', sessionWithId);\n    // Simuler un délai réseau\n    return of(sessionId).pipe(delay(1000));\n  }\n  /**\n   * Simule la récupération de toutes les sessions\n   */\n  getAllTestSessions() {\n    return of([...this.testSessions]).pipe(delay(500));\n  }\n  /**\n   * Simule la récupération des sessions d'un utilisateur\n   */\n  getUserTestSessions(userEmail) {\n    const userSessions = this.testSessions.filter(session => session.userEmail === userEmail);\n    return of(userSessions).pipe(delay(500));\n  }\n  /**\n   * Simule la mise à jour d'une session\n   */\n  updateTestSession(sessionId, updates) {\n    const sessionIndex = this.testSessions.findIndex(s => s.id === sessionId);\n    if (sessionIndex !== -1) {\n      this.testSessions[sessionIndex] = {\n        ...this.testSessions[sessionIndex],\n        ...updates\n      };\n      this.saveToLocalStorage();\n      console.log('Session mise à jour (Mock):', sessionId);\n    }\n    return of(void 0).pipe(delay(300));\n  }\n  /**\n   * Sauvegarde dans localStorage pour persistance\n   */\n  saveToLocalStorage() {\n    try {\n      localStorage.setItem('pfa_test_sessions', JSON.stringify(this.testSessions));\n      localStorage.setItem('pfa_next_id', this.nextId.toString());\n    } catch (error) {\n      console.warn('Impossible de sauvegarder dans localStorage:', error);\n    }\n  }\n  /**\n   * Charge depuis localStorage\n   */\n  loadFromLocalStorage() {\n    try {\n      const sessions = localStorage.getItem('pfa_test_sessions');\n      const nextId = localStorage.getItem('pfa_next_id');\n      if (sessions) {\n        this.testSessions = JSON.parse(sessions);\n      }\n      if (nextId) {\n        this.nextId = parseInt(nextId, 10);\n      }\n    } catch (error) {\n      console.warn('Impossible de charger depuis localStorage:', error);\n    }\n  }\n  /**\n   * Efface toutes les données (pour les tests)\n   */\n  clearAllData() {\n    this.testSessions = [];\n    this.nextId = 1;\n    localStorage.removeItem('pfa_test_sessions');\n    localStorage.removeItem('pfa_next_id');\n  }\n  /**\n   * Obtient les statistiques des tests\n   */\n  getTestStatistics() {\n    const stats = {\n      totalTests: this.testSessions.length,\n      profileDistribution: this.calculateProfileDistribution(),\n      averageConfidenceScore: this.calculateAverageConfidence(),\n      recentTests: this.testSessions.slice(-5).reverse()\n    };\n    return of(stats).pipe(delay(300));\n  }\n  calculateProfileDistribution() {\n    const distribution = {};\n    this.testSessions.forEach(session => {\n      const profile = session.finalProfile.primaryClass;\n      distribution[profile] = (distribution[profile] || 0) + 1;\n    });\n    return distribution;\n  }\n  calculateAverageConfidence() {\n    if (this.testSessions.length === 0) return 0;\n    const totalConfidence = this.testSessions.reduce((sum, session) => sum + session.finalProfile.confidenceScore, 0);\n    return Math.round(totalConfidence / this.testSessions.length);\n  }\n  static {\n    this.ɵfac = function MockFirebaseService_Factory(t) {\n      return new (t || MockFirebaseService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MockFirebaseService,\n      factory: MockFirebaseService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["of", "delay", "MockFirebaseService", "constructor", "testSessions", "nextId", "loadFromLocalStorage", "saveTestSession", "session", "sessionId", "sessionWithId", "id", "push", "saveToLocalStorage", "console", "log", "pipe", "getAllTestSessions", "getUserTestSessions", "userEmail", "userSessions", "filter", "updateTestSession", "updates", "sessionIndex", "findIndex", "s", "localStorage", "setItem", "JSON", "stringify", "toString", "error", "warn", "sessions", "getItem", "parse", "parseInt", "clearAllData", "removeItem", "getTestStatistics", "stats", "totalTests", "length", "profileDistribution", "calculateProfileDistribution", "averageConfidenceScore", "calculateAverageConfidence", "recentTests", "slice", "reverse", "distribution", "for<PERSON>ach", "profile", "finalProfile", "primaryClass", "totalConfidence", "reduce", "sum", "confidenceScore", "Math", "round", "factory", "ɵfac", "providedIn"], "sources": ["E:\\aymen\\pfa\\pfa\\src\\app\\services\\mock-firebase.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, of, delay } from 'rxjs';\nimport { TestSession } from '../models/personality-test.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MockFirebaseService {\n  private testSessions: TestSession[] = [];\n  private nextId = 1;\n\n  constructor() {\n    // Charger les données depuis localStorage si disponibles\n    this.loadFromLocalStorage();\n  }\n\n  /**\n   * Simule la sauvegarde d'une session de test\n   */\n  saveTestSession(session: TestSession): Observable<string> {\n    const sessionId = `test_${this.nextId++}`;\n    const sessionWithId = { ...session, id: sessionId };\n\n    this.testSessions.push(sessionWithId);\n    this.saveToLocalStorage();\n\n    console.log('Session sauvegardée (Mock):', sessionWithId);\n\n    // Simuler un délai réseau\n    return of(sessionId).pipe(delay(1000));\n  }\n\n  /**\n   * Simule la récupération de toutes les sessions\n   */\n  getAllTestSessions(): Observable<TestSession[]> {\n    return of([...this.testSessions]).pipe(delay(500));\n  }\n\n  /**\n   * Simule la récupération des sessions d'un utilisateur\n   */\n  getUserTestSessions(userEmail: string): Observable<TestSession[]> {\n    const userSessions = this.testSessions.filter(session => session.userEmail === userEmail);\n    return of(userSessions).pipe(delay(500));\n  }\n\n  /**\n   * Simule la mise à jour d'une session\n   */\n  updateTestSession(sessionId: string, updates: Partial<TestSession>): Observable<void> {\n    const sessionIndex = this.testSessions.findIndex(s => s.id === sessionId);\n    if (sessionIndex !== -1) {\n      this.testSessions[sessionIndex] = { ...this.testSessions[sessionIndex], ...updates };\n      this.saveToLocalStorage();\n      console.log('Session mise à jour (Mock):', sessionId);\n    }\n    return of(void 0).pipe(delay(300));\n  }\n\n  /**\n   * Sauvegarde dans localStorage pour persistance\n   */\n  private saveToLocalStorage(): void {\n    try {\n      localStorage.setItem('pfa_test_sessions', JSON.stringify(this.testSessions));\n      localStorage.setItem('pfa_next_id', this.nextId.toString());\n    } catch (error) {\n      console.warn('Impossible de sauvegarder dans localStorage:', error);\n    }\n  }\n\n  /**\n   * Charge depuis localStorage\n   */\n  private loadFromLocalStorage(): void {\n    try {\n      const sessions = localStorage.getItem('pfa_test_sessions');\n      const nextId = localStorage.getItem('pfa_next_id');\n\n      if (sessions) {\n        this.testSessions = JSON.parse(sessions);\n      }\n\n      if (nextId) {\n        this.nextId = parseInt(nextId, 10);\n      }\n    } catch (error) {\n      console.warn('Impossible de charger depuis localStorage:', error);\n    }\n  }\n\n  /**\n   * Efface toutes les données (pour les tests)\n   */\n  clearAllData(): void {\n    this.testSessions = [];\n    this.nextId = 1;\n    localStorage.removeItem('pfa_test_sessions');\n    localStorage.removeItem('pfa_next_id');\n  }\n\n  /**\n   * Obtient les statistiques des tests\n   */\n  getTestStatistics(): Observable<any> {\n    const stats = {\n      totalTests: this.testSessions.length,\n      profileDistribution: this.calculateProfileDistribution(),\n      averageConfidenceScore: this.calculateAverageConfidence(),\n      recentTests: this.testSessions.slice(-5).reverse()\n    };\n\n    return of(stats).pipe(delay(300));\n  }\n\n  private calculateProfileDistribution(): Record<string, number> {\n    const distribution: Record<string, number> = {};\n\n    this.testSessions.forEach(session => {\n      const profile = session.finalProfile.primaryClass;\n      distribution[profile] = (distribution[profile] || 0) + 1;\n    });\n\n    return distribution;\n  }\n\n  private calculateAverageConfidence(): number {\n    if (this.testSessions.length === 0) return 0;\n\n    const totalConfidence = this.testSessions.reduce(\n      (sum, session) => sum + session.finalProfile.confidenceScore,\n      0\n    );\n\n    return Math.round(totalConfidence / this.testSessions.length);\n  }\n}\n"], "mappings": "AACA,SAAqBA,EAAE,EAAEC,KAAK,QAAQ,MAAM;;AAM5C,OAAM,MAAOC,mBAAmB;EAI9BC,YAAA;IAHQ,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,MAAM,GAAG,CAAC;IAGhB;IACA,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEA;;;EAGAC,eAAeA,CAACC,OAAoB;IAClC,MAAMC,SAAS,GAAG,QAAQ,IAAI,CAACJ,MAAM,EAAE,EAAE;IACzC,MAAMK,aAAa,GAAG;MAAE,GAAGF,OAAO;MAAEG,EAAE,EAAEF;IAAS,CAAE;IAEnD,IAAI,CAACL,YAAY,CAACQ,IAAI,CAACF,aAAa,CAAC;IACrC,IAAI,CAACG,kBAAkB,EAAE;IAEzBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEL,aAAa,CAAC;IAEzD;IACA,OAAOV,EAAE,CAACS,SAAS,CAAC,CAACO,IAAI,CAACf,KAAK,CAAC,IAAI,CAAC,CAAC;EACxC;EAEA;;;EAGAgB,kBAAkBA,CAAA;IAChB,OAAOjB,EAAE,CAAC,CAAC,GAAG,IAAI,CAACI,YAAY,CAAC,CAAC,CAACY,IAAI,CAACf,KAAK,CAAC,GAAG,CAAC,CAAC;EACpD;EAEA;;;EAGAiB,mBAAmBA,CAACC,SAAiB;IACnC,MAAMC,YAAY,GAAG,IAAI,CAAChB,YAAY,CAACiB,MAAM,CAACb,OAAO,IAAIA,OAAO,CAACW,SAAS,KAAKA,SAAS,CAAC;IACzF,OAAOnB,EAAE,CAACoB,YAAY,CAAC,CAACJ,IAAI,CAACf,KAAK,CAAC,GAAG,CAAC,CAAC;EAC1C;EAEA;;;EAGAqB,iBAAiBA,CAACb,SAAiB,EAAEc,OAA6B;IAChE,MAAMC,YAAY,GAAG,IAAI,CAACpB,YAAY,CAACqB,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAKF,SAAS,CAAC;IACzE,IAAIe,YAAY,KAAK,CAAC,CAAC,EAAE;MACvB,IAAI,CAACpB,YAAY,CAACoB,YAAY,CAAC,GAAG;QAAE,GAAG,IAAI,CAACpB,YAAY,CAACoB,YAAY,CAAC;QAAE,GAAGD;MAAO,CAAE;MACpF,IAAI,CAACV,kBAAkB,EAAE;MACzBC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEN,SAAS,CAAC;;IAEvD,OAAOT,EAAE,CAAC,KAAK,CAAC,CAAC,CAACgB,IAAI,CAACf,KAAK,CAAC,GAAG,CAAC,CAAC;EACpC;EAEA;;;EAGQY,kBAAkBA,CAAA;IACxB,IAAI;MACFc,YAAY,CAACC,OAAO,CAAC,mBAAmB,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC1B,YAAY,CAAC,CAAC;MAC5EuB,YAAY,CAACC,OAAO,CAAC,aAAa,EAAE,IAAI,CAACvB,MAAM,CAAC0B,QAAQ,EAAE,CAAC;KAC5D,CAAC,OAAOC,KAAK,EAAE;MACdlB,OAAO,CAACmB,IAAI,CAAC,8CAA8C,EAAED,KAAK,CAAC;;EAEvE;EAEA;;;EAGQ1B,oBAAoBA,CAAA;IAC1B,IAAI;MACF,MAAM4B,QAAQ,GAAGP,YAAY,CAACQ,OAAO,CAAC,mBAAmB,CAAC;MAC1D,MAAM9B,MAAM,GAAGsB,YAAY,CAACQ,OAAO,CAAC,aAAa,CAAC;MAElD,IAAID,QAAQ,EAAE;QACZ,IAAI,CAAC9B,YAAY,GAAGyB,IAAI,CAACO,KAAK,CAACF,QAAQ,CAAC;;MAG1C,IAAI7B,MAAM,EAAE;QACV,IAAI,CAACA,MAAM,GAAGgC,QAAQ,CAAChC,MAAM,EAAE,EAAE,CAAC;;KAErC,CAAC,OAAO2B,KAAK,EAAE;MACdlB,OAAO,CAACmB,IAAI,CAAC,4CAA4C,EAAED,KAAK,CAAC;;EAErE;EAEA;;;EAGAM,YAAYA,CAAA;IACV,IAAI,CAAClC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,MAAM,GAAG,CAAC;IACfsB,YAAY,CAACY,UAAU,CAAC,mBAAmB,CAAC;IAC5CZ,YAAY,CAACY,UAAU,CAAC,aAAa,CAAC;EACxC;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,MAAMC,KAAK,GAAG;MACZC,UAAU,EAAE,IAAI,CAACtC,YAAY,CAACuC,MAAM;MACpCC,mBAAmB,EAAE,IAAI,CAACC,4BAA4B,EAAE;MACxDC,sBAAsB,EAAE,IAAI,CAACC,0BAA0B,EAAE;MACzDC,WAAW,EAAE,IAAI,CAAC5C,YAAY,CAAC6C,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;KACjD;IAED,OAAOlD,EAAE,CAACyC,KAAK,CAAC,CAACzB,IAAI,CAACf,KAAK,CAAC,GAAG,CAAC,CAAC;EACnC;EAEQ4C,4BAA4BA,CAAA;IAClC,MAAMM,YAAY,GAA2B,EAAE;IAE/C,IAAI,CAAC/C,YAAY,CAACgD,OAAO,CAAC5C,OAAO,IAAG;MAClC,MAAM6C,OAAO,GAAG7C,OAAO,CAAC8C,YAAY,CAACC,YAAY;MACjDJ,YAAY,CAACE,OAAO,CAAC,GAAG,CAACF,YAAY,CAACE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1D,CAAC,CAAC;IAEF,OAAOF,YAAY;EACrB;EAEQJ,0BAA0BA,CAAA;IAChC,IAAI,IAAI,CAAC3C,YAAY,CAACuC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAE5C,MAAMa,eAAe,GAAG,IAAI,CAACpD,YAAY,CAACqD,MAAM,CAC9C,CAACC,GAAG,EAAElD,OAAO,KAAKkD,GAAG,GAAGlD,OAAO,CAAC8C,YAAY,CAACK,eAAe,EAC5D,CAAC,CACF;IAED,OAAOC,IAAI,CAACC,KAAK,CAACL,eAAe,GAAG,IAAI,CAACpD,YAAY,CAACuC,MAAM,CAAC;EAC/D;;;uBAjIWzC,mBAAmB;IAAA;EAAA;;;aAAnBA,mBAAmB;MAAA4D,OAAA,EAAnB5D,mBAAmB,CAAA6D,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}