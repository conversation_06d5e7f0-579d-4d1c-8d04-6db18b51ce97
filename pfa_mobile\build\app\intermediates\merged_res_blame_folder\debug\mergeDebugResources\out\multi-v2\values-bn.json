{"logs": [{"outputFile": "com.example.pfa_mobile.app-mergeDebugResources-48:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\847a26655a62540eb6e1b568482ff8fb\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,111", "endOffsets": "164,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2835,2949", "endColumns": "113,111", "endOffsets": "2944,3056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\eb80b7c20f86db896fc82173afcd18d6\\transformed\\jetified-play-services-base-18.1.0\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,453,577,684,816,934,1042,1142,1281,1386,1538,1662,1791,1935,1991,2054", "endColumns": "104,154,123,106,131,117,107,99,138,104,151,123,128,143,55,62,85", "endOffsets": "297,452,576,683,815,933,1041,1141,1280,1385,1537,1661,1790,1934,1990,2053,2139"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3790,3899,4058,4186,4297,4433,4555,4667,4923,5066,5175,5331,5459,5592,5740,5800,5867", "endColumns": "108,158,127,110,135,121,111,103,142,108,155,127,132,147,59,66,89", "endOffsets": "3894,4053,4181,4292,4428,4550,4662,4766,5061,5170,5326,5454,5587,5735,5795,5862,5952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8672542fff6e2103c63e7b98b328f638\\transformed\\appcompat-1.1.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,906,997,1089,1183,1277,1378,1471,1566,1660,1751,1842,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,82,90,91,93,93,100,92,94,93,90,90,84,109,103,102,107,107,104,164,104,85", "endOffsets": "208,314,420,509,614,735,818,901,992,1084,1178,1272,1373,1466,1561,1655,1746,1837,1922,2032,2136,2239,2347,2455,2560,2725,2830,2916"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,906,997,1089,1183,1277,1378,1471,1566,1660,1751,1842,1927,2037,2141,2244,2352,2460,2565,2730,6765", "endColumns": "107,105,105,88,104,120,82,82,90,91,93,93,100,92,94,93,90,90,84,109,103,102,107,107,104,164,104,85", "endOffsets": "208,314,420,509,614,735,818,901,992,1084,1178,1272,1373,1466,1561,1655,1746,1837,1922,2032,2136,2239,2347,2455,2560,2725,2830,6846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6fec79dfcef552889e3835cc7bab572a\\transformed\\preference-1.2.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,261,341,490,659,740", "endColumns": "71,83,79,148,168,80,77", "endOffsets": "172,256,336,485,654,735,813"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5957,6135,6536,6616,6952,7121,7202", "endColumns": "71,83,79,148,168,80,77", "endOffsets": "6024,6214,6611,6760,7116,7197,7275"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7f46bbd13206153774356ca6103aa890\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3061,3160,3262,3364,3467,3568,3670,6851", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "3155,3257,3359,3462,3563,3665,3785,6947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\cf636ef2f0738047fe8129f320ef339e\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-bn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4771", "endColumns": "151", "endOffsets": "4918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6643c4258ae11fe541bbf5ee341bcb98\\transformed\\browser-1.4.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6029,6219,6321,6430", "endColumns": "105,101,108,105", "endOffsets": "6130,6316,6425,6531"}}]}]}