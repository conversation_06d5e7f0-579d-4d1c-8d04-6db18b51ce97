{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nexport class AccueilComponent {\n  static {\n    this.ɵfac = function AccueilComponent_Factory(t) {\n      return new (t || AccueilComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccueilComponent,\n      selectors: [[\"app-accueil\"]],\n      decls: 58,\n      vars: 2,\n      consts: [[1, \"page-container\", \"accueil\"], [1, \"container\"], [1, \"navbar\"], [1, \"logo\"], [1, \"nav-links\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 3, \"routerLinkActiveOptions\"], [\"href\", \"#\"], [1, \"register-button\"], [1, \"content\"], [1, \"card\", \"main-card\"], [1, \"card-inner\"], [1, \"card-front\"], [1, \"text-content\"], [1, \"title\"], [1, \"divider\"], [1, \"description\"], [1, \"image-container\"], [\"src\", \"assets/iris.png\", \"alt\", \"Iris\", 1, \"main-image\"], [1, \"image-decoration\"], [1, \"action-container\"], [\"routerLink\", \"/suivantacc\", 1, \"btn\", \"start-btn\"], [1, \"icon\"], [1, \"features\"], [1, \"feature-card\"]],\n      template: function AccueilComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"nav\", 2)(3, \"div\", 3);\n          i0.ɵɵtext(4, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"ul\", 4)(6, \"li\")(7, \"a\", 5);\n          i0.ɵɵtext(8, \"Accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"li\")(10, \"a\", 6);\n          i0.ɵɵtext(11, \"\\u00C0 propos\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"li\")(13, \"a\", 6);\n          i0.ɵɵtext(14, \"Contact\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"button\", 7);\n          i0.ɵɵtext(16, \"Register\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"div\", 9)(19, \"div\", 10)(20, \"div\", 11)(21, \"div\", 12)(22, \"h1\", 13);\n          i0.ɵɵtext(23, \"Iris & Identit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"div\", 14);\n          i0.ɵɵelementStart(25, \"p\", 15);\n          i0.ɵɵtext(26, \" Chaque iris est une signature unique. Notre syst\\u00E8me de profilage biom\\u00E9trique offre une s\\u00E9curit\\u00E9 in\\u00E9gal\\u00E9e, inspir\\u00E9e directement par la nature humaine. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 16);\n          i0.ɵɵelement(28, \"img\", 17)(29, \"div\", 18);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(30, \"div\", 19)(31, \"a\", 20)(32, \"span\");\n          i0.ɵɵtext(33, \"Commencer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"span\", 21);\n          i0.ɵɵtext(35, \"\\u2192\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"div\", 22)(37, \"div\", 23)(38, \"div\", 21);\n          i0.ɵɵtext(39, \"\\uD83D\\uDD0D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"h3\");\n          i0.ɵɵtext(41, \"Analyse Pr\\u00E9cise\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"p\");\n          i0.ɵɵtext(43, \"Identification des traits de personnalit\\u00E9 \\u00E0 travers les motifs de l'iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"div\", 23)(45, \"div\", 21);\n          i0.ɵɵtext(46, \"\\uD83E\\uDDEC\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"h3\");\n          i0.ɵɵtext(48, \"Base Scientifique\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"p\");\n          i0.ɵɵtext(50, \"Fond\\u00E9e sur des recherches approfondies en iridologie et biom\\u00E9trie\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"div\", 23)(52, \"div\", 21);\n          i0.ɵɵtext(53, \"\\uD83D\\uDD10\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"h3\");\n          i0.ɵɵtext(55, \"S\\u00E9curit\\u00E9 Avanc\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\");\n          i0.ɵɵtext(57, \"Protection des donn\\u00E9es et confidentialit\\u00E9 garanties\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(1, _c0));\n        }\n      },\n      dependencies: [i1.RouterLink, i1.RouterLinkActive],\n      styles: [\".page-container.accueil[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\\n  padding: 20px 0;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 0;\\n  margin-bottom: 40px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  color: #333;\\n  position: relative;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 30px;\\n  height: 2px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  border-radius: 2px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  gap: 30px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #555;\\n  text-decoration: none;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background-color: var(--fleur-primary);\\n  transition: width 0.3s ease;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  color: var(--fleur-primary);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:after, .page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%]:after {\\n  width: 100%;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%] {\\n  padding: 10px 25px;\\n  background-color: transparent;\\n  border: 2px solid var(--fleur-primary);\\n  color: var(--fleur-primary);\\n  border-radius: 50px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--fleur-primary);\\n  color: white;\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-bottom: 60px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 1000px;\\n  height: 400px;\\n  margin-bottom: 40px;\\n  perspective: 1000px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  transform-style: preserve-3d;\\n  transition: transform 0.6s;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  backface-visibility: hidden;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  padding: 40px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding-right: 40px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 3rem;\\n  color: #333;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  margin-bottom: 20px;\\n  border-radius: 3px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  line-height: 1.7;\\n  color: #666;\\n  margin-bottom: 30px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\\n  z-index: 2;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 330px;\\n  height: 330px;\\n  border-radius: 50%;\\n  border: 2px dashed var(--fleur-secondary);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_rotate 20s linear infinite;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .start-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 15px 35px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  color: white;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n  box-shadow: 0 10px 25px rgba(138, 79, 255, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .start-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(138, 79, 255, 0.4);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .start-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 30px;\\n  margin-top: 60px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  margin-bottom: 20px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  color: #333;\\n  margin-bottom: 15px;\\n}\\n.page-container.accueil[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  color: #666;\\n  line-height: 1.6;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: 30px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%] {\\n    padding-right: 0;\\n    margin-bottom: 30px;\\n    text-align: center;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n    margin: 0 auto 20px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    width: 250px;\\n    height: 250px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n    width: 280px;\\n    height: 280px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%] {\\n    gap: 20px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    width: 200px;\\n    height: 200px;\\n  }\\n  .page-container.accueil[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n    width: 230px;\\n    height: 230px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AccueilComponent", "selectors", "decls", "vars", "consts", "template", "AccueilComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-accueil',\n  templateUrl: './accueil.component.html',\n  styleUrls: ['./accueil.component.scss']\n})\nexport class AccueilComponent {\n\n}\n", "<div class=\"page-container accueil\">\n  <div class=\"container\">\n    <nav class=\"navbar\">\n      <div class=\"logo\">IrisLock</div>\n      <ul class=\"nav-links\">\n        <li><a routerLink=\"/\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{ exact: true }\">Accueil</a></li>\n        <li><a href=\"#\">À propos</a></li>\n        <li><a href=\"#\">Contact</a></li>\n      </ul>\n      <button class=\"register-button\">Register</button>\n    </nav>\n\n    <div class=\"content\">\n      <div class=\"card main-card\">\n        <div class=\"card-inner\">\n          <div class=\"card-front\">\n            <div class=\"text-content\">\n              <h1 class=\"title\">Iris & Identité</h1>\n              <div class=\"divider\"></div>\n              <p class=\"description\">\n                Chaque iris est une signature unique. Notre système de profilage biométrique offre une sécurité inégalée, inspirée directement par la nature humaine.\n              </p>\n            </div>\n            <div class=\"image-container\">\n              <img src=\"assets/iris.png\" alt=\"Iris\" class=\"main-image\" />\n              <div class=\"image-decoration\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"action-container\">\n        <a routerLink=\"/suivantacc\" class=\"btn start-btn\">\n          <span>Commencer</span>\n          <span class=\"icon\">→</span>\n        </a>\n      </div>\n    </div>\n\n    <div class=\"features\">\n      <div class=\"feature-card\">\n        <div class=\"icon\">🔍</div>\n        <h3>Analyse Précise</h3>\n        <p>Identification des traits de personnalité à travers les motifs de l'iris</p>\n      </div>\n      <div class=\"feature-card\">\n        <div class=\"icon\">🧬</div>\n        <h3>Base Scientifique</h3>\n        <p>Fondée sur des recherches approfondies en iridologie et biométrie</p>\n      </div>\n      <div class=\"feature-card\">\n        <div class=\"icon\">🔐</div>\n        <h3>Sécurité Avancée</h3>\n        <p>Protection des données et confidentialité garanties</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;AAOA,OAAM,MAAOA,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BE,EAAA,CAAAC,cAAA,aAAoC;UAGZD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,YAAsB;UACwED,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvGH,EAAA,CAAAC,cAAA,SAAI;UAAYD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5BH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE7BH,EAAA,CAAAC,cAAA,iBAAgC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGnDH,EAAA,CAAAC,cAAA,cAAqB;UAKOD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtCH,EAAA,CAAAI,SAAA,eAA2B;UAC3BJ,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,MAAA,kMACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAI,SAAA,eAA2D;UAE7DJ,EAAA,CAAAG,YAAA,EAAM;UAKZH,EAAA,CAAAC,cAAA,eAA8B;UAEpBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtBH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKjCH,EAAA,CAAAC,cAAA,eAAsB;UAEAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,0FAAwE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjFH,EAAA,CAAAC,cAAA,eAA0B;UACND,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,mFAAiE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE1EH,EAAA,CAAAC,cAAA,eAA0B;UACND,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,uCAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qEAAmD;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UAhDVH,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAM,UAAA,4BAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}