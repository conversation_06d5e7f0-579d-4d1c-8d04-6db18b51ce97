{"ast": null, "code": "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A container for all of the Logger instances\n */\nconst instances = [];\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nvar LogLevel;\n(function (LogLevel) {\n  LogLevel[LogLevel[\"DEBUG\"] = 0] = \"DEBUG\";\n  LogLevel[LogLevel[\"VERBOSE\"] = 1] = \"VERBOSE\";\n  LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n  LogLevel[LogLevel[\"WARN\"] = 3] = \"WARN\";\n  LogLevel[LogLevel[\"ERROR\"] = 4] = \"ERROR\";\n  LogLevel[LogLevel[\"SILENT\"] = 5] = \"SILENT\";\n})(LogLevel || (LogLevel = {}));\nconst levelStringToEnum = {\n  'debug': LogLevel.DEBUG,\n  'verbose': LogLevel.VERBOSE,\n  'info': LogLevel.INFO,\n  'warn': LogLevel.WARN,\n  'error': LogLevel.ERROR,\n  'silent': LogLevel.SILENT\n};\n/**\n * The default log level\n */\nconst defaultLogLevel = LogLevel.INFO;\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n  [LogLevel.DEBUG]: 'log',\n  [LogLevel.VERBOSE]: 'log',\n  [LogLevel.INFO]: 'info',\n  [LogLevel.WARN]: 'warn',\n  [LogLevel.ERROR]: 'error'\n};\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler = (instance, logType, ...args) => {\n  if (logType < instance.logLevel) {\n    return;\n  }\n  const now = new Date().toISOString();\n  const method = ConsoleMethod[logType];\n  if (method) {\n    console[method](`[${now}]  ${instance.name}:`, ...args);\n  } else {\n    throw new Error(`Attempted to log a message with an invalid logType (value: ${logType})`);\n  }\n};\nclass Logger {\n  /**\n   * Gives you an instance of a Logger to capture messages according to\n   * Firebase's logging scheme.\n   *\n   * @param name The name that the logs will be associated with\n   */\n  constructor(name) {\n    this.name = name;\n    /**\n     * The log level of the given Logger instance.\n     */\n    this._logLevel = defaultLogLevel;\n    /**\n     * The main (internal) log handler for the Logger instance.\n     * Can be set to a new function in internal package code but not by user.\n     */\n    this._logHandler = defaultLogHandler;\n    /**\n     * The optional, additional, user-defined log handler for the Logger instance.\n     */\n    this._userLogHandler = null;\n    /**\n     * Capture the current instance for later use\n     */\n    instances.push(this);\n  }\n  get logLevel() {\n    return this._logLevel;\n  }\n  set logLevel(val) {\n    if (!(val in LogLevel)) {\n      throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n    }\n    this._logLevel = val;\n  }\n  // Workaround for setter/getter having to be the same type.\n  setLogLevel(val) {\n    this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n  }\n  get logHandler() {\n    return this._logHandler;\n  }\n  set logHandler(val) {\n    if (typeof val !== 'function') {\n      throw new TypeError('Value assigned to `logHandler` must be a function');\n    }\n    this._logHandler = val;\n  }\n  get userLogHandler() {\n    return this._userLogHandler;\n  }\n  set userLogHandler(val) {\n    this._userLogHandler = val;\n  }\n  /**\n   * The functions below are all based on the `console` interface\n   */\n  debug(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n    this._logHandler(this, LogLevel.DEBUG, ...args);\n  }\n  log(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n    this._logHandler(this, LogLevel.VERBOSE, ...args);\n  }\n  info(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n    this._logHandler(this, LogLevel.INFO, ...args);\n  }\n  warn(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n    this._logHandler(this, LogLevel.WARN, ...args);\n  }\n  error(...args) {\n    this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n    this._logHandler(this, LogLevel.ERROR, ...args);\n  }\n}\nfunction setLogLevel(level) {\n  instances.forEach(inst => {\n    inst.setLogLevel(level);\n  });\n}\nfunction setUserLogHandler(logCallback, options) {\n  for (const instance of instances) {\n    let customLogLevel = null;\n    if (options && options.level) {\n      customLogLevel = levelStringToEnum[options.level];\n    }\n    if (logCallback === null) {\n      instance.userLogHandler = null;\n    } else {\n      instance.userLogHandler = (instance, level, ...args) => {\n        const message = args.map(arg => {\n          if (arg == null) {\n            return null;\n          } else if (typeof arg === 'string') {\n            return arg;\n          } else if (typeof arg === 'number' || typeof arg === 'boolean') {\n            return arg.toString();\n          } else if (arg instanceof Error) {\n            return arg.message;\n          } else {\n            try {\n              return JSON.stringify(arg);\n            } catch (ignored) {\n              return null;\n            }\n          }\n        }).filter(arg => arg).join(' ');\n        if (level >= (customLogLevel !== null && customLogLevel !== void 0 ? customLogLevel : instance.logLevel)) {\n          logCallback({\n            level: LogLevel[level].toLowerCase(),\n            message,\n            args,\n            type: instance.name\n          });\n        }\n      };\n    }\n  }\n}\nexport { LogLevel, Logger, setLogLevel, setUserLogHandler };", "map": {"version": 3, "names": ["instances", "LogLevel", "levelStringToEnum", "DEBUG", "VERBOSE", "INFO", "WARN", "ERROR", "SILENT", "defaultLogLevel", "ConsoleMethod", "defaultLogHandler", "instance", "logType", "args", "logLevel", "now", "Date", "toISOString", "method", "console", "name", "Error", "<PERSON><PERSON>", "constructor", "_logLevel", "_log<PERSON><PERSON><PERSON>", "_userLogHandler", "push", "val", "TypeError", "setLogLevel", "log<PERSON><PERSON><PERSON>", "userLogHandler", "debug", "log", "info", "warn", "error", "level", "for<PERSON>ach", "inst", "setUserLogHandler", "logCallback", "options", "customLogLevel", "message", "map", "arg", "toString", "JSON", "stringify", "ignored", "filter", "join", "toLowerCase", "type"], "sources": ["D:/ProjetPfa/pfa/pfa/node_modules/@firebase/logger/dist/esm/index.esm2017.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * A container for all of the Logger instances\n */\nconst instances = [];\n/**\n * The JS SDK supports 5 log levels and also allows a user the ability to\n * silence the logs altogether.\n *\n * The order is a follows:\n * DEBUG < VERBOSE < INFO < WARN < ERROR\n *\n * All of the log types above the current log level will be captured (i.e. if\n * you set the log level to `INFO`, errors will still be logged, but `DEBUG` and\n * `VERBOSE` logs will not)\n */\nvar LogLevel;\n(function (LogLevel) {\n    LogLevel[LogLevel[\"DEBUG\"] = 0] = \"DEBUG\";\n    LogLevel[LogLevel[\"VERBOSE\"] = 1] = \"VERBOSE\";\n    LogLevel[LogLevel[\"INFO\"] = 2] = \"INFO\";\n    LogLevel[LogLevel[\"WARN\"] = 3] = \"WARN\";\n    LogLevel[LogLevel[\"ERROR\"] = 4] = \"ERROR\";\n    LogLevel[LogLevel[\"SILENT\"] = 5] = \"SILENT\";\n})(LogLevel || (LogLevel = {}));\nconst levelStringToEnum = {\n    'debug': LogLevel.DEBUG,\n    'verbose': LogLevel.VERBOSE,\n    'info': LogLevel.INFO,\n    'warn': LogLevel.WARN,\n    'error': LogLevel.ERROR,\n    'silent': LogLevel.SILENT\n};\n/**\n * The default log level\n */\nconst defaultLogLevel = LogLevel.INFO;\n/**\n * By default, `console.debug` is not displayed in the developer console (in\n * chrome). To avoid forcing users to have to opt-in to these logs twice\n * (i.e. once for firebase, and once in the console), we are sending `DEBUG`\n * logs to the `console.log` function.\n */\nconst ConsoleMethod = {\n    [LogLevel.DEBUG]: 'log',\n    [LogLevel.VERBOSE]: 'log',\n    [LogLevel.INFO]: 'info',\n    [LogLevel.WARN]: 'warn',\n    [LogLevel.ERROR]: 'error'\n};\n/**\n * The default log handler will forward DEBUG, VERBOSE, INFO, WARN, and ERROR\n * messages on to their corresponding console counterparts (if the log method\n * is supported by the current log level)\n */\nconst defaultLogHandler = (instance, logType, ...args) => {\n    if (logType < instance.logLevel) {\n        return;\n    }\n    const now = new Date().toISOString();\n    const method = ConsoleMethod[logType];\n    if (method) {\n        console[method](`[${now}]  ${instance.name}:`, ...args);\n    }\n    else {\n        throw new Error(`Attempted to log a message with an invalid logType (value: ${logType})`);\n    }\n};\nclass Logger {\n    /**\n     * Gives you an instance of a Logger to capture messages according to\n     * Firebase's logging scheme.\n     *\n     * @param name The name that the logs will be associated with\n     */\n    constructor(name) {\n        this.name = name;\n        /**\n         * The log level of the given Logger instance.\n         */\n        this._logLevel = defaultLogLevel;\n        /**\n         * The main (internal) log handler for the Logger instance.\n         * Can be set to a new function in internal package code but not by user.\n         */\n        this._logHandler = defaultLogHandler;\n        /**\n         * The optional, additional, user-defined log handler for the Logger instance.\n         */\n        this._userLogHandler = null;\n        /**\n         * Capture the current instance for later use\n         */\n        instances.push(this);\n    }\n    get logLevel() {\n        return this._logLevel;\n    }\n    set logLevel(val) {\n        if (!(val in LogLevel)) {\n            throw new TypeError(`Invalid value \"${val}\" assigned to \\`logLevel\\``);\n        }\n        this._logLevel = val;\n    }\n    // Workaround for setter/getter having to be the same type.\n    setLogLevel(val) {\n        this._logLevel = typeof val === 'string' ? levelStringToEnum[val] : val;\n    }\n    get logHandler() {\n        return this._logHandler;\n    }\n    set logHandler(val) {\n        if (typeof val !== 'function') {\n            throw new TypeError('Value assigned to `logHandler` must be a function');\n        }\n        this._logHandler = val;\n    }\n    get userLogHandler() {\n        return this._userLogHandler;\n    }\n    set userLogHandler(val) {\n        this._userLogHandler = val;\n    }\n    /**\n     * The functions below are all based on the `console` interface\n     */\n    debug(...args) {\n        this._userLogHandler && this._userLogHandler(this, LogLevel.DEBUG, ...args);\n        this._logHandler(this, LogLevel.DEBUG, ...args);\n    }\n    log(...args) {\n        this._userLogHandler &&\n            this._userLogHandler(this, LogLevel.VERBOSE, ...args);\n        this._logHandler(this, LogLevel.VERBOSE, ...args);\n    }\n    info(...args) {\n        this._userLogHandler && this._userLogHandler(this, LogLevel.INFO, ...args);\n        this._logHandler(this, LogLevel.INFO, ...args);\n    }\n    warn(...args) {\n        this._userLogHandler && this._userLogHandler(this, LogLevel.WARN, ...args);\n        this._logHandler(this, LogLevel.WARN, ...args);\n    }\n    error(...args) {\n        this._userLogHandler && this._userLogHandler(this, LogLevel.ERROR, ...args);\n        this._logHandler(this, LogLevel.ERROR, ...args);\n    }\n}\nfunction setLogLevel(level) {\n    instances.forEach(inst => {\n        inst.setLogLevel(level);\n    });\n}\nfunction setUserLogHandler(logCallback, options) {\n    for (const instance of instances) {\n        let customLogLevel = null;\n        if (options && options.level) {\n            customLogLevel = levelStringToEnum[options.level];\n        }\n        if (logCallback === null) {\n            instance.userLogHandler = null;\n        }\n        else {\n            instance.userLogHandler = (instance, level, ...args) => {\n                const message = args\n                    .map(arg => {\n                    if (arg == null) {\n                        return null;\n                    }\n                    else if (typeof arg === 'string') {\n                        return arg;\n                    }\n                    else if (typeof arg === 'number' || typeof arg === 'boolean') {\n                        return arg.toString();\n                    }\n                    else if (arg instanceof Error) {\n                        return arg.message;\n                    }\n                    else {\n                        try {\n                            return JSON.stringify(arg);\n                        }\n                        catch (ignored) {\n                            return null;\n                        }\n                    }\n                })\n                    .filter(arg => arg)\n                    .join(' ');\n                if (level >= (customLogLevel !== null && customLogLevel !== void 0 ? customLogLevel : instance.logLevel)) {\n                    logCallback({\n                        level: LogLevel[level].toLowerCase(),\n                        message,\n                        args,\n                        type: instance.name\n                    });\n                }\n            };\n        }\n    }\n}\n\nexport { LogLevel, Logger, setLogLevel, setUserLogHandler };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,SAAS,GAAG,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ;AACZ,CAAC,UAAUA,QAAQ,EAAE;EACjBA,QAAQ,CAACA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzCA,QAAQ,CAACA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EAC7CA,QAAQ,CAACA,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvCA,QAAQ,CAACA,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EACvCA,QAAQ,CAACA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACzCA,QAAQ,CAACA,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AAC/C,CAAC,EAAEA,QAAQ,KAAKA,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAMC,iBAAiB,GAAG;EACtB,OAAO,EAAED,QAAQ,CAACE,KAAK;EACvB,SAAS,EAAEF,QAAQ,CAACG,OAAO;EAC3B,MAAM,EAAEH,QAAQ,CAACI,IAAI;EACrB,MAAM,EAAEJ,QAAQ,CAACK,IAAI;EACrB,OAAO,EAAEL,QAAQ,CAACM,KAAK;EACvB,QAAQ,EAAEN,QAAQ,CAACO;AACvB,CAAC;AACD;AACA;AACA;AACA,MAAMC,eAAe,GAAGR,QAAQ,CAACI,IAAI;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,aAAa,GAAG;EAClB,CAACT,QAAQ,CAACE,KAAK,GAAG,KAAK;EACvB,CAACF,QAAQ,CAACG,OAAO,GAAG,KAAK;EACzB,CAACH,QAAQ,CAACI,IAAI,GAAG,MAAM;EACvB,CAACJ,QAAQ,CAACK,IAAI,GAAG,MAAM;EACvB,CAACL,QAAQ,CAACM,KAAK,GAAG;AACtB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMI,iBAAiB,GAAGA,CAACC,QAAQ,EAAEC,OAAO,EAAE,GAAGC,IAAI,KAAK;EACtD,IAAID,OAAO,GAAGD,QAAQ,CAACG,QAAQ,EAAE;IAC7B;EACJ;EACA,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACpC,MAAMC,MAAM,GAAGT,aAAa,CAACG,OAAO,CAAC;EACrC,IAAIM,MAAM,EAAE;IACRC,OAAO,CAACD,MAAM,CAAC,CAAE,IAAGH,GAAI,MAAKJ,QAAQ,CAACS,IAAK,GAAE,EAAE,GAAGP,IAAI,CAAC;EAC3D,CAAC,MACI;IACD,MAAM,IAAIQ,KAAK,CAAE,8DAA6DT,OAAQ,GAAE,CAAC;EAC7F;AACJ,CAAC;AACD,MAAMU,MAAM,CAAC;EACT;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACH,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB;AACR;AACA;IACQ,IAAI,CAACI,SAAS,GAAGhB,eAAe;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACiB,WAAW,GAAGf,iBAAiB;IACpC;AACR;AACA;IACQ,IAAI,CAACgB,eAAe,GAAG,IAAI;IAC3B;AACR;AACA;IACQ3B,SAAS,CAAC4B,IAAI,CAAC,IAAI,CAAC;EACxB;EACA,IAAIb,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACU,SAAS;EACzB;EACA,IAAIV,QAAQA,CAACc,GAAG,EAAE;IACd,IAAI,EAAEA,GAAG,IAAI5B,QAAQ,CAAC,EAAE;MACpB,MAAM,IAAI6B,SAAS,CAAE,kBAAiBD,GAAI,4BAA2B,CAAC;IAC1E;IACA,IAAI,CAACJ,SAAS,GAAGI,GAAG;EACxB;EACA;EACAE,WAAWA,CAACF,GAAG,EAAE;IACb,IAAI,CAACJ,SAAS,GAAG,OAAOI,GAAG,KAAK,QAAQ,GAAG3B,iBAAiB,CAAC2B,GAAG,CAAC,GAAGA,GAAG;EAC3E;EACA,IAAIG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACN,WAAW;EAC3B;EACA,IAAIM,UAAUA,CAACH,GAAG,EAAE;IAChB,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;MAC3B,MAAM,IAAIC,SAAS,CAAC,mDAAmD,CAAC;IAC5E;IACA,IAAI,CAACJ,WAAW,GAAGG,GAAG;EAC1B;EACA,IAAII,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACN,eAAe;EAC/B;EACA,IAAIM,cAAcA,CAACJ,GAAG,EAAE;IACpB,IAAI,CAACF,eAAe,GAAGE,GAAG;EAC9B;EACA;AACJ;AACA;EACIK,KAAKA,CAAC,GAAGpB,IAAI,EAAE;IACX,IAAI,CAACa,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE1B,QAAQ,CAACE,KAAK,EAAE,GAAGW,IAAI,CAAC;IAC3E,IAAI,CAACY,WAAW,CAAC,IAAI,EAAEzB,QAAQ,CAACE,KAAK,EAAE,GAAGW,IAAI,CAAC;EACnD;EACAqB,GAAGA,CAAC,GAAGrB,IAAI,EAAE;IACT,IAAI,CAACa,eAAe,IAChB,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE1B,QAAQ,CAACG,OAAO,EAAE,GAAGU,IAAI,CAAC;IACzD,IAAI,CAACY,WAAW,CAAC,IAAI,EAAEzB,QAAQ,CAACG,OAAO,EAAE,GAAGU,IAAI,CAAC;EACrD;EACAsB,IAAIA,CAAC,GAAGtB,IAAI,EAAE;IACV,IAAI,CAACa,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE1B,QAAQ,CAACI,IAAI,EAAE,GAAGS,IAAI,CAAC;IAC1E,IAAI,CAACY,WAAW,CAAC,IAAI,EAAEzB,QAAQ,CAACI,IAAI,EAAE,GAAGS,IAAI,CAAC;EAClD;EACAuB,IAAIA,CAAC,GAAGvB,IAAI,EAAE;IACV,IAAI,CAACa,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE1B,QAAQ,CAACK,IAAI,EAAE,GAAGQ,IAAI,CAAC;IAC1E,IAAI,CAACY,WAAW,CAAC,IAAI,EAAEzB,QAAQ,CAACK,IAAI,EAAE,GAAGQ,IAAI,CAAC;EAClD;EACAwB,KAAKA,CAAC,GAAGxB,IAAI,EAAE;IACX,IAAI,CAACa,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC,IAAI,EAAE1B,QAAQ,CAACM,KAAK,EAAE,GAAGO,IAAI,CAAC;IAC3E,IAAI,CAACY,WAAW,CAAC,IAAI,EAAEzB,QAAQ,CAACM,KAAK,EAAE,GAAGO,IAAI,CAAC;EACnD;AACJ;AACA,SAASiB,WAAWA,CAACQ,KAAK,EAAE;EACxBvC,SAAS,CAACwC,OAAO,CAACC,IAAI,IAAI;IACtBA,IAAI,CAACV,WAAW,CAACQ,KAAK,CAAC;EAC3B,CAAC,CAAC;AACN;AACA,SAASG,iBAAiBA,CAACC,WAAW,EAAEC,OAAO,EAAE;EAC7C,KAAK,MAAMhC,QAAQ,IAAIZ,SAAS,EAAE;IAC9B,IAAI6C,cAAc,GAAG,IAAI;IACzB,IAAID,OAAO,IAAIA,OAAO,CAACL,KAAK,EAAE;MAC1BM,cAAc,GAAG3C,iBAAiB,CAAC0C,OAAO,CAACL,KAAK,CAAC;IACrD;IACA,IAAII,WAAW,KAAK,IAAI,EAAE;MACtB/B,QAAQ,CAACqB,cAAc,GAAG,IAAI;IAClC,CAAC,MACI;MACDrB,QAAQ,CAACqB,cAAc,GAAG,CAACrB,QAAQ,EAAE2B,KAAK,EAAE,GAAGzB,IAAI,KAAK;QACpD,MAAMgC,OAAO,GAAGhC,IAAI,CACfiC,GAAG,CAACC,GAAG,IAAI;UACZ,IAAIA,GAAG,IAAI,IAAI,EAAE;YACb,OAAO,IAAI;UACf,CAAC,MACI,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;YAC9B,OAAOA,GAAG;UACd,CAAC,MACI,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,SAAS,EAAE;YAC1D,OAAOA,GAAG,CAACC,QAAQ,CAAC,CAAC;UACzB,CAAC,MACI,IAAID,GAAG,YAAY1B,KAAK,EAAE;YAC3B,OAAO0B,GAAG,CAACF,OAAO;UACtB,CAAC,MACI;YACD,IAAI;cACA,OAAOI,IAAI,CAACC,SAAS,CAACH,GAAG,CAAC;YAC9B,CAAC,CACD,OAAOI,OAAO,EAAE;cACZ,OAAO,IAAI;YACf;UACJ;QACJ,CAAC,CAAC,CACGC,MAAM,CAACL,GAAG,IAAIA,GAAG,CAAC,CAClBM,IAAI,CAAC,GAAG,CAAC;QACd,IAAIf,KAAK,KAAKM,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGjC,QAAQ,CAACG,QAAQ,CAAC,EAAE;UACtG4B,WAAW,CAAC;YACRJ,KAAK,EAAEtC,QAAQ,CAACsC,KAAK,CAAC,CAACgB,WAAW,CAAC,CAAC;YACpCT,OAAO;YACPhC,IAAI;YACJ0C,IAAI,EAAE5C,QAAQ,CAACS;UACnB,CAAC,CAAC;QACN;MACJ,CAAC;IACL;EACJ;AACJ;AAEA,SAASpB,QAAQ,EAAEsB,MAAM,EAAEQ,WAAW,EAAEW,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}