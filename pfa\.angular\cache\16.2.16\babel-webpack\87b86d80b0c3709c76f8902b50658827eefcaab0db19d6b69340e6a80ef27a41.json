{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nexport class AccueilComponent {\n  static {\n    this.ɵfac = function AccueilComponent_Factory(t) {\n      return new (t || AccueilComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccueilComponent,\n      selectors: [[\"app-accueil\"]],\n      decls: 31,\n      vars: 2,\n      consts: [[1, \"accueil-container\"], [1, \"navbar\"], [1, \"logo\"], [1, \"nav-links\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 3, \"routerLinkActiveOptions\"], [\"href\", \"#\"], [1, \"register-button\"], [1, \"content\"], [1, \"text-zone\"], [1, \"start-button\"], [1, \"image-zone\"], [1, \"rectangles\"], [1, \"rectangle-vertical\"], [1, \"rectangle-horizontal\"], [\"src\", \"assets/iris.png\", \"alt\", \"Iris illustration\"]],\n      template: function AccueilComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"nav\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"ul\", 3)(5, \"li\")(6, \"a\", 4);\n          i0.ɵɵtext(7, \"accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"li\")(9, \"a\", 5);\n          i0.ɵɵtext(10, \"\\u00C0 propos de nous\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"li\")(12, \"a\", 5);\n          i0.ɵɵtext(13, \"Contact\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"button\", 6);\n          i0.ɵɵtext(15, \"Register\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 7)(17, \"div\", 8)(18, \"h1\");\n          i0.ɵɵtext(19, \"Iris & Identit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\");\n          i0.ɵɵtext(21, \" Chaque iris est une signature. \");\n          i0.ɵɵelement(22, \"br\");\n          i0.ɵɵtext(23, \" Notre syst\\u00E8me de profilage biom\\u00E9trique offre une s\\u00E9curit\\u00E9 in\\u00E9gal\\u00E9e, inspir\\u00E9e directement par la nature humaine. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 9);\n          i0.ɵɵtext(25, \"Commencer\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 10)(27, \"div\", 11);\n          i0.ɵɵelement(28, \"div\", 12)(29, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"img\", 14);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(1, _c0));\n        }\n      },\n      dependencies: [i1.RouterLink, i1.RouterLinkActive],\n      styles: [\".accueil-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #e1d8f1, #fbdde5);\\n  min-height: 100vh;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  font-weight: 500;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-family: \\\"Pacifico\\\", cursive;\\n  font-size: 24px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%] {\\n  list-style: none;\\n  display: flex;\\n  gap: 30px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: #000;\\n  font-size: 16px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  border: 2px solid #000;\\n  background: transparent;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  cursor: pointer;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  flex-grow: 1;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 600px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  font-weight: bold;\\n  margin-bottom: 20px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-style: italic;\\n  margin-bottom: 30px;\\n  line-height: 1.6;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   .start-button[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  border: 2px solid #000;\\n  border-radius: 30px;\\n  background-color: transparent;\\n  cursor: pointer;\\n  font-size: 16px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .bottom-illustration[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-top: 40px;\\n  text-align: center;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .bottom-illustration[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 400px;\\n  width: 90%;\\n  height: auto;\\n  position: relative;\\n  z-index: 2;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .bottom-illustration[_ngcontent-%COMP%]   .rectangles[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 10%;\\n  bottom: 20px;\\n  z-index: 1;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .bottom-illustration[_ngcontent-%COMP%]   .rectangles[_ngcontent-%COMP%]   .rectangle-vertical[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 80px;\\n  background-color: white;\\n  margin-bottom: 10px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .bottom-illustration[_ngcontent-%COMP%]   .rectangles[_ngcontent-%COMP%]   .rectangle-horizontal[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 6px;\\n  background-color: white;\\n  margin-top: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AccueilComponent", "selectors", "decls", "vars", "consts", "template", "AccueilComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-accueil',\n  templateUrl: './accueil.component.html',\n  styleUrls: ['./accueil.component.scss']\n})\nexport class AccueilComponent {\n\n}\n", "<div class=\"accueil-container\">\n    <nav class=\"navbar\">\n      <div class=\"logo\">IrisLock</div>\n      <ul class=\"nav-links\">\n        <li><a routerLink=\"/\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{ exact: true }\">accueil</a></li>\n        <li><a href=\"#\">À propos de nous</a></li>\n        <li><a href=\"#\">Contact</a></li>\n      </ul>\n      <button class=\"register-button\">Register</button>\n    </nav>\n  \n    <div class=\"content\">\n      <div class=\"text-zone\">\n        <h1>Iris & Identité</h1>\n        <p>\n          Chaque iris est une signature. <br />\n          Notre système de profilage biométrique offre une sécurité inégalée,\n          inspirée directement par la nature humaine.\n        </p>\n        <button class=\"start-button\">Commencer</button>\n      </div>\n  \n      <div class=\"image-zone\">\n        <div class=\"rectangles\">\n          <div class=\"rectangle-vertical\"></div>\n          <div class=\"rectangle-horizontal\"></div>\n        </div>\n        <img src=\"assets/iris.png\" alt=\"Iris illustration\" />\n      </div>\n    </div>\n  </div>\n  "], "mappings": ";;;;;;;AAOA,OAAM,MAAOA,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BE,EAAA,CAAAC,cAAA,aAA+B;UAEPD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,YAAsB;UACwED,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvGH,EAAA,CAAAC,cAAA,SAAI;UAAYD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpCH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE7BH,EAAA,CAAAC,cAAA,iBAAgC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGnDH,EAAA,CAAAC,cAAA,cAAqB;UAEbD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,wCAA+B;UAAAF,EAAA,CAAAI,SAAA,UAAM;UACrCJ,EAAA,CAAAE,MAAA,4JAEF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGjDH,EAAA,CAAAC,cAAA,eAAwB;UAEpBD,EAAA,CAAAI,SAAA,eAAsC;UAExCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAI,SAAA,eAAqD;UACvDJ,EAAA,CAAAG,YAAA,EAAM;;;UAxB4CH,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAM,UAAA,4BAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}