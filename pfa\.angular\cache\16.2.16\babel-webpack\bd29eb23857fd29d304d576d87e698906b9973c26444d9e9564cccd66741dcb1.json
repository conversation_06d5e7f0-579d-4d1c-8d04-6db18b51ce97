{"ast": null, "code": "import * as i0 from \"@angular/core\";\nconst _c0 = function () {\n  return {\n    exact: true\n  };\n};\nexport class AccueilComponent {\n  static {\n    this.ɵfac = function AccueilComponent_Factory(t) {\n      return new (t || AccueilComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccueilComponent,\n      selectors: [[\"app-accueil\"]],\n      decls: 25,\n      vars: 2,\n      consts: [[1, \"logo\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 3, \"routerLinkActiveOptions\"], [\"routerLink\", \"/about\"], [\"routerLink\", \"/contact\"], [1, \"register-btn\"], [1, \"hero\"], [1, \"hero-text\"], [1, \"start-btn\"], [1, \"hero-image\"], [\"src\", \"assets/images/iris.png\", \"alt\", \"Illustration d'iris biom\\u00E9triques\"]],\n      template: function AccueilComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\")(1, \"div\", 0);\n          i0.ɵɵtext(2, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nav\")(4, \"a\", 1);\n          i0.ɵɵtext(5, \"accueil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"a\", 2);\n          i0.ɵɵtext(7, \"\\u00C0 propos de nous\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"a\", 3);\n          i0.ɵɵtext(9, \"Contact\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"button\", 4);\n          i0.ɵɵtext(11, \"Register\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"main\")(13, \"section\", 5)(14, \"div\", 6)(15, \"h1\");\n          i0.ɵɵtext(16, \"Iris & Identit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\");\n          i0.ɵɵtext(18, \" Chaque iris est une signature.\");\n          i0.ɵɵelement(19, \"br\");\n          i0.ɵɵtext(20, \" Notre syst\\u00E8me de profilage biom\\u00E9trique offre une s\\u00E9curit\\u00E9 in\\u00E9gal\\u00E9e, inspir\\u00E9e directement par la nature humaine. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 7);\n          i0.ɵɵtext(22, \"Commencer\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 8);\n          i0.ɵɵelement(24, \"img\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(1, _c0));\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AccueilComponent", "selectors", "decls", "vars", "consts", "template", "AccueilComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-accueil',\n  templateUrl: './accueil.component.html',\n  styleUrls: ['./accueil.component.scss']\n})\nexport class AccueilComponent {\n\n}\n", "<header>\n    <div class=\"logo\">IrisLock</div>\n    <nav>\n      <a routerLink=\"/\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{ exact: true }\">accueil</a>\n      <a routerLink=\"/about\">À propos de nous</a>\n      <a routerLink=\"/contact\">Contact</a>\n    </nav>\n    <button class=\"register-btn\">Register</button>\n  </header>\n  \n  <main>\n    <section class=\"hero\">\n      <div class=\"hero-text\">\n        <h1>Iris & Identité</h1>\n        <p>\n          Chaque iris est une signature.<br />\n          Notre système de profilage biométrique offre une sécurité inégalée,\n          inspirée directement par la nature humaine.\n        </p>\n        <button class=\"start-btn\">Commencer</button>\n      </div>\n      <div class=\"hero-image\">\n        <img src=\"assets/images/iris.png\" alt=\"Illustration d'iris biométriques\" />\n      </div>\n    </section>\n  </main>\n  "], "mappings": ";;;;;;AAOA,OAAM,MAAOA,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BE,EAAA,CAAAC,cAAA,aAAQ;UACcD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,UAAK;UACqFD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnGH,EAAA,CAAAC,cAAA,WAAuB;UAAAD,EAAA,CAAAE,MAAA,4BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC3CH,EAAA,CAAAC,cAAA,WAAyB;UAAAD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEtCH,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGhDH,EAAA,CAAAC,cAAA,YAAM;UAGID,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,uCAA8B;UAAAF,EAAA,CAAAI,SAAA,UAAM;UACpCJ,EAAA,CAAAE,MAAA,4JAEF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,iBAA0B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE9CH,EAAA,CAAAC,cAAA,cAAwB;UACtBD,EAAA,CAAAI,SAAA,cAA2E;UAC7EJ,EAAA,CAAAG,YAAA,EAAM;;;UApBsCH,EAAA,CAAAK,SAAA,GAA2C;UAA3CL,EAAA,CAAAM,UAAA,4BAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAA2C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}