{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class IrisCompatibilityService {\n  constructor() {\n    // Types d'iris avec leurs personnalités compatibles\n    this.irisTypes = [{\n      id: 'crypte-dominant',\n      name: 'Crypte Dominant',\n      description: 'Iris avec des cryptes prononcées, indique une personnalité analytique',\n      compatiblePersonalities: ['Jewel', 'Jewel-Shaker', 'Flower-Jewel'],\n      characteristics: ['Analytique', 'Méthodique', 'Logique', 'Structuré']\n    }, {\n      id: 'lacunes-radiales',\n      name: 'Lacunes Radiales',\n      description: 'Iris avec des lacunes radiales, indique une personnalité créative',\n      compatiblePersonalities: ['Flower', 'Stream-Flower', 'Flower-Jewel'],\n      characteristics: ['Créatif', 'Émotionnel', 'Intuitif', 'Artistique']\n    }, {\n      id: 'fibres-rayonnantes',\n      name: 'Fibres Rayonnantes',\n      description: 'Iris avec des fibres rayonnantes, indique une personnalité dynamique',\n      compatiblePersonalities: ['Shaker', 'Jewel-Shaker', 'Shaker-Stream'],\n      characteristics: ['Dynamique', 'Énergique', 'Aventurier', 'Spontané']\n    }, {\n      id: 'texture-fine',\n      name: 'Texture Fine',\n      description: 'Iris avec une texture fine, indique une personnalité paisible',\n      compatiblePersonalities: ['Stream', 'Stream-Flower', 'Shaker-Stream'],\n      characteristics: ['Paisible', 'Réfléchi', 'Calme', 'Diplomate']\n    }, {\n      id: 'mixte-complexe',\n      name: 'Mixte Complexe',\n      description: 'Iris avec plusieurs caractéristiques, personnalité équilibrée',\n      compatiblePersonalities: ['Flower-Jewel', 'Jewel-Shaker', 'Shaker-Stream', 'Stream-Flower'],\n      characteristics: ['Équilibré', 'Adaptable', 'Polyvalent', 'Complexe']\n    }];\n  }\n  /**\n   * Simule la détection du type d'iris (en réalité, cela viendrait d'une analyse d'image)\n   */\n  detectIrisType(userEmail) {\n    // Simulation basée sur l'email pour les tests\n    const hash = this.hashCode(userEmail);\n    const index = Math.abs(hash) % this.irisTypes.length;\n    return this.irisTypes[index];\n  }\n  /**\n   * Vérifie la compatibilité entre l'iris et le profil de personnalité\n   */\n  checkCompatibility(personalityProfile, userEmail) {\n    const irisType = this.detectIrisType(userEmail);\n    const isCompatible = irisType.compatiblePersonalities.includes(personalityProfile.primaryClass);\n    let compatibilityScore = 0;\n    if (isCompatible) {\n      compatibilityScore = 85 + Math.random() * 15; // 85-100%\n    } else {\n      compatibilityScore = 30 + Math.random() * 40; // 30-70%\n    }\n\n    const recommendations = this.generateRecommendations(irisType, personalityProfile, isCompatible);\n    const corrections = this.generateCorrections(irisType, personalityProfile, isCompatible);\n    return {\n      isCompatible,\n      compatibilityScore: Math.round(compatibilityScore),\n      irisType,\n      personalityProfile,\n      recommendations,\n      corrections\n    };\n  }\n  /**\n   * Génère des recommandations basées sur la compatibilité\n   */\n  generateRecommendations(iris, personality, isCompatible) {\n    if (isCompatible) {\n      return [`Votre iris ${iris.name} est parfaitement compatible avec votre profil ${personality.primaryClass}`, `Continuez à développer vos traits ${iris.characteristics.join(', ').toLowerCase()}`, `Votre authenticité naturelle est un atout majeur`, `Exploitez cette harmonie pour maximiser votre potentiel`];\n    } else {\n      return [`Il y a une divergence entre votre iris ${iris.name} et votre profil ${personality.primaryClass}`, `Considérez développer davantage vos aspects ${iris.characteristics.join(', ').toLowerCase()}`, `Cette différence peut être une source de croissance personnelle`, `Travaillez sur l'équilibre entre vos tendances naturelles et acquises`];\n    }\n  }\n  /**\n   * Génère des corrections suggérées\n   */\n  generateCorrections(iris, personality, isCompatible) {\n    if (isCompatible) {\n      return ['Aucune correction majeure nécessaire', 'Continuez sur votre voie actuelle', 'Affinez vos points forts existants'];\n    } else {\n      const corrections = [];\n      // Corrections spécifiques selon le type d'iris\n      switch (iris.id) {\n        case 'crypte-dominant':\n          corrections.push('Développez davantage votre côté analytique et méthodique', 'Prenez plus de temps pour planifier vos actions', 'Utilisez des outils de structuration (listes, tableaux, plans)');\n          break;\n        case 'lacunes-radiales':\n          corrections.push('Explorez davantage votre créativité et votre intuition', 'Accordez plus d\\'importance à vos émotions', 'Engagez-vous dans des activités artistiques ou créatives');\n          break;\n        case 'fibres-rayonnantes':\n          corrections.push('Embrassez davantage l\\'action et le dynamisme', 'Sortez de votre zone de confort plus souvent', 'Prenez des initiatives et soyez plus spontané');\n          break;\n        case 'texture-fine':\n          corrections.push('Cultivez la patience et la réflexion', 'Privilégiez les environnements calmes', 'Développez vos capacités d\\'écoute et de médiation');\n          break;\n        default:\n          corrections.push('Travaillez sur l\\'équilibre entre tous vos aspects', 'Développez votre adaptabilité', 'Explorez différentes facettes de votre personnalité');\n      }\n      return corrections;\n    }\n  }\n  /**\n   * Fonction de hachage simple pour simuler la détection d'iris\n   */\n  hashCode(str) {\n    let hash = 0;\n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i);\n      hash = (hash << 5) - hash + char;\n      hash = hash & hash; // Convert to 32bit integer\n    }\n\n    return hash;\n  }\n  /**\n   * Obtient tous les types d'iris disponibles\n   */\n  getAllIrisTypes() {\n    return [...this.irisTypes];\n  }\n  /**\n   * Obtient un type d'iris par son ID\n   */\n  getIrisTypeById(id) {\n    return this.irisTypes.find(iris => iris.id === id);\n  }\n  static {\n    this.ɵfac = function IrisCompatibilityService_Factory(t) {\n      return new (t || IrisCompatibilityService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: IrisCompatibilityService,\n      factory: IrisCompatibilityService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["IrisCompatibilityService", "constructor", "irisTypes", "id", "name", "description", "compatiblePersonalities", "characteristics", "detectIrisType", "userEmail", "hash", "hashCode", "index", "Math", "abs", "length", "checkCompatibility", "personalityProfile", "irisType", "isCompatible", "includes", "primaryClass", "compatibilityScore", "random", "recommendations", "generateRecommendations", "corrections", "generateCorrections", "round", "iris", "personality", "join", "toLowerCase", "push", "str", "i", "char", "charCodeAt", "getAllIrisTypes", "getIrisTypeById", "find", "factory", "ɵfac", "providedIn"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\services\\iris-compatibility.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { PersonalityClass, PersonalityProfile } from '../models/personality-test.model';\nimport { IrisImageService, IrisAnalysisResult } from './iris-image.service';\n\nexport interface IrisType {\n  id: string;\n  name: string;\n  description: string;\n  compatiblePersonalities: PersonalityClass[];\n  characteristics: string[];\n}\n\nexport interface CompatibilityResult {\n  isCompatible: boolean;\n  compatibilityScore: number;\n  irisType: IrisType;\n  personalityProfile: PersonalityProfile;\n  recommendations: string[];\n  corrections: string[];\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class IrisCompatibilityService {\n\n  // Types d'iris avec leurs personnalités compatibles\n  private irisTypes: IrisType[] = [\n    {\n      id: 'crypte-dominant',\n      name: 'Crypte Dominant',\n      description: 'Iris avec des cryptes prononcées, indique une personnalité analytique',\n      compatiblePersonalities: ['Jewel', 'Jewel-Shaker', 'Flower-Jewel'],\n      characteristics: ['Analytique', 'Méthodique', 'Logique', 'Structuré']\n    },\n    {\n      id: 'lacunes-radiales',\n      name: 'Lacunes Radiales',\n      description: 'Iris avec des lacunes radiales, indique une personnalité créative',\n      compatiblePersonalities: ['Flower', 'Stream-Flower', 'Flower-Jewel'],\n      characteristics: ['Créatif', 'Émotionnel', 'Intuitif', 'Artistique']\n    },\n    {\n      id: 'fibres-rayonnantes',\n      name: 'Fibres Rayonnantes',\n      description: 'Iris avec des fibres rayonnantes, indique une personnalité dynamique',\n      compatiblePersonalities: ['Shaker', 'Jewel-Shaker', 'Shaker-Stream'],\n      characteristics: ['Dynamique', 'Énergique', 'Aventurier', 'Spontané']\n    },\n    {\n      id: 'texture-fine',\n      name: 'Texture Fine',\n      description: 'Iris avec une texture fine, indique une personnalité paisible',\n      compatiblePersonalities: ['Stream', 'Stream-Flower', 'Shaker-Stream'],\n      characteristics: ['Paisible', 'Réfléchi', 'Calme', 'Diplomate']\n    },\n    {\n      id: 'mixte-complexe',\n      name: 'Mixte Complexe',\n      description: 'Iris avec plusieurs caractéristiques, personnalité équilibrée',\n      compatiblePersonalities: ['Flower-Jewel', 'Jewel-Shaker', 'Shaker-Stream', 'Stream-Flower'],\n      characteristics: ['Équilibré', 'Adaptable', 'Polyvalent', 'Complexe']\n    }\n  ];\n\n  constructor() { }\n\n  /**\n   * Simule la détection du type d'iris (en réalité, cela viendrait d'une analyse d'image)\n   */\n  detectIrisType(userEmail: string): IrisType {\n    // Simulation basée sur l'email pour les tests\n    const hash = this.hashCode(userEmail);\n    const index = Math.abs(hash) % this.irisTypes.length;\n    return this.irisTypes[index];\n  }\n\n  /**\n   * Vérifie la compatibilité entre l'iris et le profil de personnalité\n   */\n  checkCompatibility(personalityProfile: PersonalityProfile, userEmail: string): CompatibilityResult {\n    const irisType = this.detectIrisType(userEmail);\n    const isCompatible = irisType.compatiblePersonalities.includes(personalityProfile.primaryClass);\n\n    let compatibilityScore = 0;\n    if (isCompatible) {\n      compatibilityScore = 85 + Math.random() * 15; // 85-100%\n    } else {\n      compatibilityScore = 30 + Math.random() * 40; // 30-70%\n    }\n\n    const recommendations = this.generateRecommendations(irisType, personalityProfile, isCompatible);\n    const corrections = this.generateCorrections(irisType, personalityProfile, isCompatible);\n\n    return {\n      isCompatible,\n      compatibilityScore: Math.round(compatibilityScore),\n      irisType,\n      personalityProfile,\n      recommendations,\n      corrections\n    };\n  }\n\n  /**\n   * Génère des recommandations basées sur la compatibilité\n   */\n  private generateRecommendations(iris: IrisType, personality: PersonalityProfile, isCompatible: boolean): string[] {\n    if (isCompatible) {\n      return [\n        `Votre iris ${iris.name} est parfaitement compatible avec votre profil ${personality.primaryClass}`,\n        `Continuez à développer vos traits ${iris.characteristics.join(', ').toLowerCase()}`,\n        `Votre authenticité naturelle est un atout majeur`,\n        `Exploitez cette harmonie pour maximiser votre potentiel`\n      ];\n    } else {\n      return [\n        `Il y a une divergence entre votre iris ${iris.name} et votre profil ${personality.primaryClass}`,\n        `Considérez développer davantage vos aspects ${iris.characteristics.join(', ').toLowerCase()}`,\n        `Cette différence peut être une source de croissance personnelle`,\n        `Travaillez sur l'équilibre entre vos tendances naturelles et acquises`\n      ];\n    }\n  }\n\n  /**\n   * Génère des corrections suggérées\n   */\n  private generateCorrections(iris: IrisType, personality: PersonalityProfile, isCompatible: boolean): string[] {\n    if (isCompatible) {\n      return [\n        'Aucune correction majeure nécessaire',\n        'Continuez sur votre voie actuelle',\n        'Affinez vos points forts existants'\n      ];\n    } else {\n      const corrections = [];\n\n      // Corrections spécifiques selon le type d'iris\n      switch (iris.id) {\n        case 'crypte-dominant':\n          corrections.push(\n            'Développez davantage votre côté analytique et méthodique',\n            'Prenez plus de temps pour planifier vos actions',\n            'Utilisez des outils de structuration (listes, tableaux, plans)'\n          );\n          break;\n        case 'lacunes-radiales':\n          corrections.push(\n            'Explorez davantage votre créativité et votre intuition',\n            'Accordez plus d\\'importance à vos émotions',\n            'Engagez-vous dans des activités artistiques ou créatives'\n          );\n          break;\n        case 'fibres-rayonnantes':\n          corrections.push(\n            'Embrassez davantage l\\'action et le dynamisme',\n            'Sortez de votre zone de confort plus souvent',\n            'Prenez des initiatives et soyez plus spontané'\n          );\n          break;\n        case 'texture-fine':\n          corrections.push(\n            'Cultivez la patience et la réflexion',\n            'Privilégiez les environnements calmes',\n            'Développez vos capacités d\\'écoute et de médiation'\n          );\n          break;\n        default:\n          corrections.push(\n            'Travaillez sur l\\'équilibre entre tous vos aspects',\n            'Développez votre adaptabilité',\n            'Explorez différentes facettes de votre personnalité'\n          );\n      }\n\n      return corrections;\n    }\n  }\n\n  /**\n   * Fonction de hachage simple pour simuler la détection d'iris\n   */\n  private hashCode(str: string): number {\n    let hash = 0;\n    for (let i = 0; i < str.length; i++) {\n      const char = str.charCodeAt(i);\n      hash = ((hash << 5) - hash) + char;\n      hash = hash & hash; // Convert to 32bit integer\n    }\n    return hash;\n  }\n\n  /**\n   * Obtient tous les types d'iris disponibles\n   */\n  getAllIrisTypes(): IrisType[] {\n    return [...this.irisTypes];\n  }\n\n  /**\n   * Obtient un type d'iris par son ID\n   */\n  getIrisTypeById(id: string): IrisType | undefined {\n    return this.irisTypes.find(iris => iris.id === id);\n  }\n}\n"], "mappings": ";AAwBA,OAAM,MAAOA,wBAAwB;EAyCnCC,YAAA;IAvCA;IACQ,KAAAC,SAAS,GAAe,CAC9B;MACEC,EAAE,EAAE,iBAAiB;MACrBC,IAAI,EAAE,iBAAiB;MACvBC,WAAW,EAAE,uEAAuE;MACpFC,uBAAuB,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,cAAc,CAAC;MAClEC,eAAe,EAAE,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW;KACrE,EACD;MACEJ,EAAE,EAAE,kBAAkB;MACtBC,IAAI,EAAE,kBAAkB;MACxBC,WAAW,EAAE,mEAAmE;MAChFC,uBAAuB,EAAE,CAAC,QAAQ,EAAE,eAAe,EAAE,cAAc,CAAC;MACpEC,eAAe,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY;KACpE,EACD;MACEJ,EAAE,EAAE,oBAAoB;MACxBC,IAAI,EAAE,oBAAoB;MAC1BC,WAAW,EAAE,sEAAsE;MACnFC,uBAAuB,EAAE,CAAC,QAAQ,EAAE,cAAc,EAAE,eAAe,CAAC;MACpEC,eAAe,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU;KACrE,EACD;MACEJ,EAAE,EAAE,cAAc;MAClBC,IAAI,EAAE,cAAc;MACpBC,WAAW,EAAE,+DAA+D;MAC5EC,uBAAuB,EAAE,CAAC,QAAQ,EAAE,eAAe,EAAE,eAAe,CAAC;MACrEC,eAAe,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW;KAC/D,EACD;MACEJ,EAAE,EAAE,gBAAgB;MACpBC,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE,+DAA+D;MAC5EC,uBAAuB,EAAE,CAAC,cAAc,EAAE,cAAc,EAAE,eAAe,EAAE,eAAe,CAAC;MAC3FC,eAAe,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU;KACrE,CACF;EAEe;EAEhB;;;EAGAC,cAAcA,CAACC,SAAiB;IAC9B;IACA,MAAMC,IAAI,GAAG,IAAI,CAACC,QAAQ,CAACF,SAAS,CAAC;IACrC,MAAMG,KAAK,GAAGC,IAAI,CAACC,GAAG,CAACJ,IAAI,CAAC,GAAG,IAAI,CAACR,SAAS,CAACa,MAAM;IACpD,OAAO,IAAI,CAACb,SAAS,CAACU,KAAK,CAAC;EAC9B;EAEA;;;EAGAI,kBAAkBA,CAACC,kBAAsC,EAAER,SAAiB;IAC1E,MAAMS,QAAQ,GAAG,IAAI,CAACV,cAAc,CAACC,SAAS,CAAC;IAC/C,MAAMU,YAAY,GAAGD,QAAQ,CAACZ,uBAAuB,CAACc,QAAQ,CAACH,kBAAkB,CAACI,YAAY,CAAC;IAE/F,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,IAAIH,YAAY,EAAE;MAChBG,kBAAkB,GAAG,EAAE,GAAGT,IAAI,CAACU,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;KAC/C,MAAM;MACLD,kBAAkB,GAAG,EAAE,GAAGT,IAAI,CAACU,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC;;;IAGhD,MAAMC,eAAe,GAAG,IAAI,CAACC,uBAAuB,CAACP,QAAQ,EAAED,kBAAkB,EAAEE,YAAY,CAAC;IAChG,MAAMO,WAAW,GAAG,IAAI,CAACC,mBAAmB,CAACT,QAAQ,EAAED,kBAAkB,EAAEE,YAAY,CAAC;IAExF,OAAO;MACLA,YAAY;MACZG,kBAAkB,EAAET,IAAI,CAACe,KAAK,CAACN,kBAAkB,CAAC;MAClDJ,QAAQ;MACRD,kBAAkB;MAClBO,eAAe;MACfE;KACD;EACH;EAEA;;;EAGQD,uBAAuBA,CAACI,IAAc,EAAEC,WAA+B,EAAEX,YAAqB;IACpG,IAAIA,YAAY,EAAE;MAChB,OAAO,CACL,cAAcU,IAAI,CAACzB,IAAI,kDAAkD0B,WAAW,CAACT,YAAY,EAAE,EACnG,qCAAqCQ,IAAI,CAACtB,eAAe,CAACwB,IAAI,CAAC,IAAI,CAAC,CAACC,WAAW,EAAE,EAAE,EACpF,kDAAkD,EAClD,yDAAyD,CAC1D;KACF,MAAM;MACL,OAAO,CACL,0CAA0CH,IAAI,CAACzB,IAAI,oBAAoB0B,WAAW,CAACT,YAAY,EAAE,EACjG,+CAA+CQ,IAAI,CAACtB,eAAe,CAACwB,IAAI,CAAC,IAAI,CAAC,CAACC,WAAW,EAAE,EAAE,EAC9F,iEAAiE,EACjE,uEAAuE,CACxE;;EAEL;EAEA;;;EAGQL,mBAAmBA,CAACE,IAAc,EAAEC,WAA+B,EAAEX,YAAqB;IAChG,IAAIA,YAAY,EAAE;MAChB,OAAO,CACL,sCAAsC,EACtC,mCAAmC,EACnC,oCAAoC,CACrC;KACF,MAAM;MACL,MAAMO,WAAW,GAAG,EAAE;MAEtB;MACA,QAAQG,IAAI,CAAC1B,EAAE;QACb,KAAK,iBAAiB;UACpBuB,WAAW,CAACO,IAAI,CACd,0DAA0D,EAC1D,iDAAiD,EACjD,gEAAgE,CACjE;UACD;QACF,KAAK,kBAAkB;UACrBP,WAAW,CAACO,IAAI,CACd,wDAAwD,EACxD,4CAA4C,EAC5C,0DAA0D,CAC3D;UACD;QACF,KAAK,oBAAoB;UACvBP,WAAW,CAACO,IAAI,CACd,+CAA+C,EAC/C,8CAA8C,EAC9C,+CAA+C,CAChD;UACD;QACF,KAAK,cAAc;UACjBP,WAAW,CAACO,IAAI,CACd,sCAAsC,EACtC,uCAAuC,EACvC,oDAAoD,CACrD;UACD;QACF;UACEP,WAAW,CAACO,IAAI,CACd,oDAAoD,EACpD,+BAA+B,EAC/B,qDAAqD,CACtD;;MAGL,OAAOP,WAAW;;EAEtB;EAEA;;;EAGQf,QAAQA,CAACuB,GAAW;IAC1B,IAAIxB,IAAI,GAAG,CAAC;IACZ,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACnB,MAAM,EAAEoB,CAAC,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAGF,GAAG,CAACG,UAAU,CAACF,CAAC,CAAC;MAC9BzB,IAAI,GAAI,CAACA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAI0B,IAAI;MAClC1B,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC,CAAC;;;IAEtB,OAAOA,IAAI;EACb;EAEA;;;EAGA4B,eAAeA,CAAA;IACb,OAAO,CAAC,GAAG,IAAI,CAACpC,SAAS,CAAC;EAC5B;EAEA;;;EAGAqC,eAAeA,CAACpC,EAAU;IACxB,OAAO,IAAI,CAACD,SAAS,CAACsC,IAAI,CAACX,IAAI,IAAIA,IAAI,CAAC1B,EAAE,KAAKA,EAAE,CAAC;EACpD;;;uBArLWH,wBAAwB;IAAA;EAAA;;;aAAxBA,wBAAwB;MAAAyC,OAAA,EAAxBzC,wBAAwB,CAAA0C,IAAA;MAAAC,UAAA,EAFvB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}