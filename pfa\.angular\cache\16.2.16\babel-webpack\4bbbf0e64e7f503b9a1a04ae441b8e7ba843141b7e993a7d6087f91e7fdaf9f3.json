{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ShakerComponent {\n  static {\n    this.ɵfac = function ShakerComponent_Factory(t) {\n      return new (t || ShakerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShakerComponent,\n      selectors: [[\"app-shaker\"]],\n      decls: 2,\n      vars: 0,\n      template: function ShakerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"shaker works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\".iris-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  background: linear-gradient(to right, #f8e8ff, #f6f1ff);\\n  min-height: 100vh;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 180px;\\n  height: 180px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\\n  margin-bottom: 25px;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: bold;\\n  color: #5e548e;\\n  margin-bottom: 15px;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n  max-width: 700px;\\n  font-size: 1.1rem;\\n  line-height: 1.7;\\n  color: #3d3d3d;\\n  text-align: center;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  background: rgba(255, 255, 255, 0.8);\\n  padding: 20px;\\n  border-radius: 20px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (max-width: 768px) {\\n  .iris-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n    width: 140px;\\n    height: 140px;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    padding: 15px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ShakerComponent", "selectors", "decls", "vars", "template", "ShakerComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shaker\\shaker.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shaker\\shaker.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-shaker',\n  templateUrl: './shaker.component.html',\n  styleUrls: ['./shaker.component.scss']\n})\nexport class ShakerComponent {\n\n}\n", "<p>shaker works!</p>\n"], "mappings": ";AAOA,OAAM,MAAOA,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}