{"ast": null, "code": "import _asyncToGenerator from \"D:/ProjetPfa/pfa/pfa/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/iris-image.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction LoginComponent_div_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"L'email est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Veuillez entrer un email valide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, LoginComponent_div_20_span_1_Template, 2, 0, \"span\", 35);\n    i0.ɵɵtemplate(2, LoginComponent_div_20_span_2_Template, 2, 0, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r1 = i0.ɵɵreference(19);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"email\"]);\n  }\n}\nfunction LoginComponent_div_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le mot de passe est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_31_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le mot de passe doit contenir au moins 6 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtemplate(1, LoginComponent_div_31_span_1_Template, 2, 0, \"span\", 35);\n    i0.ɵɵtemplate(2, LoginComponent_div_31_span_2_Template, 2, 0, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(28);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r3.errors == null ? null : _r3.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r3.errors == null ? null : _r3.errors[\"minlength\"]);\n  }\n}\nfunction LoginComponent_div_50_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵtext(2, \"\\uD83D\\uDCF8\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"T\\u00E9l\\u00E9charger une image d'iris\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Formats accept\\u00E9s : JPG, PNG, WebP (max 5MB)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"input\", 59);\n    i0.ɵɵlistener(\"change\", function LoginComponent_div_50_div_2_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onIrisFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 60);\n    i0.ɵɵtext(9, \" Choisir une image \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoginComponent_div_50_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\uD83D\\uDD0D Analyser l'iris\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_50_div_3_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u23F3 Analyse en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_50_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelement(1, \"img\", 62);\n    i0.ɵɵelementStart(2, \"div\", 63)(3, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_50_div_3_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.analyzeIris());\n    });\n    i0.ɵɵtemplate(4, LoginComponent_div_50_div_3_span_4_Template, 2, 0, \"span\", 35);\n    i0.ɵɵtemplate(5, LoginComponent_div_50_div_3_span_5_Template, 2, 0, \"span\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_50_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.removeIrisImage());\n    });\n    i0.ɵɵtext(7, \" \\uD83D\\uDDD1\\uFE0F Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r14.irisPreviewUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r14.isAnalyzingIris);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r14.isAnalyzingIris);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.isAnalyzingIris);\n  }\n}\nfunction LoginComponent_div_50_div_4_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 79);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const char_r25 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", char_r25, \" \");\n  }\n}\nfunction LoginComponent_div_50_div_4_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const color_r26 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", color_r26, \" \");\n  }\n}\nfunction LoginComponent_div_50_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"h4\");\n    i0.ɵɵtext(2, \"\\uD83D\\uDCCA R\\u00E9sultats de l'analyse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 67)(4, \"div\", 68)(5, \"span\", 69);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 70);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 71)(10, \"h5\");\n    i0.ɵɵtext(11, \"Caract\\u00E9ristiques d\\u00E9tect\\u00E9es :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 72);\n    i0.ɵɵtemplate(13, LoginComponent_div_50_div_4_span_13_Template, 2, 1, \"span\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 74)(15, \"h5\");\n    i0.ɵɵtext(16, \"Couleurs dominantes :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 75);\n    i0.ɵɵtemplate(18, LoginComponent_div_50_div_4_span_18_Template, 2, 1, \"span\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 77)(20, \"span\", 78);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Type d\\u00E9tect\\u00E9 : \", ctx_r15.irisAnalysisResult.irisType, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Confiance : \", ctx_r15.irisAnalysisResult.confidence, \"%\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.irisAnalysisResult.characteristics);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.irisAnalysisResult.dominantColors);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Score de compatibilit\\u00E9 : \", ctx_r15.irisAnalysisResult.compatibilityScore, \"% \");\n  }\n}\nfunction LoginComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53);\n    i0.ɵɵtemplate(2, LoginComponent_div_50_div_2_Template, 10, 0, \"div\", 54);\n    i0.ɵɵtemplate(3, LoginComponent_div_50_div_3_Template, 8, 4, \"div\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, LoginComponent_div_50_div_4_Template, 22, 5, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"has-image\", ctx_r5.irisPreviewUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.irisPreviewUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.irisPreviewUrl);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.irisAnalysisResult);\n  }\n}\nfunction LoginComponent_span_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Connexion en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_72_Template_div_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r29);\n      const account_r27 = restoredCtx.$implicit;\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.fillTestAccount(account_r27));\n    });\n    i0.ɵɵelementStart(1, \"div\", 82)(2, \"h4\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 83);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 84);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 85);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const account_r27 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(account_r27.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(account_r27.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Mot de passe: \", account_r27.password, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"role-\" + account_r27.role);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(account_r27.role);\n  }\n}\nexport class LoginComponent {\n  constructor(router, irisImageService) {\n    this.router = router;\n    this.irisImageService = irisImageService;\n    this.loginData = {\n      email: '',\n      password: '',\n      rememberMe: false\n    };\n    this.showPassword = false;\n    this.isLoading = false;\n    // Variables pour l'image d'iris\n    this.selectedIrisFile = null;\n    this.irisPreviewUrl = null;\n    this.isAnalyzingIris = false;\n    this.irisAnalysisResult = null;\n    this.showIrisSection = false;\n    // Comptes statiques pour les tests\n    this.staticAccounts = [{\n      email: '<EMAIL>',\n      password: 'admin123',\n      name: 'Administrateur Test',\n      role: 'admin'\n    }, {\n      email: '<EMAIL>',\n      password: 'user123',\n      name: 'Utilisateur Test',\n      role: 'user'\n    }, {\n      email: '<EMAIL>',\n      password: 'marie123',\n      name: 'Marie Dubois',\n      role: 'user'\n    }, {\n      email: '<EMAIL>',\n      password: 'jean123',\n      name: 'Jean Martin',\n      role: 'user'\n    }];\n  }\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  onSubmit() {\n    if (this.isLoading) return;\n    this.isLoading = true;\n    // Simuler une connexion avec un délai\n    setTimeout(() => {\n      console.log('Tentative de connexion avec:', this.loginData);\n      // Vérifier si les identifiants correspondent à un compte statique\n      const account = this.staticAccounts.find(acc => acc.email === this.loginData.email && acc.password === this.loginData.password);\n      if (account) {\n        console.log('Connexion réussie avec le compte:', account.name);\n        // Sauvegarder l'image d'iris si elle a été analysée\n        if (this.selectedIrisFile && this.irisAnalysisResult) {\n          this.saveIrisImage(account.email, account.name);\n        }\n        // Sauvegarder les informations de l'utilisateur connecté\n        localStorage.setItem('currentUser', JSON.stringify({\n          name: account.name,\n          email: account.email,\n          role: account.role,\n          hasIrisImage: this.selectedIrisFile !== null,\n          irisAnalysis: this.irisAnalysisResult\n        }));\n        // Rediriger selon le rôle\n        if (account.role === 'admin') {\n          this.router.navigate(['/dashboard']);\n        } else {\n          // Les utilisateurs normaux vont directement au test de personnalité\n          this.router.navigate(['/personality-test']);\n        }\n      } else {\n        console.log('Identifiants incorrects');\n        alert('Email ou mot de passe incorrect. Utilisez un des comptes de test.');\n      }\n      this.isLoading = false;\n    }, 1500);\n  }\n  /**\n   * Remplit automatiquement le formulaire avec un compte de test\n   */\n  fillTestAccount(account) {\n    this.loginData.email = account.email;\n    this.loginData.password = account.password;\n  }\n  /**\n   * Affiche/masque la section d'upload d'iris\n   */\n  toggleIrisSection() {\n    this.showIrisSection = !this.showIrisSection;\n  }\n  /**\n   * Gère la sélection d'un fichier d'iris\n   */\n  onIrisFileSelected(event) {\n    const file = event.target.files[0];\n    if (!file) return;\n    // Valider le fichier\n    const validation = this.irisImageService.validateImageFile(file);\n    if (!validation.isValid) {\n      alert(validation.error);\n      return;\n    }\n    this.selectedIrisFile = file;\n    // Créer un aperçu de l'image\n    const reader = new FileReader();\n    reader.onload = e => {\n      this.irisPreviewUrl = e.target.result;\n    };\n    reader.readAsDataURL(file);\n    console.log('📸 Image d\\'iris sélectionnée:', file.name);\n  }\n  /**\n   * Analyse l'image d'iris\n   */\n  analyzeIris() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.selectedIrisFile) {\n        alert('Veuillez d\\'abord sélectionner une image d\\'iris');\n        return;\n      }\n      _this.isAnalyzingIris = true;\n      console.log('🔍 Début de l\\'analyse de l\\'iris...');\n      try {\n        // Convertir l'image en base64\n        const imageBase64 = yield _this.irisImageService.convertImageToBase64(_this.selectedIrisFile);\n        // Obtenir les dimensions\n        const dimensions = yield _this.irisImageService.getImageDimensions(_this.selectedIrisFile);\n        // Analyser l'iris\n        _this.irisAnalysisResult = yield _this.irisImageService.analyzeIrisImage(imageBase64);\n        console.log('✅ Analyse terminée:', _this.irisAnalysisResult);\n      } catch (error) {\n        console.error('❌ Erreur lors de l\\'analyse:', error);\n        alert('Erreur lors de l\\'analyse de l\\'iris. Veuillez réessayer.');\n      } finally {\n        _this.isAnalyzingIris = false;\n      }\n    })();\n  }\n  /**\n   * Sauvegarde l'image d'iris lors de la connexion\n   */\n  saveIrisImage(userEmail, userName) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.selectedIrisFile || !_this2.irisAnalysisResult) return;\n      try {\n        const imageBase64 = yield _this2.irisImageService.convertImageToBase64(_this2.selectedIrisFile);\n        const dimensions = yield _this2.irisImageService.getImageDimensions(_this2.selectedIrisFile);\n        const irisData = {\n          userEmail: userEmail,\n          userName: userName,\n          imageUrl: '',\n          imageBase64: imageBase64,\n          uploadedAt: new Date(),\n          analysisResult: _this2.irisAnalysisResult,\n          metadata: {\n            fileName: _this2.selectedIrisFile.name,\n            fileSize: _this2.selectedIrisFile.size,\n            fileType: _this2.selectedIrisFile.type,\n            imageWidth: dimensions.width,\n            imageHeight: dimensions.height\n          }\n        };\n        // Sauvegarder dans Firebase\n        _this2.irisImageService.saveIrisImage(irisData).subscribe({\n          next: docId => {\n            console.log('✅ Image d\\'iris sauvegardée avec l\\'ID:', docId);\n          },\n          error: error => {\n            console.error('❌ Erreur sauvegarde iris:', error);\n          }\n        });\n      } catch (error) {\n        console.error('❌ Erreur lors de la sauvegarde de l\\'iris:', error);\n      }\n    })();\n  }\n  /**\n   * Supprime l'image sélectionnée\n   */\n  removeIrisImage() {\n    this.selectedIrisFile = null;\n    this.irisPreviewUrl = null;\n    this.irisAnalysisResult = null;\n    // Reset du input file\n    const fileInput = document.getElementById('irisFile');\n    if (fileInput) {\n      fileInput.value = '';\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.IrisImageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 84,\n      vars: 14,\n      consts: [[1, \"auth-container\", \"login\"], [1, \"container\"], [1, \"auth-card\"], [1, \"auth-header\"], [1, \"title\"], [1, \"subtitle\"], [1, \"divider\"], [1, \"auth-form\"], [3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"email\"], [1, \"input-container\"], [1, \"input-icon\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"Votre adresse email\", 3, \"ngModel\", \"ngModelChange\"], [\"email\", \"ngModel\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"for\", \"password\"], [\"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"minlength\", \"6\", \"placeholder\", \"Votre mot de passe\", 3, \"type\", \"ngModel\", \"ngModelChange\"], [\"password\", \"ngModel\"], [\"type\", \"button\", 1, \"toggle-password\", 3, \"click\"], [1, \"form-options\"], [1, \"remember-me\"], [\"type\", \"checkbox\", \"id\", \"remember\", \"name\", \"remember\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"remember\"], [\"href\", \"#\", 1, \"forgot-password\"], [1, \"iris-section\"], [1, \"iris-toggle\"], [\"type\", \"button\", 1, \"toggle-iris-btn\", 3, \"click\"], [1, \"icon\"], [1, \"arrow\"], [1, \"iris-info\"], [\"class\", \"iris-upload\", 4, \"ngIf\"], [1, \"form-actions\"], [\"type\", \"submit\", 1, \"submit-btn\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"social-login\"], [1, \"separator\"], [1, \"social-buttons\"], [1, \"social-btn\", \"google\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google\"], [1, \"social-btn\", \"facebook\"], [1, \"facebook-icon\"], [1, \"test-accounts\"], [1, \"test-info\"], [1, \"test-accounts-grid\"], [\"class\", \"test-account-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"auth-footer\"], [\"routerLink\", \"/signup\"], [1, \"navigation\"], [\"routerLink\", \"/accueil\", 1, \"back-btn\"], [1, \"error-message\"], [1, \"iris-upload\"], [1, \"upload-area\"], [\"class\", \"upload-content\", 4, \"ngIf\"], [\"class\", \"image-preview\", 4, \"ngIf\"], [\"class\", \"analysis-results\", 4, \"ngIf\"], [1, \"upload-content\"], [1, \"upload-icon\"], [\"type\", \"file\", \"id\", \"irisFile\", \"accept\", \"image/jpeg,image/jpg,image/png,image/webp\", \"hidden\", \"\", 3, \"change\"], [\"type\", \"button\", \"onclick\", \"document.getElementById('irisFile').click()\", 1, \"upload-btn\"], [1, \"image-preview\"], [\"alt\", \"Aper\\u00E7u iris\", 1, \"iris-preview\", 3, \"src\"], [1, \"image-actions\"], [\"type\", \"button\", 1, \"btn-analyze\", 3, \"disabled\", \"click\"], [\"type\", \"button\", 1, \"btn-remove\", 3, \"click\"], [1, \"analysis-results\"], [1, \"result-card\"], [1, \"result-header\"], [1, \"iris-type\"], [1, \"confidence\"], [1, \"characteristics\"], [1, \"char-tags\"], [\"class\", \"char-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"colors\"], [1, \"color-tags\"], [\"class\", \"color-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"compatibility-preview\"], [1, \"compatibility-score\"], [1, \"char-tag\"], [1, \"color-tag\"], [1, \"test-account-card\", 3, \"click\"], [1, \"account-info\"], [1, \"email\"], [1, \"password\"], [1, \"role-badge\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"Connexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Acc\\u00E9dez \\u00E0 votre compte IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"form\", 8, 9);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"label\", 11);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"span\", 13);\n          i0.ɵɵtext(17, \"\\u2709\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 14, 15);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.loginData.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(20, LoginComponent_div_20_Template, 3, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"label\", 17);\n          i0.ɵɵtext(23, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"span\", 13);\n          i0.ɵɵtext(26, \"\\uD83D\\uDD12\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 18, 19);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_27_listener($event) {\n            return ctx.loginData.password = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_29_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, LoginComponent_div_31_Template, 3, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 21)(33, \"div\", 22)(34, \"input\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_34_listener($event) {\n            return ctx.loginData.rememberMe = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"label\", 24);\n          i0.ɵɵtext(36, \"Se souvenir de moi\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"a\", 25);\n          i0.ɵɵtext(38, \"Mot de passe oubli\\u00E9?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 26)(40, \"div\", 27)(41, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_41_listener() {\n            return ctx.toggleIrisSection();\n          });\n          i0.ɵɵelementStart(42, \"span\", 29);\n          i0.ɵɵtext(43, \"\\uD83D\\uDC41\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\");\n          i0.ɵɵtext(45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"span\", 30);\n          i0.ɵɵtext(47);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"p\", 31);\n          i0.ɵɵtext(49, \"Optionnel : T\\u00E9l\\u00E9chargez une photo de votre iris pour une analyse personnalis\\u00E9e\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(50, LoginComponent_div_50_Template, 5, 5, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 33)(52, \"button\", 34);\n          i0.ɵɵtemplate(53, LoginComponent_span_53_Template, 2, 0, \"span\", 35);\n          i0.ɵɵtemplate(54, LoginComponent_span_54_Template, 2, 0, \"span\", 35);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"div\", 36)(56, \"p\", 37);\n          i0.ɵɵtext(57, \"Ou connectez-vous avec\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"div\", 38)(59, \"button\", 39);\n          i0.ɵɵelement(60, \"img\", 40);\n          i0.ɵɵtext(61, \" Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"button\", 41)(63, \"span\", 42);\n          i0.ɵɵtext(64, \"f\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65, \" Facebook \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(66, \"div\", 43)(67, \"h3\");\n          i0.ɵɵtext(68, \"Comptes de test disponibles\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"p\", 44);\n          i0.ɵɵtext(70, \"Cliquez sur un compte pour remplir automatiquement le formulaire :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 45);\n          i0.ɵɵtemplate(72, LoginComponent_div_72_Template, 10, 6, \"div\", 46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"div\", 47)(74, \"p\");\n          i0.ɵɵtext(75, \"Vous n'avez pas de compte? \");\n          i0.ɵɵelementStart(76, \"a\", 48);\n          i0.ɵɵtext(77, \"Inscrivez-vous\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(78, \"div\", 49)(79, \"a\", 50)(80, \"span\", 29);\n          i0.ɵɵtext(81, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"span\");\n          i0.ɵɵtext(83, \"Retour \\u00E0 l'accueil\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(11);\n          const _r1 = i0.ɵɵreference(19);\n          const _r3 = i0.ɵɵreference(28);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngModel\", ctx.loginData.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && (_r1.dirty || _r1.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\")(\"ngModel\", ctx.loginData.password);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.showPassword ? \"\\uD83D\\uDC41\\uFE0F\" : \"\\uD83D\\uDC41\\uFE0F\\u200D\\uD83D\\uDDE8\\uFE0F\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r3.invalid && (_r3.dirty || _r3.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.loginData.rememberMe);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\", ctx.showIrisSection ? \"Masquer\" : \"Ajouter\", \" l'analyse d'iris\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.showIrisSection ? \"\\u25B2\" : \"\\u25BC\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.showIrisSection);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", _r0.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.staticAccounts);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i1.RouterLink, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.CheckboxControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.MinLengthValidator, i4.EmailValidator, i4.NgModel, i4.NgForm],\n      styles: [\".auth-container.login[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\\n  padding: 40px 0;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 20px;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  max-width: 500px;\\n  margin: 0 auto 40px;\\n  padding: 40px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2.2rem;\\n  color: #333;\\n  margin-bottom: 10px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1rem;\\n  margin-bottom: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  margin: 0 auto;\\n  border-radius: 3px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  color: #555;\\n  margin-bottom: 8px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 15px;\\n  font-size: 1.1rem;\\n  color: #aaa;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 15px 12px 45px;\\n  border: 1px solid #ddd;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--fleur-primary);\\n  box-shadow: 0 0 0 3px rgba(138, 79, 255, 0.1);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .toggle-password[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  background: none;\\n  border: none;\\n  font-size: 1.1rem;\\n  cursor: pointer;\\n  color: #aaa;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .toggle-password[_ngcontent-%COMP%]:hover {\\n  color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #e74c3c;\\n  margin-top: 5px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 3px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 25px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n  cursor: pointer;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--fleur-primary);\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 25px rgba(138, 79, 255, 0.4);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%] {\\n  text-align: center;\\n  position: relative;\\n  color: #999;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:before, .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 40%;\\n  height: 1px;\\n  background-color: #ddd;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:before {\\n  left: 0;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:after {\\n  right: 0;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  padding: 12px;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 1px solid #ddd;\\n  background-color: white;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]   .facebook-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background-color: #1877f2;\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn.google[_ngcontent-%COMP%]:hover {\\n  border-color: #ea4335;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn.facebook[_ngcontent-%COMP%]:hover {\\n  border-color: #1877f2;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  border-radius: 12px;\\n  border: 1px solid #dee2e6;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 1.1rem;\\n  margin-bottom: 10px;\\n  text-align: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-info[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-accounts-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 15px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  border-color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 0.95rem;\\n  margin: 0 0 8px 0;\\n  font-weight: 600;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .email[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.8rem;\\n  margin: 0 0 5px 0;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .password[_ngcontent-%COMP%] {\\n  color: #888;\\n  font-size: 0.75rem;\\n  margin: 0 0 10px 0;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .role-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 0.7rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .role-badge.role-admin[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);\\n  color: white;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .test-accounts[_ngcontent-%COMP%]   .test-account-card[_ngcontent-%COMP%]   .account-info[_ngcontent-%COMP%]   .role-badge.role-user[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);\\n  color: white;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--fleur-primary);\\n  text-decoration: none;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 12px 25px;\\n  background-color: white;\\n  color: #555;\\n  border-radius: 50px;\\n  font-weight: 500;\\n  text-decoration: none;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\\n  color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n@media (max-width: 576px) {\\n  .auth-container.login[_ngcontent-%COMP%] {\\n    padding: 20px 0;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%] {\\n    padding: 25px;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "LoginComponent_div_20_span_1_Template", "LoginComponent_div_20_span_2_Template", "ɵɵadvance", "ɵɵproperty", "_r1", "errors", "LoginComponent_div_31_span_1_Template", "LoginComponent_div_31_span_2_Template", "_r3", "ɵɵlistener", "LoginComponent_div_50_div_2_Template_input_change_7_listener", "$event", "ɵɵrestoreView", "_r17", "ctx_r16", "ɵɵnextContext", "ɵɵresetView", "onIrisFileSelected", "ɵɵelement", "LoginComponent_div_50_div_3_Template_button_click_3_listener", "_r21", "ctx_r20", "analyzeIris", "LoginComponent_div_50_div_3_span_4_Template", "LoginComponent_div_50_div_3_span_5_Template", "LoginComponent_div_50_div_3_Template_button_click_6_listener", "ctx_r22", "removeIrisImage", "ctx_r14", "irisPreviewUrl", "ɵɵsanitizeUrl", "isAnalyzingIris", "ɵɵtextInterpolate1", "char_r25", "color_r26", "LoginComponent_div_50_div_4_span_13_Template", "LoginComponent_div_50_div_4_span_18_Template", "ctx_r15", "irisAnalysisResult", "irisType", "confidence", "characteristics", "dominantColors", "compatibilityScore", "LoginComponent_div_50_div_2_Template", "LoginComponent_div_50_div_3_Template", "LoginComponent_div_50_div_4_Template", "ɵɵclassProp", "ctx_r5", "LoginComponent_div_72_Template_div_click_0_listener", "restoredCtx", "_r29", "account_r27", "$implicit", "ctx_r28", "fillTestAccount", "ɵɵtextInterpolate", "name", "email", "password", "ɵɵclassMap", "role", "LoginComponent", "constructor", "router", "irisImageService", "loginData", "rememberMe", "showPassword", "isLoading", "selected<PERSON>ris<PERSON><PERSON>", "showIrisSection", "staticAccounts", "togglePasswordVisibility", "onSubmit", "setTimeout", "console", "log", "account", "find", "acc", "saveIrisImage", "localStorage", "setItem", "JSON", "stringify", "hasIrisImage", "irisAnalysis", "navigate", "alert", "toggleIrisSection", "event", "file", "target", "files", "validation", "validateImageFile", "<PERSON><PERSON><PERSON><PERSON>", "error", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "_this", "_asyncToGenerator", "imageBase64", "convertImageToBase64", "dimensions", "getImageDimensions", "analyzeIrisImage", "userEmail", "userName", "_this2", "irisData", "imageUrl", "uploadedAt", "Date", "analysisResult", "metadata", "fileName", "fileSize", "size", "fileType", "type", "imageWidth", "width", "imageHeight", "height", "subscribe", "next", "docId", "fileInput", "document", "getElementById", "value", "ɵɵdirectiveInject", "i1", "Router", "i2", "IrisImageService", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "LoginComponent_Template_form_ngSubmit_10_listener", "LoginComponent_Template_input_ngModelChange_18_listener", "LoginComponent_div_20_Template", "LoginComponent_Template_input_ngModelChange_27_listener", "LoginComponent_Template_button_click_29_listener", "LoginComponent_div_31_Template", "LoginComponent_Template_input_ngModelChange_34_listener", "LoginComponent_Template_button_click_41_listener", "LoginComponent_div_50_Template", "LoginComponent_span_53_Template", "LoginComponent_span_54_Template", "LoginComponent_div_72_Template", "invalid", "dirty", "touched", "_r0"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\login\\login.component.ts", "D:\\ProjetPfa\\pfa\\pfa\\src\\app\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { IrisImageService, IrisImageData, IrisAnalysisResult } from '../services/iris-image.service';\n\ninterface LoginData {\n  email: string;\n  password: string;\n  rememberMe: boolean;\n}\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n})\nexport class LoginComponent {\n  loginData: LoginData = {\n    email: '',\n    password: '',\n    rememberMe: false\n  };\n\n  showPassword: boolean = false;\n  isLoading: boolean = false;\n\n  // Variables pour l'image d'iris\n  selectedIrisFile: File | null = null;\n  irisPreviewUrl: string | null = null;\n  isAnalyzingIris: boolean = false;\n  irisAnalysisResult: IrisAnalysisResult | null = null;\n  showIrisSection: boolean = false;\n\n  // Comptes statiques pour les tests\n  staticAccounts = [\n    {\n      email: '<EMAIL>',\n      password: 'admin123',\n      name: 'Administrateur Test',\n      role: 'admin'\n    },\n    {\n      email: '<EMAIL>',\n      password: 'user123',\n      name: 'Utilisateur Test',\n      role: 'user'\n    },\n    {\n      email: '<EMAIL>',\n      password: 'marie123',\n      name: 'Marie Dubois',\n      role: 'user'\n    },\n    {\n      email: '<EMAIL>',\n      password: 'jean123',\n      name: 'Jean Martin',\n      role: 'user'\n    }\n  ];\n\n  constructor(\n    private router: Router,\n    private irisImageService: IrisImageService\n  ) {}\n\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  onSubmit(): void {\n    if (this.isLoading) return;\n\n    this.isLoading = true;\n\n    // Simuler une connexion avec un délai\n    setTimeout(() => {\n      console.log('Tentative de connexion avec:', this.loginData);\n\n      // Vérifier si les identifiants correspondent à un compte statique\n      const account = this.staticAccounts.find(acc =>\n        acc.email === this.loginData.email && acc.password === this.loginData.password\n      );\n\n      if (account) {\n        console.log('Connexion réussie avec le compte:', account.name);\n\n        // Sauvegarder l'image d'iris si elle a été analysée\n        if (this.selectedIrisFile && this.irisAnalysisResult) {\n          this.saveIrisImage(account.email, account.name);\n        }\n\n        // Sauvegarder les informations de l'utilisateur connecté\n        localStorage.setItem('currentUser', JSON.stringify({\n          name: account.name,\n          email: account.email,\n          role: account.role,\n          hasIrisImage: this.selectedIrisFile !== null,\n          irisAnalysis: this.irisAnalysisResult\n        }));\n\n        // Rediriger selon le rôle\n        if (account.role === 'admin') {\n          this.router.navigate(['/dashboard']);\n        } else {\n          // Les utilisateurs normaux vont directement au test de personnalité\n          this.router.navigate(['/personality-test']);\n        }\n      } else {\n        console.log('Identifiants incorrects');\n        alert('Email ou mot de passe incorrect. Utilisez un des comptes de test.');\n      }\n\n      this.isLoading = false;\n    }, 1500);\n  }\n\n  /**\n   * Remplit automatiquement le formulaire avec un compte de test\n   */\n  fillTestAccount(account: any): void {\n    this.loginData.email = account.email;\n    this.loginData.password = account.password;\n  }\n\n  /**\n   * Affiche/masque la section d'upload d'iris\n   */\n  toggleIrisSection(): void {\n    this.showIrisSection = !this.showIrisSection;\n  }\n\n  /**\n   * Gère la sélection d'un fichier d'iris\n   */\n  onIrisFileSelected(event: any): void {\n    const file = event.target.files[0];\n    if (!file) return;\n\n    // Valider le fichier\n    const validation = this.irisImageService.validateImageFile(file);\n    if (!validation.isValid) {\n      alert(validation.error);\n      return;\n    }\n\n    this.selectedIrisFile = file;\n\n    // Créer un aperçu de l'image\n    const reader = new FileReader();\n    reader.onload = (e: any) => {\n      this.irisPreviewUrl = e.target.result;\n    };\n    reader.readAsDataURL(file);\n\n    console.log('📸 Image d\\'iris sélectionnée:', file.name);\n  }\n\n  /**\n   * Analyse l'image d'iris\n   */\n  async analyzeIris(): Promise<void> {\n    if (!this.selectedIrisFile) {\n      alert('Veuillez d\\'abord sélectionner une image d\\'iris');\n      return;\n    }\n\n    this.isAnalyzingIris = true;\n    console.log('🔍 Début de l\\'analyse de l\\'iris...');\n\n    try {\n      // Convertir l'image en base64\n      const imageBase64 = await this.irisImageService.convertImageToBase64(this.selectedIrisFile);\n\n      // Obtenir les dimensions\n      const dimensions = await this.irisImageService.getImageDimensions(this.selectedIrisFile);\n\n      // Analyser l'iris\n      this.irisAnalysisResult = await this.irisImageService.analyzeIrisImage(imageBase64);\n\n      console.log('✅ Analyse terminée:', this.irisAnalysisResult);\n\n    } catch (error) {\n      console.error('❌ Erreur lors de l\\'analyse:', error);\n      alert('Erreur lors de l\\'analyse de l\\'iris. Veuillez réessayer.');\n    } finally {\n      this.isAnalyzingIris = false;\n    }\n  }\n\n  /**\n   * Sauvegarde l'image d'iris lors de la connexion\n   */\n  private async saveIrisImage(userEmail: string, userName: string): Promise<void> {\n    if (!this.selectedIrisFile || !this.irisAnalysisResult) return;\n\n    try {\n      const imageBase64 = await this.irisImageService.convertImageToBase64(this.selectedIrisFile);\n      const dimensions = await this.irisImageService.getImageDimensions(this.selectedIrisFile);\n\n      const irisData: IrisImageData = {\n        userEmail: userEmail,\n        userName: userName,\n        imageUrl: '', // Pas d'URL externe pour l'instant\n        imageBase64: imageBase64,\n        uploadedAt: new Date(),\n        analysisResult: this.irisAnalysisResult,\n        metadata: {\n          fileName: this.selectedIrisFile.name,\n          fileSize: this.selectedIrisFile.size,\n          fileType: this.selectedIrisFile.type,\n          imageWidth: dimensions.width,\n          imageHeight: dimensions.height\n        }\n      };\n\n      // Sauvegarder dans Firebase\n      this.irisImageService.saveIrisImage(irisData).subscribe({\n        next: (docId) => {\n          console.log('✅ Image d\\'iris sauvegardée avec l\\'ID:', docId);\n        },\n        error: (error) => {\n          console.error('❌ Erreur sauvegarde iris:', error);\n        }\n      });\n\n    } catch (error) {\n      console.error('❌ Erreur lors de la sauvegarde de l\\'iris:', error);\n    }\n  }\n\n  /**\n   * Supprime l'image sélectionnée\n   */\n  removeIrisImage(): void {\n    this.selectedIrisFile = null;\n    this.irisPreviewUrl = null;\n    this.irisAnalysisResult = null;\n\n    // Reset du input file\n    const fileInput = document.getElementById('irisFile') as HTMLInputElement;\n    if (fileInput) {\n      fileInput.value = '';\n    }\n  }\n}\n", "<div class=\"auth-container login\">\n  <div class=\"container\">\n    <div class=\"auth-card\">\n      <div class=\"auth-header\">\n        <h1 class=\"title\">Connexion</h1>\n        <p class=\"subtitle\">Accédez à votre compte IrisLock</p>\n        <div class=\"divider\"></div>\n      </div>\n\n      <div class=\"auth-form\">\n        <form (ngSubmit)=\"onSubmit()\" #loginForm=\"ngForm\">\n          <div class=\"form-group\">\n            <label for=\"email\">Email</label>\n            <div class=\"input-container\">\n              <span class=\"input-icon\">✉️</span>\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                [(ngModel)]=\"loginData.email\"\n                required\n                email\n                #email=\"ngModel\"\n                placeholder=\"Votre adresse email\"\n              >\n            </div>\n            <div class=\"error-message\" *ngIf=\"email.invalid && (email.dirty || email.touched)\">\n              <span *ngIf=\"email.errors?.['required']\">L'email est requis</span>\n              <span *ngIf=\"email.errors?.['email']\">Veuillez entrer un email valide</span>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"password\">Mot de passe</label>\n            <div class=\"input-container\">\n              <span class=\"input-icon\">🔒</span>\n              <input\n                [type]=\"showPassword ? 'text' : 'password'\"\n                id=\"password\"\n                name=\"password\"\n                [(ngModel)]=\"loginData.password\"\n                required\n                minlength=\"6\"\n                #password=\"ngModel\"\n                placeholder=\"Votre mot de passe\"\n              >\n              <button\n                type=\"button\"\n                class=\"toggle-password\"\n                (click)=\"togglePasswordVisibility()\"\n              >\n                {{ showPassword ? '👁️' : '👁️‍🗨️' }}\n              </button>\n            </div>\n            <div class=\"error-message\" *ngIf=\"password.invalid && (password.dirty || password.touched)\">\n              <span *ngIf=\"password.errors?.['required']\">Le mot de passe est requis</span>\n              <span *ngIf=\"password.errors?.['minlength']\">Le mot de passe doit contenir au moins 6 caractères</span>\n            </div>\n          </div>\n\n          <div class=\"form-options\">\n            <div class=\"remember-me\">\n              <input\n                type=\"checkbox\"\n                id=\"remember\"\n                name=\"remember\"\n                [(ngModel)]=\"loginData.rememberMe\"\n              >\n              <label for=\"remember\">Se souvenir de moi</label>\n            </div>\n            <a href=\"#\" class=\"forgot-password\">Mot de passe oublié?</a>\n          </div>\n\n          <!-- Section d'upload d'iris -->\n          <div class=\"iris-section\">\n            <div class=\"iris-toggle\">\n              <button\n                type=\"button\"\n                class=\"toggle-iris-btn\"\n                (click)=\"toggleIrisSection()\">\n                <span class=\"icon\">👁️</span>\n                <span>{{ showIrisSection ? 'Masquer' : 'Ajouter' }} l'analyse d'iris</span>\n                <span class=\"arrow\">{{ showIrisSection ? '▲' : '▼' }}</span>\n              </button>\n              <p class=\"iris-info\">Optionnel : Téléchargez une photo de votre iris pour une analyse personnalisée</p>\n            </div>\n\n            <div class=\"iris-upload\" *ngIf=\"showIrisSection\">\n              <div class=\"upload-area\" [class.has-image]=\"irisPreviewUrl\">\n                <div class=\"upload-content\" *ngIf=\"!irisPreviewUrl\">\n                  <div class=\"upload-icon\">📸</div>\n                  <h4>Télécharger une image d'iris</h4>\n                  <p>Formats acceptés : JPG, PNG, WebP (max 5MB)</p>\n                  <input\n                    type=\"file\"\n                    id=\"irisFile\"\n                    accept=\"image/jpeg,image/jpg,image/png,image/webp\"\n                    (change)=\"onIrisFileSelected($event)\"\n                    hidden>\n                  <button type=\"button\" class=\"upload-btn\" onclick=\"document.getElementById('irisFile').click()\">\n                    Choisir une image\n                  </button>\n                </div>\n\n                <div class=\"image-preview\" *ngIf=\"irisPreviewUrl\">\n                  <img [src]=\"irisPreviewUrl\" alt=\"Aperçu iris\" class=\"iris-preview\">\n                  <div class=\"image-actions\">\n                    <button type=\"button\" class=\"btn-analyze\" (click)=\"analyzeIris()\" [disabled]=\"isAnalyzingIris\">\n                      <span *ngIf=\"!isAnalyzingIris\">🔍 Analyser l'iris</span>\n                      <span *ngIf=\"isAnalyzingIris\">⏳ Analyse en cours...</span>\n                    </button>\n                    <button type=\"button\" class=\"btn-remove\" (click)=\"removeIrisImage()\">\n                      🗑️ Supprimer\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Résultats de l'analyse -->\n              <div class=\"analysis-results\" *ngIf=\"irisAnalysisResult\">\n                <h4>📊 Résultats de l'analyse</h4>\n                <div class=\"result-card\">\n                  <div class=\"result-header\">\n                    <span class=\"iris-type\">Type détecté : {{ irisAnalysisResult.irisType }}</span>\n                    <span class=\"confidence\">Confiance : {{ irisAnalysisResult.confidence }}%</span>\n                  </div>\n\n                  <div class=\"characteristics\">\n                    <h5>Caractéristiques détectées :</h5>\n                    <div class=\"char-tags\">\n                      <span *ngFor=\"let char of irisAnalysisResult.characteristics\" class=\"char-tag\">\n                        {{ char }}\n                      </span>\n                    </div>\n                  </div>\n\n                  <div class=\"colors\">\n                    <h5>Couleurs dominantes :</h5>\n                    <div class=\"color-tags\">\n                      <span *ngFor=\"let color of irisAnalysisResult.dominantColors\" class=\"color-tag\">\n                        {{ color }}\n                      </span>\n                    </div>\n                  </div>\n\n                  <div class=\"compatibility-preview\">\n                    <span class=\"compatibility-score\">\n                      Score de compatibilité : {{ irisAnalysisResult.compatibilityScore }}%\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-actions\">\n            <button\n              type=\"submit\"\n              class=\"submit-btn\"\n              [disabled]=\"loginForm.invalid || isLoading\"\n            >\n              <span *ngIf=\"!isLoading\">Se connecter</span>\n              <span *ngIf=\"isLoading\">Connexion en cours...</span>\n            </button>\n          </div>\n        </form>\n\n        <div class=\"social-login\">\n          <p class=\"separator\">Ou connectez-vous avec</p>\n          <div class=\"social-buttons\">\n            <button class=\"social-btn google\">\n              <img src=\"assets/google-icon.png\" alt=\"Google\">\n              Google\n            </button>\n            <button class=\"social-btn facebook\">\n              <span class=\"facebook-icon\">f</span>\n              Facebook\n            </button>\n          </div>\n        </div>\n\n        <div class=\"test-accounts\">\n          <h3>Comptes de test disponibles</h3>\n          <p class=\"test-info\">Cliquez sur un compte pour remplir automatiquement le formulaire :</p>\n          <div class=\"test-accounts-grid\">\n            <div\n              *ngFor=\"let account of staticAccounts\"\n              class=\"test-account-card\"\n              (click)=\"fillTestAccount(account)\">\n              <div class=\"account-info\">\n                <h4>{{ account.name }}</h4>\n                <p class=\"email\">{{ account.email }}</p>\n                <p class=\"password\">Mot de passe: {{ account.password }}</p>\n                <span class=\"role-badge\" [class]=\"'role-' + account.role\">{{ account.role }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"auth-footer\">\n          <p>Vous n'avez pas de compte? <a routerLink=\"/signup\">Inscrivez-vous</a></p>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"navigation\">\n      <a routerLink=\"/accueil\" class=\"back-btn\">\n        <span class=\"icon\">←</span>\n        <span>Retour à l'accueil</span>\n      </a>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;IC2BcA,EAAA,CAAAC,cAAA,WAAyC;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAClEH,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF9EH,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAI,UAAA,IAAAC,qCAAA,mBAAkE;IAClEL,EAAA,CAAAI,UAAA,IAAAE,qCAAA,mBAA4E;IAC9EN,EAAA,CAAAG,YAAA,EAAM;;;;;IAFGH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAgC;IAChCV,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,UAA6B;;;;;IA2BpCV,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7EH,EAAA,CAAAC,cAAA,WAA6C;IAAAD,EAAA,CAAAE,MAAA,+DAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFzGH,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAI,UAAA,IAAAO,qCAAA,mBAA6E;IAC7EX,EAAA,CAAAI,UAAA,IAAAQ,qCAAA,mBAAuG;IACzGZ,EAAA,CAAAG,YAAA,EAAM;;;;;IAFGH,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAAH,MAAA,kBAAAG,GAAA,CAAAH,MAAA,aAAmC;IACnCV,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAAH,MAAA,kBAAAG,GAAA,CAAAH,MAAA,cAAoC;;;;;;IAiCzCV,EAAA,CAAAC,cAAA,cAAoD;IACzBD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,6CAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,uDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClDH,EAAA,CAAAC,cAAA,gBAKS;IADPD,EAAA,CAAAc,UAAA,oBAAAC,6DAAAC,MAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAAUpB,EAAA,CAAAqB,WAAA,CAAAF,OAAA,CAAAG,kBAAA,CAAAN,MAAA,CAA0B;IAAA,EAAC;IAJvChB,EAAA,CAAAG,YAAA,EAKS;IACTH,EAAA,CAAAC,cAAA,iBAA+F;IAC7FD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAOLH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,mCAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACxDH,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAE,MAAA,iCAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IALhEH,EAAA,CAAAC,cAAA,cAAkD;IAChDD,EAAA,CAAAuB,SAAA,cAAmE;IACnEvB,EAAA,CAAAC,cAAA,cAA2B;IACiBD,EAAA,CAAAc,UAAA,mBAAAU,6DAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAQ,IAAA;MAAA,MAAAC,OAAA,GAAA1B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAK,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAC/D3B,EAAA,CAAAI,UAAA,IAAAwB,2CAAA,mBAAwD;IACxD5B,EAAA,CAAAI,UAAA,IAAAyB,2CAAA,mBAA0D;IAC5D7B,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAqE;IAA5BD,EAAA,CAAAc,UAAA,mBAAAgB,6DAAA;MAAA9B,EAAA,CAAAiB,aAAA,CAAAQ,IAAA;MAAA,MAAAM,OAAA,GAAA/B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAU,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IAClEhC,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IARNH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,UAAA,QAAAyB,OAAA,CAAAC,cAAA,EAAAlC,EAAA,CAAAmC,aAAA,CAAsB;IAEyCnC,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAQ,UAAA,aAAAyB,OAAA,CAAAG,eAAA,CAA4B;IACrFpC,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,UAAA,UAAAyB,OAAA,CAAAG,eAAA,CAAsB;IACtBpC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,UAAA,SAAAyB,OAAA,CAAAG,eAAA,CAAqB;;;;;IAqB5BpC,EAAA,CAAAC,cAAA,eAA+E;IAC7ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAqC,kBAAA,MAAAC,QAAA,MACF;;;;;IAOAtC,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAqC,kBAAA,MAAAE,SAAA,MACF;;;;;IAtBRvC,EAAA,CAAAC,cAAA,cAAyD;IACnDD,EAAA,CAAAE,MAAA,+CAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,cAAyB;IAEGD,EAAA,CAAAE,MAAA,GAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/EH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGlFH,EAAA,CAAAC,cAAA,cAA6B;IACvBD,EAAA,CAAAE,MAAA,mDAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAI,UAAA,KAAAoC,4CAAA,mBAEO;IACTxC,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAAoB;IACdD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAAI,UAAA,KAAAqC,4CAAA,mBAEO;IACTzC,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAC,cAAA,eAAmC;IAE/BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAzBiBH,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAqC,kBAAA,8BAAAK,OAAA,CAAAC,kBAAA,CAAAC,QAAA,KAAgD;IAC/C5C,EAAA,CAAAO,SAAA,GAAgD;IAAhDP,EAAA,CAAAqC,kBAAA,iBAAAK,OAAA,CAAAC,kBAAA,CAAAE,UAAA,MAAgD;IAMhD7C,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAQ,UAAA,YAAAkC,OAAA,CAAAC,kBAAA,CAAAG,eAAA,CAAqC;IASpC9C,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,YAAAkC,OAAA,CAAAC,kBAAA,CAAAI,cAAA,CAAoC;IAQ5D/C,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAqC,kBAAA,oCAAAK,OAAA,CAAAC,kBAAA,CAAAK,kBAAA,OACF;;;;;IA7DRhD,EAAA,CAAAC,cAAA,cAAiD;IAE7CD,EAAA,CAAAI,UAAA,IAAA6C,oCAAA,mBAaM;IAENjD,EAAA,CAAAI,UAAA,IAAA8C,oCAAA,kBAWM;IACRlD,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAI,UAAA,IAAA+C,oCAAA,mBAgCM;IACRnD,EAAA,CAAAG,YAAA,EAAM;;;;IAhEqBH,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAoD,WAAA,cAAAC,MAAA,CAAAnB,cAAA,CAAkC;IAC5BlC,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAQ,UAAA,UAAA6C,MAAA,CAAAnB,cAAA,CAAqB;IAetBlC,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,UAAA,SAAA6C,MAAA,CAAAnB,cAAA,CAAoB;IAenBlC,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,UAAA,SAAA6C,MAAA,CAAAV,kBAAA,CAAwB;;;;;IA0CvD3C,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5CH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAuBtDH,EAAA,CAAAC,cAAA,cAGqC;IAAnCD,EAAA,CAAAc,UAAA,mBAAAwC,oDAAA;MAAA,MAAAC,WAAA,GAAAvD,EAAA,CAAAiB,aAAA,CAAAuC,IAAA;MAAA,MAAAC,WAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAsC,OAAA,CAAAC,eAAA,CAAAH,WAAA,CAAwB;IAAA,EAAC;IAClCzD,EAAA,CAAAC,cAAA,cAA0B;IACpBD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,YAAiB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxCH,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAAC,cAAA,eAA0D;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAH/EH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAA6D,iBAAA,CAAAJ,WAAA,CAAAK,IAAA,CAAkB;IACL9D,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA6D,iBAAA,CAAAJ,WAAA,CAAAM,KAAA,CAAmB;IAChB/D,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAqC,kBAAA,mBAAAoB,WAAA,CAAAO,QAAA,KAAoC;IAC/BhE,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAiE,UAAA,WAAAR,WAAA,CAAAS,IAAA,CAAgC;IAAClE,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAA6D,iBAAA,CAAAJ,WAAA,CAAAS,IAAA,CAAkB;;;ADlL5F,OAAM,MAAOC,cAAc;EA6CzBC,YACUC,MAAc,EACdC,gBAAkC;IADlC,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IA9C1B,KAAAC,SAAS,GAAc;MACrBR,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZQ,UAAU,EAAE;KACb;IAED,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAC,gBAAgB,GAAgB,IAAI;IACpC,KAAAzC,cAAc,GAAkB,IAAI;IACpC,KAAAE,eAAe,GAAY,KAAK;IAChC,KAAAO,kBAAkB,GAA8B,IAAI;IACpD,KAAAiC,eAAe,GAAY,KAAK;IAEhC;IACA,KAAAC,cAAc,GAAG,CACf;MACEd,KAAK,EAAE,gBAAgB;MACvBC,QAAQ,EAAE,UAAU;MACpBF,IAAI,EAAE,qBAAqB;MAC3BI,IAAI,EAAE;KACP,EACD;MACEH,KAAK,EAAE,eAAe;MACtBC,QAAQ,EAAE,SAAS;MACnBF,IAAI,EAAE,kBAAkB;MACxBI,IAAI,EAAE;KACP,EACD;MACEH,KAAK,EAAE,uBAAuB;MAC9BC,QAAQ,EAAE,UAAU;MACpBF,IAAI,EAAE,cAAc;MACpBI,IAAI,EAAE;KACP,EACD;MACEH,KAAK,EAAE,sBAAsB;MAC7BC,QAAQ,EAAE,SAAS;MACnBF,IAAI,EAAE,aAAa;MACnBI,IAAI,EAAE;KACP,CACF;EAKE;EAEHY,wBAAwBA,CAAA;IACtB,IAAI,CAACL,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAM,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,SAAS,EAAE;IAEpB,IAAI,CAACA,SAAS,GAAG,IAAI;IAErB;IACAM,UAAU,CAAC,MAAK;MACdC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACX,SAAS,CAAC;MAE3D;MACA,MAAMY,OAAO,GAAG,IAAI,CAACN,cAAc,CAACO,IAAI,CAACC,GAAG,IAC1CA,GAAG,CAACtB,KAAK,KAAK,IAAI,CAACQ,SAAS,CAACR,KAAK,IAAIsB,GAAG,CAACrB,QAAQ,KAAK,IAAI,CAACO,SAAS,CAACP,QAAQ,CAC/E;MAED,IAAImB,OAAO,EAAE;QACXF,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEC,OAAO,CAACrB,IAAI,CAAC;QAE9D;QACA,IAAI,IAAI,CAACa,gBAAgB,IAAI,IAAI,CAAChC,kBAAkB,EAAE;UACpD,IAAI,CAAC2C,aAAa,CAACH,OAAO,CAACpB,KAAK,EAAEoB,OAAO,CAACrB,IAAI,CAAC;;QAGjD;QACAyB,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEC,IAAI,CAACC,SAAS,CAAC;UACjD5B,IAAI,EAAEqB,OAAO,CAACrB,IAAI;UAClBC,KAAK,EAAEoB,OAAO,CAACpB,KAAK;UACpBG,IAAI,EAAEiB,OAAO,CAACjB,IAAI;UAClByB,YAAY,EAAE,IAAI,CAAChB,gBAAgB,KAAK,IAAI;UAC5CiB,YAAY,EAAE,IAAI,CAACjD;SACpB,CAAC,CAAC;QAEH;QACA,IAAIwC,OAAO,CAACjB,IAAI,KAAK,OAAO,EAAE;UAC5B,IAAI,CAACG,MAAM,CAACwB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;SACrC,MAAM;UACL;UACA,IAAI,CAACxB,MAAM,CAACwB,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;;OAE9C,MAAM;QACLZ,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;QACtCY,KAAK,CAAC,mEAAmE,CAAC;;MAG5E,IAAI,CAACpB,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGAd,eAAeA,CAACuB,OAAY;IAC1B,IAAI,CAACZ,SAAS,CAACR,KAAK,GAAGoB,OAAO,CAACpB,KAAK;IACpC,IAAI,CAACQ,SAAS,CAACP,QAAQ,GAAGmB,OAAO,CAACnB,QAAQ;EAC5C;EAEA;;;EAGA+B,iBAAiBA,CAAA;IACf,IAAI,CAACnB,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEA;;;EAGAtD,kBAAkBA,CAAC0E,KAAU;IAC3B,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAI,CAACF,IAAI,EAAE;IAEX;IACA,MAAMG,UAAU,GAAG,IAAI,CAAC9B,gBAAgB,CAAC+B,iBAAiB,CAACJ,IAAI,CAAC;IAChE,IAAI,CAACG,UAAU,CAACE,OAAO,EAAE;MACvBR,KAAK,CAACM,UAAU,CAACG,KAAK,CAAC;MACvB;;IAGF,IAAI,CAAC5B,gBAAgB,GAAGsB,IAAI;IAE5B;IACA,MAAMO,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAIC,CAAM,IAAI;MACzB,IAAI,CAACzE,cAAc,GAAGyE,CAAC,CAACT,MAAM,CAACU,MAAM;IACvC,CAAC;IACDJ,MAAM,CAACK,aAAa,CAACZ,IAAI,CAAC;IAE1BhB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEe,IAAI,CAACnC,IAAI,CAAC;EAC1D;EAEA;;;EAGMnC,WAAWA,CAAA;IAAA,IAAAmF,KAAA;IAAA,OAAAC,iBAAA;MACf,IAAI,CAACD,KAAI,CAACnC,gBAAgB,EAAE;QAC1BmB,KAAK,CAAC,kDAAkD,CAAC;QACzD;;MAGFgB,KAAI,CAAC1E,eAAe,GAAG,IAAI;MAC3B6C,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MAEnD,IAAI;QACF;QACA,MAAM8B,WAAW,SAASF,KAAI,CAACxC,gBAAgB,CAAC2C,oBAAoB,CAACH,KAAI,CAACnC,gBAAgB,CAAC;QAE3F;QACA,MAAMuC,UAAU,SAASJ,KAAI,CAACxC,gBAAgB,CAAC6C,kBAAkB,CAACL,KAAI,CAACnC,gBAAgB,CAAC;QAExF;QACAmC,KAAI,CAACnE,kBAAkB,SAASmE,KAAI,CAACxC,gBAAgB,CAAC8C,gBAAgB,CAACJ,WAAW,CAAC;QAEnF/B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE4B,KAAI,CAACnE,kBAAkB,CAAC;OAE5D,CAAC,OAAO4D,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpDT,KAAK,CAAC,2DAA2D,CAAC;OACnE,SAAS;QACRgB,KAAI,CAAC1E,eAAe,GAAG,KAAK;;IAC7B;EACH;EAEA;;;EAGckD,aAAaA,CAAC+B,SAAiB,EAAEC,QAAgB;IAAA,IAAAC,MAAA;IAAA,OAAAR,iBAAA;MAC7D,IAAI,CAACQ,MAAI,CAAC5C,gBAAgB,IAAI,CAAC4C,MAAI,CAAC5E,kBAAkB,EAAE;MAExD,IAAI;QACF,MAAMqE,WAAW,SAASO,MAAI,CAACjD,gBAAgB,CAAC2C,oBAAoB,CAACM,MAAI,CAAC5C,gBAAgB,CAAC;QAC3F,MAAMuC,UAAU,SAASK,MAAI,CAACjD,gBAAgB,CAAC6C,kBAAkB,CAACI,MAAI,CAAC5C,gBAAgB,CAAC;QAExF,MAAM6C,QAAQ,GAAkB;UAC9BH,SAAS,EAAEA,SAAS;UACpBC,QAAQ,EAAEA,QAAQ;UAClBG,QAAQ,EAAE,EAAE;UACZT,WAAW,EAAEA,WAAW;UACxBU,UAAU,EAAE,IAAIC,IAAI,EAAE;UACtBC,cAAc,EAAEL,MAAI,CAAC5E,kBAAkB;UACvCkF,QAAQ,EAAE;YACRC,QAAQ,EAAEP,MAAI,CAAC5C,gBAAgB,CAACb,IAAI;YACpCiE,QAAQ,EAAER,MAAI,CAAC5C,gBAAgB,CAACqD,IAAI;YACpCC,QAAQ,EAAEV,MAAI,CAAC5C,gBAAgB,CAACuD,IAAI;YACpCC,UAAU,EAAEjB,UAAU,CAACkB,KAAK;YAC5BC,WAAW,EAAEnB,UAAU,CAACoB;;SAE3B;QAED;QACAf,MAAI,CAACjD,gBAAgB,CAACgB,aAAa,CAACkC,QAAQ,CAAC,CAACe,SAAS,CAAC;UACtDC,IAAI,EAAGC,KAAK,IAAI;YACdxD,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEuD,KAAK,CAAC;UAC/D,CAAC;UACDlC,KAAK,EAAGA,KAAK,IAAI;YACftB,OAAO,CAACsB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UACnD;SACD,CAAC;OAEH,CAAC,OAAOA,KAAK,EAAE;QACdtB,OAAO,CAACsB,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;;IACnE;EACH;EAEA;;;EAGAvE,eAAeA,CAAA;IACb,IAAI,CAAC2C,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACzC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACS,kBAAkB,GAAG,IAAI;IAE9B;IACA,MAAM+F,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,UAAU,CAAqB;IACzE,IAAIF,SAAS,EAAE;MACbA,SAAS,CAACG,KAAK,GAAG,EAAE;;EAExB;;;uBApOW1E,cAAc,EAAAnE,EAAA,CAAA8I,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAhJ,EAAA,CAAA8I,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAd/E,cAAc;MAAAgF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCf3BzJ,EAAA,CAAAC,cAAA,aAAkC;UAIRD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,WAAoB;UAAAD,EAAA,CAAAE,MAAA,gDAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvDH,EAAA,CAAAuB,SAAA,aAA2B;UAC7BvB,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAAuB;UACfD,EAAA,CAAAc,UAAA,sBAAA6I,kDAAA;YAAA,OAAYD,GAAA,CAAA3E,QAAA,EAAU;UAAA,EAAC;UAC3B/E,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,eAA6B;UACFD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,qBASC;UALCD,EAAA,CAAAc,UAAA,2BAAA8I,wDAAA5I,MAAA;YAAA,OAAA0I,GAAA,CAAAnF,SAAA,CAAAR,KAAA,GAAA/C,MAAA;UAAA,EAA6B;UAJ/BhB,EAAA,CAAAG,YAAA,EASC;UAEHH,EAAA,CAAAI,UAAA,KAAAyJ,8BAAA,kBAGM;UACR7J,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,eAA6B;UACFD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,qBASC;UALCD,EAAA,CAAAc,UAAA,2BAAAgJ,wDAAA9I,MAAA;YAAA,OAAA0I,GAAA,CAAAnF,SAAA,CAAAP,QAAA,GAAAhD,MAAA;UAAA,EAAgC;UAJlChB,EAAA,CAAAG,YAAA,EASC;UACDH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAc,UAAA,mBAAAiJ,iDAAA;YAAA,OAASL,GAAA,CAAA5E,wBAAA,EAA0B;UAAA,EAAC;UAEpC9E,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAI,UAAA,KAAA4J,8BAAA,kBAGM;UACRhK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA0B;UAMpBD,EAAA,CAAAc,UAAA,2BAAAmJ,wDAAAjJ,MAAA;YAAA,OAAA0I,GAAA,CAAAnF,SAAA,CAAAC,UAAA,GAAAxD,MAAA;UAAA,EAAkC;UAJpChB,EAAA,CAAAG,YAAA,EAKC;UACDH,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAElDH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,iCAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI9DH,EAAA,CAAAC,cAAA,eAA0B;UAKpBD,EAAA,CAAAc,UAAA,mBAAAoJ,iDAAA;YAAA,OAASR,GAAA,CAAA3D,iBAAA,EAAmB;UAAA,EAAC;UAC7B/F,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,0BAAG;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA8D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3EH,EAAA,CAAAC,cAAA,gBAAoB;UAAAD,EAAA,CAAAE,MAAA,IAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE9DH,EAAA,CAAAC,cAAA,aAAqB;UAAAD,EAAA,CAAAE,MAAA,qGAA8E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGzGH,EAAA,CAAAI,UAAA,KAAA+J,8BAAA,kBAiEM;UACRnK,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA0B;UAMtBD,EAAA,CAAAI,UAAA,KAAAgK,+BAAA,mBAA4C;UAC5CpK,EAAA,CAAAI,UAAA,KAAAiK,+BAAA,mBAAoD;UACtDrK,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA0B;UACHD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC/CH,EAAA,CAAAC,cAAA,eAA4B;UAExBD,EAAA,CAAAuB,SAAA,eAA+C;UAC/CvB,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAoC;UACND,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpCH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA2B;UACrBD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,aAAqB;UAAAD,EAAA,CAAAE,MAAA,0EAAkE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC3FH,EAAA,CAAAC,cAAA,eAAgC;UAC9BD,EAAA,CAAAI,UAAA,KAAAkK,8BAAA,mBAUM;UACRtK,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAAyB;UACpBD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAC,cAAA,aAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK9EH,EAAA,CAAAC,cAAA,eAAwB;UAEDD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,+BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;UA7LvBH,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAQ,UAAA,YAAAkJ,GAAA,CAAAnF,SAAA,CAAAR,KAAA,CAA6B;UAOL/D,EAAA,CAAAO,SAAA,GAAqD;UAArDP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAA8J,OAAA,KAAA9J,GAAA,CAAA+J,KAAA,IAAA/J,GAAA,CAAAgK,OAAA,EAAqD;UAW7EzK,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,UAAA,SAAAkJ,GAAA,CAAAjF,YAAA,uBAA2C,YAAAiF,GAAA,CAAAnF,SAAA,CAAAP,QAAA;UAc3ChE,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAqC,kBAAA,MAAAqH,GAAA,CAAAjF,YAAA,4EACF;UAE0BzE,EAAA,CAAAO,SAAA,GAA8D;UAA9DP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAA0J,OAAA,KAAA1J,GAAA,CAAA2J,KAAA,IAAA3J,GAAA,CAAA4J,OAAA,EAA8D;UAYtFzK,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,YAAAkJ,GAAA,CAAAnF,SAAA,CAAAC,UAAA,CAAkC;UAe5BxE,EAAA,CAAAO,SAAA,IAA8D;UAA9DP,EAAA,CAAAqC,kBAAA,KAAAqH,GAAA,CAAA9E,eAAA,8CAA8D;UAChD5E,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAA6D,iBAAA,CAAA6F,GAAA,CAAA9E,eAAA,uBAAiC;UAK/B5E,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAQ,UAAA,SAAAkJ,GAAA,CAAA9E,eAAA,CAAqB;UAwE7C5E,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,UAAA,aAAAkK,GAAA,CAAAH,OAAA,IAAAb,GAAA,CAAAhF,SAAA,CAA2C;UAEpC1E,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAQ,UAAA,UAAAkJ,GAAA,CAAAhF,SAAA,CAAgB;UAChB1E,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAAkJ,GAAA,CAAAhF,SAAA,CAAe;UAwBF1E,EAAA,CAAAO,SAAA,IAAiB;UAAjBP,EAAA,CAAAQ,UAAA,YAAAkJ,GAAA,CAAA7E,cAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}