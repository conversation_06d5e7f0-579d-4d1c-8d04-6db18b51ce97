{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ShakerComponent {\n  static {\n    this.ɵfac = function ShakerComponent_Factory(t) {\n      return new (t || ShakerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShakerComponent,\n      selectors: [[\"app-shaker\"]],\n      decls: 26,\n      vars: 0,\n      consts: [[1, \"iris-details\"], [\"src\", \"assets/4.png\", \"alt\", \"Shaker\", 1, \"iris-img\"], [1, \"iris-description\"]],\n      template: function ShakerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4, \"Shaker\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"ul\")(6, \"li\");\n          i0.ɵɵtext(7, \"Le Visionnaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"li\");\n          i0.ɵɵtext(9, \"Type motiv\\u00E9, expressif, pionnier, orient\\u00E9 action et innovation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"li\");\n          i0.ɵɵtext(11, \"D\\u00E9passe la pens\\u00E9e conventionnelle, \\u00E9nergique et inspirant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"li\");\n          i0.ɵɵtext(13, \"Combine les forces de Bijou (visuel) et Fleur (auditif)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"li\");\n          i0.ɵɵtext(15, \"D\\u00E9cide vite et tient ses choix\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"li\");\n          i0.ɵɵtext(17, \"Apprentissage par le mouvement, le toucher et l\\u2019intuition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"li\");\n          i0.ɵɵtext(19, \"Communique avec gestes dynamiques et passion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"li\");\n          i0.ɵɵtext(21, \"Agent de changement, inventif, motivateur, leader n\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"li\");\n          i0.ɵɵtext(23, \"Peut devenir instable, autoritaire, autocritique\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"li\");\n          i0.ɵɵtext(25, \"Le\\u00E7on de vie : cultiver la coh\\u00E9rence, la confiance et l\\u2019ancrage\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\".iris-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  background: linear-gradient(to right, #f8e8ff, #f6f1ff);\\n  min-height: 100vh;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 180px;\\n  height: 180px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\\n  margin-bottom: 25px;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: bold;\\n  color: #5e548e;\\n  margin-bottom: 15px;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n  max-width: 700px;\\n  font-size: 1.1rem;\\n  line-height: 1.7;\\n  color: #3d3d3d;\\n  text-align: center;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  background: rgba(255, 255, 255, 0.8);\\n  padding: 20px;\\n  border-radius: 20px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (max-width: 768px) {\\n  .iris-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n    width: 140px;\\n    height: 140px;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    padding: 15px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ShakerComponent", "selectors", "decls", "vars", "consts", "template", "ShakerComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shaker\\shaker.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shaker\\shaker.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-shaker',\n  templateUrl: './shaker.component.html',\n  styleUrls: ['./shaker.component.scss']\n})\nexport class ShakerComponent {\n\n}\n", "<div class=\"iris-details\">\n    <img src=\"assets/4.png\" alt=\"Shaker\" class=\"iris-img\" />\n    <div class=\"iris-description\">\n      <h2>Shaker</h2>\n      <ul>\n        <li>Le Visionnaire</li>\n        <li>Type motivé, expressif, pionnier, orienté action et innovation</li>\n        <li>Dépasse la pensée conventionnelle, énergique et inspirant</li>\n        <li>Combine les forces de Bijou (visuel) et Fleur (auditif)</li>\n        <li>Décide vite et tient ses choix</li>\n        <li>Apprentissage par le mouvement, le toucher et l’intuition</li>\n        <li>Communique avec gestes dynamiques et passion</li>\n        <li>Agent de changement, inventif, motivateur, leader né</li>\n        <li>Peut devenir instable, autoritaire, autocritique</li>\n        <li>Leçon de vie : cultiver la cohérence, la confiance et l’ancrage</li>\n      </ul>\n    </div>\n  </div>"], "mappings": ";AAOA,OAAM,MAAOA,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,aAA0B;UACtBD,EAAA,CAAAE,SAAA,aAAwD;UACxDF,EAAA,CAAAC,cAAA,aAA8B;UACxBD,EAAA,CAAAG,MAAA,aAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACfJ,EAAA,CAAAC,cAAA,SAAI;UACED,EAAA,CAAAG,MAAA,qBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvBJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,+EAA8D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,gFAAyD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,+DAAuD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,2CAA8B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvCJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,sEAAyD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,oDAA4C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACrDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,iEAAoD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC7DJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,wDAAgD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,sFAA+D;UAAAH,EAAA,CAAAI,YAAA,EAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}