{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class TypeirisComponent {\n  static {\n    this.ɵfac = function TypeirisComponent_Factory(t) {\n      return new (t || TypeirisComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TypeirisComponent,\n      selectors: [[\"app-typeiris\"]],\n      decls: 16,\n      vars: 0,\n      consts: [[1, \"typeiris-container\"], [1, \"logo\"], [1, \"title\"], [1, \"underline\"], [1, \"content\"], [\"src\", \"assets/iris3.png\", \"alt\", \"Iris illustration\", 1, \"iris-image\"], [1, \"description\"], [\"routerLink\", \"/iris2\", 1, \"next-button\"]],\n      template: function TypeirisComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\")(2, \"h1\", 1);\n          i0.ɵɵtext(3, \"Iris\");\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵtext(5, \"Lock\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"main\")(7, \"h2\", 2);\n          i0.ɵɵtext(8, \"Iris Types\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(9, \"hr\", 3);\n          i0.ɵɵelementStart(10, \"div\", 4);\n          i0.ɵɵelement(11, \"img\", 5);\n          i0.ɵɵelementStart(12, \"p\", 6);\n          i0.ɵɵtext(13, \" Bien que nous soyons g\\u00E9n\\u00E9ralement domin\\u00E9s par un seul motif d\\u2019iris, ou parfois par une combinaison de deux, chacun de nous porte en lui l\\u2019essence des quatre types fondamentaux. Ces caract\\u00E9ristiques ne sont pas fig\\u00E9es : elles \\u00E9voluent et se modulent selon notre rang de naissance, notre parcours de vie et nos habitudes quotidiennes. Par exemple, la place occup\\u00E9e dans la fratrie influence la mani\\u00E8re dont s\\u2019exprime le type d\\u2019iris, tout comme notre \\u00E9tat de sant\\u00E9 et nos activit\\u00E9s personnelles. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 7);\n          i0.ɵɵtext(15, \"Suivant\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".typeiris-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  background: linear-gradient(to right, #e3d4f5, #fcd9de);\\n  font-family: \\\"Segoe UI\\\", sans-serif;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-family: \\\"Georgia\\\", serif;\\n  font-weight: bold;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: normal;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-family: \\\"Georgia\\\", serif;\\n  margin-top: 40px;\\n  margin-bottom: 10px;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .underline[_ngcontent-%COMP%] {\\n  width: 320px;\\n  height: 4px;\\n  background-color: white;\\n  border: none;\\n  margin-bottom: 30px;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 30px;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 160px;\\n  height: 160px;\\n  object-fit: contain;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-family: \\\"Georgia\\\", serif;\\n  font-size: 1.2rem;\\n  line-height: 1.8rem;\\n  text-align: justify;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .next-button-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 40px;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .next-button-container[_ngcontent-%COMP%]   .next-button[_ngcontent-%COMP%] {\\n  background-color: #e0aaff;\\n  color: #fff;\\n  border: none;\\n  padding: 12px 30px;\\n  font-size: 1.1rem;\\n  font-weight: bold;\\n  border-radius: 30px;\\n  transition: background-color 0.3s, transform 0.2s;\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .next-button-container[_ngcontent-%COMP%]   .next-button[_ngcontent-%COMP%]:hover {\\n  background-color: #d68bf5;\\n  transform: scale(1.05);\\n}\\n.typeiris-container[_ngcontent-%COMP%]   main[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .next-button-container[_ngcontent-%COMP%]   .next-button[_ngcontent-%COMP%]:active {\\n  transform: scale(1);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["TypeirisComponent", "selectors", "decls", "vars", "consts", "template", "TypeirisComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\typeiris\\typeiris.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\typeiris\\typeiris.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-typeiris',\n  templateUrl: './typeiris.component.html',\n  styleUrls: ['./typeiris.component.scss']\n})\nexport class TypeirisComponent {\n\n}\n", "<div class=\"typeiris-container\">\n    <header>\n      <h1 class=\"logo\">Iris<span>Lock</span></h1>\n    </header>\n  \n    <main>\n      <h2 class=\"title\">Iris Types</h2>\n      <hr class=\"underline\" />\n  \n      <div class=\"content\">\n        <img src=\"assets/iris3.png\" alt=\"Iris illustration\" class=\"iris-image\" />\n  \n        <p class=\"description\">\n          Bien que nous soyons généralement dominés par un seul motif d’iris, ou parfois par une combinaison de deux,\n          chacun de nous porte en lui l’essence des quatre types fondamentaux. Ces caractéristiques ne sont pas figées :\n          elles évoluent et se modulent selon notre rang de naissance, notre parcours de vie et nos habitudes quotidiennes.\n          Par exemple, la place occupée dans la fratrie influence la manière dont s’exprime le type d’iris, tout comme notre\n          état de santé et nos activités personnelles.\n        </p>\n        <button class=\"next-button\" routerLink=\"/iris2\">Suivant</button>\n      </div>\n    </main>\n  </div>\n  "], "mappings": ";;AAOA,OAAM,MAAOA,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP9BE,EAAA,CAAAC,cAAA,aAAgC;UAETD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGxCH,EAAA,CAAAC,cAAA,WAAM;UACcD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAI,SAAA,YAAwB;UAExBJ,EAAA,CAAAC,cAAA,cAAqB;UACnBD,EAAA,CAAAI,SAAA,cAAyE;UAEzEJ,EAAA,CAAAC,cAAA,YAAuB;UACrBD,EAAA,CAAAE,MAAA,ikBAKF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,iBAAgD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}