{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FluxComponent {\n  static {\n    this.ɵfac = function FluxComponent_Factory(t) {\n      return new (t || FluxComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FluxComponent,\n      selectors: [[\"app-flux\"]],\n      decls: 26,\n      vars: 0,\n      consts: [[1, \"iris-details\"], [\"src\", \"assets/3.png\", \"alt\", \"Flux\", 1, \"iris-img\"], [1, \"iris-description\"]],\n      template: function FluxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4, \"Flux\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"ul\")(6, \"li\");\n          i0.ɵɵtext(7, \"Le Sensitif\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"li\");\n          i0.ɵɵtext(9, \"Type intuitif, physique et empathique par nature\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"li\");\n          i0.ɵɵtext(11, \"Int\\u00E8gre la vie via l\\u2019exp\\u00E9rience sensorielle et corporelle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"li\");\n          i0.ɵɵtext(13, \"Apprentissage kinesth\\u00E9sique : bouger, pratiquer, ressentir\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"li\");\n          i0.ɵɵtext(15, \"Calme, pos\\u00E9, attentionn\\u00E9, \\u00E9quilibre les autres\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"li\");\n          i0.ɵɵtext(17, \"Adapt\\u00E9 aux soins, sport, et services humains\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"li\");\n          i0.ɵɵtext(19, \"Communication physique : posture, gestes, toucher contr\\u00F4l\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"li\");\n          i0.ɵɵtext(21, \"Apporte stabilit\\u00E9, empathie et soutien\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"li\");\n          i0.ɵɵtext(23, \"Peut se sentir impuissant ou d\\u00E9bord\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"li\");\n          i0.ɵɵtext(25, \"Le\\u00E7on de vie : faire confiance, l\\u00E2cher prise et trouver sa mission\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\".iris-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  background: linear-gradient(to right, #f8e8ff, #f6f1ff);\\n  min-height: 100vh;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 180px;\\n  height: 180px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\\n  margin-bottom: 25px;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-title[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: bold;\\n  color: #5e548e;\\n  margin-bottom: 15px;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n}\\n.iris-container[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n  max-width: 700px;\\n  font-size: 1.1rem;\\n  line-height: 1.7;\\n  color: #3d3d3d;\\n  text-align: center;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  background: rgba(255, 255, 255, 0.8);\\n  padding: 20px;\\n  border-radius: 20px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (max-width: 768px) {\\n  .iris-container[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n    width: 140px;\\n    height: 140px;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .iris-container[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    padding: 15px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FluxComponent", "selectors", "decls", "vars", "consts", "template", "FluxComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\flux\\flux.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\flux\\flux.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-flux',\n  templateUrl: './flux.component.html',\n  styleUrls: ['./flux.component.scss']\n})\nexport class FluxComponent {\n\n}\n", "<div class=\"iris-details\">\n    <img src=\"assets/3.png\" alt=\"Flux\" class=\"iris-img\" />\n    <div class=\"iris-description\">\n      <h2>Flux</h2>\n      <ul>\n        <li>Le Sensitif</li>\n        <li>Type intuitif, physique et empathique par nature</li>\n        <li>Intègre la vie via l’expérience sensorielle et corporelle</li>\n        <li>Apprentissage kinesthésique : bouger, pratiquer, ressentir</li>\n        <li><PERSON><PERSON>, posé, attentionn<PERSON>, équilibre les autres</li>\n        <li>Adapté aux soins, sport, et services humains</li>\n        <li>Communication physique : posture, gestes, toucher contrôlé</li>\n        <li>Apporte stabilité, empathie et soutien</li>\n        <li>Peut se sentir impuissant ou débordé</li>\n        <li>Leçon de vie : faire confiance, lâcher prise et trouver sa mission</li>\n      </ul>\n    </div>\n  </div>"], "mappings": ";AAOA,OAAM,MAAOA,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BE,EAAA,CAAAC,cAAA,aAA0B;UACtBD,EAAA,CAAAE,SAAA,aAAsD;UACtDF,EAAA,CAAAC,cAAA,aAA8B;UACxBD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACbJ,EAAA,CAAAC,cAAA,SAAI;UACED,EAAA,CAAAG,MAAA,kBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpBJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,uDAAgD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,gFAAyD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,uEAA0D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,qEAA8C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,yDAA4C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACrDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,4EAA0D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,mDAAsC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/CJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,sDAAoC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC7CJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,oFAAkE;UAAAH,EAAA,CAAAI,YAAA,EAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}