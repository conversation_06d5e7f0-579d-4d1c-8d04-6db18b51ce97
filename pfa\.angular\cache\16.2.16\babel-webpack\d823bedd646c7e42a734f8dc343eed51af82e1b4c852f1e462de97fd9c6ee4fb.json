{"ast": null, "code": "import { getApps as getApps$1, getApp as getApp$1, registerVersion as registerVersion$1, deleteApp as deleteApp$1, initializeApp as initializeApp$1, onLog as onLog$1, setLogLevel as setLogLevel$1 } from 'firebase/app';\nexport * from 'firebase/app';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, VERSION as VERSION$1, PLATFORM_ID, NgModule, Inject, NgZone, Injector } from '@angular/core';\nimport { VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\nclass FirebaseApp {\n  constructor(app) {\n    return app;\n  }\n}\nclass FirebaseApps {\n  constructor() {\n    return getApps$1();\n  }\n}\nconst firebaseApp$ = timer(0, 300).pipe(concatMap(() => from(getApps$1())), distinct());\nfunction defaultFirebaseAppFactory(provided) {\n  // Use the provided app, if there is only one, otherwise fetch the default app\n  if (provided && provided.length === 1) {\n    return provided[0];\n  }\n  return new FirebaseApp(getApp$1());\n}\n// With FIREBASE_APPS I wanted to capture the default app instance, if it is initialized by\n// the reserved URL; ɵPROVIDED_FIREBASE_APPS is not for public consumption and serves to ensure that all\n// provideFirebaseApp(...) calls are satisfied before FirebaseApp$ or FirebaseApp is resolved\nconst PROVIDED_FIREBASE_APPS = new InjectionToken('angularfire2._apps');\n// Injecting FirebaseApp will now only inject the default Firebase App\n// this allows allows beginners to import /__/firebase/init.js to auto initialize Firebase App\n// from the reserved URL.\nconst DEFAULT_FIREBASE_APP_PROVIDER = {\n  provide: FirebaseApp,\n  useFactory: defaultFirebaseAppFactory,\n  deps: [[new Optional(), PROVIDED_FIREBASE_APPS]]\n};\nconst FIREBASE_APPS_PROVIDER = {\n  provide: FirebaseApps,\n  deps: [[new Optional(), PROVIDED_FIREBASE_APPS]]\n};\nfunction firebaseAppFactory(fn) {\n  return (zone, injector) => {\n    const app = zone.runOutsideAngular(() => fn(injector));\n    return new FirebaseApp(app);\n  };\n}\nclass FirebaseAppModule {\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  constructor(platformId) {\n    registerVersion$1('angularfire', VERSION.full, 'core');\n    registerVersion$1('angularfire', VERSION.full, 'app');\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    registerVersion$1('angular', VERSION$1.full, platformId.toString());\n  }\n  static ɵfac = function FirebaseAppModule_Factory(t) {\n    return new (t || FirebaseAppModule)(i0.ɵɵinject(PLATFORM_ID));\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FirebaseAppModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [DEFAULT_FIREBASE_APP_PROVIDER, FIREBASE_APPS_PROVIDER]\n  });\n}\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FirebaseAppModule, [{\n    type: NgModule,\n    args: [{\n      providers: [DEFAULT_FIREBASE_APP_PROVIDER, FIREBASE_APPS_PROVIDER]\n    }]\n  }], function () {\n    return [{\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})();\n// Calling initializeApp({ ... }, 'name') multiple times will add more FirebaseApps into the FIREBASE_APPS\n// injection scope. This allows developers to more easily work with multiple Firebase Applications. Downside\n// is that DI for app name and options doesn't really make sense anymore.\nfunction provideFirebaseApp(fn, ...deps) {\n  return {\n    ngModule: FirebaseAppModule,\n    providers: [{\n      provide: PROVIDED_FIREBASE_APPS,\n      useFactory: firebaseAppFactory(fn),\n      multi: true,\n      deps: [NgZone, Injector, ɵAngularFireSchedulers, ...deps]\n    }]\n  };\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst deleteApp = ɵzoneWrap(deleteApp$1, true);\nconst getApp = ɵzoneWrap(getApp$1, true);\nconst getApps = ɵzoneWrap(getApps$1, true);\nconst initializeApp = ɵzoneWrap(initializeApp$1, true);\nconst onLog = ɵzoneWrap(onLog$1, true);\nconst registerVersion = ɵzoneWrap(registerVersion$1, true);\nconst setLogLevel = ɵzoneWrap(setLogLevel$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FirebaseApp, FirebaseAppModule, FirebaseApps, deleteApp, firebaseApp$, getApp, getApps, initializeApp, onLog, provideFirebaseApp, registerVersion, setLogLevel };", "map": {"version": 3, "names": ["getApps", "getApps$1", "getApp", "getApp$1", "registerVersion", "registerVersion$1", "deleteApp", "deleteApp$1", "initializeApp", "initializeApp$1", "onLog", "onLog$1", "setLogLevel", "setLogLevel$1", "timer", "from", "concatMap", "distinct", "i0", "InjectionToken", "Optional", "VERSION", "VERSION$1", "PLATFORM_ID", "NgModule", "Inject", "NgZone", "Injector", "ɵAngularFireSchedulers", "ɵzoneWrap", "FirebaseApp", "constructor", "app", "FirebaseApps", "firebaseApp$", "pipe", "defaultFirebaseAppFactory", "provided", "length", "PROVIDED_FIREBASE_APPS", "DEFAULT_FIREBASE_APP_PROVIDER", "provide", "useFactory", "deps", "FIREBASE_APPS_PROVIDER", "firebaseAppFactory", "fn", "zone", "injector", "runOutsideAngular", "FirebaseAppModule", "platformId", "full", "toString", "ɵfac", "FirebaseAppModule_Factory", "t", "ɵɵinject", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "providers", "ngDevMode", "ɵsetClassMetadata", "args", "Object", "decorators", "provideFirebaseApp", "ngModule", "multi"], "sources": ["D:/ProjetPfa/pfa/pfa/node_modules/@angular/fire/fesm2022/angular-fire-app.mjs"], "sourcesContent": ["import { getApps as getApps$1, getApp as getApp$1, registerVersion as registerVersion$1, deleteApp as deleteApp$1, initializeApp as initializeApp$1, onLog as onLog$1, setLogLevel as setLogLevel$1 } from 'firebase/app';\nexport * from 'firebase/app';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, VERSION as VERSION$1, PLATFORM_ID, NgModule, Inject, NgZone, Injector } from '@angular/core';\nimport { VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\n\nclass FirebaseApp {\n    constructor(app) {\n        return app;\n    }\n}\nclass FirebaseApps {\n    constructor() {\n        return getApps$1();\n    }\n}\nconst firebaseApp$ = timer(0, 300).pipe(concatMap(() => from(getApps$1())), distinct());\n\nfunction defaultFirebaseAppFactory(provided) {\n    // Use the provided app, if there is only one, otherwise fetch the default app\n    if (provided && provided.length === 1) {\n        return provided[0];\n    }\n    return new FirebaseApp(getApp$1());\n}\n// With FIREBASE_APPS I wanted to capture the default app instance, if it is initialized by\n// the reserved URL; ɵPROVIDED_FIREBASE_APPS is not for public consumption and serves to ensure that all\n// provideFirebaseApp(...) calls are satisfied before FirebaseApp$ or FirebaseApp is resolved\nconst PROVIDED_FIREBASE_APPS = new InjectionToken('angularfire2._apps');\n// Injecting FirebaseApp will now only inject the default Firebase App\n// this allows allows beginners to import /__/firebase/init.js to auto initialize Firebase App\n// from the reserved URL.\nconst DEFAULT_FIREBASE_APP_PROVIDER = {\n    provide: FirebaseApp,\n    useFactory: defaultFirebaseAppFactory,\n    deps: [\n        [new Optional(), PROVIDED_FIREBASE_APPS],\n    ],\n};\nconst FIREBASE_APPS_PROVIDER = {\n    provide: FirebaseApps,\n    deps: [\n        [new Optional(), PROVIDED_FIREBASE_APPS],\n    ],\n};\nfunction firebaseAppFactory(fn) {\n    return (zone, injector) => {\n        const app = zone.runOutsideAngular(() => fn(injector));\n        return new FirebaseApp(app);\n    };\n}\nclass FirebaseAppModule {\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    constructor(platformId) {\n        registerVersion$1('angularfire', VERSION.full, 'core');\n        registerVersion$1('angularfire', VERSION.full, 'app');\n        // eslint-disable-next-line @typescript-eslint/no-base-to-string\n        registerVersion$1('angular', VERSION$1.full, platformId.toString());\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"16.2.10\", ngImport: i0, type: FirebaseAppModule, deps: [{ token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"16.2.10\", ngImport: i0, type: FirebaseAppModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"16.2.10\", ngImport: i0, type: FirebaseAppModule, providers: [\n            DEFAULT_FIREBASE_APP_PROVIDER,\n            FIREBASE_APPS_PROVIDER,\n        ] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"16.2.10\", ngImport: i0, type: FirebaseAppModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        DEFAULT_FIREBASE_APP_PROVIDER,\n                        FIREBASE_APPS_PROVIDER,\n                    ]\n                }]\n        }], ctorParameters: function () { return [{ type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }]; } });\n// Calling initializeApp({ ... }, 'name') multiple times will add more FirebaseApps into the FIREBASE_APPS\n// injection scope. This allows developers to more easily work with multiple Firebase Applications. Downside\n// is that DI for app name and options doesn't really make sense anymore.\nfunction provideFirebaseApp(fn, ...deps) {\n    return {\n        ngModule: FirebaseAppModule,\n        providers: [{\n                provide: PROVIDED_FIREBASE_APPS,\n                useFactory: firebaseAppFactory(fn),\n                multi: true,\n                deps: [\n                    NgZone,\n                    Injector,\n                    ɵAngularFireSchedulers,\n                    ...deps,\n                ],\n            }],\n    };\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst deleteApp = ɵzoneWrap(deleteApp$1, true);\nconst getApp = ɵzoneWrap(getApp$1, true);\nconst getApps = ɵzoneWrap(getApps$1, true);\nconst initializeApp = ɵzoneWrap(initializeApp$1, true);\nconst onLog = ɵzoneWrap(onLog$1, true);\nconst registerVersion = ɵzoneWrap(registerVersion$1, true);\nconst setLogLevel = ɵzoneWrap(setLogLevel$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FirebaseApp, FirebaseAppModule, FirebaseApps, deleteApp, firebaseApp$, getApp, getApps, initializeApp, onLog, provideFirebaseApp, registerVersion, setLogLevel };\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,SAAS,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,eAAe,IAAIC,iBAAiB,EAAEC,SAAS,IAAIC,WAAW,EAAEC,aAAa,IAAIC,eAAe,EAAEC,KAAK,IAAIC,OAAO,EAAEC,WAAW,IAAIC,aAAa,QAAQ,cAAc;AACzN,cAAc,cAAc;AAC5B,SAASC,KAAK,EAAEC,IAAI,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;AACpD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,QAAQ,EAAEC,OAAO,IAAIC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC/H,SAASN,OAAO,EAAEO,sBAAsB,EAAEC,SAAS,QAAQ,eAAe;AAE1E,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAACC,GAAG,EAAE;IACb,OAAOA,GAAG;EACd;AACJ;AACA,MAAMC,YAAY,CAAC;EACfF,WAAWA,CAAA,EAAG;IACV,OAAO9B,SAAS,CAAC,CAAC;EACtB;AACJ;AACA,MAAMiC,YAAY,GAAGpB,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAACqB,IAAI,CAACnB,SAAS,CAAC,MAAMD,IAAI,CAACd,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEgB,QAAQ,CAAC,CAAC,CAAC;AAEvF,SAASmB,yBAAyBA,CAACC,QAAQ,EAAE;EACzC;EACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;IACnC,OAAOD,QAAQ,CAAC,CAAC,CAAC;EACtB;EACA,OAAO,IAAIP,WAAW,CAAC3B,QAAQ,CAAC,CAAC,CAAC;AACtC;AACA;AACA;AACA;AACA,MAAMoC,sBAAsB,GAAG,IAAIpB,cAAc,CAAC,oBAAoB,CAAC;AACvE;AACA;AACA;AACA,MAAMqB,6BAA6B,GAAG;EAClCC,OAAO,EAAEX,WAAW;EACpBY,UAAU,EAAEN,yBAAyB;EACrCO,IAAI,EAAE,CACF,CAAC,IAAIvB,QAAQ,CAAC,CAAC,EAAEmB,sBAAsB,CAAC;AAEhD,CAAC;AACD,MAAMK,sBAAsB,GAAG;EAC3BH,OAAO,EAAER,YAAY;EACrBU,IAAI,EAAE,CACF,CAAC,IAAIvB,QAAQ,CAAC,CAAC,EAAEmB,sBAAsB,CAAC;AAEhD,CAAC;AACD,SAASM,kBAAkBA,CAACC,EAAE,EAAE;EAC5B,OAAO,CAACC,IAAI,EAAEC,QAAQ,KAAK;IACvB,MAAMhB,GAAG,GAAGe,IAAI,CAACE,iBAAiB,CAAC,MAAMH,EAAE,CAACE,QAAQ,CAAC,CAAC;IACtD,OAAO,IAAIlB,WAAW,CAACE,GAAG,CAAC;EAC/B,CAAC;AACL;AACA,MAAMkB,iBAAiB,CAAC;EACpB;EACAnB,WAAWA,CAACoB,UAAU,EAAE;IACpB9C,iBAAiB,CAAC,aAAa,EAAEgB,OAAO,CAAC+B,IAAI,EAAE,MAAM,CAAC;IACtD/C,iBAAiB,CAAC,aAAa,EAAEgB,OAAO,CAAC+B,IAAI,EAAE,KAAK,CAAC;IACrD;IACA/C,iBAAiB,CAAC,SAAS,EAAEiB,SAAS,CAAC8B,IAAI,EAAED,UAAU,CAACE,QAAQ,CAAC,CAAC,CAAC;EACvE;EACA,OAAOC,IAAI,YAAAC,0BAAAC,CAAA;IAAA,YAAAA,CAAA,IAAyFN,iBAAiB,EAA3BhC,EAAE,CAAAuC,QAAA,CAA2ClC,WAAW;EAAA;EAClJ,OAAOmC,IAAI,kBAD+ExC,EAAE,CAAAyC,gBAAA;IAAAC,IAAA,EACSV;EAAiB;EACtH,OAAOW,IAAI,kBAF+E3C,EAAE,CAAA4C,gBAAA;IAAAC,SAAA,EAEuC,CAC3HvB,6BAA6B,EAC7BI,sBAAsB;EACzB;AACT;AACA;EAAA,QAAAoB,SAAA,oBAAAA,SAAA,KAP8F9C,EAAE,CAAA+C,iBAAA,CAOJf,iBAAiB,EAAc,CAAC;IAChHU,IAAI,EAAEpC,QAAQ;IACd0C,IAAI,EAAE,CAAC;MACCH,SAAS,EAAE,CACPvB,6BAA6B,EAC7BI,sBAAsB;IAE9B,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEgB,IAAI,EAAEO,MAAM;MAAEC,UAAU,EAAE,CAAC;QAC3DR,IAAI,EAAEnC,MAAM;QACZyC,IAAI,EAAE,CAAC3C,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxB;AACA;AACA;AACA,SAAS8C,kBAAkBA,CAACvB,EAAE,EAAE,GAAGH,IAAI,EAAE;EACrC,OAAO;IACH2B,QAAQ,EAAEpB,iBAAiB;IAC3Ba,SAAS,EAAE,CAAC;MACJtB,OAAO,EAAEF,sBAAsB;MAC/BG,UAAU,EAAEG,kBAAkB,CAACC,EAAE,CAAC;MAClCyB,KAAK,EAAE,IAAI;MACX5B,IAAI,EAAE,CACFjB,MAAM,EACNC,QAAQ,EACRC,sBAAsB,EACtB,GAAGe,IAAI;IAEf,CAAC;EACT,CAAC;AACL;;AAEA;AACA,MAAMrC,SAAS,GAAGuB,SAAS,CAACtB,WAAW,EAAE,IAAI,CAAC;AAC9C,MAAML,MAAM,GAAG2B,SAAS,CAAC1B,QAAQ,EAAE,IAAI,CAAC;AACxC,MAAMH,OAAO,GAAG6B,SAAS,CAAC5B,SAAS,EAAE,IAAI,CAAC;AAC1C,MAAMO,aAAa,GAAGqB,SAAS,CAACpB,eAAe,EAAE,IAAI,CAAC;AACtD,MAAMC,KAAK,GAAGmB,SAAS,CAAClB,OAAO,EAAE,IAAI,CAAC;AACtC,MAAMP,eAAe,GAAGyB,SAAS,CAACxB,iBAAiB,EAAE,IAAI,CAAC;AAC1D,MAAMO,WAAW,GAAGiB,SAAS,CAAChB,aAAa,EAAE,IAAI,CAAC;;AAElD;AACA;AACA;;AAEA,SAASiB,WAAW,EAAEoB,iBAAiB,EAAEjB,YAAY,EAAE3B,SAAS,EAAE4B,YAAY,EAAEhC,MAAM,EAAEF,OAAO,EAAEQ,aAAa,EAAEE,KAAK,EAAE2D,kBAAkB,EAAEjE,eAAe,EAAEQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}