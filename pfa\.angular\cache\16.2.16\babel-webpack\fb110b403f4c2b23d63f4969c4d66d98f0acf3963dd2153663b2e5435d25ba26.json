{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nexport class SignupComponent {\n  constructor() {\n    this.user = {\n      name: '',\n      email: '',\n      password: '',\n      terms: false\n    };\n  }\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 50,\n      vars: 5,\n      consts: [[1, \"signup-container\"], [1, \"left-section\"], [1, \"graphic\"], [1, \"iris-collage\"], [\"src\", \"assets/iris-collage.png\", \"alt\", \"Iris Collage\", 1, \"iris-collage-img\"], [1, \"slogan\"], [1, \"right-section\"], [1, \"form-container\"], [1, \"logo\"], [1, \"custom-logo\"], [1, \"google-btn\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google Icon\"], [1, \"divider\"], [3, \"ngSubmit\"], [\"signupForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \"Leslie Alexander\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"placeholder\", \"<EMAIL>\", \"required\", \"\", \"email\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"placeholder\", \"At least 8 characters\", \"required\", \"\", \"minlength\", \"8\", 3, \"ngModel\", \"ngModelChange\"], [1, \"checkbox-group\"], [\"type\", \"checkbox\", \"id\", \"terms\", \"name\", \"terms\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"terms\"], [\"href\", \"#\"], [\"type\", \"submit\", 1, \"signup-btn\", 3, \"disabled\"], [1, \"login-link\"], [\"routerLink\", \"/login\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"p\", 5);\n          i0.ɵɵtext(6, \" Each iris is a unique story written by nature, waiting to be decoded by technology \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9);\n          i0.ɵɵelement(11, \"span\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"h1\");\n          i0.ɵɵtext(13, \"Sign Up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"p\");\n          i0.ɵɵtext(15, \"Votre identit\\u00E9, red\\u00E9finie par la technologie. Cr\\u00E9ez votre compte.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"button\", 10);\n          i0.ɵɵelement(17, \"img\", 11);\n          i0.ɵɵtext(18, \" Continue with Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 12);\n          i0.ɵɵtext(20, \"or Sign in with Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"form\", 13, 14);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_21_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(23, \"div\", 15)(24, \"label\", 16);\n          i0.ɵɵtext(25, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"input\", 17);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_26_listener($event) {\n            return ctx.user.name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 15)(28, \"label\", 18);\n          i0.ɵɵtext(29, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"input\", 19);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_30_listener($event) {\n            return ctx.user.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 15)(32, \"label\", 20);\n          i0.ɵɵtext(33, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"input\", 21);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_34_listener($event) {\n            return ctx.user.password = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 22)(36, \"input\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_36_listener($event) {\n            return ctx.user.terms = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"label\", 24);\n          i0.ɵɵtext(38, \"I agree with \");\n          i0.ɵɵelementStart(39, \"a\", 25);\n          i0.ɵɵtext(40, \"Terms\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(41, \" and \");\n          i0.ɵɵelementStart(42, \"a\", 25);\n          i0.ɵɵtext(43, \"Privacy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"button\", 26);\n          i0.ɵɵtext(45, \" Sign Up \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"p\", 27);\n          i0.ɵɵtext(47, \" Already have an account? \");\n          i0.ɵɵelementStart(48, \"a\", 28);\n          i0.ɵɵtext(49, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(22);\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.terms);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", !_r0.valid);\n        }\n      },\n      dependencies: [i1.RouterLink, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MinLengthValidator, i2.CheckboxRequiredValidator, i2.EmailValidator, i2.NgModel, i2.NgForm],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.signup-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  font-family: \\\"Arial\\\", sans-serif;\\n  background: #fff;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, #e6e6fa, #f5f5ff); \\n\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.graphic[_ngcontent-%COMP%] {\\n  position: relative;\\n  text-align: center;\\n}\\n\\n.iris-collage[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.iris-collage-img[_ngcontent-%COMP%] {\\n  width: 450px;\\n  height: auto;\\n  object-fit: contain;\\n}\\n\\n\\n\\n.circle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border-radius: 50%;\\n  background: rgba(255, 182, 193, 0.3); \\n\\n}\\n\\n\\n\\n.circle1[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  top: 20px;\\n  left: 50px;\\n}\\n\\n.circle2[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  bottom: 50px;\\n  right: 30px;\\n}\\n\\n.circle3[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  top: 80px;\\n  right: 80px;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%]::before, .left-section[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  border-radius: 50%;\\n  background: #ff4040; \\n\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before {\\n  width: 5px;\\n  height: 5px;\\n  top: 10%;\\n  left: 20%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::after {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 15%;\\n  right: 10%;\\n}\\n\\n.slogan[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #6a5acd; \\n\\n  text-align: center;\\n  margin-top: 20px;\\n  font-style: italic;\\n  font-family: \\\"Dancing Script\\\", cursive;\\n}\\n\\n\\n\\n.right-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background: #fff;\\n}\\n\\n.form-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  padding: 20px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.custom-logo[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  margin: 0 auto;\\n  position: relative;\\n  background: transparent;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd; \\n\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  bottom: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd;\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  left: 5px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  right: 5px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  color: #333;\\n  font-weight: 700;\\n  margin: 10px 0;\\n}\\n\\n.logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n}\\n\\n.google-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  padding: 10px;\\n  background: #fff;\\n  border: 1px solid #ccc;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  font-size: 1rem;\\n  color: #333;\\n  margin-bottom: 20px;\\n  transition: background-color 0.3s;\\n}\\n\\n.google-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n\\n.google-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  margin-right: 10px;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin: 20px 0;\\n  position: relative;\\n}\\n\\n.divider[_ngcontent-%COMP%]::before, .divider[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 40%;\\n  height: 1px;\\n  background: #ccc;\\n}\\n\\n.divider[_ngcontent-%COMP%]::before {\\n  left: 0;\\n}\\n\\n.divider[_ngcontent-%COMP%]::after {\\n  right: 0;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.9rem;\\n  color: #333;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  border: 1px solid #ccc;\\n  border-radius: 5px;\\n  font-size: 1rem;\\n  color: #333;\\n  background: #fff;\\n  transition: border-color 0.3s;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #6a5acd; \\n\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd; \\n\\n  text-decoration: none;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px;\\n  background: #6a5acd; \\n\\n  color: #fff;\\n  border: none;\\n  border-radius: 5px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  text-transform: uppercase;\\n  transition: background-color 0.3s;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:hover {\\n  background: #5a4bbd;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 20px;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd; \\n\\n  text-decoration: none;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SignupComponent", "constructor", "user", "name", "email", "password", "terms", "onSubmit", "console", "log", "alert", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_21_listener", "SignupComponent_Template_input_ngModelChange_26_listener", "$event", "SignupComponent_Template_input_ngModelChange_30_listener", "SignupComponent_Template_input_ngModelChange_34_listener", "SignupComponent_Template_input_ngModelChange_36_listener", "ɵɵadvance", "ɵɵproperty", "_r0", "valid"], "sources": ["C:\\pfa\\src\\app\\signup\\signup.component.ts", "C:\\pfa\\src\\app\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.scss']\n})\nexport class SignupComponent {\n  user = {\n    name: '',\n    email: '',\n    password: '',\n    terms: false\n  };\n\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n}", "<div class=\"signup-container\">\n    <!-- Section gauche : Graphique et texte -->\n    <div class=\"left-section\">\n      <div class=\"graphic\">\n        <!-- Conserver l'image des iris -->\n        <div class=\"iris-collage\">\n          <img src=\"assets/iris-collage.png\" alt=\"Iris Collage\" class=\"iris-collage-img\" />\n        </div>\n      </div>\n      <p class=\"slogan\">\n        Each iris is a unique story written by nature, waiting to be decoded by technology\n      </p>\n    </div>\n  \n    <!-- Section droite : Formulaire -->\n    <div class=\"right-section\">\n      <div class=\"form-container\">\n        <div class=\"logo\">\n          <!-- Logo en forme de croix -->\n          <div class=\"custom-logo\"><span></span></div>\n          <h1>Sign Up</h1>\n          <p>Votre identité, redéfinie par la technologie. Créez votre compte.</p>\n        </div>\n  \n        <!-- Bouton \"Continue with Google\" -->\n        <button class=\"google-btn\">\n          <img src=\"assets/google-icon.png\" alt=\"Google Icon\" /> Continue with Google\n        </button>\n  \n        <div class=\"divider\">or Sign in with Email</div>\n  \n        <!-- Formulaire -->\n        <form (ngSubmit)=\"onSubmit()\" #signupForm=\"ngForm\">\n          <div class=\"form-group\">\n            <label for=\"name\">Name</label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              placeholder=\"Leslie Alexander\"\n              [(ngModel)]=\"user.name\"\n              required\n            />\n          </div>\n  \n          <div class=\"form-group\">\n            <label for=\"email\">Email</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              placeholder=\"<EMAIL>\"\n              [(ngModel)]=\"user.email\"\n              required\n              email\n            />\n          </div>\n  \n          <div class=\"form-group\">\n            <label for=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              placeholder=\"At least 8 characters\"\n              [(ngModel)]=\"user.password\"\n              required\n              minlength=\"8\"\n            />\n          </div>\n  \n          <div class=\"checkbox-group\">\n            <input\n              type=\"checkbox\"\n              id=\"terms\"\n              name=\"terms\"\n              [(ngModel)]=\"user.terms\"\n              required\n            />\n            <label for=\"terms\">I agree with <a href=\"#\">Terms</a> and <a href=\"#\">Privacy</a></label>\n          </div>\n  \n          <button type=\"submit\" class=\"signup-btn\" [disabled]=\"!signupForm.valid\">\n            Sign Up\n          </button>\n        </form>\n  \n        <p class=\"login-link\">\n          Already have an account? <a routerLink=\"/login\">Log in</a>\n        </p>\n      </div>\n    </div>\n  </div>"], "mappings": ";;;AAOA,OAAM,MAAOA,eAAe;EAL5BC,YAAA;IAME,KAAAC,IAAI,GAAG;MACLC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;KACR;;EAEDC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,IAAI,CAACI,KAAK,EAAE;MACnBE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACP,IAAI,CAAC;MACzC;KACD,MAAM;MACLQ,KAAK,CAAC,+CAA+C,CAAC;;EAE1D;;;uBAfWV,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAW,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,aAA8B;UAMpBD,EAAA,CAAAE,SAAA,aAAiF;UACnFF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAC,cAAA,WAAkB;UAChBD,EAAA,CAAAI,MAAA,2FACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,aAA2B;UAIID,EAAA,CAAAE,SAAA,YAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,wFAAiE;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAI1EH,EAAA,CAAAC,cAAA,kBAA2B;UACzBD,EAAA,CAAAE,SAAA,eAAsD;UAACF,EAAA,CAAAI,MAAA,8BACzD;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAI,MAAA,6BAAqB;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAGhDH,EAAA,CAAAC,cAAA,oBAAmD;UAA7CD,EAAA,CAAAK,UAAA,sBAAAC,mDAAA;YAAA,OAAYP,GAAA,CAAAX,QAAA,EAAU;UAAA,EAAC;UAC3BY,EAAA,CAAAC,cAAA,eAAwB;UACJD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAC,cAAA,iBAOE;UAFAD,EAAA,CAAAK,UAAA,2BAAAE,yDAAAC,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAC,IAAA,GAAAwB,MAAA;UAAA,EAAuB;UALzBR,EAAA,CAAAG,YAAA,EAOE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAI,yDAAAD,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAE,KAAA,GAAAuB,MAAA;UAAA,EAAwB;UAL1BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAK,yDAAAF,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAG,QAAA,GAAAsB,MAAA;UAAA,EAA2B;UAL7BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAA4B;UAKxBD,EAAA,CAAAK,UAAA,2BAAAM,yDAAAH,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAI,KAAA,GAAAqB,MAAA;UAAA,EAAwB;UAJ1BR,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAAmB;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAI,MAAA,aAAI;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGnFH,EAAA,CAAAC,cAAA,kBAAwE;UACtED,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,aAAsB;UACpBD,EAAA,CAAAI,MAAA,kCAAyB;UAAAJ,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;UAhDtDH,EAAA,CAAAY,SAAA,IAAuB;UAAvBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAC,IAAA,CAAuB;UAYvBgB,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAE,KAAA,CAAwB;UAaxBe,EAAA,CAAAY,SAAA,GAA2B;UAA3BZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAG,QAAA,CAA2B;UAW3Bc,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAI,KAAA,CAAwB;UAMaa,EAAA,CAAAY,SAAA,GAA8B;UAA9BZ,EAAA,CAAAa,UAAA,cAAAC,GAAA,CAAAC,KAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}