{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class Iris2Component {\n  static {\n    this.ɵfac = function Iris2Component_Factory(t) {\n      return new (t || Iris2Component)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Iris2Component,\n      selectors: [[\"app-iris2\"]],\n      decls: 17,\n      vars: 0,\n      consts: [[1, \"iris-grid\"], [1, \"iris-card\"], [\"src\", \"assets/1.png\", \"alt\", \"Fleur\"], [\"routerLink\", \"/fleur\", 1, \"iris-name\"], [\"src\", \"assets/2.png\", \"alt\", \"Bijou\"], [\"routerLink\", \"/bijou\", 1, \"iris-name\"], [\"src\", \"assets/3.png\", \"alt\", \"Flux\"], [\"routerLink\", \"/flux\", 1, \"iris-name\"], [\"src\", \"assets/4.png\", \"alt\", \"Shaker\"], [\"routerLink\", \"/shaker\", 1, \"iris-name\"]],\n      template: function Iris2Component_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementStart(3, \"a\", 3);\n          i0.ɵɵtext(4, \"Fleur\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 1);\n          i0.ɵɵelement(6, \"img\", 4);\n          i0.ɵɵelementStart(7, \"a\", 5);\n          i0.ɵɵtext(8, \"Bijou\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 1);\n          i0.ɵɵelement(10, \"img\", 6);\n          i0.ɵɵelementStart(11, \"a\", 7);\n          i0.ɵɵtext(12, \"Flux\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 1);\n          i0.ɵɵelement(14, \"img\", 8);\n          i0.ɵɵelementStart(15, \"a\", 9);\n          i0.ɵɵtext(16, \"Shaker\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".iris-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  gap: 50px;\\n  padding: 40px;\\n  background: linear-gradient(to right, #f8e8ff, #f6f1ff);\\n  flex-wrap: wrap;\\n}\\n\\n.iris-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 160px;\\n  transition: transform 0.3s;\\n}\\n.iris-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n.iris-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 130px;\\n  height: 130px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\\n  transition: transform 0.3s;\\n}\\n.iris-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.iris-card[_ngcontent-%COMP%]   .iris-name[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #5e548e;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  letter-spacing: 0.5px;\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvaXJpczIvaXJpczIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxTQUFBO0VBQ0EsYUFBQTtFQUNBLHVEQUFBO0VBQ0EsZUFBQTtBQUNKOztBQUdBO0VBQ0UsYUFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSxZQUFBO0VBQ0EsMEJBQUE7QUFBRjtBQUVFO0VBQ0UsMkJBQUE7QUFBSjtBQUdFO0VBQ0UsWUFBQTtFQUNBLGFBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0EseUNBQUE7RUFDQSwwQkFBQTtBQURKO0FBR0k7RUFDRSxzQkFBQTtBQUROO0FBS0U7RUFDRSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxjQUFBO0VBQ0Esa0NBQUE7RUFDQSxxQkFBQTtFQUNBLGtCQUFBO0FBSEoiLCJzb3VyY2VzQ29udGVudCI6WyIuaXJpcy1ncmlkIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsgICAgICAgLy8gQ2VudHJlIGhvcml6b250YWxlbWVudFxyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjsgICAgICAgICAgIC8vIENlbnRyZSB2ZXJ0aWNhbGVtZW50XHJcbiAgICBoZWlnaHQ6IDEwMHZoOyAgICAgICAgICAgICAgICAgLy8gUHJlbmQgdG91dGUgbGEgaGF1dGV1ciBkZSBsYSBmZW7Dg8KqdHJlXHJcbiAgICBnYXA6IDUwcHg7XHJcbiAgICBwYWRkaW5nOiA0MHB4O1xyXG4gICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCAjZjhlOGZmLCAjZjZmMWZmKTtcclxuICAgIGZsZXgtd3JhcDogd3JhcDsgICAgICAgICAgICAgICAvLyBQb3VyIHF1ZSDDg8KnYSByZXN0ZSByZXNwb25zaXZlXHJcbiAgfVxyXG4gIFxyXG5cclxuLmlyaXMtY2FyZCB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgd2lkdGg6IDE2MHB4O1xyXG4gIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzO1xyXG5cclxuICAmOmhvdmVyIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNXB4KTtcclxuICB9XHJcblxyXG4gIGltZyB7XHJcbiAgICB3aWR0aDogMTMwcHg7XHJcbiAgICBoZWlnaHQ6IDEzMHB4O1xyXG4gICAgb2JqZWN0LWZpdDogY292ZXI7XHJcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICBib3gtc2hhZG93OiAwIDVweCAxNXB4IHJnYmEoMCwgMCwgMCwgMC4yKTtcclxuICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzO1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMDUpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLmlyaXMtbmFtZSB7XHJcbiAgICBtYXJnaW4tdG9wOiAxMnB4O1xyXG4gICAgZm9udC1zaXplOiAxLjFyZW07XHJcbiAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgY29sb3I6ICM1ZTU0OGU7XHJcbiAgICBmb250LWZhbWlseTogJ1BvcHBpbnMnLCBzYW5zLXNlcmlmO1xyXG4gICAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xyXG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIH1cclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Iris2Component", "selectors", "decls", "vars", "consts", "template", "Iris2Component_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris2\\iris2.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris2\\iris2.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-iris2',\n  templateUrl: './iris2.component.html',\n  styleUrls: ['./iris2.component.scss']\n})\nexport class Iris2Component {\n\n}\n", "<div class=\"iris-grid\">\n    <div class=\"iris-card\">\n      <img src=\"assets/1.png\" alt=\"Fleur\" />\n      <a routerLink=\"/fleur\" class=\"iris-name\">Fleur</a>\n    </div>\n    <div class=\"iris-card\">\n      <img src=\"assets/2.png\" alt=\"Bijou\" />\n      <a routerLink=\"/bijou\" class=\"iris-name\">Bijou</a>\n    </div>\n    <div class=\"iris-card\">\n      <img src=\"assets/3.png\" alt=\"Flux\" />\n      <a routerLink=\"/flux\" class=\"iris-name\">Flux</a>\n    </div>\n    <div class=\"iris-card\">\n      <img src=\"assets/4.png\" alt=\"Shaker\" />\n      <a routerLink=\"/shaker\" class=\"iris-name\">Shaker</a>\n    </div>\n  </div>\n  "], "mappings": ";;AAOA,OAAM,MAAOA,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3BE,EAAA,CAAAC,cAAA,aAAuB;UAEjBD,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAC,cAAA,WAAyC;UAAAD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAEpDJ,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAC,cAAA,WAAyC;UAAAD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAEpDJ,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,SAAA,cAAqC;UACrCF,EAAA,CAAAC,cAAA,YAAwC;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAElDJ,EAAA,CAAAC,cAAA,cAAuB;UACrBD,EAAA,CAAAE,SAAA,cAAuC;UACvCF,EAAA,CAAAC,cAAA,YAA0C;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}