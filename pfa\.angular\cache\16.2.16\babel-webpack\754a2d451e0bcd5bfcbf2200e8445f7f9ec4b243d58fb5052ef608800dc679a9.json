{"ast": null, "code": "// Configuration Firebase pour ProfilingIris\n// Configuration obtenue depuis Firebase Console\nexport const firebaseConfig = {\n  apiKey: \"AIzaSyAkdthQO0aaXLR-wVaYYB5C_0HgSESWGns\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.firebasestorage.app\",\n  messagingSenderId: \"770202078532\",\n  appId: \"1:770202078532:web:ae40074feaca3f62011b11\",\n  measurementId: \"G-ER3TYLNDY7\"\n};\n// Pour obtenir vos vraies clés Firebase :\n// 1. Allez sur https://console.firebase.google.com/project/profilingiris/settings/general\n// 2. Cliquez sur \"Ajouter une application\" ou sélectionnez votre app web existante\n// 3. Copiez les valeurs de configuration et remplacez-les ci-dessus\n// Exemple de configuration complète :\n/*\nexport const firebaseConfig = {\n  apiKey: \"AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"123456789012\",\n  appId: \"1:123456789012:web:abcdef1234567890\"\n};\n*/", "map": {"version": 3, "names": ["firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\environments\\firebase.config.ts"], "sourcesContent": ["// Configuration Firebase pour ProfilingIris\n// Configuration obtenue depuis Firebase Console\n\nexport const firebaseConfig = {\n  apiKey: \"AIzaSyAkdthQO0aaXLR-wVaYYB5C_0HgSESWGns\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.firebasestorage.app\",\n  messagingSenderId: \"770202078532\",\n  appId: \"1:770202078532:web:ae40074feaca3f62011b11\",\n  measurementId: \"G-ER3TYLNDY7\"\n};\n\n// Pour obtenir vos vraies clés Firebase :\n// 1. Allez sur https://console.firebase.google.com/project/profilingiris/settings/general\n// 2. Cliquez sur \"Ajouter une application\" ou sélectionnez votre app web existante\n// 3. Copiez les valeurs de configuration et remplacez-les ci-dessus\n\n// Exemple de configuration complète :\n/*\nexport const firebaseConfig = {\n  apiKey: \"AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"123456789012\",\n  appId: \"1:123456789012:web:abcdef1234567890\"\n};\n*/\n"], "mappings": "AAAA;AACA;AAEA,OAAO,MAAMA,cAAc,GAAG;EAC5BC,MAAM,EAAE,yCAAyC;EACjDC,UAAU,EAAE,+BAA+B;EAC3CC,SAAS,EAAE,eAAe;EAC1BC,aAAa,EAAE,mCAAmC;EAClDC,iBAAiB,EAAE,cAAc;EACjCC,KAAK,EAAE,2CAA2C;EAClDC,aAAa,EAAE;CAChB;AAED;AACA;AACA;AACA;AAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}