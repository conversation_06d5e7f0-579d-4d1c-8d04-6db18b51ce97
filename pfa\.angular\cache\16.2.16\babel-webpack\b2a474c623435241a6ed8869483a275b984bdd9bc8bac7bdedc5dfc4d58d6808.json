{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n// Firebase imports\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\n// Firebase configuration pour PFA1\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDummy\",\n  authDomain: \"pfa1-13f62.firebaseapp.com\",\n  databaseURL: \"https://pfa1-13f62-default-rtdb.firebaseio.com/\",\n  projectId: \"pfa1-13f62\",\n  storageBucket: \"pfa1-13f62.appspot.com\",\n  messagingSenderId: \"*********\",\n  appId: \"1:*********:web:dummy\"\n};\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { IrisFormComponent } from './iris-form/iris-form.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { FooterComponent } from './shared/footer/footer.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, AccueilComponent, SuivantaccComponent, TypeirisComponent, Iris2Component, FleurComponent, BijouComponent, FluxComponent, ShakerComponent, IrisFormComponent, LoginComponent, SignupComponent, IrisDiversityComponent, DashboardComponent, FooterComponent, PersonalityTestComponent],\n  imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, provideFirebaseApp(() => initializeApp(firebaseConfig)), provideFirestore(() => getFirestore())],\n  providers: [],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "FormsModule", "ReactiveFormsModule", "AppRoutingModule", "AppComponent", "initializeApp", "provideFirebaseApp", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "databaseURL", "projectId", "storageBucket", "messagingSenderId", "appId", "AccueilComponent", "SuivantaccComponent", "TypeirisComponent", "Iris2Component", "FleurComponent", "BijouComponent", "FluxComponent", "ShakerComponent", "IrisFormComponent", "LoginComponent", "SignupComponent", "IrisDiversityComponent", "DashboardComponent", "FooterComponent", "PersonalityTestComponent", "AppModule", "__decorate", "declarations", "imports", "provideFirestore", "getFirestore", "providers", "bootstrap"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n\n// Firebase imports\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\nimport { provideDatabase, getDatabase } from '@angular/fire/database';\n\n// Firebase configuration pour PFA1\nconst firebaseConfig = {\n  apiKey: \"AIzaSyDummy\", // Remplacez par votre vraie clé API\n  authDomain: \"pfa1-13f62.firebaseapp.com\",\n  databaseURL: \"https://pfa1-13f62-default-rtdb.firebaseio.com/\",\n  projectId: \"pfa1-13f62\",\n  storageBucket: \"pfa1-13f62.appspot.com\",\n  messagingSenderId: \"*********\",\n  appId: \"1:*********:web:dummy\"\n};\n\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { IrisFormComponent } from './iris-form/iris-form.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { FooterComponent } from './shared/footer/footer.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    AccueilComponent,\n    SuivantaccComponent,\n    TypeirisComponent,\n    Iris2Component,\n    FleurComponent,\n    BijouComponent,\n    FluxComponent,\n    ShakerComponent,\n    IrisFormComponent,\n    LoginComponent,\n    SignupComponent,\n    IrisDiversityComponent,\n    DashboardComponent,\n    FooterComponent,\n    PersonalityTestComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    provideFirebaseApp(() => initializeApp(firebaseConfig)),\n    provideFirestore(() => getFirestore())\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB,CAAC,CAAC;AACnE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AAGrE;AACA,MAAMC,cAAc,GAAG;EACrBC,MAAM,EAAE,aAAa;EACrBC,UAAU,EAAE,4BAA4B;EACxCC,WAAW,EAAE,iDAAiD;EAC9DC,SAAS,EAAE,YAAY;EACvBC,aAAa,EAAE,wBAAwB;EACvCC,iBAAiB,EAAE,WAAW;EAC9BC,KAAK,EAAE;CACR;AAED,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,wBAAwB,QAAQ,+CAA+C;AAgCjF,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EA9BrBhC,QAAQ,CAAC;EACRiC,YAAY,EAAE,CACZ5B,YAAY,EACZW,gBAAgB,EAChBC,mBAAmB,EACnBC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,eAAe,EACfC,sBAAsB,EACtBC,kBAAkB,EAClBC,eAAe,EACfC,wBAAwB,CACzB;EACDI,OAAO,EAAE,CACPjC,aAAa,EACbG,gBAAgB,EAChBF,WAAW,EACXC,mBAAmB,EACnBI,kBAAkB,CAAC,MAAMD,aAAa,CAACE,cAAc,CAAC,CAAC,EACvD2B,gBAAgB,CAAC,MAAMC,YAAY,EAAE,CAAC,CACvC;EACDC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,CAACjC,YAAY;CACzB,CAAC,C,EACW0B,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}