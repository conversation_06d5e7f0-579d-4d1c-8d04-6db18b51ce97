{"ast": null, "code": "import { not } from '../util/not';\nimport { filter } from './filter';\nexport function partition(predicate, thisArg) {\n  return source => [filter(predicate, thisArg)(source), filter(not(predicate, thisArg))(source)];\n}", "map": {"version": 3, "names": ["not", "filter", "partition", "predicate", "thisArg", "source"], "sources": ["C:/pfa/node_modules/rxjs/dist/esm/internal/operators/partition.js"], "sourcesContent": ["import { not } from '../util/not';\nimport { filter } from './filter';\nexport function partition(predicate, thisArg) {\n    return (source) => [filter(predicate, thisArg)(source), filter(not(predicate, thisArg))(source)];\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,aAAa;AACjC,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAO,SAASC,SAASA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAC1C,OAAQC,MAAM,IAAK,CAACJ,MAAM,CAACE,SAAS,EAAEC,OAAO,CAAC,CAACC,MAAM,CAAC,EAAEJ,MAAM,CAACD,GAAG,CAACG,SAAS,EAAEC,OAAO,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACpG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}