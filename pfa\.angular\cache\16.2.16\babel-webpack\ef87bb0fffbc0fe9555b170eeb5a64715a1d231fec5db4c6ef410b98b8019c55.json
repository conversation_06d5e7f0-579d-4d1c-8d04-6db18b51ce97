{"ast": null, "code": "// Configuration Firebase pour ProfilingIris\n// IMPORTANT: Remplacez ces valeurs par vos vraies clés Firebase\nexport const firebaseConfig = {\n  apiKey: \"COLLEZ_VOTRE_API_KEY_ICI\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"COLLEZ_VOTRE_SENDER_ID_ICI\",\n  appId: \"COLLEZ_VOTRE_APP_ID_ICI\" // Remplacez par la vraie valeur\n};\n// Pour obtenir vos vraies clés Firebase :\n// 1. Allez sur https://console.firebase.google.com/project/profilingiris/settings/general\n// 2. Cliquez sur \"Ajouter une application\" ou sélectionnez votre app web existante\n// 3. Copiez les valeurs de configuration et remplacez-les ci-dessus\n// Exemple de configuration complète :\n/*\nexport const firebaseConfig = {\n  apiKey: \"AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"123456789012\",\n  appId: \"1:123456789012:web:abcdef1234567890\"\n};\n*/", "map": {"version": 3, "names": ["firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\environments\\firebase.config.ts"], "sourcesContent": ["// Configuration Firebase pour ProfilingIris\n// IMPORTANT: Remplacez ces valeurs par vos vraies clés Firebase\n\nexport const firebaseConfig = {\n  apiKey: \"COLLEZ_VOTRE_API_KEY_ICI\", // Remplacez par la vraie valeur de Firebase Console\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"COLLEZ_VOTRE_SENDER_ID_ICI\", // Remplacez par la vraie valeur\n  appId: \"COLLEZ_VOTRE_APP_ID_ICI\" // Remplacez par la vraie valeur\n};\n\n// Pour obtenir vos vraies clés Firebase :\n// 1. Allez sur https://console.firebase.google.com/project/profilingiris/settings/general\n// 2. Cliquez sur \"Ajouter une application\" ou sélectionnez votre app web existante\n// 3. Copiez les valeurs de configuration et remplacez-les ci-dessus\n\n// Exemple de configuration complète :\n/*\nexport const firebaseConfig = {\n  apiKey: \"AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz\",\n  authDomain: \"profilingiris.firebaseapp.com\",\n  projectId: \"profilingiris\",\n  storageBucket: \"profilingiris.appspot.com\",\n  messagingSenderId: \"123456789012\",\n  appId: \"1:123456789012:web:abcdef1234567890\"\n};\n*/\n"], "mappings": "AAAA;AACA;AAEA,OAAO,MAAMA,cAAc,GAAG;EAC5BC,MAAM,EAAE,0BAA0B;EAClCC,UAAU,EAAE,+BAA+B;EAC3CC,SAAS,EAAE,eAAe;EAC1BC,aAAa,EAAE,2BAA2B;EAC1CC,iBAAiB,EAAE,4BAA4B;EAC/CC,KAAK,EAAE,yBAAyB,CAAC;CAClC;AAED;AACA;AACA;AACA;AAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}