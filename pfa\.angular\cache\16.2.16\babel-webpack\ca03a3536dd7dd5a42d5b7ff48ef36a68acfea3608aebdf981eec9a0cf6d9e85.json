{"ast": null, "code": "import { map, take } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    return this.authService.isAuthenticated$.pipe(take(1), map(isAuthenticated => {\n      if (isAuthenticated) {\n        return true;\n      } else {\n        // Rediriger vers la page de connexion si non authentifié\n        this.router.navigate(['/login'], {\n          queryParams: {\n            returnUrl: state.url\n          }\n        });\n        return false;\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport class AdminGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    return this.authService.userProfile$.pipe(take(1), map(userProfile => {\n      if (userProfile && userProfile.role === 'admin') {\n        return true;\n      } else {\n        // Rediriger vers la page d'accueil si pas admin\n        this.router.navigate(['/']);\n        return false;\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function AdminGuard_Factory(t) {\n      return new (t || AdminGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AdminGuard,\n      factory: AdminGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\nexport class GuestGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    return this.authService.isAuthenticated$.pipe(take(1), map(isAuthenticated => {\n      if (!isAuthenticated) {\n        return true;\n      } else {\n        // Rediriger vers le dashboard si déjà connecté\n        const userProfile = this.authService.getCurrentUserProfile();\n        if (userProfile?.role === 'admin') {\n          this.router.navigate(['/dashboard']);\n        } else {\n          this.router.navigate(['/personality-test']);\n        }\n        return false;\n      }\n    }));\n  }\n  static {\n    this.ɵfac = function GuestGuard_Factory(t) {\n      return new (t || GuestGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GuestGuard,\n      factory: GuestGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["map", "take", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "isAuthenticated$", "pipe", "isAuthenticated", "navigate", "queryParams", "returnUrl", "url", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "factory", "ɵfac", "providedIn", "<PERSON><PERSON><PERSON><PERSON>", "userProfile$", "userProfile", "role", "<PERSON><PERSON><PERSON>", "getCurrentUserProfile"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';\nimport { Observable, map, take } from 'rxjs';\nimport { AuthService } from '../services/auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    \n    return this.authService.isAuthenticated$.pipe(\n      take(1),\n      map(isAuthenticated => {\n        if (isAuthenticated) {\n          return true;\n        } else {\n          // Rediriger vers la page de connexion si non authentifié\n          this.router.navigate(['/login'], { \n            queryParams: { returnUrl: state.url } \n          });\n          return false;\n        }\n      })\n    );\n  }\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AdminGuard implements CanActivate {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    \n    return this.authService.userProfile$.pipe(\n      take(1),\n      map(userProfile => {\n        if (userProfile && userProfile.role === 'admin') {\n          return true;\n        } else {\n          // Rediriger vers la page d'accueil si pas admin\n          this.router.navigate(['/']);\n          return false;\n        }\n      })\n    );\n  }\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class GuestGuard implements CanActivate {\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  canActivate(\n    route: ActivatedRouteSnapshot,\n    state: RouterStateSnapshot\n  ): Observable<boolean> | Promise<boolean> | boolean {\n    \n    return this.authService.isAuthenticated$.pipe(\n      take(1),\n      map(isAuthenticated => {\n        if (!isAuthenticated) {\n          return true;\n        } else {\n          // Rediriger vers le dashboard si déjà connecté\n          const userProfile = this.authService.getCurrentUserProfile();\n          if (userProfile?.role === 'admin') {\n            this.router.navigate(['/dashboard']);\n          } else {\n            this.router.navigate(['/personality-test']);\n          }\n          return false;\n        }\n      })\n    );\n  }\n}\n"], "mappings": "AAEA,SAAqBA,GAAG,EAAEC,IAAI,QAAQ,MAAM;;;;AAM5C,OAAM,MAAOC,SAAS;EAEpBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAG1B,OAAO,IAAI,CAACJ,WAAW,CAACK,gBAAgB,CAACC,IAAI,CAC3CT,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACW,eAAe,IAAG;MACpB,IAAIA,eAAe,EAAE;QACnB,OAAO,IAAI;OACZ,MAAM;QACL;QACA,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE;UAC/BC,WAAW,EAAE;YAAEC,SAAS,EAAEN,KAAK,CAACO;UAAG;SACpC,CAAC;QACF,OAAO,KAAK;;IAEhB,CAAC,CAAC,CACH;EACH;;;uBA1BWb,SAAS,EAAAc,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATnB,SAAS;MAAAoB,OAAA,EAATpB,SAAS,CAAAqB,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA;;AAkCpB,OAAM,MAAOC,UAAU;EAErBtB,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAG1B,OAAO,IAAI,CAACJ,WAAW,CAACsB,YAAY,CAAChB,IAAI,CACvCT,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAAC2B,WAAW,IAAG;MAChB,IAAIA,WAAW,IAAIA,WAAW,CAACC,IAAI,KAAK,OAAO,EAAE;QAC/C,OAAO,IAAI;OACZ,MAAM;QACL;QACA,IAAI,CAACvB,MAAM,CAACO,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC3B,OAAO,KAAK;;IAEhB,CAAC,CAAC,CACH;EACH;;;uBAxBWa,UAAU,EAAAT,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAVI,UAAU;MAAAH,OAAA,EAAVG,UAAU,CAAAF,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA;;AAgCpB,OAAM,MAAOK,UAAU;EAErB1B,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CACTC,KAA6B,EAC7BC,KAA0B;IAG1B,OAAO,IAAI,CAACJ,WAAW,CAACK,gBAAgB,CAACC,IAAI,CAC3CT,IAAI,CAAC,CAAC,CAAC,EACPD,GAAG,CAACW,eAAe,IAAG;MACpB,IAAI,CAACA,eAAe,EAAE;QACpB,OAAO,IAAI;OACZ,MAAM;QACL;QACA,MAAMgB,WAAW,GAAG,IAAI,CAACvB,WAAW,CAAC0B,qBAAqB,EAAE;QAC5D,IAAIH,WAAW,EAAEC,IAAI,KAAK,OAAO,EAAE;UACjC,IAAI,CAACvB,MAAM,CAACO,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;SACrC,MAAM;UACL,IAAI,CAACP,MAAM,CAACO,QAAQ,CAAC,CAAC,mBAAmB,CAAC,CAAC;;QAE7C,OAAO,KAAK;;IAEhB,CAAC,CAAC,CACH;EACH;;;uBA7BWiB,UAAU,EAAAb,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAVQ,UAAU;MAAAP,OAAA,EAAVO,UAAU,CAAAN,IAAA;MAAAC,UAAA,EAFT;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}