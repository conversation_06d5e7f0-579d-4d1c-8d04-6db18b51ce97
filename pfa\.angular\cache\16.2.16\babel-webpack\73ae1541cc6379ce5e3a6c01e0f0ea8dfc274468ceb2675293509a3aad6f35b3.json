{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class Iris2Component {\n  static {\n    this.ɵfac = function Iris2Component_Factory(t) {\n      return new (t || Iris2Component)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Iris2Component,\n      selectors: [[\"app-iris2\"]],\n      decls: 17,\n      vars: 0,\n      consts: [[1, \"iris-grid\"], [1, \"iris-card\"], [\"src\", \"assets/1.png\", \"alt\", \"Fleur\"], [1, \"iris-name\"], [\"src\", \"assets/2.png\", \"alt\", \"Bijou\"], [\"src\", \"assets/3.png\", \"alt\", \"Flux\"], [\"src\", \"assets/4.png\", \"alt\", \"Shaker\"]],\n      template: function Iris2Component_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementStart(3, \"span\", 3);\n          i0.ɵɵtext(4, \"Fleur\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 1);\n          i0.ɵɵelement(6, \"img\", 4);\n          i0.ɵɵelementStart(7, \"span\", 3);\n          i0.ɵɵtext(8, \"Bijou\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 1);\n          i0.ɵɵelement(10, \"img\", 5);\n          i0.ɵɵelementStart(11, \"span\", 3);\n          i0.ɵɵtext(12, \"Flux\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 1);\n          i0.ɵɵelement(14, \"img\", 6);\n          i0.ɵɵelementStart(15, \"span\", 3);\n          i0.ɵɵtext(16, \"Shaker\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      styles: [\".iris-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  gap: 50px;\\n  padding: 40px;\\n  background: linear-gradient(to right, #f8e8ff, #f6f1ff);\\n  flex-wrap: wrap;\\n}\\n\\n.iris-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 160px;\\n  transition: transform 0.3s;\\n}\\n.iris-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n.iris-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 130px;\\n  height: 130px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\\n  transition: transform 0.3s;\\n}\\n.iris-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.iris-card[_ngcontent-%COMP%]   .iris-name[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #5e548e;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  letter-spacing: 0.5px;\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvaXJpczIvaXJpczIuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSx1QkFBQTtFQUNBLFNBQUE7RUFDQSxhQUFBO0VBQ0EsdURBQUE7RUFDQSxlQUFBO0FBQ0Y7O0FBRUE7RUFDRSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLFlBQUE7RUFDQSwwQkFBQTtBQUNGO0FBQ0U7RUFDRSwyQkFBQTtBQUNKO0FBRUU7RUFDRSxZQUFBO0VBQ0EsYUFBQTtFQUNBLGlCQUFBO0VBQ0Esa0JBQUE7RUFDQSx5Q0FBQTtFQUNBLDBCQUFBO0FBQUo7QUFFSTtFQUNFLHNCQUFBO0FBQU47QUFJRTtFQUNFLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxnQkFBQTtFQUNBLGNBQUE7RUFDQSxrQ0FBQTtFQUNBLHFCQUFBO0VBQ0Esa0JBQUE7QUFGSiIsInNvdXJjZXNDb250ZW50IjpbIi5pcmlzLWdyaWQge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XHJcbiAgZ2FwOiA1MHB4O1xyXG4gIHBhZGRpbmc6IDQwcHg7XHJcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCAjZjhlOGZmLCAjZjZmMWZmKTtcclxuICBmbGV4LXdyYXA6IHdyYXA7IC8vIFBlcm1ldCBkZSBzw6LCgMKZYWRhcHRlciBhdXggcGV0aXRzIMODwqljcmFuc1xyXG59XHJcblxyXG4uaXJpcy1jYXJkIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICB3aWR0aDogMTYwcHg7XHJcbiAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3M7XHJcblxyXG4gICY6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01cHgpO1xyXG4gIH1cclxuXHJcbiAgaW1nIHtcclxuICAgIHdpZHRoOiAxMzBweDtcclxuICAgIGhlaWdodDogMTMwcHg7XHJcbiAgICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgIGJveC1zaGFkb3c6IDAgNXB4IDE1cHggcmdiYSgwLCAwLCAwLCAwLjIpO1xyXG4gICAgdHJhbnNpdGlvbjogdHJhbnNmb3JtIDAuM3M7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuaXJpcy1uYW1lIHtcclxuICAgIG1hcmdpbi10b3A6IDEycHg7XHJcbiAgICBmb250LXNpemU6IDEuMXJlbTtcclxuICAgIGZvbnQtd2VpZ2h0OiA2MDA7XHJcbiAgICBjb2xvcjogIzVlNTQ4ZTtcclxuICAgIGZvbnQtZmFtaWx5OiAnUG9wcGlucycsIHNhbnMtc2VyaWY7XHJcbiAgICBsZXR0ZXItc3BhY2luZzogMC41cHg7XHJcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Iris2Component", "selectors", "decls", "vars", "consts", "template", "Iris2Component_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris2\\iris2.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris2\\iris2.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-iris2',\n  templateUrl: './iris2.component.html',\n  styleUrls: ['./iris2.component.scss']\n})\nexport class Iris2Component {\n\n}\n", "<div class=\"iris-grid\">\n    <div class=\"iris-card\">\n      <img src=\"assets/1.png\" alt=\"Fleur\" />\n      <span class=\"iris-name\">Fleur</span>\n    </div>\n    <div class=\"iris-card\">\n      <img src=\"assets/2.png\" alt=\"Bijou\" />\n      <span class=\"iris-name\">Bijou</span>\n    </div>\n    <div class=\"iris-card\">\n      <img src=\"assets/3.png\" alt=\"Flux\" />\n      <span class=\"iris-name\">Flux</span>\n    </div>\n    <div class=\"iris-card\">\n      <img src=\"assets/4.png\" alt=\"Shaker\" />\n      <span class=\"iris-name\">Shaker</span>\n    </div>\n  </div>\n  "], "mappings": ";AAOA,OAAM,MAAOA,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3BE,EAAA,CAAAC,cAAA,aAAuB;UAEjBD,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAEtCJ,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAEtCJ,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,SAAA,cAAqC;UACrCF,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAErCJ,EAAA,CAAAC,cAAA,cAAuB;UACrBD,EAAA,CAAAE,SAAA,cAAuC;UACvCF,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}