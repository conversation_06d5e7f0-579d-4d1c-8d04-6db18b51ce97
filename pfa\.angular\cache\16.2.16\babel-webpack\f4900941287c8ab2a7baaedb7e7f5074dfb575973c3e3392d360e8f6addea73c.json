{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class SuivantaccComponent {\n  static {\n    this.ɵfac = function SuivantaccComponent_Factory(t) {\n      return new (t || SuivantaccComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuivantaccComponent,\n      selectors: [[\"app-suivantacc\"]],\n      decls: 53,\n      vars: 0,\n      consts: [[1, \"page-container\", \"suivantacc\"], [1, \"container\"], [1, \"header\"], [1, \"logo\"], [1, \"nav\"], [\"routerLink\", \"/accueil\"], [1, \"content\"], [1, \"card\", \"main-card\"], [1, \"card-inner\"], [1, \"card-front\"], [1, \"image-container\"], [\"src\", \"assets/iris3.png\", \"alt\", \"Iris\", 1, \"main-image\"], [1, \"image-decoration\"], [1, \"text-content\"], [1, \"title\"], [1, \"divider\"], [1, \"intro\"], [1, \"description\"], [1, \"action-container\"], [\"routerLink\", \"/typeiris\", 1, \"btn\", \"next-btn\"], [1, \"icon\"], [1, \"features\"], [1, \"feature-card\"]],\n      template: function SuivantaccComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"header\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Iris\");\n          i0.ɵɵelementStart(5, \"span\");\n          i0.ɵɵtext(6, \"Lock\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"nav\", 4)(8, \"a\", 5);\n          i0.ɵɵtext(9, \"Accueil\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"div\", 7)(12, \"div\", 8)(13, \"div\", 9)(14, \"div\", 10);\n          i0.ɵɵelement(15, \"img\", 11)(16, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 13)(18, \"h2\", 14);\n          i0.ɵɵtext(19, \"D\\u00E9couvrir l'unicit\\u00E9 de chacun\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"div\", 15);\n          i0.ɵɵelementStart(21, \"p\", 16);\n          i0.ɵɵtext(22, \" L'iris est une structure biom\\u00E9trique complexe et unique \\u00E0 chaque individu. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 17);\n          i0.ɵɵtext(24, \" Ses motifs, distincts et inimitables, peuvent fournir des informations pr\\u00E9cieuses sur les caract\\u00E9ristiques physiologiques, psychologiques et comportementales d'une personne. L'analyse de la structure irienne permet d'identifier des traits de personnalit\\u00E9, des pr\\u00E9dispositions h\\u00E9r\\u00E9ditaires, ainsi que d'\\u00E9ventuelles implications sur la sant\\u00E9 et les relations interpersonnelles. \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(25, \"div\", 18)(26, \"a\", 19)(27, \"span\");\n          i0.ɵɵtext(28, \"Suivant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"span\", 20);\n          i0.ɵɵtext(30, \"\\u2192\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(31, \"div\", 21)(32, \"div\", 22)(33, \"div\", 20);\n          i0.ɵɵtext(34, \"\\uD83D\\uDC41\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"h3\");\n          i0.ɵɵtext(36, \"Structure Unique\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"p\");\n          i0.ɵɵtext(38, \"Chaque iris poss\\u00E8de une structure aussi unique qu'une empreinte digitale\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 22)(40, \"div\", 20);\n          i0.ɵɵtext(41, \"\\uD83E\\uDDE0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"h3\");\n          i0.ɵɵtext(43, \"Reflet de la Personnalit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"p\");\n          i0.ɵɵtext(45, \"Les motifs de l'iris r\\u00E9v\\u00E8lent des aspects profonds de notre personnalit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 22)(47, \"div\", 20);\n          i0.ɵɵtext(48, \"\\uD83D\\uDD04\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"h3\");\n          i0.ɵɵtext(50, \"\\u00C9volution Continue\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"p\");\n          i0.ɵɵtext(52, \"Les caract\\u00E9ristiques \\u00E9voluent selon notre parcours de vie et nos habitudes\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".page-container.suivantacc[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #e6f0ff 0%, #e0e6ff 100%);\\n  padding: 20px 0;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 0;\\n  margin-bottom: 40px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  font-style: italic;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #555;\\n  text-decoration: none;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background-color: var(--bijou-primary);\\n  transition: width 0.3s ease;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: var(--bijou-primary);\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:after {\\n  width: 100%;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-bottom: 60px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 1000px;\\n  margin-bottom: 40px;\\n  perspective: 1000px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  transform-style: preserve-3d;\\n  transition: transform 0.6s;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%] {\\n  width: 100%;\\n  backface-visibility: hidden;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  padding: 40px;\\n  gap: 40px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\\n  z-index: 2;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 330px;\\n  height: 330px;\\n  border-radius: 50%;\\n  border: 2px dashed var(--bijou-secondary);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_rotate 20s linear infinite;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2.2rem;\\n  color: #333;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--bijou-primary), var(--flux-primary));\\n  margin-bottom: 20px;\\n  border-radius: 3px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .intro[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 500;\\n  color: #555;\\n  margin-bottom: 15px;\\n  font-style: italic;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  line-height: 1.7;\\n  color: #666;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 15px 35px;\\n  background: linear-gradient(to right, var(--bijou-primary), var(--flux-primary));\\n  color: white;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n  box-shadow: 0 10px 25px rgba(79, 138, 255, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(79, 138, 255, 0.4);\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 30px;\\n  margin-top: 60px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  margin-bottom: 20px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  color: #333;\\n  margin-bottom: 15px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  color: #666;\\n  line-height: 1.6;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n    padding: 30px;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%] {\\n    margin-bottom: 30px;\\n    text-align: center;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n    margin: 0 auto 20px;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n    margin-bottom: 30px;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    width: 250px;\\n    height: 250px;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n    width: 280px;\\n    height: 280px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n    text-align: center;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .intro[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    width: 200px;\\n    height: 200px;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n    width: 230px;\\n    height: 230px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SuivantaccComponent", "selectors", "decls", "vars", "consts", "template", "SuivantaccComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\suivantacc\\suivantacc.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\suivantacc\\suivantacc.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-suivantacc',\n  templateUrl: './suivantacc.component.html',\n  styleUrls: ['./suivantacc.component.scss']\n})\nexport class SuivantaccComponent {\n\n}\n", "<div class=\"page-container suivantacc\">\n  <div class=\"container\">\n    <header class=\"header\">\n      <h1 class=\"logo\">Iris<span>Lock</span></h1>\n      <nav class=\"nav\">\n        <a routerLink=\"/accueil\">Accueil</a>\n      </nav>\n    </header>\n\n    <div class=\"content\">\n      <div class=\"card main-card\">\n        <div class=\"card-inner\">\n          <div class=\"card-front\">\n            <div class=\"image-container\">\n              <img src=\"assets/iris3.png\" alt=\"Iris\" class=\"main-image\" />\n              <div class=\"image-decoration\"></div>\n            </div>\n            <div class=\"text-content\">\n              <h2 class=\"title\">Découvrir l'unicité de chacun</h2>\n              <div class=\"divider\"></div>\n              <p class=\"intro\">\n                L'iris est une structure biométrique complexe et unique à chaque individu.\n              </p>\n              <p class=\"description\">\n                Ses motifs, distincts et inimitables, peuvent fournir des informations précieuses\n                sur les caractéristiques physiologiques, psychologiques et comportementales d'une personne.\n                L'analyse de la structure irienne permet d'identifier des traits de personnalité,\n                des prédispositions héréditaires, ainsi que d'éventuelles implications sur la santé\n                et les relations interpersonnelles.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"action-container\">\n        <a routerLink=\"/typeiris\" class=\"btn next-btn\">\n          <span>Suivant</span>\n          <span class=\"icon\">→</span>\n        </a>\n      </div>\n    </div>\n\n    <div class=\"features\">\n      <div class=\"feature-card\">\n        <div class=\"icon\">👁️</div>\n        <h3>Structure Unique</h3>\n        <p>Chaque iris possède une structure aussi unique qu'une empreinte digitale</p>\n      </div>\n      <div class=\"feature-card\">\n        <div class=\"icon\">🧠</div>\n        <h3>Reflet de la Personnalité</h3>\n        <p>Les motifs de l'iris révèlent des aspects profonds de notre personnalité</p>\n      </div>\n      <div class=\"feature-card\">\n        <div class=\"icon\">🔄</div>\n        <h3>Évolution Continue</h3>\n        <p>Les caractéristiques évoluent selon notre parcours de vie et nos habitudes</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPhCE,EAAA,CAAAC,cAAA,aAAuC;UAGhBD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtCH,EAAA,CAAAC,cAAA,aAAiB;UACUD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIxCH,EAAA,CAAAC,cAAA,cAAqB;UAKXD,EAAA,CAAAI,SAAA,eAA4D;UAE9DJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAA0B;UACND,EAAA,CAAAE,MAAA,+CAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpDH,EAAA,CAAAI,SAAA,eAA2B;UAC3BJ,EAAA,CAAAC,cAAA,aAAiB;UACfD,EAAA,CAAAE,MAAA,8FACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,MAAA,yaAKF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMZH,EAAA,CAAAC,cAAA,eAA8B;UAEpBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpBH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKjCH,EAAA,CAAAC,cAAA,eAAsB;UAEAD,EAAA,CAAAE,MAAA,0BAAG;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC3BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qFAAwE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjFH,EAAA,CAAAC,cAAA,eAA0B;UACND,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,sCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,+FAAwE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjFH,EAAA,CAAAC,cAAA,eAA0B;UACND,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,+BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,4FAA0E;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}