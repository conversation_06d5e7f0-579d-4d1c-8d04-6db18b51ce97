{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction LoginComponent_div_20_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"L'email est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_20_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Veuillez entrer un email valide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, LoginComponent_div_20_span_1_Template, 2, 0, \"span\", 28);\n    i0.ɵɵtemplate(2, LoginComponent_div_20_span_2_Template, 2, 0, \"span\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r1 = i0.ɵɵreference(19);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"email\"]);\n  }\n}\nfunction LoginComponent_div_31_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le mot de passe est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_31_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le mot de passe doit contenir au moins 6 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtemplate(1, LoginComponent_div_31_span_1_Template, 2, 0, \"span\", 28);\n    i0.ɵɵtemplate(2, LoginComponent_div_31_span_2_Template, 2, 0, \"span\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(28);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r3.errors == null ? null : _r3.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r3.errors == null ? null : _r3.errors[\"minlength\"]);\n  }\n}\nfunction LoginComponent_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Connexion en cours...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(router) {\n    this.router = router;\n    this.loginData = {\n      email: '',\n      password: '',\n      rememberMe: false\n    };\n    this.showPassword = false;\n    this.isLoading = false;\n    // Comptes statiques pour les tests\n    this.staticAccounts = [{\n      email: '<EMAIL>',\n      password: 'admin123',\n      name: 'Administrateur Test',\n      role: 'admin'\n    }, {\n      email: '<EMAIL>',\n      password: 'user123',\n      name: 'Utilisateur Test',\n      role: 'user'\n    }, {\n      email: '<EMAIL>',\n      password: 'marie123',\n      name: 'Marie Dubois',\n      role: 'user'\n    }, {\n      email: '<EMAIL>',\n      password: 'jean123',\n      name: 'Jean Martin',\n      role: 'user'\n    }];\n  }\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  onSubmit() {\n    if (this.isLoading) return;\n    this.isLoading = true;\n    // Simuler une connexion avec un délai\n    setTimeout(() => {\n      console.log('Tentative de connexion avec:', this.loginData);\n      // Ici, vous implémenteriez la logique réelle de connexion\n      // Pour l'instant, nous simulons simplement une connexion réussie\n      // Rediriger vers le tableau de bord après connexion\n      this.router.navigate(['/dashboard']);\n      this.isLoading = false;\n    }, 1500);\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 65,\n      vars: 10,\n      consts: [[1, \"auth-container\", \"login\"], [1, \"container\"], [1, \"auth-card\"], [1, \"auth-header\"], [1, \"title\"], [1, \"subtitle\"], [1, \"divider\"], [1, \"auth-form\"], [3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"email\"], [1, \"input-container\"], [1, \"input-icon\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"required\", \"\", \"email\", \"\", \"placeholder\", \"Votre adresse email\", 3, \"ngModel\", \"ngModelChange\"], [\"email\", \"ngModel\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"for\", \"password\"], [\"id\", \"password\", \"name\", \"password\", \"required\", \"\", \"minlength\", \"6\", \"placeholder\", \"Votre mot de passe\", 3, \"type\", \"ngModel\", \"ngModelChange\"], [\"password\", \"ngModel\"], [\"type\", \"button\", 1, \"toggle-password\", 3, \"click\"], [1, \"form-options\"], [1, \"remember-me\"], [\"type\", \"checkbox\", \"id\", \"remember\", \"name\", \"remember\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"remember\"], [\"href\", \"#\", 1, \"forgot-password\"], [1, \"form-actions\"], [\"type\", \"submit\", 1, \"submit-btn\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"social-login\"], [1, \"separator\"], [1, \"social-buttons\"], [1, \"social-btn\", \"google\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google\"], [1, \"social-btn\", \"facebook\"], [1, \"facebook-icon\"], [1, \"auth-footer\"], [\"routerLink\", \"/signup\"], [1, \"navigation\"], [\"routerLink\", \"/accueil\", 1, \"back-btn\"], [1, \"icon\"], [1, \"error-message\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n          i0.ɵɵtext(5, \"Connexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \"Acc\\u00E9dez \\u00E0 votre compte IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(8, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"form\", 8, 9);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"label\", 11);\n          i0.ɵɵtext(14, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"span\", 13);\n          i0.ɵɵtext(17, \"\\u2709\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 14, 15);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.loginData.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(20, LoginComponent_div_20_Template, 3, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"label\", 17);\n          i0.ɵɵtext(23, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"span\", 13);\n          i0.ɵɵtext(26, \"\\uD83D\\uDD12\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 18, 19);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_27_listener($event) {\n            return ctx.loginData.password = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_29_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵtext(30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, LoginComponent_div_31_Template, 3, 2, \"div\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 21)(33, \"div\", 22)(34, \"input\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_34_listener($event) {\n            return ctx.loginData.rememberMe = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"label\", 24);\n          i0.ɵɵtext(36, \"Se souvenir de moi\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"a\", 25);\n          i0.ɵɵtext(38, \"Mot de passe oubli\\u00E9?\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 26)(40, \"button\", 27);\n          i0.ɵɵtemplate(41, LoginComponent_span_41_Template, 2, 0, \"span\", 28);\n          i0.ɵɵtemplate(42, LoginComponent_span_42_Template, 2, 0, \"span\", 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"div\", 29)(44, \"p\", 30);\n          i0.ɵɵtext(45, \"Ou connectez-vous avec\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 31)(47, \"button\", 32);\n          i0.ɵɵelement(48, \"img\", 33);\n          i0.ɵɵtext(49, \" Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"button\", 34)(51, \"span\", 35);\n          i0.ɵɵtext(52, \"f\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" Facebook \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 36)(55, \"p\");\n          i0.ɵɵtext(56, \"Vous n'avez pas de compte? \");\n          i0.ɵɵelementStart(57, \"a\", 37);\n          i0.ɵɵtext(58, \"Inscrivez-vous\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(59, \"div\", 38)(60, \"a\", 39)(61, \"span\", 40);\n          i0.ɵɵtext(62, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"span\");\n          i0.ɵɵtext(64, \"Retour \\u00E0 l'accueil\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(11);\n          const _r1 = i0.ɵɵreference(19);\n          const _r3 = i0.ɵɵreference(28);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngModel\", ctx.loginData.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && (_r1.dirty || _r1.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\")(\"ngModel\", ctx.loginData.password);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" \", ctx.showPassword ? \"\\uD83D\\uDC41\\uFE0F\" : \"\\uD83D\\uDC41\\uFE0F\\u200D\\uD83D\\uDDE8\\uFE0F\", \" \");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r3.invalid && (_r3.dirty || _r3.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.loginData.rememberMe);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", _r0.invalid || ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i2.NgIf, i1.RouterLink, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MinLengthValidator, i3.EmailValidator, i3.NgModel, i3.NgForm],\n      styles: [\".auth-container.login[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\\n  padding: 40px 0;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 20px;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  max-width: 500px;\\n  margin: 0 auto 40px;\\n  padding: 40px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2.2rem;\\n  color: #333;\\n  margin-bottom: 10px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1rem;\\n  margin-bottom: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  margin: 0 auto;\\n  border-radius: 3px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  color: #555;\\n  margin-bottom: 8px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 15px;\\n  font-size: 1.1rem;\\n  color: #aaa;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 15px 12px 45px;\\n  border: 1px solid #ddd;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--fleur-primary);\\n  box-shadow: 0 0 0 3px rgba(138, 79, 255, 0.1);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .toggle-password[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  background: none;\\n  border: none;\\n  font-size: 1.1rem;\\n  cursor: pointer;\\n  color: #aaa;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .input-container[_ngcontent-%COMP%]   .toggle-password[_ngcontent-%COMP%]:hover {\\n  color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #e74c3c;\\n  margin-top: 5px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 3px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 25px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .remember-me[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n  cursor: pointer;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: var(--fleur-primary);\\n  text-decoration: none;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%]   .forgot-password[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 25px rgba(138, 79, 255, 0.4);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  cursor: not-allowed;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%] {\\n  text-align: center;\\n  position: relative;\\n  color: #999;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:before, .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 40%;\\n  height: 1px;\\n  background-color: #ddd;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:before {\\n  left: 0;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .separator[_ngcontent-%COMP%]:after {\\n  right: 0;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 10px;\\n  padding: 12px;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  border: 1px solid #ddd;\\n  background-color: white;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]   .facebook-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  background-color: #1877f2;\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn.google[_ngcontent-%COMP%]:hover {\\n  border-color: #ea4335;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%]   .social-btn.facebook[_ngcontent-%COMP%]:hover {\\n  border-color: #1877f2;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--fleur-primary);\\n  text-decoration: none;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .auth-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 12px 25px;\\n  background-color: white;\\n  color: #555;\\n  border-radius: 50px;\\n  font-weight: 500;\\n  text-decoration: none;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease;\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\\n  color: var(--fleur-primary);\\n}\\n.auth-container.login[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n@media (max-width: 576px) {\\n  .auth-container.login[_ngcontent-%COMP%] {\\n    padding: 20px 0;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%] {\\n    padding: 25px;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .form-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 15px;\\n  }\\n  .auth-container.login[_ngcontent-%COMP%]   .auth-card[_ngcontent-%COMP%]   .auth-form[_ngcontent-%COMP%]   .social-login[_ngcontent-%COMP%]   .social-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "LoginComponent_div_20_span_1_Template", "LoginComponent_div_20_span_2_Template", "ɵɵadvance", "ɵɵproperty", "_r1", "errors", "LoginComponent_div_31_span_1_Template", "LoginComponent_div_31_span_2_Template", "_r3", "LoginComponent", "constructor", "router", "loginData", "email", "password", "rememberMe", "showPassword", "isLoading", "staticAccounts", "name", "role", "togglePasswordVisibility", "onSubmit", "setTimeout", "console", "log", "navigate", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_10_listener", "LoginComponent_Template_input_ngModelChange_18_listener", "$event", "LoginComponent_div_20_Template", "LoginComponent_Template_input_ngModelChange_27_listener", "LoginComponent_Template_button_click_29_listener", "LoginComponent_div_31_Template", "LoginComponent_Template_input_ngModelChange_34_listener", "LoginComponent_span_41_Template", "LoginComponent_span_42_Template", "invalid", "dirty", "touched", "ɵɵtextInterpolate1", "_r0"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\login\\login.component.ts", "D:\\ProjetPfa\\pfa\\pfa\\src\\app\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\ninterface LoginData {\n  email: string;\n  password: string;\n  rememberMe: boolean;\n}\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.scss']\n})\nexport class LoginComponent {\n  loginData: LoginData = {\n    email: '',\n    password: '',\n    rememberMe: false\n  };\n\n  showPassword: boolean = false;\n  isLoading: boolean = false;\n\n  // Comptes statiques pour les tests\n  staticAccounts = [\n    {\n      email: '<EMAIL>',\n      password: 'admin123',\n      name: 'Administrateur Test',\n      role: 'admin'\n    },\n    {\n      email: '<EMAIL>',\n      password: 'user123',\n      name: 'Utilisateur Test',\n      role: 'user'\n    },\n    {\n      email: '<EMAIL>',\n      password: 'marie123',\n      name: '<PERSON>',\n      role: 'user'\n    },\n    {\n      email: '<EMAIL>',\n      password: 'jean123',\n      name: '<PERSON>',\n      role: 'user'\n    }\n  ];\n\n  constructor(private router: Router) {}\n\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  onSubmit(): void {\n    if (this.isLoading) return;\n\n    this.isLoading = true;\n\n    // Simuler une connexion avec un délai\n    setTimeout(() => {\n      console.log('Tentative de connexion avec:', this.loginData);\n\n      // Ici, vous implémenteriez la logique réelle de connexion\n      // Pour l'instant, nous simulons simplement une connexion réussie\n\n      // Rediriger vers le tableau de bord après connexion\n      this.router.navigate(['/dashboard']);\n\n      this.isLoading = false;\n    }, 1500);\n  }\n}\n", "<div class=\"auth-container login\">\n  <div class=\"container\">\n    <div class=\"auth-card\">\n      <div class=\"auth-header\">\n        <h1 class=\"title\">Connexion</h1>\n        <p class=\"subtitle\">Accédez à votre compte IrisLock</p>\n        <div class=\"divider\"></div>\n      </div>\n\n      <div class=\"auth-form\">\n        <form (ngSubmit)=\"onSubmit()\" #loginForm=\"ngForm\">\n          <div class=\"form-group\">\n            <label for=\"email\">Email</label>\n            <div class=\"input-container\">\n              <span class=\"input-icon\">✉️</span>\n              <input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                [(ngModel)]=\"loginData.email\"\n                required\n                email\n                #email=\"ngModel\"\n                placeholder=\"Votre adresse email\"\n              >\n            </div>\n            <div class=\"error-message\" *ngIf=\"email.invalid && (email.dirty || email.touched)\">\n              <span *ngIf=\"email.errors?.['required']\">L'email est requis</span>\n              <span *ngIf=\"email.errors?.['email']\">Veuillez entrer un email valide</span>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <label for=\"password\">Mot de passe</label>\n            <div class=\"input-container\">\n              <span class=\"input-icon\">🔒</span>\n              <input\n                [type]=\"showPassword ? 'text' : 'password'\"\n                id=\"password\"\n                name=\"password\"\n                [(ngModel)]=\"loginData.password\"\n                required\n                minlength=\"6\"\n                #password=\"ngModel\"\n                placeholder=\"Votre mot de passe\"\n              >\n              <button\n                type=\"button\"\n                class=\"toggle-password\"\n                (click)=\"togglePasswordVisibility()\"\n              >\n                {{ showPassword ? '👁️' : '👁️‍🗨️' }}\n              </button>\n            </div>\n            <div class=\"error-message\" *ngIf=\"password.invalid && (password.dirty || password.touched)\">\n              <span *ngIf=\"password.errors?.['required']\">Le mot de passe est requis</span>\n              <span *ngIf=\"password.errors?.['minlength']\">Le mot de passe doit contenir au moins 6 caractères</span>\n            </div>\n          </div>\n\n          <div class=\"form-options\">\n            <div class=\"remember-me\">\n              <input\n                type=\"checkbox\"\n                id=\"remember\"\n                name=\"remember\"\n                [(ngModel)]=\"loginData.rememberMe\"\n              >\n              <label for=\"remember\">Se souvenir de moi</label>\n            </div>\n            <a href=\"#\" class=\"forgot-password\">Mot de passe oublié?</a>\n          </div>\n\n          <div class=\"form-actions\">\n            <button\n              type=\"submit\"\n              class=\"submit-btn\"\n              [disabled]=\"loginForm.invalid || isLoading\"\n            >\n              <span *ngIf=\"!isLoading\">Se connecter</span>\n              <span *ngIf=\"isLoading\">Connexion en cours...</span>\n            </button>\n          </div>\n        </form>\n\n        <div class=\"social-login\">\n          <p class=\"separator\">Ou connectez-vous avec</p>\n          <div class=\"social-buttons\">\n            <button class=\"social-btn google\">\n              <img src=\"assets/google-icon.png\" alt=\"Google\">\n              Google\n            </button>\n            <button class=\"social-btn facebook\">\n              <span class=\"facebook-icon\">f</span>\n              Facebook\n            </button>\n          </div>\n        </div>\n\n        <div class=\"auth-footer\">\n          <p>Vous n'avez pas de compte? <a routerLink=\"/signup\">Inscrivez-vous</a></p>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"navigation\">\n      <a routerLink=\"/accueil\" class=\"back-btn\">\n        <span class=\"icon\">←</span>\n        <span>Retour à l'accueil</span>\n      </a>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;IC2BcA,EAAA,CAAAC,cAAA,WAAyC;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAClEH,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF9EH,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAI,UAAA,IAAAC,qCAAA,mBAAkE;IAClEL,EAAA,CAAAI,UAAA,IAAAE,qCAAA,mBAA4E;IAC9EN,EAAA,CAAAG,YAAA,EAAM;;;;;IAFGH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAgC;IAChCV,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,UAA6B;;;;;IA2BpCV,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC7EH,EAAA,CAAAC,cAAA,WAA6C;IAAAD,EAAA,CAAAE,MAAA,+DAAmD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFzGH,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAI,UAAA,IAAAO,qCAAA,mBAA6E;IAC7EX,EAAA,CAAAI,UAAA,IAAAQ,qCAAA,mBAAuG;IACzGZ,EAAA,CAAAG,YAAA,EAAM;;;;;IAFGH,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAAH,MAAA,kBAAAG,GAAA,CAAAH,MAAA,aAAmC;IACnCV,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAAH,MAAA,kBAAAG,GAAA,CAAAH,MAAA,cAAoC;;;;;IAuB3CV,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC5CH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADlElE,OAAM,MAAOW,cAAc;EAsCzBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IArC1B,KAAAC,SAAS,GAAc;MACrBC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE;KACb;IAED,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,SAAS,GAAY,KAAK;IAE1B;IACA,KAAAC,cAAc,GAAG,CACf;MACEL,KAAK,EAAE,gBAAgB;MACvBC,QAAQ,EAAE,UAAU;MACpBK,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE;KACP,EACD;MACEP,KAAK,EAAE,eAAe;MACtBC,QAAQ,EAAE,SAAS;MACnBK,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE;KACP,EACD;MACEP,KAAK,EAAE,uBAAuB;MAC9BC,QAAQ,EAAE,UAAU;MACpBK,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE;KACP,EACD;MACEP,KAAK,EAAE,sBAAsB;MAC7BC,QAAQ,EAAE,SAAS;MACnBK,IAAI,EAAE,aAAa;MACnBC,IAAI,EAAE;KACP,CACF;EAEoC;EAErCC,wBAAwBA,CAAA;IACtB,IAAI,CAACL,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAM,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,SAAS,EAAE;IAEpB,IAAI,CAACA,SAAS,GAAG,IAAI;IAErB;IACAM,UAAU,CAAC,MAAK;MACdC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAACb,SAAS,CAAC;MAE3D;MACA;MAEA;MACA,IAAI,CAACD,MAAM,CAACe,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MAEpC,IAAI,CAACT,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;;;uBA7DWR,cAAc,EAAAd,EAAA,CAAAgC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdpB,cAAc;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd3BzC,EAAA,CAAAC,cAAA,aAAkC;UAIRD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,WAAoB;UAAAD,EAAA,CAAAE,MAAA,gDAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvDH,EAAA,CAAA2C,SAAA,aAA2B;UAC7B3C,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAAuB;UACfD,EAAA,CAAA4C,UAAA,sBAAAC,kDAAA;YAAA,OAAYH,GAAA,CAAAf,QAAA,EAAU;UAAA,EAAC;UAC3B3B,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,eAA6B;UACFD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,qBASC;UALCD,EAAA,CAAA4C,UAAA,2BAAAE,wDAAAC,MAAA;YAAA,OAAAL,GAAA,CAAAzB,SAAA,CAAAC,KAAA,GAAA6B,MAAA;UAAA,EAA6B;UAJ/B/C,EAAA,CAAAG,YAAA,EASC;UAEHH,EAAA,CAAAI,UAAA,KAAA4C,8BAAA,kBAGM;UACRhD,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1CH,EAAA,CAAAC,cAAA,eAA6B;UACFD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClCH,EAAA,CAAAC,cAAA,qBASC;UALCD,EAAA,CAAA4C,UAAA,2BAAAK,wDAAAF,MAAA;YAAA,OAAAL,GAAA,CAAAzB,SAAA,CAAAE,QAAA,GAAA4B,MAAA;UAAA,EAAgC;UAJlC/C,EAAA,CAAAG,YAAA,EASC;UACDH,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAA4C,UAAA,mBAAAM,iDAAA;YAAA,OAASR,GAAA,CAAAhB,wBAAA,EAA0B;UAAA,EAAC;UAEpC1B,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEXH,EAAA,CAAAI,UAAA,KAAA+C,8BAAA,kBAGM;UACRnD,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAA0B;UAMpBD,EAAA,CAAA4C,UAAA,2BAAAQ,wDAAAL,MAAA;YAAA,OAAAL,GAAA,CAAAzB,SAAA,CAAAG,UAAA,GAAA2B,MAAA;UAAA,EAAkC;UAJpC/C,EAAA,CAAAG,YAAA,EAKC;UACDH,EAAA,CAAAC,cAAA,iBAAsB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAElDH,EAAA,CAAAC,cAAA,aAAoC;UAAAD,EAAA,CAAAE,MAAA,iCAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG9DH,EAAA,CAAAC,cAAA,eAA0B;UAMtBD,EAAA,CAAAI,UAAA,KAAAiD,+BAAA,mBAA4C;UAC5CrD,EAAA,CAAAI,UAAA,KAAAkD,+BAAA,mBAAoD;UACtDtD,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAA0B;UACHD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC/CH,EAAA,CAAAC,cAAA,eAA4B;UAExBD,EAAA,CAAA2C,SAAA,eAA+C;UAC/C3C,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAoC;UACND,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpCH,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,eAAyB;UACpBD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAC,cAAA,aAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK9EH,EAAA,CAAAC,cAAA,eAAwB;UAEDD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,+BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;UAzFvBH,EAAA,CAAAO,SAAA,IAA6B;UAA7BP,EAAA,CAAAQ,UAAA,YAAAkC,GAAA,CAAAzB,SAAA,CAAAC,KAAA,CAA6B;UAOLlB,EAAA,CAAAO,SAAA,GAAqD;UAArDP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAA8C,OAAA,KAAA9C,GAAA,CAAA+C,KAAA,IAAA/C,GAAA,CAAAgD,OAAA,EAAqD;UAW7EzD,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,UAAA,SAAAkC,GAAA,CAAArB,YAAA,uBAA2C,YAAAqB,GAAA,CAAAzB,SAAA,CAAAE,QAAA;UAc3CnB,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAA0D,kBAAA,MAAAhB,GAAA,CAAArB,YAAA,4EACF;UAE0BrB,EAAA,CAAAO,SAAA,GAA8D;UAA9DP,EAAA,CAAAQ,UAAA,SAAAK,GAAA,CAAA0C,OAAA,KAAA1C,GAAA,CAAA2C,KAAA,IAAA3C,GAAA,CAAA4C,OAAA,EAA8D;UAYtFzD,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,UAAA,YAAAkC,GAAA,CAAAzB,SAAA,CAAAG,UAAA,CAAkC;UAWpCpB,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAQ,UAAA,aAAAmD,GAAA,CAAAJ,OAAA,IAAAb,GAAA,CAAApB,SAAA,CAA2C;UAEpCtB,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAQ,UAAA,UAAAkC,GAAA,CAAApB,SAAA,CAAgB;UAChBtB,EAAA,CAAAO,SAAA,GAAe;UAAfP,EAAA,CAAAQ,UAAA,SAAAkC,GAAA,CAAApB,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}