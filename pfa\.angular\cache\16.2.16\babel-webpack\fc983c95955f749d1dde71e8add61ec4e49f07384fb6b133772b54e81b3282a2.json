{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class Iris2Component {\n  static {\n    this.ɵfac = function Iris2Component_Factory(t) {\n      return new (t || Iris2Component)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Iris2Component,\n      selectors: [[\"app-iris2\"]],\n      decls: 78,\n      vars: 0,\n      consts: [[1, \"iris-types-container\"], [1, \"container\"], [1, \"header\"], [1, \"title\"], [1, \"subtitle\"], [1, \"iris-grid\"], [1, \"iris-card\", \"fleur\"], [1, \"card-inner\"], [1, \"card-front\"], [1, \"image-container\"], [\"src\", \"assets/1.png\", \"alt\", \"Fleur\"], [1, \"iris-name\"], [1, \"iris-tagline\"], [1, \"card-back\"], [\"routerLink\", \"/fleur\", 1, \"btn\", \"discover-btn\"], [1, \"iris-card\", \"bijou\"], [\"src\", \"assets/2.png\", \"alt\", \"Bijou\"], [\"routerLink\", \"/bijou\", 1, \"btn\", \"discover-btn\"], [1, \"iris-card\", \"flux\"], [\"src\", \"assets/3.png\", \"alt\", \"Flux\"], [\"routerLink\", \"/flux\", 1, \"btn\", \"discover-btn\"], [1, \"iris-card\", \"shaker\"], [\"src\", \"assets/4.png\", \"alt\", \"Shaker\"], [\"routerLink\", \"/shaker\", 1, \"btn\", \"discover-btn\"], [1, \"navigation\"], [\"routerLink\", \"/typeiris\", 1, \"btn\", \"back-btn\"], [1, \"icon\"]],\n      template: function Iris2Component_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Les Types d'Iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"D\\u00E9couvrez les quatre profils fondamentaux et leurs caract\\u00E9ristiques\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6)(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9);\n          i0.ɵɵelement(12, \"img\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"h2\", 11);\n          i0.ɵɵtext(14, \"Fleur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\", 12);\n          i0.ɵɵtext(16, \"Le Sentimental\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 13)(18, \"h3\");\n          i0.ɵɵtext(19, \"Fleur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\");\n          i0.ɵɵtext(21, \"Profil ax\\u00E9 sur les \\u00E9motions et la cr\\u00E9ativit\\u00E9. Expressif, spontan\\u00E9 et artistique.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"a\", 14);\n          i0.ɵɵtext(23, \"D\\u00E9couvrir\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(24, \"div\", 15)(25, \"div\", 7)(26, \"div\", 8)(27, \"div\", 9);\n          i0.ɵɵelement(28, \"img\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"h2\", 11);\n          i0.ɵɵtext(30, \"Bijou\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"p\", 12);\n          i0.ɵɵtext(32, \"Le R\\u00E9fl\\u00E9chi\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 13)(34, \"h3\");\n          i0.ɵɵtext(35, \"Bijou\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"p\");\n          i0.ɵɵtext(37, \"Type analytique et mental. Observateur, pr\\u00E9cis et orient\\u00E9 vers la r\\u00E9flexion.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"a\", 17);\n          i0.ɵɵtext(39, \"D\\u00E9couvrir\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(40, \"div\", 18)(41, \"div\", 7)(42, \"div\", 8)(43, \"div\", 9);\n          i0.ɵɵelement(44, \"img\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"h2\", 11);\n          i0.ɵɵtext(46, \"Flux\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"p\", 12);\n          i0.ɵɵtext(48, \"Le Sensitif\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 13)(50, \"h3\");\n          i0.ɵɵtext(51, \"Flux\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"p\");\n          i0.ɵɵtext(53, \"Type intuitif, physique et empathique. Calme, pos\\u00E9 et attentionn\\u00E9.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"a\", 20);\n          i0.ɵɵtext(55, \"D\\u00E9couvrir\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(56, \"div\", 21)(57, \"div\", 7)(58, \"div\", 8)(59, \"div\", 9);\n          i0.ɵɵelement(60, \"img\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"h2\", 11);\n          i0.ɵɵtext(62, \"Shaker\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"p\", 12);\n          i0.ɵɵtext(64, \"Le Visionnaire\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"div\", 13)(66, \"h3\");\n          i0.ɵɵtext(67, \"Shaker\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"p\");\n          i0.ɵɵtext(69, \"Type motiv\\u00E9, expressif et orient\\u00E9 action. \\u00C9nergique, innovant et inspirant.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"a\", 23);\n          i0.ɵɵtext(71, \"D\\u00E9couvrir\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(72, \"div\", 24)(73, \"a\", 25)(74, \"span\", 26);\n          i0.ɵɵtext(75, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"span\");\n          i0.ɵɵtext(77, \"Retour\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".iris-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  gap: 50px;\\n  padding: 40px;\\n  background: linear-gradient(to right, #f8e8ff, #f6f1ff);\\n  flex-wrap: wrap;\\n}\\n\\n.iris-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 160px;\\n  transition: transform 0.3s;\\n}\\n.iris-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n.iris-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 130px;\\n  height: 130px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\\n  transition: transform 0.3s;\\n}\\n.iris-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.iris-card[_ngcontent-%COMP%]   .iris-name[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #5e548e;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  letter-spacing: 0.5px;\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Iris2Component", "selectors", "decls", "vars", "consts", "template", "Iris2Component_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris2\\iris2.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris2\\iris2.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-iris2',\n  templateUrl: './iris2.component.html',\n  styleUrls: ['./iris2.component.scss']\n})\nexport class Iris2Component {\n\n}\n", "<div class=\"iris-types-container\">\n  <div class=\"container\">\n    <div class=\"header\">\n      <h1 class=\"title\">Les Types d'Iris</h1>\n      <p class=\"subtitle\">Découvrez les quatre profils fondamentaux et leurs caractéristiques</p>\n    </div>\n    \n    <div class=\"iris-grid\">\n      <div class=\"iris-card fleur\">\n        <div class=\"card-inner\">\n          <div class=\"card-front\">\n            <div class=\"image-container\">\n              <img src=\"assets/1.png\" alt=\"Fleur\" />\n            </div>\n            <h2 class=\"iris-name\">Fleur</h2>\n            <p class=\"iris-tagline\">Le Sentimental</p>\n          </div>\n          <div class=\"card-back\">\n            <h3>Fleur</h3>\n            <p>Profil axé sur les émotions et la créativité. Expressif, spontané et artistique.</p>\n            <a routerLink=\"/fleur\" class=\"btn discover-btn\">Découvrir</a>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"iris-card bijou\">\n        <div class=\"card-inner\">\n          <div class=\"card-front\">\n            <div class=\"image-container\">\n              <img src=\"assets/2.png\" alt=\"Bijou\" />\n            </div>\n            <h2 class=\"iris-name\">Bijou</h2>\n            <p class=\"iris-tagline\">Le Réfléchi</p>\n          </div>\n          <div class=\"card-back\">\n            <h3>Bijou</h3>\n            <p>Type analytique et mental. Observateur, précis et orienté vers la réflexion.</p>\n            <a routerLink=\"/bijou\" class=\"btn discover-btn\">Découvrir</a>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"iris-card flux\">\n        <div class=\"card-inner\">\n          <div class=\"card-front\">\n            <div class=\"image-container\">\n              <img src=\"assets/3.png\" alt=\"Flux\" />\n            </div>\n            <h2 class=\"iris-name\">Flux</h2>\n            <p class=\"iris-tagline\">Le Sensitif</p>\n          </div>\n          <div class=\"card-back\">\n            <h3>Flux</h3>\n            <p>Type intuitif, physique et empathique. Calme, posé et attentionné.</p>\n            <a routerLink=\"/flux\" class=\"btn discover-btn\">Découvrir</a>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"iris-card shaker\">\n        <div class=\"card-inner\">\n          <div class=\"card-front\">\n            <div class=\"image-container\">\n              <img src=\"assets/4.png\" alt=\"Shaker\" />\n            </div>\n            <h2 class=\"iris-name\">Shaker</h2>\n            <p class=\"iris-tagline\">Le Visionnaire</p>\n          </div>\n          <div class=\"card-back\">\n            <h3>Shaker</h3>\n            <p>Type motivé, expressif et orienté action. Énergique, innovant et inspirant.</p>\n            <a routerLink=\"/shaker\" class=\"btn discover-btn\">Découvrir</a>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"navigation\">\n      <a routerLink=\"/typeiris\" class=\"btn back-btn\">\n        <span class=\"icon\">←</span>\n        <span>Retour</span>\n      </a>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3BE,EAAA,CAAAC,cAAA,aAAkC;UAGVD,EAAA,CAAAE,MAAA,uBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,WAAoB;UAAAD,EAAA,CAAAE,MAAA,oFAAmE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG7FH,EAAA,CAAAC,cAAA,aAAuB;UAKbD,EAAA,CAAAI,SAAA,eAAsC;UACxCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,aAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE5CH,EAAA,CAAAC,cAAA,eAAuB;UACjBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,iHAAgF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvFH,EAAA,CAAAC,cAAA,aAAgD;UAAAD,EAAA,CAAAE,MAAA,sBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKnEH,EAAA,CAAAC,cAAA,eAA6B;UAIrBD,EAAA,CAAAI,SAAA,eAAsC;UACxCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,aAAwB;UAAAD,EAAA,CAAAE,MAAA,6BAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEzCH,EAAA,CAAAC,cAAA,eAAuB;UACjBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,mGAA4E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnFH,EAAA,CAAAC,cAAA,aAAgD;UAAAD,EAAA,CAAAE,MAAA,sBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKnEH,EAAA,CAAAC,cAAA,eAA4B;UAIpBD,EAAA,CAAAI,SAAA,eAAqC;UACvCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAC,cAAA,aAAwB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEzCH,EAAA,CAAAC,cAAA,eAAuB;UACjBD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,oFAAkE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACzEH,EAAA,CAAAC,cAAA,aAA+C;UAAAD,EAAA,CAAAE,MAAA,sBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKlEH,EAAA,CAAAC,cAAA,eAA8B;UAItBD,EAAA,CAAAI,SAAA,eAAuC;UACzCJ,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAC,cAAA,aAAwB;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE5CH,EAAA,CAAAC,cAAA,eAAuB;UACjBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACfH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,kGAA2E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClFH,EAAA,CAAAC,cAAA,aAAiD;UAAAD,EAAA,CAAAE,MAAA,sBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMtEH,EAAA,CAAAC,cAAA,eAAwB;UAEDD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}