{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class Iris2Component {\n  static {\n    this.ɵfac = function Iris2Component_Factory(t) {\n      return new (t || Iris2Component)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Iris2Component,\n      selectors: [[\"app-iris2\"]],\n      decls: 17,\n      vars: 0,\n      consts: [[1, \"iris-grid\"], [1, \"iris-card\"], [\"src\", \"assets/1.png\", \"alt\", \"Fleur\"], [1, \"iris-name\"], [\"src\", \"assets/2.png\", \"alt\", \"Bijou\"], [\"src\", \"assets/3.png\", \"alt\", \"Flux\"], [\"src\", \"assets/4.png\", \"alt\", \"Shaker\"]],\n      template: function Iris2Component_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementStart(3, \"span\", 3);\n          i0.ɵɵtext(4, \"Fleur\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"div\", 1);\n          i0.ɵɵelement(6, \"img\", 4);\n          i0.ɵɵelementStart(7, \"span\", 3);\n          i0.ɵɵtext(8, \"Bijou\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 1);\n          i0.ɵɵelement(10, \"img\", 5);\n          i0.ɵɵelementStart(11, \"span\", 3);\n          i0.ɵɵtext(12, \"Flux\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 1);\n          i0.ɵɵelement(14, \"img\", 6);\n          i0.ɵɵelementStart(15, \"span\", 3);\n          i0.ɵɵtext(16, \"Shaker\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      styles: [\".iris-grid[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: flex-start;\\n  gap: 50px;\\n  padding: 40px;\\n  background: linear-gradient(to right, #A1CFF0, #A1F0C3);\\n  flex-wrap: wrap;\\n}\\n\\n.iris-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 160px;\\n  transition: transform 0.3s;\\n}\\n.iris-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n.iris-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 130px;\\n  height: 130px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\\n  transition: transform 0.3s;\\n}\\n.iris-card[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.iris-card[_ngcontent-%COMP%]   .iris-name[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  color: #5e548e;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  letter-spacing: 0.5px;\\n  text-align: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Iris2Component", "selectors", "decls", "vars", "consts", "template", "Iris2Component_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris2\\iris2.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris2\\iris2.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-iris2',\n  templateUrl: './iris2.component.html',\n  styleUrls: ['./iris2.component.scss']\n})\nexport class Iris2Component {\n\n}\n", "<div class=\"iris-grid\">\n    <div class=\"iris-card\">\n      <img src=\"assets/1.png\" alt=\"Fleur\" />\n      <span class=\"iris-name\">Fleur</span>\n    </div>\n    <div class=\"iris-card\">\n      <img src=\"assets/2.png\" alt=\"Bijou\" />\n      <span class=\"iris-name\">Bijou</span>\n    </div>\n    <div class=\"iris-card\">\n      <img src=\"assets/3.png\" alt=\"Flux\" />\n      <span class=\"iris-name\">Flux</span>\n    </div>\n    <div class=\"iris-card\">\n      <img src=\"assets/4.png\" alt=\"Shaker\" />\n      <span class=\"iris-name\">Shaker</span>\n    </div>\n  </div>\n  "], "mappings": ";AAOA,OAAM,MAAOA,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3BE,EAAA,CAAAC,cAAA,aAAuB;UAEjBD,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAEtCJ,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,SAAA,aAAsC;UACtCF,EAAA,CAAAC,cAAA,cAAwB;UAAAD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAEtCJ,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,SAAA,cAAqC;UACrCF,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAErCJ,EAAA,CAAAC,cAAA,cAAuB;UACrBD,EAAA,CAAAE,SAAA,cAAuC;UACvCF,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}