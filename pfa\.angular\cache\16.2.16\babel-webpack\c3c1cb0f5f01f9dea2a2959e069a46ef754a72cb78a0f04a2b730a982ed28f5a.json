{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function pairwise() {\n  return operate((source, subscriber) => {\n    let prev;\n    let hasPrev = false;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const p = prev;\n      prev = value;\n      hasPrev && subscriber.next([p, value]);\n      hasPrev = true;\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "pairwise", "source", "subscriber", "prev", "has<PERSON>rev", "subscribe", "value", "p", "next"], "sources": ["E:/aymen/pfa/pfa/node_modules/rxjs/dist/esm/internal/operators/pairwise.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function pairwise() {\n    return operate((source, subscriber) => {\n        let prev;\n        let hasPrev = false;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const p = prev;\n            prev = value;\n            hasPrev && subscriber.next([p, value]);\n            hasPrev = true;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,QAAQA,CAAA,EAAG;EACvB,OAAOF,OAAO,CAAC,CAACG,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,IAAI;IACR,IAAIC,OAAO,GAAG,KAAK;IACnBH,MAAM,CAACI,SAAS,CAACN,wBAAwB,CAACG,UAAU,EAAGI,KAAK,IAAK;MAC7D,MAAMC,CAAC,GAAGJ,IAAI;MACdA,IAAI,GAAGG,KAAK;MACZF,OAAO,IAAIF,UAAU,CAACM,IAAI,CAAC,CAACD,CAAC,EAAED,KAAK,CAAC,CAAC;MACtCF,OAAO,GAAG,IAAI;IAClB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}