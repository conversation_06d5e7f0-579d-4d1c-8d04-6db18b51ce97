{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nexport class SignupComponent {\n  constructor() {\n    this.user = {\n      name: '',\n      email: '',\n      password: '',\n      terms: false\n    };\n  }\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 53,\n      vars: 5,\n      consts: [[1, \"signup-container\"], [1, \"left-section\"], [1, \"graphic\"], [1, \"iris-collage\"], [\"src\", \"assets/iris-collage.png\", \"alt\", \"Iris Collage\", 1, \"iris-collage-img\"], [1, \"circle\", \"circle1\"], [1, \"circle\", \"circle2\"], [1, \"circle\", \"circle3\"], [1, \"slogan\"], [1, \"right-section\"], [1, \"form-container\"], [1, \"logo\"], [1, \"custom-logo\"], [1, \"google-btn\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google Icon\"], [1, \"divider\"], [3, \"ngSubmit\"], [\"signupForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \"Leslie Alexander\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"placeholder\", \"<EMAIL>\", \"required\", \"\", \"email\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"placeholder\", \"At least 8 characters\", \"required\", \"\", \"minlength\", \"8\", 3, \"ngModel\", \"ngModelChange\"], [1, \"checkbox-group\"], [\"type\", \"checkbox\", \"id\", \"terms\", \"name\", \"terms\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"terms\"], [\"href\", \"#\"], [\"type\", \"submit\", 1, \"signup-btn\", 3, \"disabled\"], [1, \"login-link\"], [\"routerLink\", \"/login\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 8);\n          i0.ɵɵtext(9, \" Each iris is a unique story written by nature, waiting to be decoded by technology \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"div\", 11)(13, \"div\", 12);\n          i0.ɵɵelement(14, \"span\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"h1\");\n          i0.ɵɵtext(16, \"Sign Up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\");\n          i0.ɵɵtext(18, \"Votre identit\\u00E9, red\\u00E9finie par la technologie. Cr\\u00E9ez votre compte.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"button\", 13);\n          i0.ɵɵelement(20, \"img\", 14);\n          i0.ɵɵtext(21, \" Continue with Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 15);\n          i0.ɵɵtext(23, \"or Sign in with Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"form\", 16, 17);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_24_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(26, \"div\", 18)(27, \"label\", 19);\n          i0.ɵɵtext(28, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_29_listener($event) {\n            return ctx.user.name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 18)(31, \"label\", 21);\n          i0.ɵɵtext(32, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_33_listener($event) {\n            return ctx.user.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 18)(35, \"label\", 23);\n          i0.ɵɵtext(36, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"input\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_37_listener($event) {\n            return ctx.user.password = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 25)(39, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_39_listener($event) {\n            return ctx.user.terms = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"label\", 27);\n          i0.ɵɵtext(41, \"I agree with \");\n          i0.ɵɵelementStart(42, \"a\", 28);\n          i0.ɵɵtext(43, \"Terms\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \" and \");\n          i0.ɵɵelementStart(45, \"a\", 28);\n          i0.ɵɵtext(46, \"Privacy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"button\", 29);\n          i0.ɵɵtext(48, \" Sign Up \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"p\", 30);\n          i0.ɵɵtext(50, \" Already have an account? \");\n          i0.ɵɵelementStart(51, \"a\", 31);\n          i0.ɵɵtext(52, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(25);\n          i0.ɵɵadvance(29);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.terms);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", !_r0.valid);\n        }\n      },\n      dependencies: [i1.RouterLink, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MinLengthValidator, i2.CheckboxRequiredValidator, i2.EmailValidator, i2.NgModel, i2.NgForm],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.signup-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  font-family: \\\"Arial\\\", sans-serif;\\n  background: #fff;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, #e6e6fa, #f5f5ff); \\n\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.graphic[_ngcontent-%COMP%] {\\n  position: relative;\\n  text-align: center;\\n}\\n\\n.iris-collage[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.iris-collage-img[_ngcontent-%COMP%] {\\n  width: 450px;\\n  height: auto;\\n  object-fit: contain;\\n}\\n\\n\\n\\n.circle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border-radius: 50%;\\n  background: rgba(255, 182, 193, 0.3); \\n\\n}\\n\\n\\n\\n.circle1[_ngcontent-%COMP%] {\\n  width: 400px;\\n  height: 400px;\\n  top: -130px;\\n  bottom: 10px;\\n  left: 0px;\\n}\\n\\n.circle3[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  top: 80px;\\n  right: 80px;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%]::before, .left-section[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  border-radius: 50%;\\n  background: #ff4040; \\n\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before {\\n  width: 5px;\\n  height: 5px;\\n  top: 10%;\\n  left: 20%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::after {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 15%;\\n  right: 10%;\\n}\\n\\n.slogan[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  color: #6a5acd; \\n\\n  text-align: center;\\n  margin-top: 20px;\\n  font-style: italic;\\n  font-family: \\\"Dancing Script\\\", cursive;\\n}\\n\\n\\n\\n.right-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background: #fff;\\n}\\n\\n.form-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  padding: 20px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.custom-logo[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  margin: 0 auto;\\n  position: relative;\\n  background: transparent;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd; \\n\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  bottom: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd;\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  left: 5px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  right: 5px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  color: #333;\\n  font-weight: 700;\\n  margin: 10px 0;\\n}\\n\\n.logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-bottom: 20px;\\n}\\n\\n.google-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  padding: 10px;\\n  background: #fff;\\n  border: 1px solid #ccc;\\n  border-radius: 5px;\\n  cursor: pointer;\\n  font-size: 1rem;\\n  color: #333;\\n  margin-bottom: 20px;\\n  transition: background-color 0.3s;\\n}\\n\\n.google-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n}\\n\\n.google-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  margin-right: 10px;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin: 20px 0;\\n  position: relative;\\n}\\n\\n.divider[_ngcontent-%COMP%]::before, .divider[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 40%;\\n  height: 1px;\\n  background: #ccc;\\n}\\n\\n.divider[_ngcontent-%COMP%]::before {\\n  left: 0;\\n}\\n\\n.divider[_ngcontent-%COMP%]::after {\\n  right: 0;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 15px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.9rem;\\n  color: #333;\\n  margin-bottom: 5px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  border: 1px solid #ccc;\\n  border-radius: 5px;\\n  font-size: 1rem;\\n  color: #333;\\n  background: #fff;\\n  transition: border-color 0.3s;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #6a5acd; \\n\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd; \\n\\n  text-decoration: none;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px;\\n  background: #6a5acd; \\n\\n  color: #fff;\\n  border: none;\\n  border-radius: 5px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  text-transform: uppercase;\\n  transition: background-color 0.3s;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:hover {\\n  background: #5a4bbd;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 20px;\\n  font-size: 0.9rem;\\n  color: #666;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd; \\n\\n  text-decoration: none;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SignupComponent", "constructor", "user", "name", "email", "password", "terms", "onSubmit", "console", "log", "alert", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_24_listener", "SignupComponent_Template_input_ngModelChange_29_listener", "$event", "SignupComponent_Template_input_ngModelChange_33_listener", "SignupComponent_Template_input_ngModelChange_37_listener", "SignupComponent_Template_input_ngModelChange_39_listener", "ɵɵadvance", "ɵɵproperty", "_r0", "valid"], "sources": ["C:\\pfa\\src\\app\\signup\\signup.component.ts", "C:\\pfa\\src\\app\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.scss']\n})\nexport class SignupComponent {\n  user = {\n    name: '',\n    email: '',\n    password: '',\n    terms: false\n  };\n\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n}", "<div class=\"signup-container\">\n    <!-- Section gauche : Graphique et texte -->\n    <div class=\"left-section\">\n      <div class=\"graphic\">\n        <!-- Afficher une seule image contenant les quatre iris -->\n        <div class=\"iris-collage\">\n          <img src=\"assets/iris-collage.png\" alt=\"Iris Collage\" class=\"iris-collage-img\" />\n        </div>\n        <!-- Cercles décoratifs -->\n        <div class=\"circle circle1\"></div>\n        <div class=\"circle circle2\"></div>\n        <div class=\"circle circle3\"></div>\n      </div>\n      <p class=\"slogan\">\n        Each iris is a unique story written by nature, waiting to be decoded by technology\n      </p>\n    </div>\n  \n    <!-- Section droite : Formulaire -->\n    <div class=\"right-section\">\n      <div class=\"form-container\">\n        <div class=\"logo\">\n          <!-- Logo en forme de croix -->\n          <div class=\"custom-logo\"><span></span></div>\n          <h1>Sign Up</h1>\n          <p>Votre identité, redéfinie par la technologie. Créez votre compte.</p>\n        </div>\n  \n        <!-- Bouton \"Continue with Google\" -->\n        <button class=\"google-btn\">\n          <img src=\"assets/google-icon.png\" alt=\"Google Icon\" /> Continue with Google\n        </button>\n  \n        <div class=\"divider\">or Sign in with Email</div>\n  \n        <!-- Formulaire -->\n        <form (ngSubmit)=\"onSubmit()\" #signupForm=\"ngForm\">\n          <div class=\"form-group\">\n            <label for=\"name\">Name</label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              placeholder=\"Leslie Alexander\"\n              [(ngModel)]=\"user.name\"\n              required\n            />\n          </div>\n  \n          <div class=\"form-group\">\n            <label for=\"email\">Email</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              placeholder=\"<EMAIL>\"\n              [(ngModel)]=\"user.email\"\n              required\n              email\n            />\n          </div>\n  \n          <div class=\"form-group\">\n            <label for=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              placeholder=\"At least 8 characters\"\n              [(ngModel)]=\"user.password\"\n              required\n              minlength=\"8\"\n            />\n          </div>\n  \n          <div class=\"checkbox-group\">\n            <input\n              type=\"checkbox\"\n              id=\"terms\"\n              name=\"terms\"\n              [(ngModel)]=\"user.terms\"\n              required\n            />\n            <label for=\"terms\">I agree with <a href=\"#\">Terms</a> and <a href=\"#\">Privacy</a></label>\n          </div>\n  \n          <button type=\"submit\" class=\"signup-btn\" [disabled]=\"!signupForm.valid\">\n            Sign Up\n          </button>\n        </form>\n  \n        <p class=\"login-link\">\n          Already have an account? <a routerLink=\"/login\">Log in</a>\n        </p>\n      </div>\n    </div>\n  </div>"], "mappings": ";;;AAOA,OAAM,MAAOA,eAAe;EAL5BC,YAAA;IAME,KAAAC,IAAI,GAAG;MACLC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;KACR;;EAEDC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,IAAI,CAACI,KAAK,EAAE;MACnBE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACP,IAAI,CAAC;MACzC;KACD,MAAM;MACLQ,KAAK,CAAC,+CAA+C,CAAC;;EAE1D;;;uBAfWV,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAW,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,aAA8B;UAMpBD,EAAA,CAAAE,SAAA,aAAiF;UACnFF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAE,SAAA,aAAkC;UAGpCF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,WAAkB;UAChBD,EAAA,CAAAI,MAAA,2FACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,cAA2B;UAIID,EAAA,CAAAE,SAAA,YAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,wFAAiE;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAI1EH,EAAA,CAAAC,cAAA,kBAA2B;UACzBD,EAAA,CAAAE,SAAA,eAAsD;UAACF,EAAA,CAAAI,MAAA,8BACzD;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAI,MAAA,6BAAqB;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAGhDH,EAAA,CAAAC,cAAA,oBAAmD;UAA7CD,EAAA,CAAAK,UAAA,sBAAAC,mDAAA;YAAA,OAAYP,GAAA,CAAAX,QAAA,EAAU;UAAA,EAAC;UAC3BY,EAAA,CAAAC,cAAA,eAAwB;UACJD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAC,cAAA,iBAOE;UAFAD,EAAA,CAAAK,UAAA,2BAAAE,yDAAAC,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAC,IAAA,GAAAwB,MAAA;UAAA,EAAuB;UALzBR,EAAA,CAAAG,YAAA,EAOE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAI,yDAAAD,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAE,KAAA,GAAAuB,MAAA;UAAA,EAAwB;UAL1BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAK,yDAAAF,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAG,QAAA,GAAAsB,MAAA;UAAA,EAA2B;UAL7BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAA4B;UAKxBD,EAAA,CAAAK,UAAA,2BAAAM,yDAAAH,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAI,KAAA,GAAAqB,MAAA;UAAA,EAAwB;UAJ1BR,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAAmB;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAI,MAAA,aAAI;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGnFH,EAAA,CAAAC,cAAA,kBAAwE;UACtED,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,aAAsB;UACpBD,EAAA,CAAAI,MAAA,kCAAyB;UAAAJ,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;UAhDtDH,EAAA,CAAAY,SAAA,IAAuB;UAAvBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAC,IAAA,CAAuB;UAYvBgB,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAE,KAAA,CAAwB;UAaxBe,EAAA,CAAAY,SAAA,GAA2B;UAA3BZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAG,QAAA,CAA2B;UAW3Bc,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAI,KAAA,CAAwB;UAMaa,EAAA,CAAAY,SAAA,GAA8B;UAA9BZ,EAAA,CAAAa,UAAA,cAAAC,GAAA,CAAAC,KAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}