{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class FooterComponent {\n  static {\n    this.ɵfac = function FooterComponent_Factory(t) {\n      return new (t || FooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FooterComponent,\n      selectors: [[\"app-footer\"]],\n      decls: 70,\n      vars: 0,\n      consts: [[1, \"footer\"], [1, \"footer-container\"], [1, \"footer-top\"], [1, \"footer-logo\"], [1, \"footer-links\"], [1, \"footer-column\"], [\"routerLink\", \"/accueil\"], [\"routerLink\", \"/iris-types\"], [\"routerLink\", \"/iris-diversity\"], [\"routerLink\", \"/dashboard\"], [\"href\", \"#\"], [1, \"footer-bottom\"], [1, \"social-icons\"], [\"href\", \"#\", 1, \"social-icon\"], [1, \"fab\", \"fa-facebook-f\"], [1, \"fab\", \"fa-twitter\"], [1, \"fab\", \"fa-instagram\"], [1, \"fab\", \"fa-linkedin-in\"], [1, \"copyright\"]],\n      template: function FooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\");\n          i0.ɵɵtext(5, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\");\n          i0.ɵɵtext(7, \"S\\u00E9curit\\u00E9 et authentification par iris de pointe\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 4)(9, \"div\", 5)(10, \"h3\");\n          i0.ɵɵtext(11, \"Navigation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"ul\")(13, \"li\")(14, \"a\", 6);\n          i0.ɵɵtext(15, \"Accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"li\")(17, \"a\", 7);\n          i0.ɵɵtext(18, \"Types d'Iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"li\")(20, \"a\", 8);\n          i0.ɵɵtext(21, \"Diversit\\u00E9 d'Iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"li\")(23, \"a\", 9);\n          i0.ɵɵtext(24, \"Tableau de bord\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 5)(26, \"h3\");\n          i0.ɵɵtext(27, \"Ressources\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"ul\")(29, \"li\")(30, \"a\", 10);\n          i0.ɵɵtext(31, \"Documentation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"li\")(33, \"a\", 10);\n          i0.ɵɵtext(34, \"Tutoriels\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"li\")(36, \"a\", 10);\n          i0.ɵɵtext(37, \"FAQ\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"li\")(39, \"a\", 10);\n          i0.ɵɵtext(40, \"Support\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 5)(42, \"h3\");\n          i0.ɵɵtext(43, \"L\\u00E9gal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"ul\")(45, \"li\")(46, \"a\", 10);\n          i0.ɵɵtext(47, \"Conditions d'utilisation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"li\")(49, \"a\", 10);\n          i0.ɵɵtext(50, \"Politique de confidentialit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\")(52, \"a\", 10);\n          i0.ɵɵtext(53, \"Mentions l\\u00E9gales\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"li\")(55, \"a\", 10);\n          i0.ɵɵtext(56, \"Cookies\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(57, \"div\", 11)(58, \"div\", 12)(59, \"a\", 13);\n          i0.ɵɵelement(60, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"a\", 13);\n          i0.ɵɵelement(62, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"a\", 13);\n          i0.ɵɵelement(64, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"a\", 13);\n          i0.ɵɵelement(66, \"i\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 18)(68, \"p\");\n          i0.ɵɵtext(69, \"\\u00A9 2025 IrisLock. Tous droits r\\u00E9serv\\u00E9s.\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".footer[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #495057;\\n  padding: 0;\\n  margin-top: 60px;\\n  border-top: 1px solid #e9ecef;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n\\n.footer-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n\\n.footer-top[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  padding: 50px 0 30px;\\n  border-bottom: 1px solid #e9ecef;\\n}\\n\\n.footer-logo[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 250px;\\n  margin-bottom: 30px;\\n}\\n.footer-logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  margin: 0 0 10px 0;\\n  color: #2c7be5;\\n  font-weight: 700;\\n}\\n.footer-logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  margin: 0;\\n  color: #495057;\\n  max-width: 300px;\\n}\\n\\n.footer-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  flex: 2;\\n}\\n\\n.footer-column[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 160px;\\n  margin-bottom: 20px;\\n  padding: 0 15px;\\n}\\n.footer-column[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  color: #495057;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #495057;\\n  text-decoration: none;\\n  font-size: 0.9rem;\\n  transition: color 0.2s ease;\\n  position: relative;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  width: 0;\\n  height: 1px;\\n  bottom: -2px;\\n  left: 0;\\n  background-color: #2c7be5;\\n  transition: width 0.3s ease;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #2c7be5;\\n}\\n.footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:before {\\n  width: 100%;\\n}\\n\\n.footer-bottom[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 0;\\n}\\n\\n.social-icons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n  margin-bottom: 15px;\\n}\\n.social-icons[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  background-color: #f1f3f5;\\n  color: #495057;\\n  text-decoration: none;\\n  transition: all 0.2s ease;\\n}\\n.social-icons[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.social-icons[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%]:hover {\\n  background-color: #2c7be5;\\n  color: white;\\n  transform: translateY(-3px);\\n}\\n\\n.copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.85rem;\\n  color: #495057;\\n}\\n\\n.footer.dark[_ngcontent-%COMP%] {\\n  background-color: #212529;\\n  color: #f8f9fa;\\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n.footer.dark[_ngcontent-%COMP%]   .footer-logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.footer.dark[_ngcontent-%COMP%]   .footer-logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .footer.dark[_ngcontent-%COMP%]   .footer-column[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .footer.dark[_ngcontent-%COMP%]   .footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%], .footer.dark[_ngcontent-%COMP%]   .copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #f8f9fa;\\n}\\n.footer.dark[_ngcontent-%COMP%]   .footer-top[_ngcontent-%COMP%], .footer.dark[_ngcontent-%COMP%]   .footer-bottom[_ngcontent-%COMP%] {\\n  border-color: rgba(255, 255, 255, 0.1);\\n}\\n.footer.dark[_ngcontent-%COMP%]   .social-icon[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n  color: #f8f9fa;\\n}\\n\\n@media (max-width: 768px) {\\n  .footer-top[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .footer-links[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .footer-column[_ngcontent-%COMP%] {\\n    margin-bottom: 30px;\\n    padding: 0;\\n  }\\n  .footer-bottom[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .social-icons[_ngcontent-%COMP%] {\\n    margin: 0 auto 15px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .footer-logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .footer-column[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .footer-column[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .copyright[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FooterComponent", "selectors", "decls", "vars", "consts", "template", "FooterComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shared\\footer\\footer.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shared\\footer\\footer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-footer',\n  templateUrl: './footer.component.html',\n  styleUrls: ['./footer.component.scss']\n})\nexport class FooterComponent {\n\n}\n", "<footer class=\"footer\">\n  <div class=\"footer-container\">\n    <div class=\"footer-top\">\n      <div class=\"footer-logo\">\n        <h2>IrisLock</h2>\n        <p>Sécurité et authentification par iris de pointe</p>\n      </div>\n\n      <div class=\"footer-links\">\n        <div class=\"footer-column\">\n          <h3>Navigation</h3>\n          <ul>\n            <li><a routerLink=\"/accueil\">Accueil</a></li>\n            <li><a routerLink=\"/iris-types\">Types d'Iris</a></li>\n            <li><a routerLink=\"/iris-diversity\">Diversité d'Iris</a></li>\n            <li><a routerLink=\"/dashboard\">Tableau de bord</a></li>\n          </ul>\n        </div>\n\n        <div class=\"footer-column\">\n          <h3>Ressources</h3>\n          <ul>\n            <li><a href=\"#\">Documentation</a></li>\n            <li><a href=\"#\">Tutoriels</a></li>\n            <li><a href=\"#\">FAQ</a></li>\n            <li><a href=\"#\">Support</a></li>\n          </ul>\n        </div>\n\n        <div class=\"footer-column\">\n          <h3>Légal</h3>\n          <ul>\n            <li><a href=\"#\">Conditions d'utilisation</a></li>\n            <li><a href=\"#\">Politique de confidentialité</a></li>\n            <li><a href=\"#\">Mentions légales</a></li>\n            <li><a href=\"#\">Cookies</a></li>\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"footer-bottom\">\n      <div class=\"social-icons\">\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-facebook-f\"></i></a>\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-twitter\"></i></a>\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-instagram\"></i></a>\n        <a href=\"#\" class=\"social-icon\"><i class=\"fab fa-linkedin-in\"></i></a>\n      </div>\n\n      <div class=\"copyright\">\n        <p>&copy; 2025 IrisLock. Tous droits réservés.</p>\n      </div>\n    </div>\n  </div>\n</footer>\n"], "mappings": ";;AAOA,OAAM,MAAOA,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,gBAAuB;UAIXD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,gEAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGxDH,EAAA,CAAAC,cAAA,aAA0B;UAElBD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,UAAI;UAC2BD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACxCH,EAAA,CAAAC,cAAA,UAAI;UAA4BD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChDH,EAAA,CAAAC,cAAA,UAAI;UAAgCD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACxDH,EAAA,CAAAC,cAAA,UAAI;UAA2BD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAItDH,EAAA,CAAAC,cAAA,cAA2B;UACrBD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,UAAI;UACcD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACjCH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7BH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvBH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI/BH,EAAA,CAAAC,cAAA,cAA2B;UACrBD,EAAA,CAAAE,MAAA,kBAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACdH,EAAA,CAAAC,cAAA,UAAI;UACcD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,yCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChDH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpCH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAMnCH,EAAA,CAAAC,cAAA,eAA2B;UAESD,EAAA,CAAAI,SAAA,aAAiC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACrEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,SAAA,aAA8B;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAClEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,SAAA,aAAgC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACpEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAI,SAAA,aAAkC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGxEH,EAAA,CAAAC,cAAA,eAAuB;UAClBD,EAAA,CAAAE,MAAA,6DAA2C;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}