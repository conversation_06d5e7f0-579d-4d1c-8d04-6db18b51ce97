{"ast": null, "code": "import { PERSONALITY_QUESTIONS } from '../models/personality-test.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/personality-test.service\";\nimport * as i2 from \"../services/iris-compatibility.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction PersonalityTestComponent_div_1_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_div_47_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.logout());\n    });\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Se d\\u00E9connecter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 14);\n    i0.ɵɵtext(5, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PersonalityTestComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"h1\", 8);\n    i0.ɵɵtext(4, \"Test de Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 9);\n    i0.ɵɵelementStart(6, \"p\", 10);\n    i0.ɵɵtext(7, \"D\\u00E9couvrez votre profil psychotechnique\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"div\", 12)(10, \"div\", 13)(11, \"span\", 14);\n    i0.ɵɵtext(12, \"\\uD83D\\uDCDD\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 15)(14, \"h3\");\n    i0.ɵɵtext(15, \"32 Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\");\n    i0.ɵɵtext(17, \"Questions cibl\\u00E9es pour analyser votre personnalit\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 13)(19, \"span\", 14);\n    i0.ɵɵtext(20, \"\\u23F1\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 15)(22, \"h3\");\n    i0.ɵɵtext(23, \"5-10 Minutes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\");\n    i0.ɵɵtext(25, \"Temps estim\\u00E9 pour compl\\u00E9ter le test\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 13)(27, \"span\", 14);\n    i0.ɵɵtext(28, \"\\uD83C\\uDFAF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 15)(30, \"h3\");\n    i0.ɵɵtext(31, \"4 Profils Principaux\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"Flower, Jewel, Shaker, Stream + profils interm\\u00E9diaires\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 16)(35, \"h3\");\n    i0.ɵɵtext(36, \"Informations du test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"p\")(38, \"strong\");\n    i0.ɵɵtext(39, \"Nom:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"p\")(42, \"strong\");\n    i0.ɵɵtext(43, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"p\", 17);\n    i0.ɵɵtext(46, \"Les r\\u00E9sultats seront sauvegard\\u00E9s dans la base de donn\\u00E9es PFA1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, PersonalityTestComponent_div_1_div_47_Template, 6, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 19)(49, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.startTest());\n    });\n    i0.ɵɵelementStart(50, \"span\");\n    i0.ɵɵtext(51, \"Commencer le Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"span\", 14);\n    i0.ɵɵtext(53, \"\\u2192\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_Template_button_click_54_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.goToHome());\n    });\n    i0.ɵɵtext(55, \" Retour \\u00E0 l'accueil \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(40);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedUser.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedUser.email, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser);\n  }\n}\nfunction PersonalityTestComponent_div_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"h2\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 36)(5, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_2_div_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.answerQuestion(true));\n    });\n    i0.ɵɵelementStart(6, \"span\", 38);\n    i0.ɵɵtext(7, \"\\u2713\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"Oui\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_2_div_11_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.answerQuestion(false));\n    });\n    i0.ɵɵelementStart(11, \"span\", 38);\n    i0.ɵɵtext(12, \"\\u2717\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Non\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r10.currentQuestion.question);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.isLoading);\n  }\n}\nfunction PersonalityTestComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"div\", 27)(4, \"span\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 30);\n    i0.ɵɵelement(10, \"div\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, PersonalityTestComponent_div_2_div_11_Template, 15, 3, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"Question \", ctx_r1.currentQuestionNumber, \" sur \", ctx_r1.totalQuestions, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(8, 6, ctx_r1.progressPercentage, \"1.0-0\"), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.progressPercentage, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentQuestion);\n  }\n}\nfunction PersonalityTestComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"div\", 42);\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Analyse de votre profil...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Calcul des scores et d\\u00E9termination de votre personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 43)(8, \"div\", 44);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 44);\n    i0.ɵɵtext(11, \"\\uD83D\\uDD04 Calcul des scores par famille\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 44);\n    i0.ɵɵtext(13, \"\\uD83C\\uDFAF D\\u00E9termination du profil principal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 44);\n    i0.ɵɵtext(15, \"\\uD83D\\uDD0D V\\u00E9rification compatibilit\\u00E9 iris\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\\u2713 R\\u00E9ponses collect\\u00E9es (\", ctx_r2.responses.length, \"/32)\");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"span\", 66);\n    i0.ɵɵtext(2, \"Profil Interm\\u00E9diaire\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"span\", 67);\n    i0.ɵɵtext(2, \"Profil Principal\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const characteristic_r23 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", characteristic_r23, \" \");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_li_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const recommendation_r24 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", recommendation_r24, \" \");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_div_29_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const correction_r26 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", correction_r26, \" \");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"h4\");\n    i0.ɵɵtext(2, \"Corrections sugg\\u00E9r\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 85);\n    i0.ɵɵtemplate(4, PersonalityTestComponent_div_4_div_22_div_29_li_4_Template, 2, 1, \"li\", 81);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r22.compatibilityResult.corrections);\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"h3\");\n    i0.ɵɵtext(2, \"Compatibilit\\u00E9 avec votre Iris\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 69)(4, \"div\", 70)(5, \"div\", 71)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 72)(11, \"div\", 73)(12, \"span\", 53);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 52);\n    i0.ɵɵtext(15, \"Compatibilit\\u00E9\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 74)(17, \"span\", 75);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 76)(20, \"h4\");\n    i0.ɵɵtext(21, \"Caract\\u00E9ristiques de votre iris\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 77);\n    i0.ɵɵtemplate(23, PersonalityTestComponent_div_4_div_22_span_23_Template, 2, 1, \"span\", 78);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 79)(25, \"h4\");\n    i0.ɵɵtext(26, \"Recommandations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"ul\", 80);\n    i0.ɵɵtemplate(28, PersonalityTestComponent_div_4_div_22_li_28_Template, 2, 1, \"li\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, PersonalityTestComponent_div_4_div_22_div_29_Template, 5, 1, \"div\", 82);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r16.compatibilityResult.isCompatible ? \"compatible\" : \"incompatible\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r16.compatibilityResult.irisType.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r16.compatibilityResult.irisType.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r16.compatibilityResult.isCompatible ? \"high-score\" : \"low-score\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r16.compatibilityResult.compatibilityScore, \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r16.compatibilityResult.isCompatible ? \"compatible\" : \"incompatible\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.compatibilityResult.isCompatible ? \"\\u2713 Compatible\" : \"\\u26A0 Incompatible\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.compatibilityResult.irisType.characteristics);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.compatibilityResult.recommendations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.compatibilityResult.isCompatible);\n  }\n}\nfunction PersonalityTestComponent_div_4_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87)(2, \"h3\");\n    i0.ɵɵtext(3, \"Analyse d\\u00E9taill\\u00E9e des r\\u00E9sultats\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 88);\n    i0.ɵɵtext(5, \"R\\u00E9partition de vos r\\u00E9ponses par famille de personnalit\\u00E9\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 89)(7, \"div\", 90)(8, \"div\", 91)(9, \"span\", 52);\n    i0.ɵɵtext(10, \"Flower\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 92);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 93);\n    i0.ɵɵelement(14, \"div\", 94);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 95)(16, \"span\", 53);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 96);\n    i0.ɵɵtext(19, \"\\u00C9motionnel, Cr\\u00E9atif\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 90)(21, \"div\", 91)(22, \"span\", 52);\n    i0.ɵɵtext(23, \"Jewel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 92);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 93);\n    i0.ɵɵelement(27, \"div\", 97);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 95)(29, \"span\", 53);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 96);\n    i0.ɵɵtext(32, \"Analytique, M\\u00E9thodique\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 90)(34, \"div\", 91)(35, \"span\", 52);\n    i0.ɵɵtext(36, \"Shaker\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 92);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 93);\n    i0.ɵɵelement(40, \"div\", 98);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 95)(42, \"span\", 53);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\", 96);\n    i0.ɵɵtext(45, \"Dynamique, Aventurier\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 90)(47, \"div\", 91)(48, \"span\", 52);\n    i0.ɵɵtext(49, \"Stream\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"span\", 92);\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 93);\n    i0.ɵɵelement(53, \"div\", 99);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 95)(55, \"span\", 53);\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\", 96);\n    i0.ɵɵtext(58, \"Paisible, R\\u00E9fl\\u00E9chi\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(59, \"div\", 100)(60, \"div\", 101)(61, \"span\", 60);\n    i0.ɵɵtext(62, \"Profil dominant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"span\", 102);\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 101)(66, \"span\", 60);\n    i0.ɵɵtext(67, \"Niveau de confiance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"span\", 103);\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"highest-score\", ctx_r17.isHighestScore(\"flower\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.getScorePercentage(\"flower\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.flower / 4 * 100, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.flower, \"/4\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"highest-score\", ctx_r17.isHighestScore(\"jewel\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.getScorePercentage(\"jewel\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.jewel / 4 * 100, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.jewel, \"/4\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"highest-score\", ctx_r17.isHighestScore(\"shaker\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.getScorePercentage(\"shaker\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.shaker / 4 * 100, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.shaker, \"/4\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"highest-score\", ctx_r17.isHighestScore(\"stream\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.getScorePercentage(\"stream\"), \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.stream / 4 * 100, \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.stream, \"/4\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r17.getDominantFamily());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.finalProfile == null ? null : ctx_r17.finalProfile.confidenceScore, \"%\");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"span\", 60);\n    i0.ɵɵtext(2, \"ID de session:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r18.testSession == null ? null : ctx_r18.testSession.id);\n  }\n}\nfunction PersonalityTestComponent_div_4_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.logout());\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2, \"Se d\\u00E9connecter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 14);\n    i0.ɵɵtext(4, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"div\", 47)(3, \"h1\", 8);\n    i0.ɵɵtext(4, \"Votre Profil de Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 48)(7, \"div\", 49)(8, \"h2\", 50);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 51)(11, \"span\", 52);\n    i0.ɵɵtext(12, \"Score de confiance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 53);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(15, PersonalityTestComponent_div_4_div_15_Template, 3, 0, \"div\", 54);\n    i0.ɵɵtemplate(16, PersonalityTestComponent_div_4_div_16_Template, 3, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 55)(18, \"h3\");\n    i0.ɵɵtext(19, \"Description de votre profil\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, PersonalityTestComponent_div_4_div_22_Template, 30, 13, \"div\", 56);\n    i0.ɵɵtemplate(23, PersonalityTestComponent_div_4_div_23_Template, 70, 26, \"div\", 57);\n    i0.ɵɵelementStart(24, \"div\", 58)(25, \"div\", 59)(26, \"span\", 60);\n    i0.ɵɵtext(27, \"Test compl\\u00E9t\\u00E9 le:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 61);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, PersonalityTestComponent_div_4_div_31_Template, 5, 1, \"div\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 63)(33, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.restartTest());\n    });\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35, \"Refaire le Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 14);\n    i0.ɵɵtext(37, \"\\uD83D\\uDD04\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.goToHome());\n    });\n    i0.ɵɵtext(39, \" Retour \\u00E0 l'accueil \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(40, PersonalityTestComponent_div_4_button_40_Template, 5, 0, \"button\", 64);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMap(\"profile-\" + ctx_r3.finalProfile.primaryClass.toLowerCase().replace(\"-\", \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.finalProfile.primaryClass);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.finalProfile.confidenceScore, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.finalProfile.isIntermediate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.finalProfile.isIntermediate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.finalProfile.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.compatibilityResult);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testSession);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(30, 12, ctx_r3.testSession == null ? null : ctx_r3.testSession.completedAt, \"dd/MM/yyyy \\u00E0 HH:mm\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testSession == null ? null : ctx_r3.testSession.id);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentUser);\n  }\n}\nexport class PersonalityTestComponent {\n  constructor(personalityTestService, irisCompatibilityService, router) {\n    this.personalityTestService = personalityTestService;\n    this.irisCompatibilityService = irisCompatibilityService;\n    this.router = router;\n    // Données du test\n    this.questions = PERSONALITY_QUESTIONS;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.testSession = null;\n    // État du composant\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.isLoading = false;\n    this.showResults = false;\n    this.currentSessionId = '';\n    // Comptes statiques pour les tests\n    this.staticUsers = [{\n      id: 1,\n      name: 'Marie Dubois',\n      email: '<EMAIL>',\n      description: 'Profil créatif et émotionnel'\n    }, {\n      id: 2,\n      name: 'Jean Martin',\n      email: '<EMAIL>',\n      description: 'Profil analytique et structuré'\n    }, {\n      id: 3,\n      name: 'Sophie Leroy',\n      email: '<EMAIL>',\n      description: 'Profil dynamique et aventurier'\n    }, {\n      id: 4,\n      name: 'Pierre Moreau',\n      email: '<EMAIL>',\n      description: 'Profil paisible et réfléchi'\n    }, {\n      id: 5,\n      name: 'Emma Bernard',\n      email: '<EMAIL>',\n      description: 'Profil mixte créatif-analytique'\n    }];\n    this.selectedUser = this.staticUsers[0]; // Utilisateur par défaut\n    this.currentUser = null; // Utilisateur connecté\n    // Résultats\n    this.finalProfile = null;\n    this.compatibilityResult = null;\n  }\n  ngOnInit() {\n    // Récupérer l'utilisateur connecté\n    const currentUserData = localStorage.getItem('currentUser');\n    if (currentUserData) {\n      this.currentUser = JSON.parse(currentUserData);\n      // Utiliser l'utilisateur connecté pour le test\n      this.selectedUser = {\n        id: 0,\n        name: this.currentUser.name,\n        email: this.currentUser.email,\n        description: 'Utilisateur connecté'\n      };\n    }\n    this.initializeTest();\n  }\n  /**\n   * Initialise le test\n   */\n  initializeTest() {\n    this.testSession = this.personalityTestService.createTestSession(this.selectedUser.name, this.selectedUser.email);\n  }\n  /**\n   * Sélectionne un utilisateur de test\n   */\n  selectUser(user) {\n    this.selectedUser = user;\n    this.initializeTest();\n  }\n  /**\n   * Démarre le test\n   */\n  startTest() {\n    this.isTestStarted = true;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n    if (this.testSession) {\n      this.testSession.startedAt = new Date();\n      this.testSession.id = this.currentSessionId;\n    }\n    console.log('🚀 Test démarré - Session ID:', this.currentSessionId);\n  }\n  /**\n   * Traite la réponse à une question\n   */\n  answerQuestion(answer) {\n    if (!this.testSession) return;\n    const currentQuestion = this.questions[this.currentQuestionIndex];\n    const response = {\n      questionId: currentQuestion.id,\n      answer: answer,\n      timestamp: new Date()\n    };\n    this.responses.push(response);\n    this.testSession.responses = this.responses;\n    // Sauvegarder la réponse individuelle en temps réel\n    if (this.currentUser && this.currentSessionId) {\n      this.personalityTestService.saveIndividualResponse(this.currentUser.email, this.currentSessionId, response).subscribe({\n        next: success => {\n          if (success) {\n            console.log(`✅ Réponse ${currentQuestion.id} sauvegardée`);\n          } else {\n            console.warn(`⚠️ Erreur sauvegarde réponse ${currentQuestion.id}`);\n          }\n        },\n        error: error => {\n          console.error(`❌ Erreur réponse ${currentQuestion.id}:`, error);\n        }\n      });\n    }\n    // Passer à la question suivante ou terminer le test\n    if (this.currentQuestionIndex < this.questions.length - 1) {\n      this.currentQuestionIndex++;\n    } else {\n      this.completeTest();\n    }\n  }\n  /**\n   * Termine le test et calcule les résultats\n   */\n  completeTest() {\n    if (!this.testSession) return;\n    this.isLoading = true;\n    console.log('🔄 Début de l\\'analyse des résultats...');\n    // Simuler un délai d'analyse réaliste (2-3 secondes)\n    setTimeout(() => {\n      this.processTestResults();\n    }, 2500);\n  }\n  /**\n   * Traite les résultats du test\n   */\n  processTestResults() {\n    if (!this.testSession) return;\n    try {\n      console.log('📊 Calcul des scores avec', this.responses.length, 'réponses');\n      // Calculer les résultats\n      const results = this.personalityTestService.processTestResults(this.responses);\n      console.log('✅ Résultats calculés:', results);\n      // Mettre à jour la session\n      this.testSession.scores = results.scores;\n      this.testSession.finalProfile = results.profile;\n      this.testSession.completedAt = new Date();\n      this.finalProfile = results.profile;\n      // Vérifier la compatibilité avec l'iris\n      this.compatibilityResult = this.irisCompatibilityService.checkCompatibility(results.profile, this.selectedUser.email);\n      console.log('🔍 Compatibilité calculée:', this.compatibilityResult);\n      // Sauvegarder les statistiques de session\n      const sessionStats = {\n        totalQuestions: this.questions.length,\n        totalResponses: this.responses.length,\n        completionRate: this.responses.length / this.questions.length * 100,\n        averageResponseTime: this.calculateAverageResponseTime(),\n        scores: results.scores,\n        profile: results.profile,\n        userId: this.currentUser?.email,\n        completedAt: new Date().toISOString()\n      };\n      // Sauvegarder les statistiques\n      if (this.currentSessionId) {\n        this.personalityTestService.saveSessionStats(this.currentSessionId, sessionStats).subscribe({\n          next: success => {\n            console.log('📊 Statistiques de session sauvegardées:', success);\n          },\n          error: error => {\n            console.error('❌ Erreur sauvegarde stats:', error);\n          }\n        });\n      }\n      // Sauvegarder dans Firebase\n      this.personalityTestService.saveTestSession(this.testSession).subscribe({\n        next: sessionId => {\n          console.log('✅ Test sauvegardé avec l\\'ID:', sessionId);\n          this.testSession.id = sessionId;\n          this.showResultsWithDelay();\n        },\n        error: error => {\n          console.error('❌ Erreur lors de la sauvegarde:', error);\n          this.showResultsWithDelay();\n        }\n      });\n    } catch (error) {\n      console.error('❌ Erreur lors du traitement des résultats:', error);\n      this.showResultsWithDelay();\n    }\n  }\n  /**\n   * Affiche les résultats avec un délai pour une transition fluide\n   */\n  showResultsWithDelay() {\n    setTimeout(() => {\n      this.isLoading = false;\n      this.isTestCompleted = true;\n      this.showResults = true;\n      console.log('🎉 Résultats affichés !');\n    }, 500);\n  }\n  /**\n   * Calcule le temps de réponse moyen\n   */\n  calculateAverageResponseTime() {\n    if (this.responses.length === 0) return 0;\n    let totalTime = 0;\n    for (let i = 1; i < this.responses.length; i++) {\n      const timeDiff = this.responses[i].timestamp.getTime() - this.responses[i - 1].timestamp.getTime();\n      totalTime += timeDiff;\n    }\n    return totalTime / (this.responses.length - 1);\n  }\n  /**\n   * Redémarre le test\n   */\n  restartTest() {\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.showResults = false;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.finalProfile = null;\n    this.compatibilityResult = null;\n    this.currentSessionId = '';\n    this.initializeTest();\n  }\n  /**\n   * Retourne à l'accueil\n   */\n  goToHome() {\n    this.router.navigate(['/accueil']);\n  }\n  /**\n   * Déconnexion de l'utilisateur\n   */\n  logout() {\n    localStorage.removeItem('currentUser');\n    this.router.navigate(['/login']);\n  }\n  /**\n   * Obtient la question actuelle\n   */\n  get currentQuestion() {\n    return this.questions[this.currentQuestionIndex] || null;\n  }\n  /**\n   * Obtient le pourcentage de progression\n   */\n  get progressPercentage() {\n    return (this.currentQuestionIndex + 1) / this.questions.length * 100;\n  }\n  /**\n   * Obtient le numéro de la question actuelle\n   */\n  get currentQuestionNumber() {\n    return this.currentQuestionIndex + 1;\n  }\n  /**\n   * Obtient le nombre total de questions\n   */\n  get totalQuestions() {\n    return this.questions.length;\n  }\n  /**\n   * Vérifie si un score est le plus élevé\n   */\n  isHighestScore(family) {\n    if (!this.testSession?.scores) return false;\n    const scores = this.testSession.scores;\n    const currentScore = scores[family];\n    const maxScore = Math.max(scores.flower, scores.jewel, scores.shaker, scores.stream);\n    return currentScore === maxScore && maxScore > 0;\n  }\n  /**\n   * Calcule le pourcentage d'un score\n   */\n  getScorePercentage(family) {\n    if (!this.testSession?.scores) return 0;\n    const scores = this.testSession.scores;\n    const score = scores[family];\n    return Math.round(score / 4 * 100);\n  }\n  /**\n   * Obtient la famille dominante\n   */\n  getDominantFamily() {\n    if (!this.testSession?.scores) return 'Aucune';\n    const scores = this.testSession.scores;\n    const families = [{\n      name: '🌸 Flower',\n      score: scores.flower\n    }, {\n      name: '💎 Jewel',\n      score: scores.jewel\n    }, {\n      name: '⚡ Shaker',\n      score: scores.shaker\n    }, {\n      name: '🌊 Stream',\n      score: scores.stream\n    }];\n    const dominant = families.reduce((prev, current) => current.score > prev.score ? current : prev);\n    return dominant.name;\n  }\n  static {\n    this.ɵfac = function PersonalityTestComponent_Factory(t) {\n      return new (t || PersonalityTestComponent)(i0.ɵɵdirectiveInject(i1.PersonalityTestService), i0.ɵɵdirectiveInject(i2.IrisCompatibilityService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PersonalityTestComponent,\n      selectors: [[\"app-personality-test\"]],\n      decls: 5,\n      vars: 4,\n      consts: [[1, \"personality-test-container\"], [\"class\", \"test-intro\", 4, \"ngIf\"], [\"class\", \"test-interface\", 4, \"ngIf\"], [\"class\", \"loading-screen\", 4, \"ngIf\"], [\"class\", \"test-results\", 4, \"ngIf\"], [1, \"test-intro\"], [1, \"intro-card\"], [1, \"intro-header\"], [1, \"title\"], [1, \"divider\"], [1, \"subtitle\"], [1, \"intro-content\"], [1, \"test-info\"], [1, \"info-item\"], [1, \"icon\"], [1, \"info-text\"], [1, \"user-info\"], [1, \"note\"], [\"class\", \"user-actions\", 4, \"ngIf\"], [1, \"intro-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"user-actions\"], [1, \"btn\", \"btn-logout\", 3, \"click\"], [1, \"test-interface\"], [1, \"test-card\"], [1, \"progress-section\"], [1, \"progress-info\"], [1, \"question-counter\"], [1, \"progress-percentage\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [\"class\", \"question-section\", 4, \"ngIf\"], [1, \"question-section\"], [1, \"question-content\"], [1, \"question-text\"], [1, \"answer-buttons\"], [1, \"btn\", \"btn-answer\", \"btn-yes\", 3, \"disabled\", \"click\"], [1, \"answer-icon\"], [1, \"btn\", \"btn-answer\", \"btn-no\", 3, \"disabled\", \"click\"], [1, \"loading-screen\"], [1, \"loading-card\"], [1, \"loading-spinner\"], [1, \"loading-steps\"], [1, \"step\", \"active\"], [1, \"test-results\"], [1, \"results-card\"], [1, \"results-header\"], [1, \"profile-summary\"], [1, \"profile-badge\"], [1, \"profile-name\"], [1, \"confidence-score\"], [1, \"score-label\"], [1, \"score-value\"], [\"class\", \"profile-type\", 4, \"ngIf\"], [1, \"profile-description\"], [\"class\", \"iris-compatibility\", 4, \"ngIf\"], [\"class\", \"detailed-scores\", 4, \"ngIf\"], [1, \"test-info-summary\"], [1, \"info-row\"], [1, \"label\"], [1, \"value\"], [\"class\", \"info-row\", 4, \"ngIf\"], [1, \"results-actions\"], [\"class\", \"btn btn-logout\", 3, \"click\", 4, \"ngIf\"], [1, \"profile-type\"], [1, \"type-badge\", \"intermediate\"], [1, \"type-badge\", \"primary\"], [1, \"iris-compatibility\"], [1, \"compatibility-summary\"], [1, \"compatibility-header\"], [1, \"iris-info\"], [1, \"compatibility-score\"], [1, \"score-circle\"], [1, \"compatibility-status\"], [1, \"status-badge\"], [1, \"iris-characteristics\"], [1, \"characteristics-list\"], [\"class\", \"characteristic-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"recommendations\"], [1, \"recommendations-list\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"corrections\", 4, \"ngIf\"], [1, \"characteristic-tag\"], [1, \"corrections\"], [1, \"corrections-list\"], [1, \"detailed-scores\"], [1, \"scores-header\"], [1, \"scores-description\"], [1, \"scores-grid\"], [1, \"score-item\"], [1, \"score-header\"], [1, \"score-percentage\"], [1, \"score-bar\"], [1, \"score-fill\", \"flower\"], [1, \"score-details\"], [1, \"score-description\"], [1, \"score-fill\", \"jewel\"], [1, \"score-fill\", \"shaker\"], [1, \"score-fill\", \"stream\"], [1, \"score-summary\"], [1, \"summary-item\"], [1, \"value\", \"dominant\"], [1, \"value\", \"confidence\"]],\n      template: function PersonalityTestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, PersonalityTestComponent_div_1_Template, 56, 3, \"div\", 1);\n          i0.ɵɵtemplate(2, PersonalityTestComponent_div_2_Template, 12, 9, \"div\", 2);\n          i0.ɵɵtemplate(3, PersonalityTestComponent_div_3_Template, 16, 1, \"div\", 3);\n          i0.ɵɵtemplate(4, PersonalityTestComponent_div_4_Template, 41, 15, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isTestStarted && !ctx.showResults);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isTestStarted && !ctx.showResults);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showResults && ctx.finalProfile);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i4.DecimalPipe, i4.DatePipe],\n      styles: [\"[_ngcontent-%COMP%]:root {\\n  --primary-color: var(--fleur-primary);\\n  --secondary-color: var(--bijou-primary);\\n  --accent-color: var(--flux-primary);\\n  --tertiary-color: var(--shaker-primary);\\n  --success-color: #059669;\\n  --warning-color: #d97706;\\n  --danger-color: #dc2626;\\n  --text-primary: #333;\\n  --text-secondary: #555;\\n  --text-muted: #666;\\n  --text-light: #999;\\n  --background-primary: #ffffff;\\n  --background-secondary: #f8f9fa;\\n  --background-tertiary: #f5f7fa;\\n  --background-accent: #e6f0ff;\\n  --border-color: #e2e8f0;\\n  --border-light: #f1f5f9;\\n  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\\n  --shadow-sm: 0 8px 30px rgba(0, 0, 0, 0.08);\\n  --shadow-md: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  --shadow-lg: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.15);\\n  --radius-sm: 6px;\\n  --radius-md: 12px;\\n  --radius-lg: 15px;\\n  --radius-xl: 20px;\\n}\\n\\n.personality-test-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\\n  padding: 2rem 1rem;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n@media (max-width: 768px) {\\n  .personality-test-container[_ngcontent-%COMP%] {\\n    padding: 1rem 0.5rem;\\n  }\\n}\\n\\n.intro-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%], .loading-card[_ngcontent-%COMP%] {\\n  background: var(--background-primary);\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-lg);\\n  padding: 3rem;\\n  max-width: 900px;\\n  width: 100%;\\n  margin: 0 auto;\\n  transition: all 0.3s ease;\\n}\\n.intro-card[_ngcontent-%COMP%]:hover, .test-card[_ngcontent-%COMP%]:hover, .results-card[_ngcontent-%COMP%]:hover, .loading-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: var(--shadow-xl);\\n}\\n@media (max-width: 768px) {\\n  .intro-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%], .loading-card[_ngcontent-%COMP%] {\\n    padding: 2rem;\\n    margin: 0 1rem;\\n  }\\n}\\n\\n.title[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2.5rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  text-align: center;\\n  margin-bottom: 1rem;\\n  letter-spacing: -0.025em;\\n}\\n@media (max-width: 768px) {\\n  .title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: var(--text-secondary);\\n  text-align: center;\\n  margin-bottom: 2rem;\\n  font-weight: 400;\\n  line-height: 1.6;\\n}\\n@media (max-width: 768px) {\\n  .subtitle[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-bottom: 1.5rem;\\n  }\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  margin: 0 auto 2rem;\\n  border-radius: 3px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.5rem;\\n  border: 1px solid transparent;\\n  border-radius: var(--radius-md);\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  cursor: pointer;\\n  transition: all 0.15s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n  text-decoration: none;\\n  font-family: inherit;\\n  min-height: 44px;\\n  white-space: nowrap;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n  color: white;\\n  border-color: var(--primary-color);\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: var(--secondary-color);\\n  border-color: var(--secondary-color);\\n  box-shadow: var(--shadow-sm);\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--accent-color);\\n  outline-offset: 2px;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: var(--background-primary);\\n  color: var(--text-secondary);\\n  border-color: var(--border-color);\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: var(--background-tertiary);\\n  border-color: var(--text-secondary);\\n  color: var(--text-primary);\\n}\\n.btn.btn-logout[_ngcontent-%COMP%] {\\n  background: var(--danger-color);\\n  color: white;\\n  border-color: var(--danger-color);\\n}\\n.btn.btn-logout[_ngcontent-%COMP%]:hover {\\n  background: #b91c1c;\\n  border-color: #b91c1c;\\n}\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.4;\\n  cursor: not-allowed;\\n  background: var(--background-tertiary);\\n  color: var(--text-muted);\\n  border-color: var(--border-color);\\n}\\n@media (max-width: 768px) {\\n  .btn[_ngcontent-%COMP%] {\\n    padding: 0.875rem 1.25rem;\\n    font-size: 0.9rem;\\n  }\\n}\\n\\n.test-intro[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.test-intro[_ngcontent-%COMP%]   .test-info[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1.5rem;\\n  margin-bottom: 2rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1rem;\\n  padding: 1.25rem;\\n  background: var(--background-primary);\\n  border: 1px solid var(--border-color);\\n  border-radius: var(--radius-lg);\\n  transition: all 0.15s ease;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]:hover {\\n  border-color: var(--primary-color);\\n  box-shadow: var(--shadow-sm);\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  color: var(--primary-color);\\n  margin-top: 0.125rem;\\n  flex-shrink: 0;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.375rem 0;\\n  color: var(--text-primary);\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  line-height: 1.3;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: var(--text-secondary);\\n  font-size: 0.8rem;\\n  line-height: 1.4;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  background: var(--background-accent);\\n  border: 1px solid var(--border-color);\\n  padding: 1.25rem;\\n  border-radius: var(--radius-lg);\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.75rem 0;\\n  color: var(--text-primary);\\n  font-weight: 600;\\n  font-size: 0.95rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.375rem 0;\\n  color: var(--text-secondary);\\n  font-size: 0.85rem;\\n  line-height: 1.4;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   p.note[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: var(--text-muted);\\n  font-size: 0.8rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .intro-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-top: 2rem;\\n}\\n@media (max-width: 768px) {\\n  .test-intro[_ngcontent-%COMP%]   .intro-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n.test-interface[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1.5rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 1px solid var(--border-color);\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .question-counter[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--text-secondary);\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--primary-color);\\n  font-size: 0.8rem;\\n  background: var(--background-accent);\\n  padding: 0.25rem 0.5rem;\\n  border-radius: var(--radius-sm);\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 4px;\\n  background: var(--background-tertiary);\\n  border-radius: 2px;\\n  overflow: hidden;\\n  margin-bottom: 2rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: var(--primary-color);\\n  transition: width 0.3s ease;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n  padding: 2rem 1.5rem;\\n  background: var(--background-accent);\\n  border-radius: var(--radius-lg);\\n  border: 1px solid var(--border-color);\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   .question-text[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 500;\\n  color: var(--text-primary);\\n  line-height: 1.6;\\n  margin: 0;\\n  letter-spacing: -0.01em;\\n}\\n@media (max-width: 768px) {\\n  .test-interface[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   .question-text[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n}\\n.test-interface[_ngcontent-%COMP%]   .answer-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  max-width: 400px;\\n  margin: 0 auto;\\n}\\n@media (max-width: 768px) {\\n  .test-interface[_ngcontent-%COMP%]   .answer-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.75rem;\\n  }\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 1rem 1.5rem;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  border-radius: var(--radius-md);\\n  transition: all 0.15s ease;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer[_ngcontent-%COMP%]   .answer-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-yes[_ngcontent-%COMP%] {\\n  background: var(--success-color);\\n  color: white;\\n  border-color: var(--success-color);\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-yes[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #047857;\\n  border-color: #047857;\\n  box-shadow: var(--shadow-sm);\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-no[_ngcontent-%COMP%] {\\n  background: var(--danger-color);\\n  color: white;\\n  border-color: var(--danger-color);\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-no[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: #b91c1c;\\n  border-color: #b91c1c;\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border: 2px solid var(--background-tertiary);\\n  border-top: 2px solid var(--primary-color);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 0.8s linear infinite;\\n  margin: 0 auto 1.5rem;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: var(--text-primary);\\n  margin-bottom: 0.75rem;\\n  font-weight: 600;\\n  font-size: 1.25rem;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  margin: 0 0 1.5rem 0;\\n  font-size: 0.9rem;\\n  line-height: 1.5;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-steps[_ngcontent-%COMP%] {\\n  text-align: left;\\n  margin-top: 1.5rem;\\n  background: var(--background-accent);\\n  padding: 1rem;\\n  border-radius: var(--radius-lg);\\n  border: 1px solid var(--border-color);\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  padding: 0.375rem 0;\\n  color: var(--text-secondary);\\n  font-size: 0.8rem;\\n  opacity: 0.6;\\n  transition: all 0.3s ease;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  font-weight: 500;\\n  color: var(--primary-color);\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-summary[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 1.5rem;\\n  border-radius: var(--radius-xl);\\n  margin-bottom: 1.5rem;\\n  border: 1px solid var(--border-color);\\n  background: var(--background-primary);\\n  transition: all 0.15s ease;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]:hover {\\n  box-shadow: var(--shadow-sm);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-flower[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .profile-badge.profile-jewel[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .profile-badge.profile-shaker[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .profile-badge.profile-stream[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .profile-badge.profile-flowerjewel[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .profile-badge.profile-jewelshaker[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .profile-badge.profile-shakerstream[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .profile-badge.profile-streamflower[_ngcontent-%COMP%] {\\n  background: var(--background-primary);\\n  border-color: var(--primary-color);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  color: var(--text-primary);\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin: 0;\\n  letter-spacing: -0.02em;\\n  flex: 1;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n  gap: 0.25rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  color: var(--text-muted);\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05em;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  line-height: 1;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge.primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  color: white;\\n  box-shadow: 0 4px 15px rgba(106, 90, 205, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge.intermediate[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, var(--bijou-primary), var(--flux-primary));\\n  color: white;\\n  box-shadow: 0 4px 15px rgba(79, 138, 255, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%] {\\n  background: var(--background-primary);\\n  padding: 2rem;\\n  border-radius: 15px;\\n  margin: 2rem 0;\\n  box-shadow: var(--shadow-md);\\n  border: 1px solid var(--border-color);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--text-primary);\\n  margin: 0 0 1rem 0;\\n  font-weight: 600;\\n  font-family: \\\"Playfair Display\\\", serif;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  line-height: 1.6;\\n  margin: 0;\\n  font-size: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1.5rem 0;\\n  text-align: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  border-radius: 12px;\\n  margin-bottom: 2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary.compatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(56, 161, 105, 0.1) 100%);\\n  border: 2px solid rgba(72, 187, 120, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary.incompatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(229, 62, 62, 0.1) 100%);\\n  border: 2px solid rgba(245, 101, 101, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .iris-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .iris-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .iris-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle.high-score[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle.low-score[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n  font-size: 0.7rem;\\n  font-weight: 500;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.5rem 1.5rem;\\n  border-radius: 20px;\\n  font-weight: 600;\\n  font-size: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%]   .status-badge.compatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\\n  color: white;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%]   .status-badge.incompatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\\n  color: white;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.1);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  margin-bottom: 1.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%]   .characteristics-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%]   .characteristics-list[_ngcontent-%COMP%]   .characteristic-tag[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.3rem 0.8rem;\\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\\n  color: white;\\n  border-radius: 15px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.5);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  margin-bottom: 1.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 1.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  line-height: 1.6;\\n  margin-bottom: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child, .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%] {\\n  border-left: 4px solid #f56565;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #e53e3e;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n  background: var(--background-primary);\\n  border: 1px solid var(--border-color);\\n  border-radius: var(--radius-xl);\\n  overflow: hidden;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .scores-header[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  background: var(--background-accent);\\n  border-bottom: 1px solid var(--border-color);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .scores-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: var(--text-primary);\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.125rem;\\n  font-weight: 600;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .scores-header[_ngcontent-%COMP%]   .scores-description[_ngcontent-%COMP%] {\\n  color: var(--text-secondary);\\n  margin: 0;\\n  font-size: 0.875rem;\\n  line-height: 1.5;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .scores-grid[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  display: grid;\\n  gap: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background: var(--background-secondary);\\n  border: 1px solid var(--border-color);\\n  border-radius: var(--radius-md);\\n  transition: all 0.15s ease;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item.highest-score[_ngcontent-%COMP%] {\\n  border-color: var(--primary-color);\\n  background: var(--background-primary);\\n  box-shadow: var(--shadow-xs);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.75rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-header[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  font-size: 0.9rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-header[_ngcontent-%COMP%]   .score-percentage[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: var(--primary-color);\\n  font-size: 1rem;\\n  background: var(--background-accent);\\n  padding: 0.125rem 0.375rem;\\n  border-radius: var(--radius-sm);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%] {\\n  height: 6px;\\n  background: var(--background-tertiary);\\n  border-radius: 3px;\\n  overflow: hidden;\\n  margin-bottom: 0.75rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: width 0.8s ease;\\n  background: var(--primary-color);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.flower[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.jewel[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.shaker[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.stream[_ngcontent-%COMP%] {\\n  background: var(--primary-color);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-details[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--text-primary);\\n  font-size: 0.8rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-details[_ngcontent-%COMP%]   .score-description[_ngcontent-%COMP%] {\\n  color: var(--text-muted);\\n  font-size: 0.75rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n  background: var(--background-accent);\\n  border-top: 1px solid var(--border-color);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: var(--text-secondary);\\n  font-size: 0.875rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .value.dominant[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .value.confidence[_ngcontent-%COMP%] {\\n  color: var(--success-color);\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.1);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.test-results[_ngcontent-%COMP%]   .results-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-top: 2rem;\\n}\\n@media (max-width: 768px) {\\n  .test-results[_ngcontent-%COMP%]   .results-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .personality-test-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .intro-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%], .loading-card[_ngcontent-%COMP%] {\\n    padding: 2rem;\\n  }\\n  .title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .question-text[_ngcontent-%COMP%] {\\n    font-size: 1.4rem !important;\\n  }\\n  .btn-answer[_ngcontent-%COMP%] {\\n    padding: 1rem 2rem !important;\\n    font-size: 1.1rem !important;\\n    min-width: 120px !important;\\n  }\\n  .profile-name[_ngcontent-%COMP%] {\\n    font-size: 1.5rem !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["PERSONALITY_QUESTIONS", "i0", "ɵɵelementStart", "ɵɵlistener", "PersonalityTestComponent_div_1_div_47_Template_button_click_1_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "PersonalityTestComponent_div_1_div_47_Template", "PersonalityTestComponent_div_1_Template_button_click_49_listener", "_r8", "ctx_r7", "startTest", "PersonalityTestComponent_div_1_Template_button_click_54_listener", "ctx_r9", "goToHome", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "selected<PERSON>ser", "name", "email", "ɵɵproperty", "currentUser", "PersonalityTestComponent_div_2_div_11_Template_button_click_5_listener", "_r12", "ctx_r11", "answerQuestion", "PersonalityTestComponent_div_2_div_11_Template_button_click_10_listener", "ctx_r13", "ɵɵtextInterpolate", "ctx_r10", "currentQuestion", "question", "isLoading", "PersonalityTestComponent_div_2_div_11_Template", "ɵɵtextInterpolate2", "ctx_r1", "currentQuestionNumber", "totalQuestions", "ɵɵpipeBind2", "progressPercentage", "ɵɵstyleProp", "ctx_r2", "responses", "length", "characteristic_r23", "recommendation_r24", "correction_r26", "PersonalityTestComponent_div_4_div_22_div_29_li_4_Template", "ctx_r22", "compatibilityResult", "corrections", "PersonalityTestComponent_div_4_div_22_span_23_Template", "PersonalityTestComponent_div_4_div_22_li_28_Template", "PersonalityTestComponent_div_4_div_22_div_29_Template", "ɵɵclassMap", "ctx_r16", "isCompatible", "irisType", "description", "compatibilityScore", "characteristics", "recommendations", "ɵɵclassProp", "ctx_r17", "isHighestScore", "getScorePercentage", "testSession", "scores", "flower", "jewel", "shaker", "stream", "getDominantFamily", "finalProfile", "confidenceScore", "ctx_r18", "id", "PersonalityTestComponent_div_4_button_40_Template_button_click_0_listener", "_r28", "ctx_r27", "PersonalityTestComponent_div_4_div_15_Template", "PersonalityTestComponent_div_4_div_16_Template", "PersonalityTestComponent_div_4_div_22_Template", "PersonalityTestComponent_div_4_div_23_Template", "PersonalityTestComponent_div_4_div_31_Template", "PersonalityTestComponent_div_4_Template_button_click_33_listener", "_r30", "ctx_r29", "restartTest", "PersonalityTestComponent_div_4_Template_button_click_38_listener", "ctx_r31", "PersonalityTestComponent_div_4_button_40_Template", "ctx_r3", "primaryClass", "toLowerCase", "replace", "isIntermediate", "completedAt", "PersonalityTestComponent", "constructor", "personalityTestService", "irisCompatibilityService", "router", "questions", "currentQuestionIndex", "isTestStarted", "isTestCompleted", "showResults", "currentSessionId", "staticUsers", "ngOnInit", "currentUserData", "localStorage", "getItem", "JSON", "parse", "initializeTest", "createTestSession", "selectUser", "user", "Date", "now", "Math", "random", "toString", "substr", "startedAt", "console", "log", "answer", "response", "questionId", "timestamp", "push", "saveIndividualResponse", "subscribe", "next", "success", "warn", "error", "completeTest", "setTimeout", "processTestResults", "results", "profile", "checkCompatibility", "sessionStats", "totalResponses", "completionRate", "averageResponseTime", "calculateAverageResponseTime", "userId", "toISOString", "saveSessionStats", "saveTestSession", "sessionId", "showResultsWithDelay", "totalTime", "i", "timeDiff", "getTime", "navigate", "removeItem", "family", "currentScore", "maxScore", "max", "score", "round", "families", "dominant", "reduce", "prev", "current", "ɵɵdirectiveInject", "i1", "PersonalityTestService", "i2", "IrisCompatibilityService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "PersonalityTestComponent_Template", "rf", "ctx", "PersonalityTestComponent_div_1_Template", "PersonalityTestComponent_div_2_Template", "PersonalityTestComponent_div_3_Template", "PersonalityTestComponent_div_4_Template"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\personality-test\\personality-test.component.ts", "D:\\ProjetPfa\\pfa\\pfa\\src\\app\\personality-test\\personality-test.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { PersonalityTestService } from '../services/personality-test.service';\nimport { IrisCompatibilityService, CompatibilityResult } from '../services/iris-compatibility.service';\nimport {\n  Question,\n  UserResponse,\n  TestSession,\n  PersonalityProfile,\n  PERSONALITY_QUESTIONS\n} from '../models/personality-test.model';\n\n@Component({\n  selector: 'app-personality-test',\n  templateUrl: './personality-test.component.html',\n  styleUrls: ['./personality-test.component.scss']\n})\nexport class PersonalityTestComponent implements OnInit {\n  // Données du test\n  questions: Question[] = PERSONALITY_QUESTIONS;\n  currentQuestionIndex: number = 0;\n  responses: UserResponse[] = [];\n  testSession: TestSession | null = null;\n\n  // État du composant\n  isTestStarted: boolean = false;\n  isTestCompleted: boolean = false;\n  isLoading: boolean = false;\n  showResults: boolean = false;\n  currentSessionId: string = '';\n\n  // Comptes statiques pour les tests\n  staticUsers = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      description: 'Profil créatif et émotionnel'\n    },\n    {\n      id: 2,\n      name: 'Jean Martin',\n      email: '<EMAIL>',\n      description: 'Profil analytique et structuré'\n    },\n    {\n      id: 3,\n      name: 'Sophie Leroy',\n      email: '<EMAIL>',\n      description: 'Profil dynamique et aventurier'\n    },\n    {\n      id: 4,\n      name: 'Pierre Moreau',\n      email: '<EMAIL>',\n      description: 'Profil paisible et réfléchi'\n    },\n    {\n      id: 5,\n      name: 'Emma Bernard',\n      email: '<EMAIL>',\n      description: 'Profil mixte créatif-analytique'\n    }\n  ];\n\n  selectedUser = this.staticUsers[0]; // Utilisateur par défaut\n  currentUser: any = null; // Utilisateur connecté\n\n  // Résultats\n  finalProfile: PersonalityProfile | null = null;\n  compatibilityResult: CompatibilityResult | null = null;\n\n  constructor(\n    private personalityTestService: PersonalityTestService,\n    private irisCompatibilityService: IrisCompatibilityService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Récupérer l'utilisateur connecté\n    const currentUserData = localStorage.getItem('currentUser');\n    if (currentUserData) {\n      this.currentUser = JSON.parse(currentUserData);\n      // Utiliser l'utilisateur connecté pour le test\n      this.selectedUser = {\n        id: 0,\n        name: this.currentUser.name,\n        email: this.currentUser.email,\n        description: 'Utilisateur connecté'\n      };\n    }\n    this.initializeTest();\n  }\n\n  /**\n   * Initialise le test\n   */\n  initializeTest(): void {\n    this.testSession = this.personalityTestService.createTestSession(\n      this.selectedUser.name,\n      this.selectedUser.email\n    );\n  }\n\n  /**\n   * Sélectionne un utilisateur de test\n   */\n  selectUser(user: any): void {\n    this.selectedUser = user;\n    this.initializeTest();\n  }\n\n  /**\n   * Démarre le test\n   */\n  startTest(): void {\n    this.isTestStarted = true;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n    if (this.testSession) {\n      this.testSession.startedAt = new Date();\n      this.testSession.id = this.currentSessionId;\n    }\n\n    console.log('🚀 Test démarré - Session ID:', this.currentSessionId);\n  }\n\n  /**\n   * Traite la réponse à une question\n   */\n  answerQuestion(answer: boolean): void {\n    if (!this.testSession) return;\n\n    const currentQuestion = this.questions[this.currentQuestionIndex];\n    const response: UserResponse = {\n      questionId: currentQuestion.id,\n      answer: answer,\n      timestamp: new Date()\n    };\n\n    this.responses.push(response);\n    this.testSession.responses = this.responses;\n\n    // Sauvegarder la réponse individuelle en temps réel\n    if (this.currentUser && this.currentSessionId) {\n      this.personalityTestService.saveIndividualResponse(\n        this.currentUser.email,\n        this.currentSessionId,\n        response\n      ).subscribe({\n        next: (success) => {\n          if (success) {\n            console.log(`✅ Réponse ${currentQuestion.id} sauvegardée`);\n          } else {\n            console.warn(`⚠️ Erreur sauvegarde réponse ${currentQuestion.id}`);\n          }\n        },\n        error: (error) => {\n          console.error(`❌ Erreur réponse ${currentQuestion.id}:`, error);\n        }\n      });\n    }\n\n    // Passer à la question suivante ou terminer le test\n    if (this.currentQuestionIndex < this.questions.length - 1) {\n      this.currentQuestionIndex++;\n    } else {\n      this.completeTest();\n    }\n  }\n\n  /**\n   * Termine le test et calcule les résultats\n   */\n  completeTest(): void {\n    if (!this.testSession) return;\n\n    this.isLoading = true;\n    console.log('🔄 Début de l\\'analyse des résultats...');\n\n    // Simuler un délai d'analyse réaliste (2-3 secondes)\n    setTimeout(() => {\n      this.processTestResults();\n    }, 2500);\n  }\n\n  /**\n   * Traite les résultats du test\n   */\n  private processTestResults(): void {\n    if (!this.testSession) return;\n\n    try {\n      console.log('📊 Calcul des scores avec', this.responses.length, 'réponses');\n\n      // Calculer les résultats\n      const results = this.personalityTestService.processTestResults(this.responses);\n      console.log('✅ Résultats calculés:', results);\n\n      // Mettre à jour la session\n      this.testSession.scores = results.scores;\n      this.testSession.finalProfile = results.profile;\n      this.testSession.completedAt = new Date();\n\n      this.finalProfile = results.profile;\n\n      // Vérifier la compatibilité avec l'iris\n      this.compatibilityResult = this.irisCompatibilityService.checkCompatibility(\n        results.profile,\n        this.selectedUser.email\n      );\n      console.log('🔍 Compatibilité calculée:', this.compatibilityResult);\n\n      // Sauvegarder les statistiques de session\n      const sessionStats = {\n        totalQuestions: this.questions.length,\n        totalResponses: this.responses.length,\n        completionRate: (this.responses.length / this.questions.length) * 100,\n        averageResponseTime: this.calculateAverageResponseTime(),\n        scores: results.scores,\n        profile: results.profile,\n        userId: this.currentUser?.email,\n        completedAt: new Date().toISOString()\n      };\n\n      // Sauvegarder les statistiques\n      if (this.currentSessionId) {\n        this.personalityTestService.saveSessionStats(this.currentSessionId, sessionStats).subscribe({\n          next: (success) => {\n            console.log('📊 Statistiques de session sauvegardées:', success);\n          },\n          error: (error) => {\n            console.error('❌ Erreur sauvegarde stats:', error);\n          }\n        });\n      }\n\n      // Sauvegarder dans Firebase\n      this.personalityTestService.saveTestSession(this.testSession).subscribe({\n        next: (sessionId) => {\n          console.log('✅ Test sauvegardé avec l\\'ID:', sessionId);\n          this.testSession!.id = sessionId;\n          this.showResultsWithDelay();\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors de la sauvegarde:', error);\n          this.showResultsWithDelay();\n        }\n      });\n\n    } catch (error) {\n      console.error('❌ Erreur lors du traitement des résultats:', error);\n      this.showResultsWithDelay();\n    }\n  }\n\n  /**\n   * Affiche les résultats avec un délai pour une transition fluide\n   */\n  private showResultsWithDelay(): void {\n    setTimeout(() => {\n      this.isLoading = false;\n      this.isTestCompleted = true;\n      this.showResults = true;\n      console.log('🎉 Résultats affichés !');\n    }, 500);\n  }\n\n  /**\n   * Calcule le temps de réponse moyen\n   */\n  private calculateAverageResponseTime(): number {\n    if (this.responses.length === 0) return 0;\n\n    let totalTime = 0;\n    for (let i = 1; i < this.responses.length; i++) {\n      const timeDiff = this.responses[i].timestamp.getTime() - this.responses[i-1].timestamp.getTime();\n      totalTime += timeDiff;\n    }\n\n    return totalTime / (this.responses.length - 1);\n  }\n\n  /**\n   * Redémarre le test\n   */\n  restartTest(): void {\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.showResults = false;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.finalProfile = null;\n    this.compatibilityResult = null;\n    this.currentSessionId = '';\n    this.initializeTest();\n  }\n\n  /**\n   * Retourne à l'accueil\n   */\n  goToHome(): void {\n    this.router.navigate(['/accueil']);\n  }\n\n  /**\n   * Déconnexion de l'utilisateur\n   */\n  logout(): void {\n    localStorage.removeItem('currentUser');\n    this.router.navigate(['/login']);\n  }\n\n  /**\n   * Obtient la question actuelle\n   */\n  get currentQuestion(): Question | null {\n    return this.questions[this.currentQuestionIndex] || null;\n  }\n\n  /**\n   * Obtient le pourcentage de progression\n   */\n  get progressPercentage(): number {\n    return ((this.currentQuestionIndex + 1) / this.questions.length) * 100;\n  }\n\n  /**\n   * Obtient le numéro de la question actuelle\n   */\n  get currentQuestionNumber(): number {\n    return this.currentQuestionIndex + 1;\n  }\n\n  /**\n   * Obtient le nombre total de questions\n   */\n  get totalQuestions(): number {\n    return this.questions.length;\n  }\n\n  /**\n   * Vérifie si un score est le plus élevé\n   */\n  isHighestScore(family: string): boolean {\n    if (!this.testSession?.scores) return false;\n\n    const scores = this.testSession.scores;\n    const currentScore = scores[family as keyof typeof scores] as number;\n    const maxScore = Math.max(scores.flower, scores.jewel, scores.shaker, scores.stream);\n\n    return currentScore === maxScore && maxScore > 0;\n  }\n\n  /**\n   * Calcule le pourcentage d'un score\n   */\n  getScorePercentage(family: string): number {\n    if (!this.testSession?.scores) return 0;\n\n    const scores = this.testSession.scores;\n    const score = scores[family as keyof typeof scores] as number;\n\n    return Math.round((score / 4) * 100);\n  }\n\n  /**\n   * Obtient la famille dominante\n   */\n  getDominantFamily(): string {\n    if (!this.testSession?.scores) return 'Aucune';\n\n    const scores = this.testSession.scores;\n    const families = [\n      { name: '🌸 Flower', score: scores.flower },\n      { name: '💎 Jewel', score: scores.jewel },\n      { name: '⚡ Shaker', score: scores.shaker },\n      { name: '🌊 Stream', score: scores.stream }\n    ];\n\n    const dominant = families.reduce((prev, current) =>\n      current.score > prev.score ? current : prev\n    );\n\n    return dominant.name;\n  }\n}\n", "<div class=\"personality-test-container\">\n  <!-- Page d'accueil du test -->\n  <div class=\"test-intro\" *ngIf=\"!isTestStarted && !showResults\">\n    <div class=\"intro-card\">\n      <div class=\"intro-header\">\n        <h1 class=\"title\">Test de Personnalité</h1>\n        <div class=\"divider\"></div>\n        <p class=\"subtitle\">Découvrez votre profil psychotechnique</p>\n      </div>\n\n      <div class=\"intro-content\">\n        <div class=\"test-info\">\n          <div class=\"info-item\">\n            <span class=\"icon\">📝</span>\n            <div class=\"info-text\">\n              <h3>32 Questions</h3>\n              <p>Questions ciblées pour analyser votre personnalité</p>\n            </div>\n          </div>\n\n          <div class=\"info-item\">\n            <span class=\"icon\">⏱️</span>\n            <div class=\"info-text\">\n              <h3>5-10 Minutes</h3>\n              <p>Temps estimé pour compléter le test</p>\n            </div>\n          </div>\n\n          <div class=\"info-item\">\n            <span class=\"icon\">🎯</span>\n            <div class=\"info-text\">\n              <h3>4 Profils Principaux</h3>\n              <p>Flower, Jewel, Shaker, Stream + profils intermédiaires</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"user-info\">\n          <h3>Informations du test</h3>\n          <p><strong>Nom:</strong> {{ selectedUser.name }}</p>\n          <p><strong>Email:</strong> {{ selectedUser.email }}</p>\n          <p class=\"note\">Les résultats seront sauvegardés dans la base de données PFA1</p>\n\n          <div class=\"user-actions\" *ngIf=\"currentUser\">\n            <button class=\"btn btn-logout\" (click)=\"logout()\">\n              <span>Se déconnecter</span>\n              <span class=\"icon\">🚪</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"intro-actions\">\n        <button class=\"btn btn-primary\" (click)=\"startTest()\">\n          <span>Commencer le Test</span>\n          <span class=\"icon\">→</span>\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"goToHome()\">\n          Retour à l'accueil\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Interface du test -->\n  <div class=\"test-interface\" *ngIf=\"isTestStarted && !showResults\">\n    <div class=\"test-card\">\n      <!-- Barre de progression -->\n      <div class=\"progress-section\">\n        <div class=\"progress-info\">\n          <span class=\"question-counter\">Question {{ currentQuestionNumber }} sur {{ totalQuestions }}</span>\n          <span class=\"progress-percentage\">{{ progressPercentage | number:'1.0-0' }}%</span>\n        </div>\n        <div class=\"progress-bar\">\n          <div class=\"progress-fill\" [style.width.%]=\"progressPercentage\"></div>\n        </div>\n      </div>\n\n      <!-- Question actuelle -->\n      <div class=\"question-section\" *ngIf=\"currentQuestion\">\n        <div class=\"question-content\">\n          <h2 class=\"question-text\">{{ currentQuestion.question }}</h2>\n        </div>\n\n        <div class=\"answer-buttons\">\n          <button\n            class=\"btn btn-answer btn-yes\"\n            (click)=\"answerQuestion(true)\"\n            [disabled]=\"isLoading\">\n            <span class=\"answer-icon\">✓</span>\n            <span>Oui</span>\n          </button>\n\n          <button\n            class=\"btn btn-answer btn-no\"\n            (click)=\"answerQuestion(false)\"\n            [disabled]=\"isLoading\">\n            <span class=\"answer-icon\">✗</span>\n            <span>Non</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Écran de chargement -->\n  <div class=\"loading-screen\" *ngIf=\"isLoading\">\n    <div class=\"loading-card\">\n      <div class=\"loading-spinner\"></div>\n      <h2>Analyse de votre profil...</h2>\n      <p>Calcul des scores et détermination de votre personnalité</p>\n      <div class=\"loading-steps\">\n        <div class=\"step active\">✓ Réponses collectées ({{ responses.length }}/32)</div>\n        <div class=\"step active\">🔄 Calcul des scores par famille</div>\n        <div class=\"step active\">🎯 Détermination du profil principal</div>\n        <div class=\"step active\">🔍 Vérification compatibilité iris</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Résultats du test -->\n  <div class=\"test-results\" *ngIf=\"showResults && finalProfile\">\n    <div class=\"results-card\">\n      <div class=\"results-header\">\n        <h1 class=\"title\">Votre Profil de Personnalité</h1>\n        <div class=\"divider\"></div>\n      </div>\n\n      <div class=\"profile-summary\">\n        <div class=\"profile-badge\" [class]=\"'profile-' + finalProfile.primaryClass.toLowerCase().replace('-', '')\">\n          <h2 class=\"profile-name\">{{ finalProfile.primaryClass }}</h2>\n          <div class=\"confidence-score\">\n            <span class=\"score-label\">Score de confiance</span>\n            <span class=\"score-value\">{{ finalProfile.confidenceScore }}%</span>\n          </div>\n        </div>\n\n        <div class=\"profile-type\" *ngIf=\"finalProfile.isIntermediate\">\n          <span class=\"type-badge intermediate\">Profil Intermédiaire</span>\n        </div>\n        <div class=\"profile-type\" *ngIf=\"!finalProfile.isIntermediate\">\n          <span class=\"type-badge primary\">Profil Principal</span>\n        </div>\n      </div>\n\n      <div class=\"profile-description\">\n        <h3>Description de votre profil</h3>\n        <p>{{ finalProfile.description }}</p>\n      </div>\n\n      <!-- Compatibilité avec l'iris -->\n      <div class=\"iris-compatibility\" *ngIf=\"compatibilityResult\">\n        <h3>Compatibilité avec votre Iris</h3>\n\n        <div class=\"compatibility-summary\" [class]=\"compatibilityResult.isCompatible ? 'compatible' : 'incompatible'\">\n          <div class=\"compatibility-header\">\n            <div class=\"iris-info\">\n              <h4>{{ compatibilityResult.irisType.name }}</h4>\n              <p>{{ compatibilityResult.irisType.description }}</p>\n            </div>\n            <div class=\"compatibility-score\">\n              <div class=\"score-circle\" [class]=\"compatibilityResult.isCompatible ? 'high-score' : 'low-score'\">\n                <span class=\"score-value\">{{ compatibilityResult.compatibilityScore }}%</span>\n                <span class=\"score-label\">Compatibilité</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"compatibility-status\">\n            <span class=\"status-badge\" [class]=\"compatibilityResult.isCompatible ? 'compatible' : 'incompatible'\">\n              {{ compatibilityResult.isCompatible ? '✓ Compatible' : '⚠ Incompatible' }}\n            </span>\n          </div>\n        </div>\n\n        <div class=\"iris-characteristics\">\n          <h4>Caractéristiques de votre iris</h4>\n          <div class=\"characteristics-list\">\n            <span *ngFor=\"let characteristic of compatibilityResult.irisType.characteristics\"\n                  class=\"characteristic-tag\">\n              {{ characteristic }}\n            </span>\n          </div>\n        </div>\n\n        <div class=\"recommendations\">\n          <h4>Recommandations</h4>\n          <ul class=\"recommendations-list\">\n            <li *ngFor=\"let recommendation of compatibilityResult.recommendations\">\n              {{ recommendation }}\n            </li>\n          </ul>\n        </div>\n\n        <div class=\"corrections\" *ngIf=\"!compatibilityResult.isCompatible\">\n          <h4>Corrections suggérées</h4>\n          <ul class=\"corrections-list\">\n            <li *ngFor=\"let correction of compatibilityResult.corrections\">\n              {{ correction }}\n            </li>\n          </ul>\n        </div>\n      </div>\n\n      <div class=\"detailed-scores\" *ngIf=\"testSession\">\n        <div class=\"scores-header\">\n          <h3>Analyse détaillée des résultats</h3>\n          <p class=\"scores-description\">Répartition de vos réponses par famille de personnalité</p>\n        </div>\n\n        <div class=\"scores-grid\">\n          <div class=\"score-item\" [class.highest-score]=\"isHighestScore('flower')\">\n            <div class=\"score-header\">\n              <span class=\"score-label\">Flower</span>\n              <span class=\"score-percentage\">{{ getScorePercentage('flower') }}%</span>\n            </div>\n            <div class=\"score-bar\">\n              <div class=\"score-fill flower\" [style.width.%]=\"(testSession.scores.flower / 4) * 100\"></div>\n            </div>\n            <div class=\"score-details\">\n              <span class=\"score-value\">{{ testSession.scores.flower }}/4</span>\n              <span class=\"score-description\">Émotionnel, Créatif</span>\n            </div>\n          </div>\n\n          <div class=\"score-item\" [class.highest-score]=\"isHighestScore('jewel')\">\n            <div class=\"score-header\">\n              <span class=\"score-label\">Jewel</span>\n              <span class=\"score-percentage\">{{ getScorePercentage('jewel') }}%</span>\n            </div>\n            <div class=\"score-bar\">\n              <div class=\"score-fill jewel\" [style.width.%]=\"(testSession.scores.jewel / 4) * 100\"></div>\n            </div>\n            <div class=\"score-details\">\n              <span class=\"score-value\">{{ testSession.scores.jewel }}/4</span>\n              <span class=\"score-description\">Analytique, Méthodique</span>\n            </div>\n          </div>\n\n          <div class=\"score-item\" [class.highest-score]=\"isHighestScore('shaker')\">\n            <div class=\"score-header\">\n              <span class=\"score-label\">Shaker</span>\n              <span class=\"score-percentage\">{{ getScorePercentage('shaker') }}%</span>\n            </div>\n            <div class=\"score-bar\">\n              <div class=\"score-fill shaker\" [style.width.%]=\"(testSession.scores.shaker / 4) * 100\"></div>\n            </div>\n            <div class=\"score-details\">\n              <span class=\"score-value\">{{ testSession.scores.shaker }}/4</span>\n              <span class=\"score-description\">Dynamique, Aventurier</span>\n            </div>\n          </div>\n\n          <div class=\"score-item\" [class.highest-score]=\"isHighestScore('stream')\">\n            <div class=\"score-header\">\n              <span class=\"score-label\">Stream</span>\n              <span class=\"score-percentage\">{{ getScorePercentage('stream') }}%</span>\n            </div>\n            <div class=\"score-bar\">\n              <div class=\"score-fill stream\" [style.width.%]=\"(testSession.scores.stream / 4) * 100\"></div>\n            </div>\n            <div class=\"score-details\">\n              <span class=\"score-value\">{{ testSession.scores.stream }}/4</span>\n              <span class=\"score-description\">Paisible, Réfléchi</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"score-summary\">\n          <div class=\"summary-item\">\n            <span class=\"label\">Profil dominant</span>\n            <span class=\"value dominant\">{{ getDominantFamily() }}</span>\n          </div>\n          <div class=\"summary-item\">\n            <span class=\"label\">Niveau de confiance</span>\n            <span class=\"value confidence\">{{ finalProfile?.confidenceScore }}%</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"test-info-summary\">\n        <div class=\"info-row\">\n          <span class=\"label\">Test complété le:</span>\n          <span class=\"value\">{{ testSession?.completedAt | date:'dd/MM/yyyy à HH:mm' }}</span>\n        </div>\n        <div class=\"info-row\" *ngIf=\"testSession?.id\">\n          <span class=\"label\">ID de session:</span>\n          <span class=\"value\">{{ testSession?.id }}</span>\n        </div>\n      </div>\n\n      <div class=\"results-actions\">\n        <button class=\"btn btn-primary\" (click)=\"restartTest()\">\n          <span>Refaire le Test</span>\n          <span class=\"icon\">🔄</span>\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"goToHome()\">\n          Retour à l'accueil\n        </button>\n        <button class=\"btn btn-logout\" (click)=\"logout()\" *ngIf=\"currentUser\">\n          <span>Se déconnecter</span>\n          <span class=\"icon\">🚪</span>\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAIA,SAKEA,qBAAqB,QAChB,kCAAkC;;;;;;;;;ICiC/BC,EAAA,CAAAC,cAAA,cAA8C;IACbD,EAAA,CAAAE,UAAA,mBAAAC,uEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IAC/CT,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,0BAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC3BX,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAU,MAAA,mBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IA5CxCX,EAAA,CAAAC,cAAA,aAA+D;IAGvCD,EAAA,CAAAU,MAAA,gCAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC3CX,EAAA,CAAAY,SAAA,aAA2B;IAC3BZ,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAU,MAAA,kDAAsC;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAGhEX,EAAA,CAAAC,cAAA,cAA2B;IAGFD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACrBX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,oEAAkD;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAI7DX,EAAA,CAAAC,cAAA,eAAuB;IACFD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACrBX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,qDAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAI9CX,EAAA,CAAAC,cAAA,eAAuB;IACFD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,4BAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC7BX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,mEAAsD;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAKnEX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,4BAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC7BX,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAU,MAAA,YAAI;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAACX,EAAA,CAAAU,MAAA,IAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACpDX,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAACX,EAAA,CAAAU,MAAA,IAAwB;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACvDX,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAU,MAAA,oFAA6D;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEjFX,EAAA,CAAAa,UAAA,KAAAC,8CAAA,kBAKM;IACRd,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,eAA2B;IACOD,EAAA,CAAAE,UAAA,mBAAAa,iEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAS,MAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IACnDlB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,yBAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC9BX,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAU,MAAA,cAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE7BX,EAAA,CAAAC,cAAA,kBAAuD;IAArBD,EAAA,CAAAE,UAAA,mBAAAiB,iEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAI,MAAA,GAAApB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAY,MAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACpDrB,EAAA,CAAAU,MAAA,iCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IApBkBX,EAAA,CAAAsB,SAAA,IAAuB;IAAvBtB,EAAA,CAAAuB,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAC,IAAA,KAAuB;IACrB1B,EAAA,CAAAsB,SAAA,GAAwB;IAAxBtB,EAAA,CAAAuB,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAE,KAAA,KAAwB;IAGxB3B,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA4B,UAAA,SAAAJ,MAAA,CAAAK,WAAA,CAAiB;;;;;;IAoChD7B,EAAA,CAAAC,cAAA,cAAsD;IAExBD,EAAA,CAAAU,MAAA,GAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAG/DX,EAAA,CAAAC,cAAA,cAA4B;IAGxBD,EAAA,CAAAE,UAAA,mBAAA4B,uEAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwB,OAAA,CAAAC,cAAA,CAAe,IAAI,CAAC;IAAA,EAAC;IAE9BjC,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAU,MAAA,aAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAClCX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,UAAG;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGlBX,EAAA,CAAAC,cAAA,kBAGyB;IADvBD,EAAA,CAAAE,UAAA,mBAAAgC,wEAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAI,OAAA,GAAAnC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA2B,OAAA,CAAAF,cAAA,CAAe,KAAK,CAAC;IAAA,EAAC;IAE/BjC,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,cAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAClCX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,WAAG;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAjBQX,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAoC,iBAAA,CAAAC,OAAA,CAAAC,eAAA,CAAAC,QAAA,CAA8B;IAOtDvC,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAA4B,UAAA,aAAAS,OAAA,CAAAG,SAAA,CAAsB;IAQtBxC,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAA4B,UAAA,aAAAS,OAAA,CAAAG,SAAA,CAAsB;;;;;IA/BhCxC,EAAA,CAAAC,cAAA,cAAkE;IAK3BD,EAAA,CAAAU,MAAA,GAA6D;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACnGX,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAU,MAAA,GAA0C;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAErFX,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAY,SAAA,eAAsE;IACxEZ,EAAA,CAAAW,YAAA,EAAM;IAIRX,EAAA,CAAAa,UAAA,KAAA4B,8CAAA,mBAsBM;IACRzC,EAAA,CAAAW,YAAA,EAAM;;;;IAhC+BX,EAAA,CAAAsB,SAAA,GAA6D;IAA7DtB,EAAA,CAAA0C,kBAAA,cAAAC,MAAA,CAAAC,qBAAA,WAAAD,MAAA,CAAAE,cAAA,KAA6D;IAC1D7C,EAAA,CAAAsB,SAAA,GAA0C;IAA1CtB,EAAA,CAAAuB,kBAAA,KAAAvB,EAAA,CAAA8C,WAAA,OAAAH,MAAA,CAAAI,kBAAA,gBAA0C;IAGjD/C,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAgD,WAAA,UAAAL,MAAA,CAAAI,kBAAA,MAAoC;IAKpC/C,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAA4B,UAAA,SAAAe,MAAA,CAAAL,eAAA,CAAqB;;;;;IA2BxDtC,EAAA,CAAAC,cAAA,cAA8C;IAE1CD,EAAA,CAAAY,SAAA,cAAmC;IACnCZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,iCAA0B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACnCX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,yEAAwD;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAC/DX,EAAA,CAAAC,cAAA,cAA2B;IACAD,EAAA,CAAAU,MAAA,GAAiD;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAChFX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,kDAAgC;IAAAV,EAAA,CAAAW,YAAA,EAAM;IAC/DX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,2DAAoC;IAAAV,EAAA,CAAAW,YAAA,EAAM;IACnEX,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAU,MAAA,8DAAkC;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;IAHxCX,EAAA,CAAAsB,SAAA,GAAiD;IAAjDtB,EAAA,CAAAuB,kBAAA,2CAAA0B,MAAA,CAAAC,SAAA,CAAAC,MAAA,SAAiD;;;;;IAyB1EnD,EAAA,CAAAC,cAAA,cAA8D;IACtBD,EAAA,CAAAU,MAAA,gCAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAEnEX,EAAA,CAAAC,cAAA,cAA+D;IAC5BD,EAAA,CAAAU,MAAA,uBAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAqCtDX,EAAA,CAAAC,cAAA,eACiC;IAC/BD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAA6B,kBAAA,MACF;;;;;IAOApD,EAAA,CAAAC,cAAA,SAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAA8B,kBAAA,MACF;;;;;IAOArD,EAAA,CAAAC,cAAA,SAA+D;IAC7DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAA+B,cAAA,MACF;;;;;IALJtD,EAAA,CAAAC,cAAA,cAAmE;IAC7DD,EAAA,CAAAU,MAAA,sCAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC9BX,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAa,UAAA,IAAA0C,0DAAA,iBAEK;IACPvD,EAAA,CAAAW,YAAA,EAAK;;;;IAHwBX,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA4B,UAAA,YAAA4B,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAkC;;;;;IA9CnE1D,EAAA,CAAAC,cAAA,cAA4D;IACtDD,EAAA,CAAAU,MAAA,yCAA6B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAEtCX,EAAA,CAAAC,cAAA,cAA8G;IAGpGD,EAAA,CAAAU,MAAA,GAAuC;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAChDX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAA8C;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEvDX,EAAA,CAAAC,cAAA,eAAiC;IAEHD,EAAA,CAAAU,MAAA,IAA6C;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC9EX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,0BAAa;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAKpDX,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAIXX,EAAA,CAAAC,cAAA,eAAkC;IAC5BD,EAAA,CAAAU,MAAA,2CAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACvCX,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAa,UAAA,KAAA8C,sDAAA,mBAGO;IACT3D,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACxBX,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAa,UAAA,KAAA+C,oDAAA,iBAEK;IACP5D,EAAA,CAAAW,YAAA,EAAK;IAGPX,EAAA,CAAAa,UAAA,KAAAgD,qDAAA,kBAOM;IACR7D,EAAA,CAAAW,YAAA,EAAM;;;;IAhD+BX,EAAA,CAAAsB,SAAA,GAA0E;IAA1EtB,EAAA,CAAA8D,UAAA,CAAAC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,iCAA0E;IAGnGhE,EAAA,CAAAsB,SAAA,GAAuC;IAAvCtB,EAAA,CAAAoC,iBAAA,CAAA2B,OAAA,CAAAN,mBAAA,CAAAQ,QAAA,CAAAvC,IAAA,CAAuC;IACxC1B,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAoC,iBAAA,CAAA2B,OAAA,CAAAN,mBAAA,CAAAQ,QAAA,CAAAC,WAAA,CAA8C;IAGvBlE,EAAA,CAAAsB,SAAA,GAAuE;IAAvEtB,EAAA,CAAA8D,UAAA,CAAAC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,8BAAuE;IACrEhE,EAAA,CAAAsB,SAAA,GAA6C;IAA7CtB,EAAA,CAAAuB,kBAAA,KAAAwC,OAAA,CAAAN,mBAAA,CAAAU,kBAAA,MAA6C;IAOhDnE,EAAA,CAAAsB,SAAA,GAA0E;IAA1EtB,EAAA,CAAA8D,UAAA,CAAAC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,iCAA0E;IACnGhE,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAAwC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,oDACF;IAOiChE,EAAA,CAAAsB,SAAA,GAA+C;IAA/CtB,EAAA,CAAA4B,UAAA,YAAAmC,OAAA,CAAAN,mBAAA,CAAAQ,QAAA,CAAAG,eAAA,CAA+C;IAUjDpE,EAAA,CAAAsB,SAAA,GAAsC;IAAtCtB,EAAA,CAAA4B,UAAA,YAAAmC,OAAA,CAAAN,mBAAA,CAAAY,eAAA,CAAsC;IAM/CrE,EAAA,CAAAsB,SAAA,GAAuC;IAAvCtB,EAAA,CAAA4B,UAAA,UAAAmC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,CAAuC;;;;;IAUnEhE,EAAA,CAAAC,cAAA,cAAiD;IAEzCD,EAAA,CAAAU,MAAA,qDAA+B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACxCX,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAU,MAAA,6EAAuD;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAG3FX,EAAA,CAAAC,cAAA,cAAyB;IAGOD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACvCX,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE3EX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA6F;IAC/FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,IAAiC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAClEX,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAU,MAAA,qCAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAI9DX,EAAA,CAAAC,cAAA,eAAwE;IAE1CD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACtCX,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAU,MAAA,IAAkC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE1EX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA2F;IAC7FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,IAAgC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACjEX,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAU,MAAA,mCAAsB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAIjEX,EAAA,CAAAC,cAAA,eAAyE;IAE3CD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACvCX,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE3EX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA6F;IAC/FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,IAAiC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAClEX,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAU,MAAA,6BAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAIhEX,EAAA,CAAAC,cAAA,eAAyE;IAE3CD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACvCX,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE3EX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA6F;IAC/FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA2B;IACCD,EAAA,CAAAU,MAAA,IAAiC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAClEX,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAU,MAAA,oCAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAK/DX,EAAA,CAAAC,cAAA,gBAA2B;IAEHD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC1CX,EAAA,CAAAC,cAAA,iBAA6B;IAAAD,EAAA,CAAAU,MAAA,IAAyB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE/DX,EAAA,CAAAC,cAAA,gBAA0B;IACJD,EAAA,CAAAU,MAAA,2BAAmB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC9CX,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAU,MAAA,IAAoC;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAhEpDX,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAsE,WAAA,kBAAAC,OAAA,CAAAC,cAAA,WAAgD;IAGrCxE,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAE,kBAAA,gBAAmC;IAGnCzE,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAgD,WAAA,UAAAuB,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAC,MAAA,gBAAuD;IAG5D5E,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAC,MAAA,OAAiC;IAKvC5E,EAAA,CAAAsB,SAAA,GAA+C;IAA/CtB,EAAA,CAAAsE,WAAA,kBAAAC,OAAA,CAAAC,cAAA,UAA+C;IAGpCxE,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAE,kBAAA,eAAkC;IAGnCzE,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAgD,WAAA,UAAAuB,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAE,KAAA,gBAAsD;IAG1D7E,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAE,KAAA,OAAgC;IAKtC7E,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAsE,WAAA,kBAAAC,OAAA,CAAAC,cAAA,WAAgD;IAGrCxE,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAE,kBAAA,gBAAmC;IAGnCzE,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAgD,WAAA,UAAAuB,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAG,MAAA,gBAAuD;IAG5D9E,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAG,MAAA,OAAiC;IAKvC9E,EAAA,CAAAsB,SAAA,GAAgD;IAAhDtB,EAAA,CAAAsE,WAAA,kBAAAC,OAAA,CAAAC,cAAA,WAAgD;IAGrCxE,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAE,kBAAA,gBAAmC;IAGnCzE,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAgD,WAAA,UAAAuB,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAI,MAAA,gBAAuD;IAG5D/E,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAG,WAAA,CAAAC,MAAA,CAAAI,MAAA,OAAiC;IAShC/E,EAAA,CAAAsB,SAAA,GAAyB;IAAzBtB,EAAA,CAAAoC,iBAAA,CAAAmC,OAAA,CAAAS,iBAAA,GAAyB;IAIvBhF,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAuB,kBAAA,KAAAgD,OAAA,CAAAU,YAAA,kBAAAV,OAAA,CAAAU,YAAA,CAAAC,eAAA,MAAoC;;;;;IAUvElF,EAAA,CAAAC,cAAA,cAA8C;IACxBD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACzCX,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA5BX,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAAoC,iBAAA,CAAA+C,OAAA,CAAAT,WAAA,kBAAAS,OAAA,CAAAT,WAAA,CAAAU,EAAA,CAAqB;;;;;;IAY3CpF,EAAA,CAAAC,cAAA,iBAAsE;IAAvCD,EAAA,CAAAE,UAAA,mBAAAmF,0EAAA;MAAArF,EAAA,CAAAI,aAAA,CAAAkF,IAAA;MAAA,MAAAC,OAAA,GAAAvF,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+E,OAAA,CAAA9E,MAAA,EAAQ;IAAA,EAAC;IAC/CT,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,0BAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC3BX,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAU,MAAA,mBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IApLpCX,EAAA,CAAAC,cAAA,cAA8D;IAGtCD,EAAA,CAAAU,MAAA,wCAA4B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACnDX,EAAA,CAAAY,SAAA,aAA2B;IAC7BZ,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,cAA6B;IAEAD,EAAA,CAAAU,MAAA,GAA+B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC7DX,EAAA,CAAAC,cAAA,eAA8B;IACFD,EAAA,CAAAU,MAAA,0BAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACnDX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAIxEX,EAAA,CAAAa,UAAA,KAAA2E,8CAAA,kBAEM;IACNxF,EAAA,CAAAa,UAAA,KAAA4E,8CAAA,kBAEM;IACRzF,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,eAAiC;IAC3BD,EAAA,CAAAU,MAAA,mCAA2B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACpCX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,IAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAIvCX,EAAA,CAAAa,UAAA,KAAA6E,8CAAA,oBAmDM;IAEN1F,EAAA,CAAAa,UAAA,KAAA8E,8CAAA,oBA0EM;IAEN3F,EAAA,CAAAC,cAAA,eAA+B;IAEPD,EAAA,CAAAU,MAAA,mCAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5CX,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAU,MAAA,IAA0D;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAEvFX,EAAA,CAAAa,UAAA,KAAA+E,8CAAA,kBAGM;IACR5F,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,eAA6B;IACKD,EAAA,CAAAE,UAAA,mBAAA2F,iEAAA;MAAA7F,EAAA,CAAAI,aAAA,CAAA0F,IAAA;MAAA,MAAAC,OAAA,GAAA/F,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAuF,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACrDhG,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE9BX,EAAA,CAAAC,cAAA,kBAAuD;IAArBD,EAAA,CAAAE,UAAA,mBAAA+F,iEAAA;MAAAjG,EAAA,CAAAI,aAAA,CAAA0F,IAAA;MAAA,MAAAI,OAAA,GAAAlG,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA0F,OAAA,CAAA7E,QAAA,EAAU;IAAA,EAAC;IACpDrB,EAAA,CAAAU,MAAA,iCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAa,UAAA,KAAAsF,iDAAA,qBAGS;IACXnG,EAAA,CAAAW,YAAA,EAAM;;;;IA9KuBX,EAAA,CAAAsB,SAAA,GAA+E;IAA/EtB,EAAA,CAAA8D,UAAA,cAAAsC,MAAA,CAAAnB,YAAA,CAAAoB,YAAA,CAAAC,WAAA,GAAAC,OAAA,UAA+E;IAC/EvG,EAAA,CAAAsB,SAAA,GAA+B;IAA/BtB,EAAA,CAAAoC,iBAAA,CAAAgE,MAAA,CAAAnB,YAAA,CAAAoB,YAAA,CAA+B;IAG5BrG,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAAuB,kBAAA,KAAA6E,MAAA,CAAAnB,YAAA,CAAAC,eAAA,MAAmC;IAItClF,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAA4B,UAAA,SAAAwE,MAAA,CAAAnB,YAAA,CAAAuB,cAAA,CAAiC;IAGjCxG,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA4B,UAAA,UAAAwE,MAAA,CAAAnB,YAAA,CAAAuB,cAAA,CAAkC;IAO1DxG,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAoC,iBAAA,CAAAgE,MAAA,CAAAnB,YAAA,CAAAf,WAAA,CAA8B;IAIFlE,EAAA,CAAAsB,SAAA,GAAyB;IAAzBtB,EAAA,CAAA4B,UAAA,SAAAwE,MAAA,CAAA3C,mBAAA,CAAyB;IAqD5BzD,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA4B,UAAA,SAAAwE,MAAA,CAAA1B,WAAA,CAAiB;IA+EvB1E,EAAA,CAAAsB,SAAA,GAA0D;IAA1DtB,EAAA,CAAAoC,iBAAA,CAAApC,EAAA,CAAA8C,WAAA,SAAAsD,MAAA,CAAA1B,WAAA,kBAAA0B,MAAA,CAAA1B,WAAA,CAAA+B,WAAA,6BAA0D;IAEzDzG,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAA4B,UAAA,SAAAwE,MAAA,CAAA1B,WAAA,kBAAA0B,MAAA,CAAA1B,WAAA,CAAAU,EAAA,CAAqB;IAcOpF,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA4B,UAAA,SAAAwE,MAAA,CAAAvE,WAAA,CAAiB;;;AD1R5E,OAAM,MAAO6E,wBAAwB;EAuDnCC,YACUC,sBAA8C,EAC9CC,wBAAkD,EAClDC,MAAc;IAFd,KAAAF,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,MAAM,GAANA,MAAM;IAzDhB;IACA,KAAAC,SAAS,GAAehH,qBAAqB;IAC7C,KAAAiH,oBAAoB,GAAW,CAAC;IAChC,KAAA9D,SAAS,GAAmB,EAAE;IAC9B,KAAAwB,WAAW,GAAuB,IAAI;IAEtC;IACA,KAAAuC,aAAa,GAAY,KAAK;IAC9B,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAA1E,SAAS,GAAY,KAAK;IAC1B,KAAA2E,WAAW,GAAY,KAAK;IAC5B,KAAAC,gBAAgB,GAAW,EAAE;IAE7B;IACA,KAAAC,WAAW,GAAG,CACZ;MACEjC,EAAE,EAAE,CAAC;MACL1D,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BuC,WAAW,EAAE;KACd,EACD;MACEkB,EAAE,EAAE,CAAC;MACL1D,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,sBAAsB;MAC7BuC,WAAW,EAAE;KACd,EACD;MACEkB,EAAE,EAAE,CAAC;MACL1D,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BuC,WAAW,EAAE;KACd,EACD;MACEkB,EAAE,EAAE,CAAC;MACL1D,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,wBAAwB;MAC/BuC,WAAW,EAAE;KACd,EACD;MACEkB,EAAE,EAAE,CAAC;MACL1D,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BuC,WAAW,EAAE;KACd,CACF;IAED,KAAAzC,YAAY,GAAG,IAAI,CAAC4F,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,KAAAxF,WAAW,GAAQ,IAAI,CAAC,CAAC;IAEzB;IACA,KAAAoD,YAAY,GAA8B,IAAI;IAC9C,KAAAxB,mBAAmB,GAA+B,IAAI;EAMnD;EAEH6D,QAAQA,CAAA;IACN;IACA,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAC3D,IAAIF,eAAe,EAAE;MACnB,IAAI,CAAC1F,WAAW,GAAG6F,IAAI,CAACC,KAAK,CAACJ,eAAe,CAAC;MAC9C;MACA,IAAI,CAAC9F,YAAY,GAAG;QAClB2D,EAAE,EAAE,CAAC;QACL1D,IAAI,EAAE,IAAI,CAACG,WAAW,CAACH,IAAI;QAC3BC,KAAK,EAAE,IAAI,CAACE,WAAW,CAACF,KAAK;QAC7BuC,WAAW,EAAE;OACd;;IAEH,IAAI,CAAC0D,cAAc,EAAE;EACvB;EAEA;;;EAGAA,cAAcA,CAAA;IACZ,IAAI,CAAClD,WAAW,GAAG,IAAI,CAACkC,sBAAsB,CAACiB,iBAAiB,CAC9D,IAAI,CAACpG,YAAY,CAACC,IAAI,EACtB,IAAI,CAACD,YAAY,CAACE,KAAK,CACxB;EACH;EAEA;;;EAGAmG,UAAUA,CAACC,IAAS;IAClB,IAAI,CAACtG,YAAY,GAAGsG,IAAI;IACxB,IAAI,CAACH,cAAc,EAAE;EACvB;EAEA;;;EAGA1G,SAASA,CAAA;IACP,IAAI,CAAC+F,aAAa,GAAG,IAAI;IACzB,IAAI,CAACD,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAAC9D,SAAS,GAAG,EAAE;IACnB,IAAI,CAACkE,gBAAgB,GAAG,UAAU,GAAGY,IAAI,CAACC,GAAG,EAAE,GAAG,GAAG,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAE/F,IAAI,IAAI,CAAC3D,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC4D,SAAS,GAAG,IAAIN,IAAI,EAAE;MACvC,IAAI,CAACtD,WAAW,CAACU,EAAE,GAAG,IAAI,CAACgC,gBAAgB;;IAG7CmB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACpB,gBAAgB,CAAC;EACrE;EAEA;;;EAGAnF,cAAcA,CAACwG,MAAe;IAC5B,IAAI,CAAC,IAAI,CAAC/D,WAAW,EAAE;IAEvB,MAAMpC,eAAe,GAAG,IAAI,CAACyE,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC;IACjE,MAAM0B,QAAQ,GAAiB;MAC7BC,UAAU,EAAErG,eAAe,CAAC8C,EAAE;MAC9BqD,MAAM,EAAEA,MAAM;MACdG,SAAS,EAAE,IAAIZ,IAAI;KACpB;IAED,IAAI,CAAC9E,SAAS,CAAC2F,IAAI,CAACH,QAAQ,CAAC;IAC7B,IAAI,CAAChE,WAAW,CAACxB,SAAS,GAAG,IAAI,CAACA,SAAS;IAE3C;IACA,IAAI,IAAI,CAACrB,WAAW,IAAI,IAAI,CAACuF,gBAAgB,EAAE;MAC7C,IAAI,CAACR,sBAAsB,CAACkC,sBAAsB,CAChD,IAAI,CAACjH,WAAW,CAACF,KAAK,EACtB,IAAI,CAACyF,gBAAgB,EACrBsB,QAAQ,CACT,CAACK,SAAS,CAAC;QACVC,IAAI,EAAGC,OAAO,IAAI;UAChB,IAAIA,OAAO,EAAE;YACXV,OAAO,CAACC,GAAG,CAAC,aAAalG,eAAe,CAAC8C,EAAE,cAAc,CAAC;WAC3D,MAAM;YACLmD,OAAO,CAACW,IAAI,CAAC,gCAAgC5G,eAAe,CAAC8C,EAAE,EAAE,CAAC;;QAEtE,CAAC;QACD+D,KAAK,EAAGA,KAAK,IAAI;UACfZ,OAAO,CAACY,KAAK,CAAC,oBAAoB7G,eAAe,CAAC8C,EAAE,GAAG,EAAE+D,KAAK,CAAC;QACjE;OACD,CAAC;;IAGJ;IACA,IAAI,IAAI,CAACnC,oBAAoB,GAAG,IAAI,CAACD,SAAS,CAAC5D,MAAM,GAAG,CAAC,EAAE;MACzD,IAAI,CAAC6D,oBAAoB,EAAE;KAC5B,MAAM;MACL,IAAI,CAACoC,YAAY,EAAE;;EAEvB;EAEA;;;EAGAA,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC1E,WAAW,EAAE;IAEvB,IAAI,CAAClC,SAAS,GAAG,IAAI;IACrB+F,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACAa,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQA,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC5E,WAAW,EAAE;IAEvB,IAAI;MACF6D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACtF,SAAS,CAACC,MAAM,EAAE,UAAU,CAAC;MAE3E;MACA,MAAMoG,OAAO,GAAG,IAAI,CAAC3C,sBAAsB,CAAC0C,kBAAkB,CAAC,IAAI,CAACpG,SAAS,CAAC;MAC9EqF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEe,OAAO,CAAC;MAE7C;MACA,IAAI,CAAC7E,WAAW,CAACC,MAAM,GAAG4E,OAAO,CAAC5E,MAAM;MACxC,IAAI,CAACD,WAAW,CAACO,YAAY,GAAGsE,OAAO,CAACC,OAAO;MAC/C,IAAI,CAAC9E,WAAW,CAAC+B,WAAW,GAAG,IAAIuB,IAAI,EAAE;MAEzC,IAAI,CAAC/C,YAAY,GAAGsE,OAAO,CAACC,OAAO;MAEnC;MACA,IAAI,CAAC/F,mBAAmB,GAAG,IAAI,CAACoD,wBAAwB,CAAC4C,kBAAkB,CACzEF,OAAO,CAACC,OAAO,EACf,IAAI,CAAC/H,YAAY,CAACE,KAAK,CACxB;MACD4G,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC/E,mBAAmB,CAAC;MAEnE;MACA,MAAMiG,YAAY,GAAG;QACnB7G,cAAc,EAAE,IAAI,CAACkE,SAAS,CAAC5D,MAAM;QACrCwG,cAAc,EAAE,IAAI,CAACzG,SAAS,CAACC,MAAM;QACrCyG,cAAc,EAAG,IAAI,CAAC1G,SAAS,CAACC,MAAM,GAAG,IAAI,CAAC4D,SAAS,CAAC5D,MAAM,GAAI,GAAG;QACrE0G,mBAAmB,EAAE,IAAI,CAACC,4BAA4B,EAAE;QACxDnF,MAAM,EAAE4E,OAAO,CAAC5E,MAAM;QACtB6E,OAAO,EAAED,OAAO,CAACC,OAAO;QACxBO,MAAM,EAAE,IAAI,CAAClI,WAAW,EAAEF,KAAK;QAC/B8E,WAAW,EAAE,IAAIuB,IAAI,EAAE,CAACgC,WAAW;OACpC;MAED;MACA,IAAI,IAAI,CAAC5C,gBAAgB,EAAE;QACzB,IAAI,CAACR,sBAAsB,CAACqD,gBAAgB,CAAC,IAAI,CAAC7C,gBAAgB,EAAEsC,YAAY,CAAC,CAACX,SAAS,CAAC;UAC1FC,IAAI,EAAGC,OAAO,IAAI;YAChBV,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAES,OAAO,CAAC;UAClE,CAAC;UACDE,KAAK,EAAGA,KAAK,IAAI;YACfZ,OAAO,CAACY,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UACpD;SACD,CAAC;;MAGJ;MACA,IAAI,CAACvC,sBAAsB,CAACsD,eAAe,CAAC,IAAI,CAACxF,WAAW,CAAC,CAACqE,SAAS,CAAC;QACtEC,IAAI,EAAGmB,SAAS,IAAI;UAClB5B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE2B,SAAS,CAAC;UACvD,IAAI,CAACzF,WAAY,CAACU,EAAE,GAAG+E,SAAS;UAChC,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QACDjB,KAAK,EAAGA,KAAK,IAAI;UACfZ,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,IAAI,CAACiB,oBAAoB,EAAE;QAC7B;OACD,CAAC;KAEH,CAAC,OAAOjB,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,IAAI,CAACiB,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGQA,oBAAoBA,CAAA;IAC1Bf,UAAU,CAAC,MAAK;MACd,IAAI,CAAC7G,SAAS,GAAG,KAAK;MACtB,IAAI,CAAC0E,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACC,WAAW,GAAG,IAAI;MACvBoB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACxC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQsB,4BAA4BA,CAAA;IAClC,IAAI,IAAI,CAAC5G,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAEzC,IAAIkH,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACpH,SAAS,CAACC,MAAM,EAAEmH,CAAC,EAAE,EAAE;MAC9C,MAAMC,QAAQ,GAAG,IAAI,CAACrH,SAAS,CAACoH,CAAC,CAAC,CAAC1B,SAAS,CAAC4B,OAAO,EAAE,GAAG,IAAI,CAACtH,SAAS,CAACoH,CAAC,GAAC,CAAC,CAAC,CAAC1B,SAAS,CAAC4B,OAAO,EAAE;MAChGH,SAAS,IAAIE,QAAQ;;IAGvB,OAAOF,SAAS,IAAI,IAAI,CAACnH,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC;EAChD;EAEA;;;EAGA6C,WAAWA,CAAA;IACT,IAAI,CAACiB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACH,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAAC9D,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC+B,YAAY,GAAG,IAAI;IACxB,IAAI,CAACxB,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAAC2D,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACQ,cAAc,EAAE;EACvB;EAEA;;;EAGAvG,QAAQA,CAAA;IACN,IAAI,CAACyF,MAAM,CAAC2D,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGAhK,MAAMA,CAAA;IACJ+G,YAAY,CAACkD,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAAC5D,MAAM,CAAC2D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEA;;;EAGA,IAAInI,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACyE,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC,IAAI,IAAI;EAC1D;EAEA;;;EAGA,IAAIjE,kBAAkBA,CAAA;IACpB,OAAQ,CAAC,IAAI,CAACiE,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAACD,SAAS,CAAC5D,MAAM,GAAI,GAAG;EACxE;EAEA;;;EAGA,IAAIP,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACoE,oBAAoB,GAAG,CAAC;EACtC;EAEA;;;EAGA,IAAInE,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACkE,SAAS,CAAC5D,MAAM;EAC9B;EAEA;;;EAGAqB,cAAcA,CAACmG,MAAc;IAC3B,IAAI,CAAC,IAAI,CAACjG,WAAW,EAAEC,MAAM,EAAE,OAAO,KAAK;IAE3C,MAAMA,MAAM,GAAG,IAAI,CAACD,WAAW,CAACC,MAAM;IACtC,MAAMiG,YAAY,GAAGjG,MAAM,CAACgG,MAA6B,CAAW;IACpE,MAAME,QAAQ,GAAG3C,IAAI,CAAC4C,GAAG,CAACnG,MAAM,CAACC,MAAM,EAAED,MAAM,CAACE,KAAK,EAAEF,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,MAAM,CAAC;IAEpF,OAAO6F,YAAY,KAAKC,QAAQ,IAAIA,QAAQ,GAAG,CAAC;EAClD;EAEA;;;EAGApG,kBAAkBA,CAACkG,MAAc;IAC/B,IAAI,CAAC,IAAI,CAACjG,WAAW,EAAEC,MAAM,EAAE,OAAO,CAAC;IAEvC,MAAMA,MAAM,GAAG,IAAI,CAACD,WAAW,CAACC,MAAM;IACtC,MAAMoG,KAAK,GAAGpG,MAAM,CAACgG,MAA6B,CAAW;IAE7D,OAAOzC,IAAI,CAAC8C,KAAK,CAAED,KAAK,GAAG,CAAC,GAAI,GAAG,CAAC;EACtC;EAEA;;;EAGA/F,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACN,WAAW,EAAEC,MAAM,EAAE,OAAO,QAAQ;IAE9C,MAAMA,MAAM,GAAG,IAAI,CAACD,WAAW,CAACC,MAAM;IACtC,MAAMsG,QAAQ,GAAG,CACf;MAAEvJ,IAAI,EAAE,WAAW;MAAEqJ,KAAK,EAAEpG,MAAM,CAACC;IAAM,CAAE,EAC3C;MAAElD,IAAI,EAAE,UAAU;MAAEqJ,KAAK,EAAEpG,MAAM,CAACE;IAAK,CAAE,EACzC;MAAEnD,IAAI,EAAE,UAAU;MAAEqJ,KAAK,EAAEpG,MAAM,CAACG;IAAM,CAAE,EAC1C;MAAEpD,IAAI,EAAE,WAAW;MAAEqJ,KAAK,EAAEpG,MAAM,CAACI;IAAM,CAAE,CAC5C;IAED,MAAMmG,QAAQ,GAAGD,QAAQ,CAACE,MAAM,CAAC,CAACC,IAAI,EAAEC,OAAO,KAC7CA,OAAO,CAACN,KAAK,GAAGK,IAAI,CAACL,KAAK,GAAGM,OAAO,GAAGD,IAAI,CAC5C;IAED,OAAOF,QAAQ,CAACxJ,IAAI;EACtB;;;uBAlXWgF,wBAAwB,EAAA1G,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAC,sBAAA,GAAAxL,EAAA,CAAAsL,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAA1L,EAAA,CAAAsL,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBlF,wBAAwB;MAAAmF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBrCnM,EAAA,CAAAC,cAAA,aAAwC;UAEtCD,EAAA,CAAAa,UAAA,IAAAwL,uCAAA,kBA4DM;UAGNrM,EAAA,CAAAa,UAAA,IAAAyL,uCAAA,kBAsCM;UAGNtM,EAAA,CAAAa,UAAA,IAAA0L,uCAAA,kBAYM;UAGNvM,EAAA,CAAAa,UAAA,IAAA2L,uCAAA,mBAwLM;UACRxM,EAAA,CAAAW,YAAA,EAAM;;;UAhTqBX,EAAA,CAAAsB,SAAA,GAAoC;UAApCtB,EAAA,CAAA4B,UAAA,UAAAwK,GAAA,CAAAnF,aAAA,KAAAmF,GAAA,CAAAjF,WAAA,CAAoC;UA+DhCnH,EAAA,CAAAsB,SAAA,GAAmC;UAAnCtB,EAAA,CAAA4B,UAAA,SAAAwK,GAAA,CAAAnF,aAAA,KAAAmF,GAAA,CAAAjF,WAAA,CAAmC;UAyCnCnH,EAAA,CAAAsB,SAAA,GAAe;UAAftB,EAAA,CAAA4B,UAAA,SAAAwK,GAAA,CAAA5J,SAAA,CAAe;UAejBxC,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAA4B,UAAA,SAAAwK,GAAA,CAAAjF,WAAA,IAAAiF,GAAA,CAAAnH,YAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}