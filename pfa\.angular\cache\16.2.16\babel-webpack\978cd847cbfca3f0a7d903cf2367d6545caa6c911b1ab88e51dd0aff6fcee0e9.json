{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'accueil',\n  component: AccueilComponent\n}, {\n  path: 'suivantacc',\n  component: SuivantaccComponent\n}, {\n  path: 'typeiris',\n  component: TypeirisComponent\n}, {\n  path: 'iris2',\n  component: Iris2Component\n}, {\n  path: 'iris-diversity',\n  component: IrisDiversityComponent\n}, {\n  path: 'fleur',\n  component: FleurComponent\n}, {\n  path: 'bijou',\n  component: BijouComponent\n}, {\n  path: 'flux',\n  component: FluxComponent\n}, {\n  path: 'shaker',\n  component: ShakerComponent\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'signup',\n  component: SignupComponent\n}, {\n  path: '',\n  redirectTo: '/accueil',\n  pathMatch: 'full'\n} // Rediriger vers la page d'accueil par défaut\n];\n\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AccueilComponent", "SuivantaccComponent", "TypeirisComponent", "Iris2Component", "FleurComponent", "BijouComponent", "FluxComponent", "ShakerComponent", "LoginComponent", "SignupComponent", "IrisDiversityComponent", "routes", "path", "component", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\n\nconst routes: Routes = [\n  { path: 'accueil', component: AccueilComponent },  // Page d'accueil\n  { path: 'suivantacc', component: SuivantaccComponent },  // Page après avoir cliqué sur \"Commencer\"\n  { path: 'typeiris', component: TypeirisComponent },  // Page après avoir cliqué sur \"Suivant\"\n  { path: 'iris2' , component: Iris2Component},\n  { path: 'iris-diversity', component: IrisDiversityComponent },  // Page de diversité des iris\n  { path: 'fleur', component: FleurComponent },\n  { path: 'bijou', component: BijouComponent},\n  { path: 'flux', component: FluxComponent },\n  { path: 'shaker', component: ShakerComponent },\n  { path: 'login', component: LoginComponent },  // Page de connexion\n  { path: 'signup', component: SignupComponent },  // Page d'inscription\n  { path: '', redirectTo: '/accueil', pathMatch: 'full' },  // Rediriger vers la page d'accueil par défaut\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,2CAA2C;;;AAGlF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEb;AAAgB,CAAE,EAChD;EAAEY,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAEZ;AAAmB,CAAE,EACtD;EAAEW,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEX;AAAiB,CAAE,EAClD;EAAEU,IAAI,EAAE,OAAO;EAAGC,SAAS,EAAEV;AAAc,CAAC,EAC5C;EAAES,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAEH;AAAsB,CAAE,EAC7D;EAAEE,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAET;AAAc,CAAE,EAC5C;EAAEQ,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAER;AAAc,CAAC,EAC3C;EAAEO,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEP;AAAa,CAAE,EAC1C;EAAEM,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEN;AAAe,CAAE,EAC9C;EAAEK,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEL;AAAc,CAAE,EAC5C;EAAEI,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEJ;AAAe,CAAE,EAC9C;EAAEG,IAAI,EAAE,EAAE;EAAEE,UAAU,EAAE,UAAU;EAAEC,SAAS,EAAE;AAAM,CAAE,CAAG;AAAA,CAC3D;;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBjB,YAAY,CAACkB,OAAO,CAACN,MAAM,CAAC,EAC5BZ,YAAY;IAAA;EAAA;;;2EAEXiB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAApB,YAAA;IAAAqB,OAAA,GAFjBrB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}