{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class SuivantaccComponent {\n  static {\n    this.ɵfac = function SuivantaccComponent_Factory(t) {\n      return new (t || SuivantaccComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuivantaccComponent,\n      selectors: [[\"app-suivantacc\"]],\n      decls: 20,\n      vars: 0,\n      consts: [[1, \"home-container\"], [1, \"header\"], [1, \"logo\"], [1, \"nav\"], [\"href\", \"#\"], [1, \"content\"], [1, \"images\"], [\"src\", \"assets/iris2.png\", \"alt\", \"Iris 2\"], [1, \"text\"], [1, \"intro\"], [1, \"description\"], [1, \"next-button-container\"], [\"routerLink\", \"/typeiris\", 1, \"next-button\"]],\n      template: function SuivantaccComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"h1\", 2)(3, \"span\");\n          i0.ɵɵtext(4, \"Iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \"Lock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"nav\", 3)(7, \"a\", 4);\n          i0.ɵɵtext(8, \"accueil\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 5)(10, \"div\", 6);\n          i0.ɵɵelement(11, \"img\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"p\", 9);\n          i0.ɵɵtext(14, \"D\\u00E9couvrir l\\u2019unicit\\u00E9 de chacun \\u00E0 travers ce que r\\u00E9v\\u00E8le l\\u2019iris.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\", 10);\n          i0.ɵɵtext(16, \" L\\u2019iris est une structure biom\\u00E9trique complexe et unique \\u00E0 chaque individu. Ses motifs, distincts et inimitables, peuvent fournir des informations pr\\u00E9cieuses sur les caract\\u00E9ristiques physiologiques, psychologiques et comportementales d\\u2019une personne. L\\u2019analyse de la structure irienne permet d\\u2019identifier des traits de personnalit\\u00E9, des pr\\u00E9dispositions h\\u00E9r\\u00E9ditaires, ainsi que d\\u2019\\u00E9ventuelles implications sur la sant\\u00E9 et les relations interpersonnelles. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 11)(18, \"button\", 12);\n          i0.ɵɵtext(19, \"Suivant\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".home-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  padding: 20px;\\n  background: linear-gradient(to right, #e0d8f4, #fddde6);\\n  font-family: \\\"Georgia\\\", serif;\\n  color: #1a1a1a;\\n}\\n.home-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-bottom: 20px;\\n}\\n.home-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: bold;\\n}\\n.home-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-style: italic;\\n}\\n.home-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #3b3b3b;\\n  text-decoration: none;\\n}\\n.home-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 40px;\\n  align-items: center;\\n}\\n.home-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 20px;\\n}\\n.home-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%]   .iris[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  object-fit: cover;\\n}\\n.home-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%]   .iris.iris-large[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n}\\n.home-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .images[_ngcontent-%COMP%]   .iris.iris-small[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n}\\n.home-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 300px;\\n}\\n.home-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]   .intro[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-style: italic;\\n  margin-bottom: 20px;\\n}\\n.home-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  line-height: 1.8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SuivantaccComponent", "selectors", "decls", "vars", "consts", "template", "SuivantaccComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\suivantacc\\suivantacc.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\suivantacc\\suivantacc.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-suivantacc',\n  templateUrl: './suivantacc.component.html',\n  styleUrls: ['./suivantacc.component.scss']\n})\nexport class SuivantaccComponent {\n\n}\n", "<div class=\"home-container\">\n    <header class=\"header\">\n      <h1 class=\"logo\"><span>Iris</span>Lock</h1>\n      <nav class=\"nav\">\n        <a href=\"#\">accueil</a>\n      </nav>\n    </header>\n  \n    <div class=\"content\">\n      <div class=\"images\">\n       \n        <img src=\"assets/iris2.png\" alt=\"Iris 2\" />\n      </div>\n  \n      <div class=\"text\">\n        <p class=\"intro\">Découvrir l’unicité de chacun à travers ce que révèle l’iris.</p>\n        <p class=\"description\">\n          L’iris est une structure biométrique complexe et unique à chaque individu.\n          Ses motifs, distincts et inimitables, peuvent fournir des informations précieuses\n          sur les caractéristiques physiologiques, psychologiques et comportementales d’une personne.\n          L’analyse de la structure irienne permet d’identifier des traits de personnalité,\n          des prédispositions héréditaires, ainsi que d’éventuelles implications sur la santé\n          et les relations interpersonnelles.\n        </p>\n        <div class=\"next-button-container\">\n            <button class=\"next-button\" routerLink=\"/typeiris\">Suivant</button>\n          </div>\n      </div>\n    </div>\n  </div>\n  \n"], "mappings": ";;AAOA,OAAM,MAAOA,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPhCE,EAAA,CAAAC,cAAA,aAA4B;UAECD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3CH,EAAA,CAAAC,cAAA,aAAiB;UACHD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI3BH,EAAA,CAAAC,cAAA,aAAqB;UAGjBD,EAAA,CAAAI,SAAA,cAA2C;UAC7CJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAkB;UACCD,EAAA,CAAAE,MAAA,wGAA6D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClFH,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,MAAA,uhBAMF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,eAAmC;UACoBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}