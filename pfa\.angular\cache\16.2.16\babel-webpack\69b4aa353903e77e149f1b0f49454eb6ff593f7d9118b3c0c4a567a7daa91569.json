{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SignupComponent } from './signup/signup.component';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'signup',\n  component: SignupComponent\n}, {\n  path: '',\n  redirectTo: 'signup',\n  pathMatch: 'full'\n}, {\n  path: 'accueil',\n  component: AccueilComponent\n}, {\n  path: '',\n  redirectTo: '/accueil',\n  pathMatch: 'full'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SignupComponent", "AccueilComponent", "routes", "path", "component", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { SignupComponent } from './signup/signup.component';\nimport { AccueilComponent } from './accueil/accueil.component';\n\nconst routes: Routes = [\n  { path: 'signup', component: SignupComponent },\n  { path: '', redirectTo: 'signup', pathMatch: 'full' } ,// Redirection par défaut\n  { path: 'accueil', component: AccueilComponent }, // 👈 nouvelle route\n  { path: '', redirectTo: '/accueil', pathMatch: 'full' }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,6BAA6B;;;AAE9D,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEJ;AAAe,CAAE,EAC9C;EAAEG,IAAI,EAAE,EAAE;EAAEE,UAAU,EAAE,QAAQ;EAAEC,SAAS,EAAE;AAAM,CAAE,EACrD;EAAEH,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEH;AAAgB,CAAE,EAChD;EAAEE,IAAI,EAAE,EAAE;EAAEE,UAAU,EAAE,UAAU;EAAEC,SAAS,EAAE;AAAM,CAAE,CACxD;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBR,YAAY,CAACS,OAAO,CAACN,MAAM,CAAC,EAC5BH,YAAY;IAAA;EAAA;;;2EAEXQ,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFjBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}