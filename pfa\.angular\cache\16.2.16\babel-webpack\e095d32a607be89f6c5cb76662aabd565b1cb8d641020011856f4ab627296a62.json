{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class TypeirisComponent {\n  static {\n    this.ɵfac = function TypeirisComponent_Factory(t) {\n      return new (t || TypeirisComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TypeirisComponent,\n      selectors: [[\"app-typeiris\"]],\n      decls: 63,\n      vars: 0,\n      consts: [[1, \"page-container\", \"typeiris\"], [1, \"container\"], [1, \"header\"], [1, \"logo\"], [1, \"nav\"], [\"routerLink\", \"/suivantacc\"], [1, \"content\"], [1, \"card\", \"main-card\"], [1, \"card-inner\"], [1, \"card-front\"], [1, \"text-content\"], [1, \"title\"], [1, \"divider\"], [1, \"description\"], [1, \"image-container\"], [\"src\", \"assets/iris3.png\", \"alt\", \"Iris illustration\", 1, \"main-image\"], [1, \"image-decoration\"], [1, \"info-cards\"], [1, \"info-card\"], [1, \"icon\"], [1, \"action-container\"], [\"routerLink\", \"/iris2\", 1, \"btn\", \"next-btn\"], [\"routerLink\", \"/iris-diversity\", 1, \"btn\", \"diversity-btn\"]],\n      template: function TypeirisComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"header\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Iris\");\n          i0.ɵɵelementStart(5, \"span\");\n          i0.ɵɵtext(6, \"Lock\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"nav\", 4)(8, \"a\", 5);\n          i0.ɵɵtext(9, \"Retour\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"div\", 7)(12, \"div\", 8)(13, \"div\", 9)(14, \"div\", 10)(15, \"h2\", 11);\n          i0.ɵɵtext(16, \"Les Types d'Iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"div\", 12);\n          i0.ɵɵelementStart(18, \"p\", 13);\n          i0.ɵɵtext(19, \" Bien que nous soyons g\\u00E9n\\u00E9ralement domin\\u00E9s par un seul motif d'iris, ou parfois par une combinaison de deux, chacun de nous porte en lui l'essence des quatre types fondamentaux. Ces caract\\u00E9ristiques ne sont pas fig\\u00E9es : elles \\u00E9voluent et se modulent selon notre rang de naissance, notre parcours de vie et nos habitudes quotidiennes. Par exemple, la place occup\\u00E9e dans la fratrie influence la mani\\u00E8re dont s'exprime le type d'iris, tout comme notre \\u00E9tat de sant\\u00E9 et nos activit\\u00E9s personnelles. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 14);\n          i0.ɵɵelement(21, \"img\", 15)(22, \"div\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\", 18)(25, \"div\", 19);\n          i0.ɵɵtext(26, \"\\uD83E\\uDDEC\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"h3\");\n          i0.ɵɵtext(28, \"Unicit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"p\");\n          i0.ɵɵtext(30, \"Chaque iris est unique et r\\u00E9v\\u00E8le des aspects sp\\u00E9cifiques de notre personnalit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 18)(32, \"div\", 19);\n          i0.ɵɵtext(33, \"\\uD83D\\uDD04\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"h3\");\n          i0.ɵɵtext(35, \"\\u00C9volution\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"p\");\n          i0.ɵɵtext(37, \"Les caract\\u00E9ristiques de l'iris \\u00E9voluent avec notre parcours de vie\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 18)(39, \"div\", 19);\n          i0.ɵɵtext(40, \"\\uD83E\\uDDE9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"h3\");\n          i0.ɵɵtext(42, \"Combinaison\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"Nous portons en nous les quatre types fondamentaux en proportions variables\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 18)(46, \"div\", 19);\n          i0.ɵɵtext(47, \"\\uD83D\\uDC68\\u200D\\uD83D\\uDC69\\u200D\\uD83D\\uDC67\\u200D\\uD83D\\uDC66\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"h3\");\n          i0.ɵɵtext(49, \"Influence Familiale\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"p\");\n          i0.ɵɵtext(51, \"Notre rang dans la fratrie influence l'expression de notre type d'iris\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"div\", 20)(53, \"a\", 21)(54, \"span\");\n          i0.ɵɵtext(55, \"D\\u00E9couvrir les types\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"span\", 19);\n          i0.ɵɵtext(57, \"\\u2192\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"a\", 22)(59, \"span\");\n          i0.ɵɵtext(60, \"Explorer la diversit\\u00E9 des iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"span\", 19);\n          i0.ɵɵtext(62, \"\\u2192\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".page-container.typeiris[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f8f9fe 0%, #f0f2fd 100%);\\n  padding: 20px 0;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 0;\\n  margin-bottom: 40px;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 400;\\n  font-style: italic;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #555;\\n  text-decoration: none;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background-color: #8a56ff;\\n  transition: width 0.3s ease;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #8a56ff;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:after {\\n  width: 100%;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 1000px;\\n  margin-bottom: 40px;\\n  perspective: 1000px;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  transform-style: preserve-3d;\\n  transition: transform 0.6s;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%] {\\n  width: 100%;\\n  backface-visibility: hidden;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  padding: 40px;\\n  gap: 40px;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%] {\\n  flex: 1.5;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2.2rem;\\n  color: #333;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 3px;\\n  background: linear-gradient(to right, #8a56ff, #7b4bfe);\\n  margin-bottom: 20px;\\n  border-radius: 3px;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  line-height: 1.7;\\n  color: #666;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n  width: 250px;\\n  height: 250px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\\n  z-index: 2;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 280px;\\n  height: 280px;\\n  border-radius: 50%;\\n  border: 2px dashed #8a56ff;\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_rotate 20s linear infinite;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .info-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));\\n  gap: 25px;\\n  width: 100%;\\n  max-width: 1000px;\\n  margin-bottom: 40px;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 25px;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 2.2rem;\\n  margin-bottom: 15px;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #333;\\n  margin-bottom: 10px;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n  line-height: 1.6;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n  margin-top: 20px;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 15px 35px;\\n  color: white;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%] {\\n  background: #8a56ff;\\n  box-shadow: 0 10px 25px rgba(138, 86, 255, 0.3);\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 15px 35px rgba(138, 86, 255, 0.4);\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .diversity-btn[_ngcontent-%COMP%] {\\n  background: #7b4bfe;\\n  box-shadow: 0 10px 25px rgba(123, 75, 254, 0.3);\\n}\\n.page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .diversity-btn[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 15px 35px rgba(123, 75, 254, 0.4);\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n    padding: 30px;\\n  }\\n  .page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%] {\\n    margin-top: 30px;\\n    text-align: center;\\n  }\\n  .page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n    margin: 0 auto 20px;\\n  }\\n  .page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    width: 200px;\\n    height: 200px;\\n  }\\n  .page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n    width: 230px;\\n    height: 230px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-container.typeiris[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n    text-align: center;\\n  }\\n  .page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n  }\\n  .page-container.typeiris[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .info-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\\n    gap: 20px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["TypeirisComponent", "selectors", "decls", "vars", "consts", "template", "TypeirisComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\typeiris\\typeiris.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\typeiris\\typeiris.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-typeiris',\n  templateUrl: './typeiris.component.html',\n  styleUrls: ['./typeiris.component.scss']\n})\nexport class TypeirisComponent {\n\n}\n", "<div class=\"page-container typeiris\">\n  <div class=\"container\">\n    <header class=\"header\">\n      <h1 class=\"logo\">Iris<span>Lock</span></h1>\n      <nav class=\"nav\">\n        <a routerLink=\"/suivantacc\">Retour</a>\n      </nav>\n    </header>\n\n    <div class=\"content\">\n      <div class=\"card main-card\">\n        <div class=\"card-inner\">\n          <div class=\"card-front\">\n            <div class=\"text-content\">\n              <h2 class=\"title\">Les Types d'Iris</h2>\n              <div class=\"divider\"></div>\n              <p class=\"description\">\n                Bien que nous soyons généralement dominés par un seul motif d'iris, ou parfois par une combinaison de deux,\n                chacun de nous porte en lui l'essence des quatre types fondamentaux. Ces caractéristiques ne sont pas figées :\n                elles évoluent et se modulent selon notre rang de naissance, notre parcours de vie et nos habitudes quotidiennes.\n                Par exemple, la place occupée dans la fratrie influence la manière dont s'exprime le type d'iris, tout comme notre\n                état de santé et nos activités personnelles.\n              </p>\n            </div>\n            <div class=\"image-container\">\n              <img src=\"assets/iris3.png\" alt=\"Iris illustration\" class=\"main-image\" />\n              <div class=\"image-decoration\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"info-cards\">\n        <div class=\"info-card\">\n          <div class=\"icon\">🧬</div>\n          <h3>Unicité</h3>\n          <p>Chaque iris est unique et révèle des aspects spécifiques de notre personnalité</p>\n        </div>\n        <div class=\"info-card\">\n          <div class=\"icon\">🔄</div>\n          <h3>Évolution</h3>\n          <p>Les caractéristiques de l'iris évoluent avec notre parcours de vie</p>\n        </div>\n        <div class=\"info-card\">\n          <div class=\"icon\">🧩</div>\n          <h3>Combinaison</h3>\n          <p>Nous portons en nous les quatre types fondamentaux en proportions variables</p>\n        </div>\n        <div class=\"info-card\">\n          <div class=\"icon\">👨‍👩‍👧‍👦</div>\n          <h3>Influence Familiale</h3>\n          <p>Notre rang dans la fratrie influence l'expression de notre type d'iris</p>\n        </div>\n      </div>\n\n      <div class=\"action-container\">\n        <a routerLink=\"/iris2\" class=\"btn next-btn\">\n          <span>Découvrir les types</span>\n          <span class=\"icon\">→</span>\n        </a>\n        <a routerLink=\"/iris-diversity\" class=\"btn diversity-btn\">\n          <span>Explorer la diversité des iris</span>\n          <span class=\"icon\">→</span>\n        </a>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP9BE,EAAA,CAAAC,cAAA,aAAqC;UAGdD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtCH,EAAA,CAAAC,cAAA,aAAiB;UACaD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI1CH,EAAA,CAAAC,cAAA,cAAqB;UAKOD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAI,SAAA,eAA2B;UAC3BJ,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAE,MAAA,6iBAKF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAI,SAAA,eAAyE;UAE3EJ,EAAA,CAAAG,YAAA,EAAM;UAKZH,EAAA,CAAAC,cAAA,eAAwB;UAEFD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,oBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,0GAA8E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEvFH,EAAA,CAAAC,cAAA,eAAuB;UACHD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,sBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,oFAAkE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3EH,EAAA,CAAAC,cAAA,eAAuB;UACHD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,mFAA2E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEpFH,EAAA,CAAAC,cAAA,eAAuB;UACHD,EAAA,CAAAE,MAAA,0EAAW;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACnCH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8EAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIjFH,EAAA,CAAAC,cAAA,eAA8B;UAEpBD,EAAA,CAAAE,MAAA,gCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAChCH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE7BH,EAAA,CAAAC,cAAA,aAA0D;UAClDD,EAAA,CAAAE,MAAA,2CAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}