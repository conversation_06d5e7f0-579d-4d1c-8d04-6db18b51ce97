{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { timer } from '../observable/timer';\nexport function repeat(countOrConfig) {\n  let count = Infinity;\n  let delay;\n  if (countOrConfig != null) {\n    if (typeof countOrConfig === 'object') {\n      ({\n        count = Infinity,\n        delay\n      } = countOrConfig);\n    } else {\n      count = countOrConfig;\n    }\n  }\n  return count <= 0 ? () => EMPTY : operate((source, subscriber) => {\n    let soFar = 0;\n    let sourceSub;\n    const resubscribe = () => {\n      sourceSub === null || sourceSub === void 0 ? void 0 : sourceSub.unsubscribe();\n      sourceSub = null;\n      if (delay != null) {\n        const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(soFar));\n        const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n          notifierSubscriber.unsubscribe();\n          subscribeToSource();\n        });\n        notifier.subscribe(notifierSubscriber);\n      } else {\n        subscribeToSource();\n      }\n    };\n    const subscribeToSource = () => {\n      let syncUnsub = false;\n      sourceSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, () => {\n        if (++soFar < count) {\n          if (sourceSub) {\n            resubscribe();\n          } else {\n            syncUnsub = true;\n          }\n        } else {\n          subscriber.complete();\n        }\n      }));\n      if (syncUnsub) {\n        resubscribe();\n      }\n    };\n    subscribeToSource();\n  });\n}", "map": {"version": 3, "names": ["EMPTY", "operate", "createOperatorSubscriber", "innerFrom", "timer", "repeat", "countOrConfig", "count", "Infinity", "delay", "source", "subscriber", "soFar", "sourceSub", "resubscribe", "unsubscribe", "notifier", "notifierSubscriber", "subscribeToSource", "subscribe", "syncUnsub", "undefined", "complete"], "sources": ["C:/pfa/node_modules/rxjs/dist/esm/internal/operators/repeat.js"], "sourcesContent": ["import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { timer } from '../observable/timer';\nexport function repeat(countOrConfig) {\n    let count = Infinity;\n    let delay;\n    if (countOrConfig != null) {\n        if (typeof countOrConfig === 'object') {\n            ({ count = Infinity, delay } = countOrConfig);\n        }\n        else {\n            count = countOrConfig;\n        }\n    }\n    return count <= 0\n        ? () => EMPTY\n        : operate((source, subscriber) => {\n            let soFar = 0;\n            let sourceSub;\n            const resubscribe = () => {\n                sourceSub === null || sourceSub === void 0 ? void 0 : sourceSub.unsubscribe();\n                sourceSub = null;\n                if (delay != null) {\n                    const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(soFar));\n                    const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n                        notifierSubscriber.unsubscribe();\n                        subscribeToSource();\n                    });\n                    notifier.subscribe(notifierSubscriber);\n                }\n                else {\n                    subscribeToSource();\n                }\n            };\n            const subscribeToSource = () => {\n                let syncUnsub = false;\n                sourceSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, () => {\n                    if (++soFar < count) {\n                        if (sourceSub) {\n                            resubscribe();\n                        }\n                        else {\n                            syncUnsub = true;\n                        }\n                    }\n                    else {\n                        subscriber.complete();\n                    }\n                }));\n                if (syncUnsub) {\n                    resubscribe();\n                }\n            };\n            subscribeToSource();\n        });\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,OAAO,SAASC,MAAMA,CAACC,aAAa,EAAE;EAClC,IAAIC,KAAK,GAAGC,QAAQ;EACpB,IAAIC,KAAK;EACT,IAAIH,aAAa,IAAI,IAAI,EAAE;IACvB,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;MACnC,CAAC;QAAEC,KAAK,GAAGC,QAAQ;QAAEC;MAAM,CAAC,GAAGH,aAAa;IAChD,CAAC,MACI;MACDC,KAAK,GAAGD,aAAa;IACzB;EACJ;EACA,OAAOC,KAAK,IAAI,CAAC,GACX,MAAMP,KAAK,GACXC,OAAO,CAAC,CAACS,MAAM,EAAEC,UAAU,KAAK;IAC9B,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,SAAS;IACb,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACtBD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,WAAW,CAAC,CAAC;MAC7EF,SAAS,GAAG,IAAI;MAChB,IAAIJ,KAAK,IAAI,IAAI,EAAE;QACf,MAAMO,QAAQ,GAAG,OAAOP,KAAK,KAAK,QAAQ,GAAGL,KAAK,CAACK,KAAK,CAAC,GAAGN,SAAS,CAACM,KAAK,CAACG,KAAK,CAAC,CAAC;QACnF,MAAMK,kBAAkB,GAAGf,wBAAwB,CAACS,UAAU,EAAE,MAAM;UAClEM,kBAAkB,CAACF,WAAW,CAAC,CAAC;UAChCG,iBAAiB,CAAC,CAAC;QACvB,CAAC,CAAC;QACFF,QAAQ,CAACG,SAAS,CAACF,kBAAkB,CAAC;MAC1C,CAAC,MACI;QACDC,iBAAiB,CAAC,CAAC;MACvB;IACJ,CAAC;IACD,MAAMA,iBAAiB,GAAGA,CAAA,KAAM;MAC5B,IAAIE,SAAS,GAAG,KAAK;MACrBP,SAAS,GAAGH,MAAM,CAACS,SAAS,CAACjB,wBAAwB,CAACS,UAAU,EAAEU,SAAS,EAAE,MAAM;QAC/E,IAAI,EAAET,KAAK,GAAGL,KAAK,EAAE;UACjB,IAAIM,SAAS,EAAE;YACXC,WAAW,CAAC,CAAC;UACjB,CAAC,MACI;YACDM,SAAS,GAAG,IAAI;UACpB;QACJ,CAAC,MACI;UACDT,UAAU,CAACW,QAAQ,CAAC,CAAC;QACzB;MACJ,CAAC,CAAC,CAAC;MACH,IAAIF,SAAS,EAAE;QACXN,WAAW,CAAC,CAAC;MACjB;IACJ,CAAC;IACDI,iBAAiB,CAAC,CAAC;EACvB,CAAC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}