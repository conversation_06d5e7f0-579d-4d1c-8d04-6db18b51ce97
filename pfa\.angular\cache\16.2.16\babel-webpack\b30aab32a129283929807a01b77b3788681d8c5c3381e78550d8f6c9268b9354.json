{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class Iris2Component {\n  static {\n    this.ɵfac = function Iris2Component_Factory(t) {\n      return new (t || Iris2Component)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Iris2Component,\n      selectors: [[\"app-iris2\"]],\n      decls: 12,\n      vars: 0,\n      consts: [[1, \"iris-wrapper\"], [1, \"iris-container\"], [\"src\", \"assets/iristype.png\", \"alt\", \"Iris Types\"], [1, \"labels\"], [1, \"label\", \"fleur\"], [1, \"label\", \"bijou\"], [1, \"label\", \"flux\"], [1, \"label\", \"shaker\"]],\n      template: function Iris2Component_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"Fleur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"span\", 5);\n          i0.ɵɵtext(7, \"Bijou\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"span\", 6);\n          i0.ɵɵtext(9, \"Flux\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"span\", 7);\n          i0.ɵɵtext(11, \"Shaker\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Iris2Component", "selectors", "decls", "vars", "consts", "template", "Iris2Component_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris2\\iris2.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris2\\iris2.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-iris2',\n  templateUrl: './iris2.component.html',\n  styleUrls: ['./iris2.component.scss']\n})\nexport class Iris2Component {\n\n}\n", "<div class=\"iris-wrapper\">\n    <div class=\"iris-container\">\n      <img src=\"assets/iristype.png\" alt=\"Iris Types\" />\n      <div class=\"labels\">\n        <span class=\"label fleur\">Fleur</span>\n        <span class=\"label bijou\">Bijou</span>\n        <span class=\"label flux\">Flux</span>\n        <span class=\"label shaker\">Shaker</span>\n      </div>\n    </div>\n  </div>\n  "], "mappings": ";AAOA,OAAM,MAAOA,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3BE,EAAA,CAAAC,cAAA,aAA0B;UAEpBD,EAAA,CAAAE,SAAA,aAAkD;UAClDF,EAAA,CAAAC,cAAA,aAAoB;UACQD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAO;UACtCJ,EAAA,CAAAC,cAAA,cAA0B;UAAAD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAO;UACtCJ,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAO;UACpCJ,EAAA,CAAAC,cAAA,eAA2B;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}