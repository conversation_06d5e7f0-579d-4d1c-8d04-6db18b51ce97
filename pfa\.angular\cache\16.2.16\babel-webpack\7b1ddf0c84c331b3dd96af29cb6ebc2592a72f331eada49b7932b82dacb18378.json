{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class FleurComponent {\n  static {\n    this.ɵfac = function FleurComponent_Factory(t) {\n      return new (t || FleurComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FleurComponent,\n      selectors: [[\"app-fleur\"]],\n      decls: 72,\n      vars: 0,\n      consts: [[1, \"iris-profile\", \"fleur\"], [1, \"container\"], [1, \"header\"], [1, \"title\"], [1, \"subtitle\"], [1, \"content\"], [1, \"image-container\"], [\"src\", \"assets/1.png\", \"alt\", \"Fleur\", 1, \"iris-image\"], [1, \"image-decoration\"], [1, \"description\"], [1, \"card\", \"traits-card\"], [1, \"traits-list\"], [1, \"icon\"], [1, \"text\"], [1, \"navigation\"], [\"routerLink\", \"/iris2\", 1, \"btn\", \"back-btn\"]],\n      template: function FleurComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Fleur - Le Sentimental\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Profil ax\\u00E9 sur les \\u00E9motions et la cr\\u00E9ativit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6);\n          i0.ɵɵelement(9, \"img\", 7)(10, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"h2\");\n          i0.ɵɵtext(14, \"Caract\\u00E9ristiques\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"ul\", 11)(16, \"li\")(17, \"span\", 12);\n          i0.ɵɵtext(18, \"\\uD83D\\uDCAD\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 13);\n          i0.ɵɵtext(20, \"Profil ax\\u00E9 sur les sentiments\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"li\")(22, \"span\", 12);\n          i0.ɵɵtext(23, \"\\u2764\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 13);\n          i0.ɵɵtext(25, \"\\u00C9motions profondes, v\\u00E9cues et exprim\\u00E9es intens\\u00E9ment\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"li\")(27, \"span\", 12);\n          i0.ɵɵtext(28, \"\\uD83D\\uDC42\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"span\", 13);\n          i0.ɵɵtext(30, \"Tr\\u00E8s sensibles aux paroles, tendance \\u00E0 l'auto-critique\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"li\")(32, \"span\", 12);\n          i0.ɵɵtext(33, \"\\uD83D\\uDD0A\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"span\", 13);\n          i0.ɵɵtext(35, \"Mode d'apprentissage auditif, r\\u00E9actifs aux sons et aux mots\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"li\")(37, \"span\", 12);\n          i0.ɵɵtext(38, \"\\uD83C\\uDFA8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"span\", 13);\n          i0.ɵɵtext(40, \"Spontan\\u00E9s, cr\\u00E9atifs, flexibles mais facilement distraits\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\")(42, \"span\", 12);\n          i0.ɵɵtext(43, \"\\uD83E\\uDD32\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 13);\n          i0.ɵɵtext(45, \"Communiquent avec des gestes, des \\u00E9motions et des images mentales\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"li\")(47, \"span\", 12);\n          i0.ɵɵtext(48, \"\\uD83C\\uDFAD\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 13);\n          i0.ɵɵtext(50, \"Sociables, artistiques, parfois th\\u00E9\\u00E2traux ou exub\\u00E9rants\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\")(52, \"span\", 12);\n          i0.ɵɵtext(53, \"\\u2728\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"span\", 13);\n          i0.ɵɵtext(55, \"Apportent joie, harmonie et cr\\u00E9ativit\\u00E9 autour d'eux\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"li\")(57, \"span\", 12);\n          i0.ɵɵtext(58, \"\\uD83D\\uDC68\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"span\", 13);\n          i0.ɵɵtext(60, \"Lien fort mais ambivalent avec la figure paternelle\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"li\")(62, \"span\", 12);\n          i0.ɵɵtext(63, \"\\uD83C\\uDF31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"span\", 13);\n          i0.ɵɵtext(65, \"Le\\u00E7on de vie : d\\u00E9velopper la confiance en soi et canaliser l'\\u00E9motionnel\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(66, \"div\", 14)(67, \"a\", 15)(68, \"span\", 12);\n          i0.ɵɵtext(69, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"span\");\n          i0.ɵɵtext(71, \"Retour aux types d'iris\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.iris-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 40px;\\n  background: linear-gradient(to right, #f8e8ff, #f6f1ff);\\n  min-height: 100vh;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-img[_ngcontent-%COMP%] {\\n  width: 180px;\\n  height: 180px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);\\n  margin-bottom: 25px;\\n  transition: transform 0.3s ease;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n  max-width: 700px;\\n  background: rgba(255, 255, 255, 0.8);\\n  padding: 20px;\\n  border-radius: 20px;\\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\\n  text-align: center;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: bold;\\n  color: #5e548e;\\n  margin-bottom: 10px;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   .iris-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #7a6ea3;\\n  margin-bottom: 20px;\\n  font-style: italic;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style-type: none;\\n  padding: 0;\\n  text-align: left;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  line-height: 1.7;\\n  color: #3d3d3d;\\n  margin-bottom: 10px;\\n  position: relative;\\n  padding-left: 20px;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before {\\n  content: \\\"\\u2022\\\";\\n  color: #5e548e;\\n  font-weight: bold;\\n  position: absolute;\\n  left: 0;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%] {\\n  margin-top: 25px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 10px 20px;\\n  background-color: #5e548e;\\n  color: white;\\n  text-decoration: none;\\n  border-radius: 30px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: #4a4372;\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);\\n}\\n\\n@media (min-width: 768px) {\\n  .iris-details[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    align-items: flex-start;\\n    gap: 30px;\\n  }\\n  .iris-details[_ngcontent-%COMP%]   .iris-img[_ngcontent-%COMP%] {\\n    width: 220px;\\n    height: 220px;\\n  }\\n  .iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%] {\\n    text-align: left;\\n  }\\n  .iris-details[_ngcontent-%COMP%]   .iris-description[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%] {\\n    justify-content: flex-start;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FleurComponent", "selectors", "decls", "vars", "consts", "template", "FleurComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\fleur\\fleur.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\fleur\\fleur.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-fleur',\n  templateUrl: './fleur.component.html',\n  styleUrls: ['./fleur.component.scss']\n})\nexport class FleurComponent {\n\n}\n", "<div class=\"iris-profile fleur\">\n  <div class=\"container\">\n    <div class=\"header\">\n      <h1 class=\"title\">Fleur - Le Sentimental</h1>\n      <p class=\"subtitle\">Profil axé sur les émotions et la créativité</p>\n    </div>\n    \n    <div class=\"content\">\n      <div class=\"image-container\">\n        <img src=\"assets/1.png\" alt=\"Fleur\" class=\"iris-image\" />\n        <div class=\"image-decoration\"></div>\n      </div>\n      \n      <div class=\"description\">\n        <div class=\"card traits-card\">\n          <h2>Caractéristiques</h2>\n          <ul class=\"traits-list\">\n            <li>\n              <span class=\"icon\">💭</span>\n              <span class=\"text\">Profil axé sur les sentiments</span>\n            </li>\n            <li>\n              <span class=\"icon\">❤️</span>\n              <span class=\"text\">Émotions profondes, vécues et exprimées intensément</span>\n            </li>\n            <li>\n              <span class=\"icon\">👂</span>\n              <span class=\"text\">Très sensibles aux paroles, tendance à l'auto-critique</span>\n            </li>\n            <li>\n              <span class=\"icon\">🔊</span>\n              <span class=\"text\">Mode d'apprentissage auditif, réactifs aux sons et aux mots</span>\n            </li>\n            <li>\n              <span class=\"icon\">🎨</span>\n              <span class=\"text\">Spontanés, créatifs, flexibles mais facilement distraits</span>\n            </li>\n            <li>\n              <span class=\"icon\">🤲</span>\n              <span class=\"text\">Communiquent avec des gestes, des émotions et des images mentales</span>\n            </li>\n            <li>\n              <span class=\"icon\">🎭</span>\n              <span class=\"text\">Sociables, artistiques, parfois théâtraux ou exubérants</span>\n            </li>\n            <li>\n              <span class=\"icon\">✨</span>\n              <span class=\"text\">Apportent joie, harmonie et créativité autour d'eux</span>\n            </li>\n            <li>\n              <span class=\"icon\">👨</span>\n              <span class=\"text\">Lien fort mais ambivalent avec la figure paternelle</span>\n            </li>\n            <li>\n              <span class=\"icon\">🌱</span>\n              <span class=\"text\">Leçon de vie : développer la confiance en soi et canaliser l'émotionnel</span>\n            </li>\n          </ul>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"navigation\">\n      <a routerLink=\"/iris2\" class=\"btn back-btn\">\n        <span class=\"icon\">←</span>\n        <span>Retour aux types d'iris</span>\n      </a>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3BE,EAAA,CAAAC,cAAA,aAAgC;UAGRD,EAAA,CAAAE,MAAA,6BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7CH,EAAA,CAAAC,cAAA,WAAoB;UAAAD,EAAA,CAAAE,MAAA,uEAA4C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGtEH,EAAA,CAAAC,cAAA,aAAqB;UAEjBD,EAAA,CAAAI,SAAA,aAAyD;UAE3DJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAyB;UAEjBD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,cAAwB;UAEDD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,0CAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEzDH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,+EAAmD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE/EH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,wEAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAElFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,wEAA2D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEvFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,0EAAwD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEpFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,8EAAiE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE7FH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,8EAAuD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEnFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,qEAAmD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE/EH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,2DAAmD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE/EH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,8FAAuE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAO3GH,EAAA,CAAAC,cAAA,eAAwB;UAEDD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}