{"ast": null, "code": "import { Observable } from '../Observable';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleAsyncIterable(input, scheduler) {\n  if (!input) {\n    throw new Error('Iterable cannot be null');\n  }\n  return new Observable(subscriber => {\n    executeSchedule(subscriber, scheduler, () => {\n      const iterator = input[Symbol.asyncIterator]();\n      executeSchedule(subscriber, scheduler, () => {\n        iterator.next().then(result => {\n          if (result.done) {\n            subscriber.complete();\n          } else {\n            subscriber.next(result.value);\n          }\n        });\n      }, 0, true);\n    });\n  });\n}", "map": {"version": 3, "names": ["Observable", "executeSchedule", "scheduleAsyncIterable", "input", "scheduler", "Error", "subscriber", "iterator", "Symbol", "asyncIterator", "next", "then", "result", "done", "complete", "value"], "sources": ["C:/Users/<USER>/Desktop/pfa/pfa/node_modules/rxjs/dist/esm/internal/scheduled/scheduleAsyncIterable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleAsyncIterable(input, scheduler) {\n    if (!input) {\n        throw new Error('Iterable cannot be null');\n    }\n    return new Observable((subscriber) => {\n        executeSchedule(subscriber, scheduler, () => {\n            const iterator = input[Symbol.asyncIterator]();\n            executeSchedule(subscriber, scheduler, () => {\n                iterator.next().then((result) => {\n                    if (result.done) {\n                        subscriber.complete();\n                    }\n                    else {\n                        subscriber.next(result.value);\n                    }\n                });\n            }, 0, true);\n        });\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACpD,IAAI,CAACD,KAAK,EAAE;IACR,MAAM,IAAIE,KAAK,CAAC,yBAAyB,CAAC;EAC9C;EACA,OAAO,IAAIL,UAAU,CAAEM,UAAU,IAAK;IAClCL,eAAe,CAACK,UAAU,EAAEF,SAAS,EAAE,MAAM;MACzC,MAAMG,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAACC,aAAa,CAAC,CAAC,CAAC;MAC9CR,eAAe,CAACK,UAAU,EAAEF,SAAS,EAAE,MAAM;QACzCG,QAAQ,CAACG,IAAI,CAAC,CAAC,CAACC,IAAI,CAAEC,MAAM,IAAK;UAC7B,IAAIA,MAAM,CAACC,IAAI,EAAE;YACbP,UAAU,CAACQ,QAAQ,CAAC,CAAC;UACzB,CAAC,MACI;YACDR,UAAU,CAACI,IAAI,CAACE,MAAM,CAACG,KAAK,CAAC;UACjC;QACJ,CAAC,CAAC;MACN,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACf,CAAC,CAAC;EACN,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}