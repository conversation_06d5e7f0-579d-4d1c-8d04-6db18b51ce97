{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nexport class SignupComponent {\n  constructor() {\n    this.user = {\n      name: '',\n      email: '',\n      password: '',\n      terms: false\n    };\n  }\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 55,\n      vars: 5,\n      consts: [[1, \"signup-container\"], [1, \"left-section\"], [1, \"graphic\"], [1, \"iris-collage\"], [\"src\", \"assets/iris-collage.png\", \"alt\", \"Iris Collage\", 1, \"iris-collage-img\"], [1, \"circle\", \"circle1\"], [1, \"circle\", \"circle3\"], [1, \"dot\"], [1, \"slogan\"], [1, \"right-section\"], [1, \"form-container\"], [1, \"logo\"], [1, \"custom-logo\"], [1, \"google-btn\"], [\"src\", \"assets/google-icon.png\", \"alt\", \"Google Icon\"], [1, \"divider\"], [3, \"ngSubmit\"], [\"signupForm\", \"ngForm\"], [1, \"form-group\"], [\"for\", \"name\"], [\"type\", \"text\", \"id\", \"name\", \"name\", \"name\", \"placeholder\", \"Leslie Alexander\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"id\", \"email\", \"name\", \"email\", \"placeholder\", \"<EMAIL>\", \"required\", \"\", \"email\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\"], [\"type\", \"password\", \"id\", \"password\", \"name\", \"password\", \"placeholder\", \"At least 8 characters\", \"required\", \"\", \"minlength\", \"8\", 3, \"ngModel\", \"ngModelChange\"], [1, \"checkbox-group\"], [\"type\", \"checkbox\", \"id\", \"terms\", \"name\", \"terms\", \"required\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"terms\"], [\"href\", \"#\"], [\"type\", \"submit\", 1, \"signup-btn\", 3, \"disabled\"], [1, \"login-link\"], [\"routerLink\", \"/login\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 7)(9, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"p\", 8);\n          i0.ɵɵtext(11, \" Each iris is a unique story written by nature, waiting to be decoded by technology \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"div\", 10)(14, \"div\", 11)(15, \"div\", 12);\n          i0.ɵɵelement(16, \"span\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"h1\");\n          i0.ɵɵtext(18, \"Sign Up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"p\");\n          i0.ɵɵtext(20, \"Votre identit\\u00E9, red\\u00E9finie par la technologie. Cr\\u00E9ez votre compte.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"button\", 13);\n          i0.ɵɵelement(22, \"img\", 14);\n          i0.ɵɵtext(23, \" Continue with Google \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 15);\n          i0.ɵɵtext(25, \"or Sign in with Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"form\", 16, 17);\n          i0.ɵɵlistener(\"ngSubmit\", function SignupComponent_Template_form_ngSubmit_26_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(28, \"div\", 18)(29, \"label\", 19);\n          i0.ɵɵtext(30, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_31_listener($event) {\n            return ctx.user.name = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 18)(33, \"label\", 21);\n          i0.ɵɵtext(34, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"input\", 22);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_35_listener($event) {\n            return ctx.user.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 18)(37, \"label\", 23);\n          i0.ɵɵtext(38, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"input\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_39_listener($event) {\n            return ctx.user.password = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 25)(41, \"input\", 26);\n          i0.ɵɵlistener(\"ngModelChange\", function SignupComponent_Template_input_ngModelChange_41_listener($event) {\n            return ctx.user.terms = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"label\", 27);\n          i0.ɵɵtext(43, \"I agree with \");\n          i0.ɵɵelementStart(44, \"a\", 28);\n          i0.ɵɵtext(45, \"Terms\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \" and \");\n          i0.ɵɵelementStart(47, \"a\", 28);\n          i0.ɵɵtext(48, \"Privacy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(49, \"button\", 29);\n          i0.ɵɵtext(50, \" Sign Up \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"p\", 30);\n          i0.ɵɵtext(52, \" Already have an account? \");\n          i0.ɵɵelementStart(53, \"a\", 31);\n          i0.ɵɵtext(54, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(27);\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.name);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.email);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.user.terms);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", !_r0.valid);\n        }\n      },\n      dependencies: [i1.RouterLink, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.RequiredValidator, i2.MinLengthValidator, i2.CheckboxRequiredValidator, i2.EmailValidator, i2.NgModel, i2.NgForm],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.signup-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  font-family: \\\"Poppins\\\", sans-serif; \\n\\n  background: #fff;\\n  overflow: hidden;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  background: linear-gradient(135deg, #e6e6fa, #f5f5ff); \\n\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  position: relative;\\n  overflow: hidden;\\n  \\n\\n  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);\\n  background-size: 10px 10px;\\n}\\n\\n.graphic[_ngcontent-%COMP%] {\\n  position: relative;\\n  text-align: center;\\n}\\n\\n.iris-collage[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.iris-collage-img[_ngcontent-%COMP%] {\\n  width: 450px;\\n  height: auto;\\n  object-fit: contain;\\n  border: 2px solid rgba(106, 90, 205, 0.3); \\n\\n  border-radius: 50%;\\n  box-shadow: 0 0 20px rgba(106, 90, 205, 0.2); \\n\\n  transition: transform 0.3s ease;\\n}\\n\\n.iris-collage-img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05); \\n\\n}\\n\\n\\n\\n.circle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border-radius: 50%;\\n  background: rgba(255, 182, 193, 0.3); \\n\\n  animation: _ngcontent-%COMP%_float 6s ease-in-out infinite; \\n\\n}\\n\\n.circle1[_ngcontent-%COMP%] {\\n  width: 440px;\\n  height: 430px;\\n  top: -130px;\\n  left: 0px;\\n  animation-delay: 0s;\\n}\\n\\n.circle3[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  top: 80px;\\n  right: 80px;\\n  animation-delay: 2s;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%]::before, .left-section[_ngcontent-%COMP%]::after, .left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%] {\\n  content: \\\"\\\";\\n  position: absolute;\\n  border-radius: 50%;\\n  background: #ff4040; \\n\\n  animation: _ngcontent-%COMP%_twinkle 3s infinite; \\n\\n}\\n\\n.left-section[_ngcontent-%COMP%]::before {\\n  width: 5px;\\n  height: 5px;\\n  top: 10%;\\n  left: 20%;\\n}\\n\\n.left-section[_ngcontent-%COMP%]::after {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 15%;\\n  right: 10%;\\n}\\n\\n\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(1) {\\n  width: 4px;\\n  height: 4px;\\n  top: 5%;\\n  left: 30%;\\n  animation-delay: 0.5s;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(2) {\\n  width: 6px;\\n  height: 6px;\\n  top: 20%;\\n  right: 15%;\\n  animation-delay: 1s;\\n}\\n\\n.left-section[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:nth-child(3) {\\n  width: 3px;\\n  height: 3px;\\n  bottom: 20%;\\n  left: 10%;\\n  animation-delay: 1.5s;\\n}\\n\\n.slogan[_ngcontent-%COMP%] {\\n  font-size: 1.8rem; \\n\\n  color: #6a5acd; \\n\\n  text-align: center;\\n  margin-top: 80px;\\n  font-style: italic;\\n  font-family: \\\"Dancing Script\\\", cursive;\\n  text-shadow: 0 2px 4px rgba(106, 90, 205, 0.2); \\n\\n  opacity: 0;\\n  animation: _ngcontent-%COMP%_fadeIn 1.5s ease-in-out forwards; \\n\\n}\\n\\n\\n\\n.right-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  background: linear-gradient(135deg, #f5f5ff, #ffffff); \\n\\n}\\n\\n.form-container[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  padding: 30px; \\n\\n  background: rgba(255, 255, 255, 0.9); \\n\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(106, 90, 205, 0.1); \\n\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px); \\n\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n}\\n\\n\\n\\n.custom-logo[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  margin: 0 auto;\\n  position: relative;\\n  background: transparent;\\n  animation: _ngcontent-%COMP%_pulse 2s infinite; \\n\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd; \\n\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  bottom: 5px;\\n  left: 15px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before, .custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background: #6a5acd;\\n  border-radius: 50%;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  left: 5px;\\n}\\n\\n.custom-logo[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  width: 10px;\\n  height: 10px;\\n  top: 15px;\\n  right: 5px;\\n}\\n\\n.logo[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 2.8rem; \\n\\n  color: #333;\\n  font-weight: 700;\\n  margin: 10px 0;\\n  letter-spacing: 1px; \\n\\n}\\n\\n.logo[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1rem;\\n  margin-bottom: 20px;\\n  font-weight: 400;\\n}\\n\\n.google-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100%;\\n  padding: 12px;\\n  background: #fff;\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 1rem;\\n  color: #333;\\n  font-weight: 500;\\n  margin-bottom: 20px;\\n  transition: all 0.3s ease;\\n}\\n\\n.google-btn[_ngcontent-%COMP%]:hover {\\n  background: #e6e6fa;\\n  box-shadow: 0 0 15px rgba(106, 90, 205, 0.2);\\n  transform: translateY(-2px);\\n}\\n\\n.google-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  margin-right: 10px;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  text-align: center;\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin: 20px 0;\\n  position: relative;\\n  font-weight: 500;\\n}\\n\\n.divider[_ngcontent-%COMP%]::before, .divider[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 50%;\\n  width: 40%;\\n  height: 1px;\\n  background: rgba(106, 90, 205, 0.2);\\n}\\n\\n.divider[_ngcontent-%COMP%]::before {\\n  left: 0;\\n}\\n\\n.divider[_ngcontent-%COMP%]::after {\\n  right: 0;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 0.9rem;\\n  color: #333;\\n  margin-bottom: 8px;\\n  font-weight: 500;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px;\\n  border: 1px solid rgba(106, 90, 205, 0.2);\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  color: #333;\\n  background: rgba(255, 255, 255, 0.5);\\n  transition: all 0.3s ease;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #6a5acd;\\n  box-shadow: 0 0 10px rgba(106, 90, 205, 0.2);\\n  background: #fff;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder {\\n  color: #999;\\n  font-weight: 400;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.checkbox-group[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: #5a4bbd;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px;\\n  background: linear-gradient(90deg, #6a5acd, #5a4bbd); \\n\\n  color: #fff;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  text-transform: uppercase;\\n  transition: all 0.3s ease;\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(90deg, #5a4bbd, #6a5acd);\\n  box-shadow: 0 0 15px rgba(106, 90, 205, 0.4);\\n  transform: translateY(-2px);\\n}\\n\\n.signup-btn[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  cursor: not-allowed;\\n  box-shadow: none;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 20px;\\n  font-size: 0.9rem;\\n  color: #666;\\n  font-weight: 400;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #6a5acd;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n  color: #5a4bbd;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0);\\n  }\\n  50% {\\n    transform: translateY(-10px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_twinkle {\\n  0%, 100% {\\n    opacity: 1;\\n  }\\n  50% {\\n    opacity: 0.3;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  0% {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  100% {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SignupComponent", "constructor", "user", "name", "email", "password", "terms", "onSubmit", "console", "log", "alert", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "SignupComponent_Template_form_ngSubmit_26_listener", "SignupComponent_Template_input_ngModelChange_31_listener", "$event", "SignupComponent_Template_input_ngModelChange_35_listener", "SignupComponent_Template_input_ngModelChange_39_listener", "SignupComponent_Template_input_ngModelChange_41_listener", "ɵɵadvance", "ɵɵproperty", "_r0", "valid"], "sources": ["C:\\pfa\\src\\app\\signup\\signup.component.ts", "C:\\pfa\\src\\app\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-signup',\n  templateUrl: './signup.component.html',\n  styleUrls: ['./signup.component.scss']\n})\nexport class SignupComponent {\n  user = {\n    name: '',\n    email: '',\n    password: '',\n    terms: false\n  };\n\n  onSubmit() {\n    if (this.user.terms) {\n      console.log('Form submitted:', this.user);\n      // Ajoutez ici la logique pour envoyer les données au backend\n    } else {\n      alert('Please agree to the terms and privacy policy.');\n    }\n  }\n}", "<div class=\"signup-container\">\n    <!-- Section gauche : Graphique et texte -->\n    <div class=\"left-section\">\n      <div class=\"graphic\">\n        <!-- Conserver l'image des iris -->\n        <div class=\"iris-collage\">\n          <img src=\"assets/iris-collage.png\" alt=\"Iris Collage\" class=\"iris-collage-img\" />\n        </div>\n        <!-- Cercles décoratifs -->\n        <div class=\"circle circle1\"></div>\n        <div class=\"circle circle3\"></div>\n        <!-- Points décoratifs supplémentaires -->\n        <div class=\"dot\"></div>\n        <div class=\"dot\"></div>\n        <div class=\"dot\"></div>\n      </div>\n      <p class=\"slogan\">\n        Each iris is a unique story written by nature, waiting to be decoded by technology\n      </p>\n    </div>\n  \n    <!-- Section droite : Formulaire -->\n    <div class=\"right-section\">\n      <div class=\"form-container\">\n        <div class=\"logo\">\n          <!-- Logo en forme de croix -->\n          <div class=\"custom-logo\"><span></span></div>\n          <h1>Sign Up</h1>\n          <p>Votre identité, redéfinie par la technologie. Créez votre compte.</p>\n        </div>\n  \n        <!-- Bouton \"Continue with Google\" -->\n        <button class=\"google-btn\">\n          <img src=\"assets/google-icon.png\" alt=\"Google Icon\" /> Continue with Google\n        </button>\n  \n        <div class=\"divider\">or Sign in with Email</div>\n  \n        <!-- Formulaire -->\n        <form (ngSubmit)=\"onSubmit()\" #signupForm=\"ngForm\">\n          <div class=\"form-group\">\n            <label for=\"name\">Name</label>\n            <input\n              type=\"text\"\n              id=\"name\"\n              name=\"name\"\n              placeholder=\"Leslie Alexander\"\n              [(ngModel)]=\"user.name\"\n              required\n            />\n          </div>\n  \n          <div class=\"form-group\">\n            <label for=\"email\">Email</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              placeholder=\"<EMAIL>\"\n              [(ngModel)]=\"user.email\"\n              required\n              email\n            />\n          </div>\n  \n          <div class=\"form-group\">\n            <label for=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              placeholder=\"At least 8 characters\"\n              [(ngModel)]=\"user.password\"\n              required\n              minlength=\"8\"\n            />\n          </div>\n  \n          <div class=\"checkbox-group\">\n            <input\n              type=\"checkbox\"\n              id=\"terms\"\n              name=\"terms\"\n              [(ngModel)]=\"user.terms\"\n              required\n            />\n            <label for=\"terms\">I agree with <a href=\"#\">Terms</a> and <a href=\"#\">Privacy</a></label>\n          </div>\n  \n          <button type=\"submit\" class=\"signup-btn\" [disabled]=\"!signupForm.valid\">\n            Sign Up\n          </button>\n        </form>\n  \n        <p class=\"login-link\">\n          Already have an account? <a routerLink=\"/login\">Log in</a>\n        </p>\n      </div>\n    </div>\n  </div>"], "mappings": ";;;AAOA,OAAM,MAAOA,eAAe;EAL5BC,YAAA;IAME,KAAAC,IAAI,GAAG;MACLC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE;KACR;;EAEDC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,IAAI,CAACI,KAAK,EAAE;MACnBE,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACP,IAAI,CAAC;MACzC;KACD,MAAM;MACLQ,KAAK,CAAC,+CAA+C,CAAC;;EAE1D;;;uBAfWV,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAW,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,aAA8B;UAMpBD,EAAA,CAAAE,SAAA,aAAiF;UACnFF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAE,SAAA,aAAkC;UAMpCF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,YAAkB;UAChBD,EAAA,CAAAI,MAAA,4FACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAINH,EAAA,CAAAC,cAAA,cAA2B;UAIID,EAAA,CAAAE,SAAA,YAAa;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAC5CH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,wFAAiE;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAI1EH,EAAA,CAAAC,cAAA,kBAA2B;UACzBD,EAAA,CAAAE,SAAA,eAAsD;UAACF,EAAA,CAAAI,MAAA,8BACzD;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAAqB;UAAAD,EAAA,CAAAI,MAAA,6BAAqB;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAGhDH,EAAA,CAAAC,cAAA,oBAAmD;UAA7CD,EAAA,CAAAK,UAAA,sBAAAC,mDAAA;YAAA,OAAYP,GAAA,CAAAX,QAAA,EAAU;UAAA,EAAC;UAC3BY,EAAA,CAAAC,cAAA,eAAwB;UACJD,EAAA,CAAAI,MAAA,YAAI;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAC9BH,EAAA,CAAAC,cAAA,iBAOE;UAFAD,EAAA,CAAAK,UAAA,2BAAAE,yDAAAC,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAC,IAAA,GAAAwB,MAAA;UAAA,EAAuB;UALzBR,EAAA,CAAAG,YAAA,EAOE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACHD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAI,yDAAAD,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAE,KAAA,GAAAuB,MAAA;UAAA,EAAwB;UAL1BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAAwB;UACAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,iBAQE;UAHAD,EAAA,CAAAK,UAAA,2BAAAK,yDAAAF,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAG,QAAA,GAAAsB,MAAA;UAAA,EAA2B;UAL7BR,EAAA,CAAAG,YAAA,EAQE;UAGJH,EAAA,CAAAC,cAAA,eAA4B;UAKxBD,EAAA,CAAAK,UAAA,2BAAAM,yDAAAH,MAAA;YAAA,OAAAT,GAAA,CAAAhB,IAAA,CAAAI,KAAA,GAAAqB,MAAA;UAAA,EAAwB;UAJ1BR,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAC,cAAA,iBAAmB;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAI,MAAA,aAAI;UAAAJ,EAAA,CAAAC,cAAA,aAAY;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAGnFH,EAAA,CAAAC,cAAA,kBAAwE;UACtED,EAAA,CAAAI,MAAA,iBACF;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,aAAsB;UACpBD,EAAA,CAAAI,MAAA,kCAAyB;UAAAJ,EAAA,CAAAC,cAAA,aAAuB;UAAAD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;UAhDtDH,EAAA,CAAAY,SAAA,IAAuB;UAAvBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAC,IAAA,CAAuB;UAYvBgB,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAE,KAAA,CAAwB;UAaxBe,EAAA,CAAAY,SAAA,GAA2B;UAA3BZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAG,QAAA,CAA2B;UAW3Bc,EAAA,CAAAY,SAAA,GAAwB;UAAxBZ,EAAA,CAAAa,UAAA,YAAAd,GAAA,CAAAhB,IAAA,CAAAI,KAAA,CAAwB;UAMaa,EAAA,CAAAY,SAAA,GAA8B;UAA9BZ,EAAA,CAAAa,UAAA,cAAAC,GAAA,CAAAC,KAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}