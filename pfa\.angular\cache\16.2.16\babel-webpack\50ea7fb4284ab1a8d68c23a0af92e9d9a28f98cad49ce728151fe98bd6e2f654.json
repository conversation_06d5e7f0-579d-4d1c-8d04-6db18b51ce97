{"ast": null, "code": "import { PERSONALITY_QUESTIONS } from '../models/personality-test.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/personality-test.service\";\nimport * as i2 from \"../services/iris-compatibility.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction PersonalityTestComponent_div_1_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_div_47_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.logout());\n    });\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"Se d\\u00E9connecter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 14);\n    i0.ɵɵtext(5, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PersonalityTestComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"h1\", 8);\n    i0.ɵɵtext(4, \"Test de Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 9);\n    i0.ɵɵelementStart(6, \"p\", 10);\n    i0.ɵɵtext(7, \"D\\u00E9couvrez votre profil psychotechnique\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"div\", 12)(10, \"div\", 13)(11, \"span\", 14);\n    i0.ɵɵtext(12, \"\\uD83D\\uDCDD\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 15)(14, \"h3\");\n    i0.ɵɵtext(15, \"32 Questions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"p\");\n    i0.ɵɵtext(17, \"Questions cibl\\u00E9es pour analyser votre personnalit\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 13)(19, \"span\", 14);\n    i0.ɵɵtext(20, \"\\u23F1\\uFE0F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 15)(22, \"h3\");\n    i0.ɵɵtext(23, \"5-10 Minutes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\");\n    i0.ɵɵtext(25, \"Temps estim\\u00E9 pour compl\\u00E9ter le test\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 13)(27, \"span\", 14);\n    i0.ɵɵtext(28, \"\\uD83C\\uDFAF\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 15)(30, \"h3\");\n    i0.ɵɵtext(31, \"4 Profils Principaux\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"Flower, Jewel, Shaker, Stream + profils interm\\u00E9diaires\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(34, \"div\", 16)(35, \"h3\");\n    i0.ɵɵtext(36, \"Informations du test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"p\")(38, \"strong\");\n    i0.ɵɵtext(39, \"Nom:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"p\")(42, \"strong\");\n    i0.ɵɵtext(43, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"p\", 17);\n    i0.ɵɵtext(46, \"Les r\\u00E9sultats seront sauvegard\\u00E9s dans la base de donn\\u00E9es PFA1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, PersonalityTestComponent_div_1_div_47_Template, 6, 0, \"div\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 19)(49, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_Template_button_click_49_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.startTest());\n    });\n    i0.ɵɵelementStart(50, \"span\");\n    i0.ɵɵtext(51, \"Commencer le Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"span\", 14);\n    i0.ɵɵtext(53, \"\\u2192\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_1_Template_button_click_54_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.goToHome());\n    });\n    i0.ɵɵtext(55, \" Retour \\u00E0 l'accueil \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(40);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedUser.name, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedUser.email, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentUser);\n  }\n}\nfunction PersonalityTestComponent_div_2_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"h2\", 35);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 36)(5, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_2_div_11_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.answerQuestion(true));\n    });\n    i0.ɵɵelementStart(6, \"span\", 38);\n    i0.ɵɵtext(7, \"\\u2713\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\");\n    i0.ɵɵtext(9, \"Oui\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_2_div_11_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r13.answerQuestion(false));\n    });\n    i0.ɵɵelementStart(11, \"span\", 38);\n    i0.ɵɵtext(12, \"\\u2717\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \"Non\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r10.currentQuestion.question);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.isLoading);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"disabled\", ctx_r10.isLoading);\n  }\n}\nfunction PersonalityTestComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"div\", 27)(4, \"span\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 30);\n    i0.ɵɵelement(10, \"div\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(11, PersonalityTestComponent_div_2_div_11_Template, 15, 3, \"div\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"Question \", ctx_r1.currentQuestionNumber, \" sur \", ctx_r1.totalQuestions, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(8, 6, ctx_r1.progressPercentage, \"1.0-0\"), \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.progressPercentage, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentQuestion);\n  }\n}\nfunction PersonalityTestComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵelement(2, \"div\", 42);\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Analyse de votre profil...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Calcul des scores et d\\u00E9termination de votre personnalit\\u00E9\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PersonalityTestComponent_div_4_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"span\", 64);\n    i0.ɵɵtext(2, \"Profil Interm\\u00E9diaire\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"span\", 65);\n    i0.ɵɵtext(2, \"Profil Principal\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const characteristic_r23 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", characteristic_r23, \" \");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_li_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const recommendation_r24 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", recommendation_r24, \" \");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_div_29_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const correction_r26 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", correction_r26, \" \");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 82)(1, \"h4\");\n    i0.ɵɵtext(2, \"Corrections sugg\\u00E9r\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 83);\n    i0.ɵɵtemplate(4, PersonalityTestComponent_div_4_div_22_div_29_li_4_Template, 2, 1, \"li\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r22.compatibilityResult.corrections);\n  }\n}\nfunction PersonalityTestComponent_div_4_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"h3\");\n    i0.ɵɵtext(2, \"Compatibilit\\u00E9 avec votre Iris\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 67)(4, \"div\", 68)(5, \"div\", 69)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 70)(11, \"div\", 71)(12, \"span\", 51);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 50);\n    i0.ɵɵtext(15, \"Compatibilit\\u00E9\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 72)(17, \"span\", 73);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 74)(20, \"h4\");\n    i0.ɵɵtext(21, \"Caract\\u00E9ristiques de votre iris\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 75);\n    i0.ɵɵtemplate(23, PersonalityTestComponent_div_4_div_22_span_23_Template, 2, 1, \"span\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 77)(25, \"h4\");\n    i0.ɵɵtext(26, \"Recommandations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"ul\", 78);\n    i0.ɵɵtemplate(28, PersonalityTestComponent_div_4_div_22_li_28_Template, 2, 1, \"li\", 79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, PersonalityTestComponent_div_4_div_22_div_29_Template, 5, 1, \"div\", 80);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r16.compatibilityResult.isCompatible ? \"compatible\" : \"incompatible\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r16.compatibilityResult.irisType.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r16.compatibilityResult.irisType.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r16.compatibilityResult.isCompatible ? \"high-score\" : \"low-score\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r16.compatibilityResult.compatibilityScore, \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r16.compatibilityResult.isCompatible ? \"compatible\" : \"incompatible\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.compatibilityResult.isCompatible ? \"\\u2713 Compatible\" : \"\\u26A0 Incompatible\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.compatibilityResult.irisType.characteristics);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.compatibilityResult.recommendations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r16.compatibilityResult.isCompatible);\n  }\n}\nfunction PersonalityTestComponent_div_4_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84)(1, \"h3\");\n    i0.ɵɵtext(2, \"Scores d\\u00E9taill\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 85)(4, \"div\", 86)(5, \"span\", 50);\n    i0.ɵɵtext(6, \"Flower\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 87);\n    i0.ɵɵelement(8, \"div\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 51);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 86)(12, \"span\", 50);\n    i0.ɵɵtext(13, \"Jewel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 87);\n    i0.ɵɵelement(15, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 51);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 86)(19, \"span\", 50);\n    i0.ɵɵtext(20, \"Shaker\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 87);\n    i0.ɵɵelement(22, \"div\", 90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 51);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 86)(26, \"span\", 50);\n    i0.ɵɵtext(27, \"Stream\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 87);\n    i0.ɵɵelement(29, \"div\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\", 51);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.flower / 4 * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.flower, \"/4\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.jewel / 4 * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.jewel, \"/4\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.shaker / 4 * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.shaker, \"/4\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"width\", ctx_r17.testSession.scores.stream / 4 * 100, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r17.testSession.scores.stream, \"/4\");\n  }\n}\nfunction PersonalityTestComponent_div_4_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"span\", 58);\n    i0.ɵɵtext(2, \"ID de session:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 59);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r18.testSession == null ? null : ctx_r18.testSession.id);\n  }\n}\nfunction PersonalityTestComponent_div_4_button_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_button_40_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.logout());\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2, \"Se d\\u00E9connecter\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 14);\n    i0.ɵɵtext(4, \"\\uD83D\\uDEAA\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PersonalityTestComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 45)(3, \"h1\", 8);\n    i0.ɵɵtext(4, \"Votre Profil de Personnalit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 46)(7, \"div\", 47)(8, \"h2\", 48);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 49)(11, \"span\", 50);\n    i0.ɵɵtext(12, \"Score de confiance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 51);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(15, PersonalityTestComponent_div_4_div_15_Template, 3, 0, \"div\", 52);\n    i0.ɵɵtemplate(16, PersonalityTestComponent_div_4_div_16_Template, 3, 0, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 53)(18, \"h3\");\n    i0.ɵɵtext(19, \"Description de votre profil\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, PersonalityTestComponent_div_4_div_22_Template, 30, 13, \"div\", 54);\n    i0.ɵɵtemplate(23, PersonalityTestComponent_div_4_div_23_Template, 32, 12, \"div\", 55);\n    i0.ɵɵelementStart(24, \"div\", 56)(25, \"div\", 57)(26, \"span\", 58);\n    i0.ɵɵtext(27, \"Test compl\\u00E9t\\u00E9 le:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"span\", 59);\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(31, PersonalityTestComponent_div_4_div_31_Template, 5, 1, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 61)(33, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.restartTest());\n    });\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35, \"Refaire le Test\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 14);\n    i0.ɵɵtext(37, \"\\uD83D\\uDD04\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PersonalityTestComponent_div_4_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.goToHome());\n    });\n    i0.ɵɵtext(39, \" Retour \\u00E0 l'accueil \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(40, PersonalityTestComponent_div_4_button_40_Template, 5, 0, \"button\", 62);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassMap(\"profile-\" + ctx_r3.finalProfile.primaryClass.toLowerCase().replace(\"-\", \"\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.finalProfile.primaryClass);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.finalProfile.confidenceScore, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.finalProfile.isIntermediate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.finalProfile.isIntermediate);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r3.finalProfile.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.compatibilityResult);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testSession);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(30, 12, ctx_r3.testSession == null ? null : ctx_r3.testSession.completedAt, \"dd/MM/yyyy \\u00E0 HH:mm\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.testSession == null ? null : ctx_r3.testSession.id);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.currentUser);\n  }\n}\nexport class PersonalityTestComponent {\n  constructor(personalityTestService, irisCompatibilityService, router) {\n    this.personalityTestService = personalityTestService;\n    this.irisCompatibilityService = irisCompatibilityService;\n    this.router = router;\n    // Données du test\n    this.questions = PERSONALITY_QUESTIONS;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.testSession = null;\n    // État du composant\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.isLoading = false;\n    this.showResults = false;\n    this.currentSessionId = '';\n    // Comptes statiques pour les tests\n    this.staticUsers = [{\n      id: 1,\n      name: 'Marie Dubois',\n      email: '<EMAIL>',\n      description: 'Profil créatif et émotionnel'\n    }, {\n      id: 2,\n      name: 'Jean Martin',\n      email: '<EMAIL>',\n      description: 'Profil analytique et structuré'\n    }, {\n      id: 3,\n      name: 'Sophie Leroy',\n      email: '<EMAIL>',\n      description: 'Profil dynamique et aventurier'\n    }, {\n      id: 4,\n      name: 'Pierre Moreau',\n      email: '<EMAIL>',\n      description: 'Profil paisible et réfléchi'\n    }, {\n      id: 5,\n      name: 'Emma Bernard',\n      email: '<EMAIL>',\n      description: 'Profil mixte créatif-analytique'\n    }];\n    this.selectedUser = this.staticUsers[0]; // Utilisateur par défaut\n    this.currentUser = null; // Utilisateur connecté\n    // Résultats\n    this.finalProfile = null;\n    this.compatibilityResult = null;\n  }\n  ngOnInit() {\n    // Récupérer l'utilisateur connecté\n    const currentUserData = localStorage.getItem('currentUser');\n    if (currentUserData) {\n      this.currentUser = JSON.parse(currentUserData);\n      // Utiliser l'utilisateur connecté pour le test\n      this.selectedUser = {\n        id: 0,\n        name: this.currentUser.name,\n        email: this.currentUser.email,\n        description: 'Utilisateur connecté'\n      };\n    }\n    this.initializeTest();\n  }\n  /**\n   * Initialise le test\n   */\n  initializeTest() {\n    this.testSession = this.personalityTestService.createTestSession(this.selectedUser.name, this.selectedUser.email);\n  }\n  /**\n   * Sélectionne un utilisateur de test\n   */\n  selectUser(user) {\n    this.selectedUser = user;\n    this.initializeTest();\n  }\n  /**\n   * Démarre le test\n   */\n  startTest() {\n    this.isTestStarted = true;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n    if (this.testSession) {\n      this.testSession.startedAt = new Date();\n      this.testSession.id = this.currentSessionId;\n    }\n    console.log('🚀 Test démarré - Session ID:', this.currentSessionId);\n  }\n  /**\n   * Traite la réponse à une question\n   */\n  answerQuestion(answer) {\n    if (!this.testSession) return;\n    const currentQuestion = this.questions[this.currentQuestionIndex];\n    const response = {\n      questionId: currentQuestion.id,\n      answer: answer,\n      timestamp: new Date()\n    };\n    this.responses.push(response);\n    this.testSession.responses = this.responses;\n    // Sauvegarder la réponse individuelle en temps réel\n    if (this.currentUser && this.currentSessionId) {\n      this.personalityTestService.saveIndividualResponse(this.currentUser.email, this.currentSessionId, response).subscribe({\n        next: success => {\n          if (success) {\n            console.log(`✅ Réponse ${currentQuestion.id} sauvegardée`);\n          } else {\n            console.warn(`⚠️ Erreur sauvegarde réponse ${currentQuestion.id}`);\n          }\n        },\n        error: error => {\n          console.error(`❌ Erreur réponse ${currentQuestion.id}:`, error);\n        }\n      });\n    }\n    // Passer à la question suivante ou terminer le test\n    if (this.currentQuestionIndex < this.questions.length - 1) {\n      this.currentQuestionIndex++;\n    } else {\n      this.completeTest();\n    }\n  }\n  /**\n   * Termine le test et calcule les résultats\n   */\n  completeTest() {\n    if (!this.testSession) return;\n    this.isLoading = true;\n    console.log('🔄 Début de l\\'analyse des résultats...');\n    // Simuler un délai d'analyse réaliste (2-3 secondes)\n    setTimeout(() => {\n      this.processTestResults();\n    }, 2500);\n  }\n  /**\n   * Traite les résultats du test\n   */\n  processTestResults() {\n    if (!this.testSession) return;\n    try {\n      console.log('📊 Calcul des scores avec', this.responses.length, 'réponses');\n      // Calculer les résultats\n      const results = this.personalityTestService.processTestResults(this.responses);\n      console.log('✅ Résultats calculés:', results);\n      // Mettre à jour la session\n      this.testSession.scores = results.scores;\n      this.testSession.finalProfile = results.profile;\n      this.testSession.completedAt = new Date();\n      this.finalProfile = results.profile;\n      // Vérifier la compatibilité avec l'iris\n      this.compatibilityResult = this.irisCompatibilityService.checkCompatibility(results.profile, this.selectedUser.email);\n      console.log('🔍 Compatibilité calculée:', this.compatibilityResult);\n      // Sauvegarder les statistiques de session\n      const sessionStats = {\n        totalQuestions: this.questions.length,\n        totalResponses: this.responses.length,\n        completionRate: this.responses.length / this.questions.length * 100,\n        averageResponseTime: this.calculateAverageResponseTime(),\n        scores: results.scores,\n        profile: results.profile,\n        userId: this.currentUser?.email,\n        completedAt: new Date().toISOString()\n      };\n      // Sauvegarder les statistiques\n      if (this.currentSessionId) {\n        this.personalityTestService.saveSessionStats(this.currentSessionId, sessionStats).subscribe({\n          next: success => {\n            console.log('📊 Statistiques de session sauvegardées:', success);\n          },\n          error: error => {\n            console.error('❌ Erreur sauvegarde stats:', error);\n          }\n        });\n      }\n      // Sauvegarder dans Firebase\n      this.personalityTestService.saveTestSession(this.testSession).subscribe({\n        next: sessionId => {\n          console.log('✅ Test sauvegardé avec l\\'ID:', sessionId);\n          this.testSession.id = sessionId;\n          this.showResultsWithDelay();\n        },\n        error: error => {\n          console.error('❌ Erreur lors de la sauvegarde:', error);\n          this.showResultsWithDelay();\n        }\n      });\n    } catch (error) {\n      console.error('❌ Erreur lors du traitement des résultats:', error);\n      this.showResultsWithDelay();\n    }\n  }\n  /**\n   * Affiche les résultats avec un délai pour une transition fluide\n   */\n  showResultsWithDelay() {\n    setTimeout(() => {\n      this.isLoading = false;\n      this.isTestCompleted = true;\n      this.showResults = true;\n      console.log('🎉 Résultats affichés !');\n    }, 500);\n  }\n  /**\n   * Calcule le temps de réponse moyen\n   */\n  calculateAverageResponseTime() {\n    if (this.responses.length === 0) return 0;\n    let totalTime = 0;\n    for (let i = 1; i < this.responses.length; i++) {\n      const timeDiff = this.responses[i].timestamp.getTime() - this.responses[i - 1].timestamp.getTime();\n      totalTime += timeDiff;\n    }\n    return totalTime / (this.responses.length - 1);\n  }\n  /**\n   * Redémarre le test\n   */\n  restartTest() {\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.showResults = false;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.finalProfile = null;\n    this.compatibilityResult = null;\n    this.currentSessionId = '';\n    this.initializeTest();\n  }\n  /**\n   * Retourne à l'accueil\n   */\n  goToHome() {\n    this.router.navigate(['/accueil']);\n  }\n  /**\n   * Déconnexion de l'utilisateur\n   */\n  logout() {\n    localStorage.removeItem('currentUser');\n    this.router.navigate(['/login']);\n  }\n  /**\n   * Obtient la question actuelle\n   */\n  get currentQuestion() {\n    return this.questions[this.currentQuestionIndex] || null;\n  }\n  /**\n   * Obtient le pourcentage de progression\n   */\n  get progressPercentage() {\n    return (this.currentQuestionIndex + 1) / this.questions.length * 100;\n  }\n  /**\n   * Obtient le numéro de la question actuelle\n   */\n  get currentQuestionNumber() {\n    return this.currentQuestionIndex + 1;\n  }\n  /**\n   * Obtient le nombre total de questions\n   */\n  get totalQuestions() {\n    return this.questions.length;\n  }\n  static {\n    this.ɵfac = function PersonalityTestComponent_Factory(t) {\n      return new (t || PersonalityTestComponent)(i0.ɵɵdirectiveInject(i1.PersonalityTestService), i0.ɵɵdirectiveInject(i2.IrisCompatibilityService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PersonalityTestComponent,\n      selectors: [[\"app-personality-test\"]],\n      decls: 5,\n      vars: 4,\n      consts: [[1, \"personality-test-container\"], [\"class\", \"test-intro\", 4, \"ngIf\"], [\"class\", \"test-interface\", 4, \"ngIf\"], [\"class\", \"loading-screen\", 4, \"ngIf\"], [\"class\", \"test-results\", 4, \"ngIf\"], [1, \"test-intro\"], [1, \"intro-card\"], [1, \"intro-header\"], [1, \"title\"], [1, \"divider\"], [1, \"subtitle\"], [1, \"intro-content\"], [1, \"test-info\"], [1, \"info-item\"], [1, \"icon\"], [1, \"info-text\"], [1, \"user-info\"], [1, \"note\"], [\"class\", \"user-actions\", 4, \"ngIf\"], [1, \"intro-actions\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"user-actions\"], [1, \"btn\", \"btn-logout\", 3, \"click\"], [1, \"test-interface\"], [1, \"test-card\"], [1, \"progress-section\"], [1, \"progress-info\"], [1, \"question-counter\"], [1, \"progress-percentage\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [\"class\", \"question-section\", 4, \"ngIf\"], [1, \"question-section\"], [1, \"question-content\"], [1, \"question-text\"], [1, \"answer-buttons\"], [1, \"btn\", \"btn-answer\", \"btn-yes\", 3, \"disabled\", \"click\"], [1, \"answer-icon\"], [1, \"btn\", \"btn-answer\", \"btn-no\", 3, \"disabled\", \"click\"], [1, \"loading-screen\"], [1, \"loading-card\"], [1, \"loading-spinner\"], [1, \"test-results\"], [1, \"results-card\"], [1, \"results-header\"], [1, \"profile-summary\"], [1, \"profile-badge\"], [1, \"profile-name\"], [1, \"confidence-score\"], [1, \"score-label\"], [1, \"score-value\"], [\"class\", \"profile-type\", 4, \"ngIf\"], [1, \"profile-description\"], [\"class\", \"iris-compatibility\", 4, \"ngIf\"], [\"class\", \"detailed-scores\", 4, \"ngIf\"], [1, \"test-info-summary\"], [1, \"info-row\"], [1, \"label\"], [1, \"value\"], [\"class\", \"info-row\", 4, \"ngIf\"], [1, \"results-actions\"], [\"class\", \"btn btn-logout\", 3, \"click\", 4, \"ngIf\"], [1, \"profile-type\"], [1, \"type-badge\", \"intermediate\"], [1, \"type-badge\", \"primary\"], [1, \"iris-compatibility\"], [1, \"compatibility-summary\"], [1, \"compatibility-header\"], [1, \"iris-info\"], [1, \"compatibility-score\"], [1, \"score-circle\"], [1, \"compatibility-status\"], [1, \"status-badge\"], [1, \"iris-characteristics\"], [1, \"characteristics-list\"], [\"class\", \"characteristic-tag\", 4, \"ngFor\", \"ngForOf\"], [1, \"recommendations\"], [1, \"recommendations-list\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"corrections\", 4, \"ngIf\"], [1, \"characteristic-tag\"], [1, \"corrections\"], [1, \"corrections-list\"], [1, \"detailed-scores\"], [1, \"scores-grid\"], [1, \"score-item\"], [1, \"score-bar\"], [1, \"score-fill\", \"flower\"], [1, \"score-fill\", \"jewel\"], [1, \"score-fill\", \"shaker\"], [1, \"score-fill\", \"stream\"]],\n      template: function PersonalityTestComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, PersonalityTestComponent_div_1_Template, 56, 3, \"div\", 1);\n          i0.ɵɵtemplate(2, PersonalityTestComponent_div_2_Template, 12, 9, \"div\", 2);\n          i0.ɵɵtemplate(3, PersonalityTestComponent_div_3_Template, 7, 0, \"div\", 3);\n          i0.ɵɵtemplate(4, PersonalityTestComponent_div_4_Template, 41, 15, \"div\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isTestStarted && !ctx.showResults);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isTestStarted && !ctx.showResults);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showResults && ctx.finalProfile);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i4.DecimalPipe, i4.DatePipe],\n      styles: [\".personality-test-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 2rem;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n}\\n\\n.intro-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%], .loading-card[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 20px;\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\\n  padding: 3rem;\\n  max-width: 800px;\\n  width: 100%;\\n  margin: 0 auto;\\n}\\n\\n.title[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  color: #2d3748;\\n  text-align: center;\\n  margin-bottom: 1rem;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  color: #718096;\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  margin: 0 auto 2rem;\\n  border-radius: 2px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 1rem 2rem;\\n  border: none;\\n  border-radius: 12px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  text-decoration: none;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n}\\n.btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%] {\\n  background: #e2e8f0;\\n  color: #4a5568;\\n}\\n.btn.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background: #cbd5e0;\\n  transform: translateY(-1px);\\n}\\n.btn.btn-logout[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\\n  color: white;\\n}\\n.btn.btn-logout[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);\\n  transform: translateY(-2px);\\n  box-shadow: 0 10px 25px rgba(245, 101, 101, 0.4);\\n}\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  transform: none !important;\\n}\\n\\n.test-intro[_ngcontent-%COMP%]   .intro-content[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.test-intro[_ngcontent-%COMP%]   .test-info[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 1.5rem;\\n  margin-bottom: 2rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1.5rem;\\n  background: rgba(102, 126, 234, 0.1);\\n  border-radius: 12px;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .info-item[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.1);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  color: #2d3748;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0.5rem 0;\\n  color: #4a5568;\\n}\\n.test-intro[_ngcontent-%COMP%]   .user-info[_ngcontent-%COMP%]   p.note[_ngcontent-%COMP%] {\\n  font-style: italic;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n.test-intro[_ngcontent-%COMP%]   .intro-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-top: 2rem;\\n}\\n@media (max-width: 768px) {\\n  .test-intro[_ngcontent-%COMP%]   .intro-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n.test-interface[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .question-counter[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-info[_ngcontent-%COMP%]   .progress-percentage[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #667eea;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  background: #e2e8f0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n.test-interface[_ngcontent-%COMP%]   .progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  transition: width 0.3s ease;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%] {\\n  margin-bottom: 3rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .question-content[_ngcontent-%COMP%]   .question-text[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  font-weight: 600;\\n  color: #2d3748;\\n  line-height: 1.4;\\n  margin: 0;\\n}\\n.test-interface[_ngcontent-%COMP%]   .answer-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n  justify-content: center;\\n}\\n@media (max-width: 768px) {\\n  .test-interface[_ngcontent-%COMP%]   .answer-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer[_ngcontent-%COMP%] {\\n  padding: 1.5rem 3rem;\\n  font-size: 1.3rem;\\n  min-width: 150px;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer[_ngcontent-%COMP%]   .answer-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-yes[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\\n  color: white;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-yes[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 15px 30px rgba(72, 187, 120, 0.4);\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-no[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\\n  color: white;\\n}\\n.test-interface[_ngcontent-%COMP%]   .btn-answer.btn-no[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 15px 30px rgba(245, 101, 101, 0.4);\\n}\\n\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border: 4px solid #e2e8f0;\\n  border-top: 4px solid #667eea;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin: 0 auto 2rem;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin-bottom: 1rem;\\n}\\n.loading-screen[_ngcontent-%COMP%]   .loading-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #718096;\\n  margin: 0;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-summary[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 2rem;\\n  border-radius: 20px;\\n  margin-bottom: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-flower[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbb6ce 0%, #f687b3 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-jewel[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #90cdf4 0%, #63b3ed 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-shaker[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbd38d 0%, #f6ad55 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-stream[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9ae6b4 0%, #68d391 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-flowerjewel[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbb6ce 0%, #90cdf4 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-jewelshaker[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #90cdf4 0%, #fbd38d 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-shakerstream[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fbd38d 0%, #9ae6b4 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge.profile-streamflower[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9ae6b4 0%, #fbb6ce 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .profile-name[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  margin: 0 0 1rem 0;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-badge[_ngcontent-%COMP%]   .confidence-score[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%] {\\n  margin-bottom: 2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.5rem 1rem;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge.primary[_ngcontent-%COMP%] {\\n  background: rgba(102, 126, 234, 0.2);\\n  color: #667eea;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-type[_ngcontent-%COMP%]   .type-badge.intermediate[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.2);\\n  color: #764ba2;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%] {\\n  background: rgba(102, 126, 234, 0.1);\\n  padding: 2rem;\\n  border-radius: 12px;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .profile-description[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  line-height: 1.6;\\n  margin: 0;\\n  font-size: 1.1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1.5rem 0;\\n  text-align: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  border-radius: 12px;\\n  margin-bottom: 2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary.compatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(56, 161, 105, 0.1) 100%);\\n  border: 2px solid rgba(72, 187, 120, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary.incompatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(229, 62, 62, 0.1) 100%);\\n  border: 2px solid rgba(245, 101, 101, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n}\\n@media (max-width: 768px) {\\n  .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .iris-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .iris-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 0.5rem 0;\\n  font-size: 1.2rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .iris-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle.high-score[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle.low-score[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-header[_ngcontent-%COMP%]   .compatibility-score[_ngcontent-%COMP%]   .score-circle[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n  font-size: 0.7rem;\\n  font-weight: 500;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.5rem 1.5rem;\\n  border-radius: 20px;\\n  font-weight: 600;\\n  font-size: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%]   .status-badge.compatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);\\n  color: white;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .compatibility-summary[_ngcontent-%COMP%]   .compatibility-status[_ngcontent-%COMP%]   .status-badge.incompatible[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\\n  color: white;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.1);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  margin-bottom: 1.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%]   .characteristics-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .iris-characteristics[_ngcontent-%COMP%]   .characteristics-list[_ngcontent-%COMP%]   .characteristic-tag[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 0.3rem 0.8rem;\\n  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);\\n  color: white;\\n  border-radius: 15px;\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.5);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  margin-bottom: 1.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 1.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  line-height: 1.6;\\n  margin-bottom: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .recommendations[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child, .test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%] {\\n  border-left: 4px solid #f56565;\\n}\\n.test-results[_ngcontent-%COMP%]   .iris-compatibility[_ngcontent-%COMP%]   .corrections[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #e53e3e;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%] {\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  margin: 0 0 1.5rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .scores-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 1rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  padding: 1rem;\\n  background: rgba(255, 255, 255, 0.5);\\n  border-radius: 8px;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {\\n  min-width: 80px;\\n  font-weight: 600;\\n  color: #4a5568;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 8px;\\n  background: #e2e8f0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transition: width 0.5s ease;\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.flower[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #fbb6ce, #f687b3);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.jewel[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #90cdf4, #63b3ed);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.shaker[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #fbd38d, #f6ad55);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-bar[_ngcontent-%COMP%]   .score-fill.stream[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, #9ae6b4, #68d391);\\n}\\n.test-results[_ngcontent-%COMP%]   .detailed-scores[_ngcontent-%COMP%]   .score-item[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {\\n  min-width: 40px;\\n  font-weight: 600;\\n  color: #2d3748;\\n  text-align: right;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%] {\\n  background: rgba(118, 75, 162, 0.1);\\n  padding: 1.5rem;\\n  border-radius: 12px;\\n  margin: 2rem 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.5rem;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #4a5568;\\n}\\n.test-results[_ngcontent-%COMP%]   .test-info-summary[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: #2d3748;\\n  font-family: \\\"Courier New\\\", monospace;\\n}\\n.test-results[_ngcontent-%COMP%]   .results-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 1rem;\\n  justify-content: center;\\n  margin-top: 2rem;\\n}\\n@media (max-width: 768px) {\\n  .test-results[_ngcontent-%COMP%]   .results-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n@media (max-width: 768px) {\\n  .personality-test-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .intro-card[_ngcontent-%COMP%], .test-card[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%], .loading-card[_ngcontent-%COMP%] {\\n    padding: 2rem;\\n  }\\n  .title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .question-text[_ngcontent-%COMP%] {\\n    font-size: 1.4rem !important;\\n  }\\n  .btn-answer[_ngcontent-%COMP%] {\\n    padding: 1rem 2rem !important;\\n    font-size: 1.1rem !important;\\n    min-width: 120px !important;\\n  }\\n  .profile-name[_ngcontent-%COMP%] {\\n    font-size: 1.5rem !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["PERSONALITY_QUESTIONS", "i0", "ɵɵelementStart", "ɵɵlistener", "PersonalityTestComponent_div_1_div_47_Template_button_click_1_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "PersonalityTestComponent_div_1_div_47_Template", "PersonalityTestComponent_div_1_Template_button_click_49_listener", "_r8", "ctx_r7", "startTest", "PersonalityTestComponent_div_1_Template_button_click_54_listener", "ctx_r9", "goToHome", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "selected<PERSON>ser", "name", "email", "ɵɵproperty", "currentUser", "PersonalityTestComponent_div_2_div_11_Template_button_click_5_listener", "_r12", "ctx_r11", "answerQuestion", "PersonalityTestComponent_div_2_div_11_Template_button_click_10_listener", "ctx_r13", "ɵɵtextInterpolate", "ctx_r10", "currentQuestion", "question", "isLoading", "PersonalityTestComponent_div_2_div_11_Template", "ɵɵtextInterpolate2", "ctx_r1", "currentQuestionNumber", "totalQuestions", "ɵɵpipeBind2", "progressPercentage", "ɵɵstyleProp", "characteristic_r23", "recommendation_r24", "correction_r26", "PersonalityTestComponent_div_4_div_22_div_29_li_4_Template", "ctx_r22", "compatibilityResult", "corrections", "PersonalityTestComponent_div_4_div_22_span_23_Template", "PersonalityTestComponent_div_4_div_22_li_28_Template", "PersonalityTestComponent_div_4_div_22_div_29_Template", "ɵɵclassMap", "ctx_r16", "isCompatible", "irisType", "description", "compatibilityScore", "characteristics", "recommendations", "ctx_r17", "testSession", "scores", "flower", "jewel", "shaker", "stream", "ctx_r18", "id", "PersonalityTestComponent_div_4_button_40_Template_button_click_0_listener", "_r28", "ctx_r27", "PersonalityTestComponent_div_4_div_15_Template", "PersonalityTestComponent_div_4_div_16_Template", "PersonalityTestComponent_div_4_div_22_Template", "PersonalityTestComponent_div_4_div_23_Template", "PersonalityTestComponent_div_4_div_31_Template", "PersonalityTestComponent_div_4_Template_button_click_33_listener", "_r30", "ctx_r29", "restartTest", "PersonalityTestComponent_div_4_Template_button_click_38_listener", "ctx_r31", "PersonalityTestComponent_div_4_button_40_Template", "ctx_r3", "finalProfile", "primaryClass", "toLowerCase", "replace", "confidenceScore", "isIntermediate", "completedAt", "PersonalityTestComponent", "constructor", "personalityTestService", "irisCompatibilityService", "router", "questions", "currentQuestionIndex", "responses", "isTestStarted", "isTestCompleted", "showResults", "currentSessionId", "staticUsers", "ngOnInit", "currentUserData", "localStorage", "getItem", "JSON", "parse", "initializeTest", "createTestSession", "selectUser", "user", "Date", "now", "Math", "random", "toString", "substr", "startedAt", "console", "log", "answer", "response", "questionId", "timestamp", "push", "saveIndividualResponse", "subscribe", "next", "success", "warn", "error", "length", "completeTest", "setTimeout", "processTestResults", "results", "profile", "checkCompatibility", "sessionStats", "totalResponses", "completionRate", "averageResponseTime", "calculateAverageResponseTime", "userId", "toISOString", "saveSessionStats", "saveTestSession", "sessionId", "showResultsWithDelay", "totalTime", "i", "timeDiff", "getTime", "navigate", "removeItem", "ɵɵdirectiveInject", "i1", "PersonalityTestService", "i2", "IrisCompatibilityService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "PersonalityTestComponent_Template", "rf", "ctx", "PersonalityTestComponent_div_1_Template", "PersonalityTestComponent_div_2_Template", "PersonalityTestComponent_div_3_Template", "PersonalityTestComponent_div_4_Template"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\personality-test\\personality-test.component.ts", "D:\\ProjetPfa\\pfa\\pfa\\src\\app\\personality-test\\personality-test.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { PersonalityTestService } from '../services/personality-test.service';\nimport { IrisCompatibilityService, CompatibilityResult } from '../services/iris-compatibility.service';\nimport {\n  Question,\n  UserResponse,\n  TestSession,\n  PersonalityProfile,\n  PERSONALITY_QUESTIONS\n} from '../models/personality-test.model';\n\n@Component({\n  selector: 'app-personality-test',\n  templateUrl: './personality-test.component.html',\n  styleUrls: ['./personality-test.component.scss']\n})\nexport class PersonalityTestComponent implements OnInit {\n  // Données du test\n  questions: Question[] = PERSONALITY_QUESTIONS;\n  currentQuestionIndex: number = 0;\n  responses: UserResponse[] = [];\n  testSession: TestSession | null = null;\n\n  // État du composant\n  isTestStarted: boolean = false;\n  isTestCompleted: boolean = false;\n  isLoading: boolean = false;\n  showResults: boolean = false;\n  currentSessionId: string = '';\n\n  // Comptes statiques pour les tests\n  staticUsers = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      description: 'Profil créatif et émotionnel'\n    },\n    {\n      id: 2,\n      name: 'Jean Martin',\n      email: '<EMAIL>',\n      description: 'Profil analytique et structuré'\n    },\n    {\n      id: 3,\n      name: 'Sophie Leroy',\n      email: '<EMAIL>',\n      description: 'Profil dynamique et aventurier'\n    },\n    {\n      id: 4,\n      name: 'Pierre Moreau',\n      email: '<EMAIL>',\n      description: 'Profil paisible et réfléchi'\n    },\n    {\n      id: 5,\n      name: 'Emma Bernard',\n      email: '<EMAIL>',\n      description: 'Profil mixte créatif-analytique'\n    }\n  ];\n\n  selectedUser = this.staticUsers[0]; // Utilisateur par défaut\n  currentUser: any = null; // Utilisateur connecté\n\n  // Résultats\n  finalProfile: PersonalityProfile | null = null;\n  compatibilityResult: CompatibilityResult | null = null;\n\n  constructor(\n    private personalityTestService: PersonalityTestService,\n    private irisCompatibilityService: IrisCompatibilityService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Récupérer l'utilisateur connecté\n    const currentUserData = localStorage.getItem('currentUser');\n    if (currentUserData) {\n      this.currentUser = JSON.parse(currentUserData);\n      // Utiliser l'utilisateur connecté pour le test\n      this.selectedUser = {\n        id: 0,\n        name: this.currentUser.name,\n        email: this.currentUser.email,\n        description: 'Utilisateur connecté'\n      };\n    }\n    this.initializeTest();\n  }\n\n  /**\n   * Initialise le test\n   */\n  initializeTest(): void {\n    this.testSession = this.personalityTestService.createTestSession(\n      this.selectedUser.name,\n      this.selectedUser.email\n    );\n  }\n\n  /**\n   * Sélectionne un utilisateur de test\n   */\n  selectUser(user: any): void {\n    this.selectedUser = user;\n    this.initializeTest();\n  }\n\n  /**\n   * Démarre le test\n   */\n  startTest(): void {\n    this.isTestStarted = true;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n\n    if (this.testSession) {\n      this.testSession.startedAt = new Date();\n      this.testSession.id = this.currentSessionId;\n    }\n\n    console.log('🚀 Test démarré - Session ID:', this.currentSessionId);\n  }\n\n  /**\n   * Traite la réponse à une question\n   */\n  answerQuestion(answer: boolean): void {\n    if (!this.testSession) return;\n\n    const currentQuestion = this.questions[this.currentQuestionIndex];\n    const response: UserResponse = {\n      questionId: currentQuestion.id,\n      answer: answer,\n      timestamp: new Date()\n    };\n\n    this.responses.push(response);\n    this.testSession.responses = this.responses;\n\n    // Sauvegarder la réponse individuelle en temps réel\n    if (this.currentUser && this.currentSessionId) {\n      this.personalityTestService.saveIndividualResponse(\n        this.currentUser.email,\n        this.currentSessionId,\n        response\n      ).subscribe({\n        next: (success) => {\n          if (success) {\n            console.log(`✅ Réponse ${currentQuestion.id} sauvegardée`);\n          } else {\n            console.warn(`⚠️ Erreur sauvegarde réponse ${currentQuestion.id}`);\n          }\n        },\n        error: (error) => {\n          console.error(`❌ Erreur réponse ${currentQuestion.id}:`, error);\n        }\n      });\n    }\n\n    // Passer à la question suivante ou terminer le test\n    if (this.currentQuestionIndex < this.questions.length - 1) {\n      this.currentQuestionIndex++;\n    } else {\n      this.completeTest();\n    }\n  }\n\n  /**\n   * Termine le test et calcule les résultats\n   */\n  completeTest(): void {\n    if (!this.testSession) return;\n\n    this.isLoading = true;\n    console.log('🔄 Début de l\\'analyse des résultats...');\n\n    // Simuler un délai d'analyse réaliste (2-3 secondes)\n    setTimeout(() => {\n      this.processTestResults();\n    }, 2500);\n  }\n\n  /**\n   * Traite les résultats du test\n   */\n  private processTestResults(): void {\n    if (!this.testSession) return;\n\n    try {\n      console.log('📊 Calcul des scores avec', this.responses.length, 'réponses');\n\n      // Calculer les résultats\n      const results = this.personalityTestService.processTestResults(this.responses);\n      console.log('✅ Résultats calculés:', results);\n\n      // Mettre à jour la session\n      this.testSession.scores = results.scores;\n      this.testSession.finalProfile = results.profile;\n      this.testSession.completedAt = new Date();\n\n      this.finalProfile = results.profile;\n\n      // Vérifier la compatibilité avec l'iris\n      this.compatibilityResult = this.irisCompatibilityService.checkCompatibility(\n        results.profile,\n        this.selectedUser.email\n      );\n      console.log('🔍 Compatibilité calculée:', this.compatibilityResult);\n\n      // Sauvegarder les statistiques de session\n      const sessionStats = {\n        totalQuestions: this.questions.length,\n        totalResponses: this.responses.length,\n        completionRate: (this.responses.length / this.questions.length) * 100,\n        averageResponseTime: this.calculateAverageResponseTime(),\n        scores: results.scores,\n        profile: results.profile,\n        userId: this.currentUser?.email,\n        completedAt: new Date().toISOString()\n      };\n\n      // Sauvegarder les statistiques\n      if (this.currentSessionId) {\n        this.personalityTestService.saveSessionStats(this.currentSessionId, sessionStats).subscribe({\n          next: (success) => {\n            console.log('📊 Statistiques de session sauvegardées:', success);\n          },\n          error: (error) => {\n            console.error('❌ Erreur sauvegarde stats:', error);\n          }\n        });\n      }\n\n      // Sauvegarder dans Firebase\n      this.personalityTestService.saveTestSession(this.testSession).subscribe({\n        next: (sessionId) => {\n          console.log('✅ Test sauvegardé avec l\\'ID:', sessionId);\n          this.testSession!.id = sessionId;\n          this.showResultsWithDelay();\n        },\n        error: (error) => {\n          console.error('❌ Erreur lors de la sauvegarde:', error);\n          this.showResultsWithDelay();\n        }\n      });\n\n    } catch (error) {\n      console.error('❌ Erreur lors du traitement des résultats:', error);\n      this.showResultsWithDelay();\n    }\n  }\n\n  /**\n   * Affiche les résultats avec un délai pour une transition fluide\n   */\n  private showResultsWithDelay(): void {\n    setTimeout(() => {\n      this.isLoading = false;\n      this.isTestCompleted = true;\n      this.showResults = true;\n      console.log('🎉 Résultats affichés !');\n    }, 500);\n  }\n\n  /**\n   * Calcule le temps de réponse moyen\n   */\n  private calculateAverageResponseTime(): number {\n    if (this.responses.length === 0) return 0;\n\n    let totalTime = 0;\n    for (let i = 1; i < this.responses.length; i++) {\n      const timeDiff = this.responses[i].timestamp.getTime() - this.responses[i-1].timestamp.getTime();\n      totalTime += timeDiff;\n    }\n\n    return totalTime / (this.responses.length - 1);\n  }\n\n  /**\n   * Redémarre le test\n   */\n  restartTest(): void {\n    this.isTestStarted = false;\n    this.isTestCompleted = false;\n    this.showResults = false;\n    this.currentQuestionIndex = 0;\n    this.responses = [];\n    this.finalProfile = null;\n    this.compatibilityResult = null;\n    this.currentSessionId = '';\n    this.initializeTest();\n  }\n\n  /**\n   * Retourne à l'accueil\n   */\n  goToHome(): void {\n    this.router.navigate(['/accueil']);\n  }\n\n  /**\n   * Déconnexion de l'utilisateur\n   */\n  logout(): void {\n    localStorage.removeItem('currentUser');\n    this.router.navigate(['/login']);\n  }\n\n  /**\n   * Obtient la question actuelle\n   */\n  get currentQuestion(): Question | null {\n    return this.questions[this.currentQuestionIndex] || null;\n  }\n\n  /**\n   * Obtient le pourcentage de progression\n   */\n  get progressPercentage(): number {\n    return ((this.currentQuestionIndex + 1) / this.questions.length) * 100;\n  }\n\n  /**\n   * Obtient le numéro de la question actuelle\n   */\n  get currentQuestionNumber(): number {\n    return this.currentQuestionIndex + 1;\n  }\n\n  /**\n   * Obtient le nombre total de questions\n   */\n  get totalQuestions(): number {\n    return this.questions.length;\n  }\n}\n", "<div class=\"personality-test-container\">\n  <!-- Page d'accueil du test -->\n  <div class=\"test-intro\" *ngIf=\"!isTestStarted && !showResults\">\n    <div class=\"intro-card\">\n      <div class=\"intro-header\">\n        <h1 class=\"title\">Test de Personnalité</h1>\n        <div class=\"divider\"></div>\n        <p class=\"subtitle\">Découvrez votre profil psychotechnique</p>\n      </div>\n\n      <div class=\"intro-content\">\n        <div class=\"test-info\">\n          <div class=\"info-item\">\n            <span class=\"icon\">📝</span>\n            <div class=\"info-text\">\n              <h3>32 Questions</h3>\n              <p>Questions ciblées pour analyser votre personnalité</p>\n            </div>\n          </div>\n\n          <div class=\"info-item\">\n            <span class=\"icon\">⏱️</span>\n            <div class=\"info-text\">\n              <h3>5-10 Minutes</h3>\n              <p>Temps estimé pour compléter le test</p>\n            </div>\n          </div>\n\n          <div class=\"info-item\">\n            <span class=\"icon\">🎯</span>\n            <div class=\"info-text\">\n              <h3>4 Profils Principaux</h3>\n              <p>Flower, Jewel, Shaker, Stream + profils intermédiaires</p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"user-info\">\n          <h3>Informations du test</h3>\n          <p><strong>Nom:</strong> {{ selectedUser.name }}</p>\n          <p><strong>Email:</strong> {{ selectedUser.email }}</p>\n          <p class=\"note\">Les résultats seront sauvegardés dans la base de données PFA1</p>\n\n          <div class=\"user-actions\" *ngIf=\"currentUser\">\n            <button class=\"btn btn-logout\" (click)=\"logout()\">\n              <span>Se déconnecter</span>\n              <span class=\"icon\">🚪</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"intro-actions\">\n        <button class=\"btn btn-primary\" (click)=\"startTest()\">\n          <span>Commencer le Test</span>\n          <span class=\"icon\">→</span>\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"goToHome()\">\n          Retour à l'accueil\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Interface du test -->\n  <div class=\"test-interface\" *ngIf=\"isTestStarted && !showResults\">\n    <div class=\"test-card\">\n      <!-- Barre de progression -->\n      <div class=\"progress-section\">\n        <div class=\"progress-info\">\n          <span class=\"question-counter\">Question {{ currentQuestionNumber }} sur {{ totalQuestions }}</span>\n          <span class=\"progress-percentage\">{{ progressPercentage | number:'1.0-0' }}%</span>\n        </div>\n        <div class=\"progress-bar\">\n          <div class=\"progress-fill\" [style.width.%]=\"progressPercentage\"></div>\n        </div>\n      </div>\n\n      <!-- Question actuelle -->\n      <div class=\"question-section\" *ngIf=\"currentQuestion\">\n        <div class=\"question-content\">\n          <h2 class=\"question-text\">{{ currentQuestion.question }}</h2>\n        </div>\n\n        <div class=\"answer-buttons\">\n          <button\n            class=\"btn btn-answer btn-yes\"\n            (click)=\"answerQuestion(true)\"\n            [disabled]=\"isLoading\">\n            <span class=\"answer-icon\">✓</span>\n            <span>Oui</span>\n          </button>\n\n          <button\n            class=\"btn btn-answer btn-no\"\n            (click)=\"answerQuestion(false)\"\n            [disabled]=\"isLoading\">\n            <span class=\"answer-icon\">✗</span>\n            <span>Non</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Écran de chargement -->\n  <div class=\"loading-screen\" *ngIf=\"isLoading\">\n    <div class=\"loading-card\">\n      <div class=\"loading-spinner\"></div>\n      <h2>Analyse de votre profil...</h2>\n      <p>Calcul des scores et détermination de votre personnalité</p>\n    </div>\n  </div>\n\n  <!-- Résultats du test -->\n  <div class=\"test-results\" *ngIf=\"showResults && finalProfile\">\n    <div class=\"results-card\">\n      <div class=\"results-header\">\n        <h1 class=\"title\">Votre Profil de Personnalité</h1>\n        <div class=\"divider\"></div>\n      </div>\n\n      <div class=\"profile-summary\">\n        <div class=\"profile-badge\" [class]=\"'profile-' + finalProfile.primaryClass.toLowerCase().replace('-', '')\">\n          <h2 class=\"profile-name\">{{ finalProfile.primaryClass }}</h2>\n          <div class=\"confidence-score\">\n            <span class=\"score-label\">Score de confiance</span>\n            <span class=\"score-value\">{{ finalProfile.confidenceScore }}%</span>\n          </div>\n        </div>\n\n        <div class=\"profile-type\" *ngIf=\"finalProfile.isIntermediate\">\n          <span class=\"type-badge intermediate\">Profil Intermédiaire</span>\n        </div>\n        <div class=\"profile-type\" *ngIf=\"!finalProfile.isIntermediate\">\n          <span class=\"type-badge primary\">Profil Principal</span>\n        </div>\n      </div>\n\n      <div class=\"profile-description\">\n        <h3>Description de votre profil</h3>\n        <p>{{ finalProfile.description }}</p>\n      </div>\n\n      <!-- Compatibilité avec l'iris -->\n      <div class=\"iris-compatibility\" *ngIf=\"compatibilityResult\">\n        <h3>Compatibilité avec votre Iris</h3>\n\n        <div class=\"compatibility-summary\" [class]=\"compatibilityResult.isCompatible ? 'compatible' : 'incompatible'\">\n          <div class=\"compatibility-header\">\n            <div class=\"iris-info\">\n              <h4>{{ compatibilityResult.irisType.name }}</h4>\n              <p>{{ compatibilityResult.irisType.description }}</p>\n            </div>\n            <div class=\"compatibility-score\">\n              <div class=\"score-circle\" [class]=\"compatibilityResult.isCompatible ? 'high-score' : 'low-score'\">\n                <span class=\"score-value\">{{ compatibilityResult.compatibilityScore }}%</span>\n                <span class=\"score-label\">Compatibilité</span>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"compatibility-status\">\n            <span class=\"status-badge\" [class]=\"compatibilityResult.isCompatible ? 'compatible' : 'incompatible'\">\n              {{ compatibilityResult.isCompatible ? '✓ Compatible' : '⚠ Incompatible' }}\n            </span>\n          </div>\n        </div>\n\n        <div class=\"iris-characteristics\">\n          <h4>Caractéristiques de votre iris</h4>\n          <div class=\"characteristics-list\">\n            <span *ngFor=\"let characteristic of compatibilityResult.irisType.characteristics\"\n                  class=\"characteristic-tag\">\n              {{ characteristic }}\n            </span>\n          </div>\n        </div>\n\n        <div class=\"recommendations\">\n          <h4>Recommandations</h4>\n          <ul class=\"recommendations-list\">\n            <li *ngFor=\"let recommendation of compatibilityResult.recommendations\">\n              {{ recommendation }}\n            </li>\n          </ul>\n        </div>\n\n        <div class=\"corrections\" *ngIf=\"!compatibilityResult.isCompatible\">\n          <h4>Corrections suggérées</h4>\n          <ul class=\"corrections-list\">\n            <li *ngFor=\"let correction of compatibilityResult.corrections\">\n              {{ correction }}\n            </li>\n          </ul>\n        </div>\n      </div>\n\n      <div class=\"detailed-scores\" *ngIf=\"testSession\">\n        <h3>Scores détaillés</h3>\n        <div class=\"scores-grid\">\n          <div class=\"score-item\">\n            <span class=\"score-label\">Flower</span>\n            <div class=\"score-bar\">\n              <div class=\"score-fill flower\" [style.width.%]=\"(testSession.scores.flower / 4) * 100\"></div>\n            </div>\n            <span class=\"score-value\">{{ testSession.scores.flower }}/4</span>\n          </div>\n\n          <div class=\"score-item\">\n            <span class=\"score-label\">Jewel</span>\n            <div class=\"score-bar\">\n              <div class=\"score-fill jewel\" [style.width.%]=\"(testSession.scores.jewel / 4) * 100\"></div>\n            </div>\n            <span class=\"score-value\">{{ testSession.scores.jewel }}/4</span>\n          </div>\n\n          <div class=\"score-item\">\n            <span class=\"score-label\">Shaker</span>\n            <div class=\"score-bar\">\n              <div class=\"score-fill shaker\" [style.width.%]=\"(testSession.scores.shaker / 4) * 100\"></div>\n            </div>\n            <span class=\"score-value\">{{ testSession.scores.shaker }}/4</span>\n          </div>\n\n          <div class=\"score-item\">\n            <span class=\"score-label\">Stream</span>\n            <div class=\"score-bar\">\n              <div class=\"score-fill stream\" [style.width.%]=\"(testSession.scores.stream / 4) * 100\"></div>\n            </div>\n            <span class=\"score-value\">{{ testSession.scores.stream }}/4</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"test-info-summary\">\n        <div class=\"info-row\">\n          <span class=\"label\">Test complété le:</span>\n          <span class=\"value\">{{ testSession?.completedAt | date:'dd/MM/yyyy à HH:mm' }}</span>\n        </div>\n        <div class=\"info-row\" *ngIf=\"testSession?.id\">\n          <span class=\"label\">ID de session:</span>\n          <span class=\"value\">{{ testSession?.id }}</span>\n        </div>\n      </div>\n\n      <div class=\"results-actions\">\n        <button class=\"btn btn-primary\" (click)=\"restartTest()\">\n          <span>Refaire le Test</span>\n          <span class=\"icon\">🔄</span>\n        </button>\n        <button class=\"btn btn-secondary\" (click)=\"goToHome()\">\n          Retour à l'accueil\n        </button>\n        <button class=\"btn btn-logout\" (click)=\"logout()\" *ngIf=\"currentUser\">\n          <span>Se déconnecter</span>\n          <span class=\"icon\">🚪</span>\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAIA,SAKEA,qBAAqB,QAChB,kCAAkC;;;;;;;;;ICiC/BC,EAAA,CAAAC,cAAA,cAA8C;IACbD,EAAA,CAAAE,UAAA,mBAAAC,uEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IAC/CT,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,0BAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC3BX,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAU,MAAA,mBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IA5CxCX,EAAA,CAAAC,cAAA,aAA+D;IAGvCD,EAAA,CAAAU,MAAA,gCAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC3CX,EAAA,CAAAY,SAAA,aAA2B;IAC3BZ,EAAA,CAAAC,cAAA,YAAoB;IAAAD,EAAA,CAAAU,MAAA,kDAAsC;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAGhEX,EAAA,CAAAC,cAAA,cAA2B;IAGFD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACrBX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,oEAAkD;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAI7DX,EAAA,CAAAC,cAAA,eAAuB;IACFD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACrBX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,qDAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAI9CX,EAAA,CAAAC,cAAA,eAAuB;IACFD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,4BAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC7BX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,mEAAsD;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAKnEX,EAAA,CAAAC,cAAA,eAAuB;IACjBD,EAAA,CAAAU,MAAA,4BAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC7BX,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAU,MAAA,YAAI;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAACX,EAAA,CAAAU,MAAA,IAAuB;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACpDX,EAAA,CAAAC,cAAA,SAAG;IAAQD,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAS;IAACX,EAAA,CAAAU,MAAA,IAAwB;IAAAV,EAAA,CAAAW,YAAA,EAAI;IACvDX,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAU,MAAA,oFAA6D;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEjFX,EAAA,CAAAa,UAAA,KAAAC,8CAAA,kBAKM;IACRd,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,eAA2B;IACOD,EAAA,CAAAE,UAAA,mBAAAa,iEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,MAAA,GAAAjB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAS,MAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IACnDlB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,yBAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC9BX,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAU,MAAA,cAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE7BX,EAAA,CAAAC,cAAA,kBAAuD;IAArBD,EAAA,CAAAE,UAAA,mBAAAiB,iEAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAI,MAAA,GAAApB,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAY,MAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACpDrB,EAAA,CAAAU,MAAA,iCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IApBkBX,EAAA,CAAAsB,SAAA,IAAuB;IAAvBtB,EAAA,CAAAuB,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAC,IAAA,KAAuB;IACrB1B,EAAA,CAAAsB,SAAA,GAAwB;IAAxBtB,EAAA,CAAAuB,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAE,KAAA,KAAwB;IAGxB3B,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA4B,UAAA,SAAAJ,MAAA,CAAAK,WAAA,CAAiB;;;;;;IAoChD7B,EAAA,CAAAC,cAAA,cAAsD;IAExBD,EAAA,CAAAU,MAAA,GAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAG/DX,EAAA,CAAAC,cAAA,cAA4B;IAGxBD,EAAA,CAAAE,UAAA,mBAAA4B,uEAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAC,OAAA,GAAAhC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwB,OAAA,CAAAC,cAAA,CAAe,IAAI,CAAC;IAAA,EAAC;IAE9BjC,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAU,MAAA,aAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAClCX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,UAAG;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGlBX,EAAA,CAAAC,cAAA,kBAGyB;IADvBD,EAAA,CAAAE,UAAA,mBAAAgC,wEAAA;MAAAlC,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAI,OAAA,GAAAnC,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA2B,OAAA,CAAAF,cAAA,CAAe,KAAK,CAAC;IAAA,EAAC;IAE/BjC,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,cAAC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAClCX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,WAAG;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAjBQX,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAoC,iBAAA,CAAAC,OAAA,CAAAC,eAAA,CAAAC,QAAA,CAA8B;IAOtDvC,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAA4B,UAAA,aAAAS,OAAA,CAAAG,SAAA,CAAsB;IAQtBxC,EAAA,CAAAsB,SAAA,GAAsB;IAAtBtB,EAAA,CAAA4B,UAAA,aAAAS,OAAA,CAAAG,SAAA,CAAsB;;;;;IA/BhCxC,EAAA,CAAAC,cAAA,cAAkE;IAK3BD,EAAA,CAAAU,MAAA,GAA6D;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACnGX,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAU,MAAA,GAA0C;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAErFX,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAY,SAAA,eAAsE;IACxEZ,EAAA,CAAAW,YAAA,EAAM;IAIRX,EAAA,CAAAa,UAAA,KAAA4B,8CAAA,mBAsBM;IACRzC,EAAA,CAAAW,YAAA,EAAM;;;;IAhC+BX,EAAA,CAAAsB,SAAA,GAA6D;IAA7DtB,EAAA,CAAA0C,kBAAA,cAAAC,MAAA,CAAAC,qBAAA,WAAAD,MAAA,CAAAE,cAAA,KAA6D;IAC1D7C,EAAA,CAAAsB,SAAA,GAA0C;IAA1CtB,EAAA,CAAAuB,kBAAA,KAAAvB,EAAA,CAAA8C,WAAA,OAAAH,MAAA,CAAAI,kBAAA,gBAA0C;IAGjD/C,EAAA,CAAAsB,SAAA,GAAoC;IAApCtB,EAAA,CAAAgD,WAAA,UAAAL,MAAA,CAAAI,kBAAA,MAAoC;IAKpC/C,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAA4B,UAAA,SAAAe,MAAA,CAAAL,eAAA,CAAqB;;;;;IA2BxDtC,EAAA,CAAAC,cAAA,cAA8C;IAE1CD,EAAA,CAAAY,SAAA,cAAmC;IACnCZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAU,MAAA,iCAA0B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACnCX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,yEAAwD;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;;IAqB7DX,EAAA,CAAAC,cAAA,cAA8D;IACtBD,EAAA,CAAAU,MAAA,gCAAoB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAEnEX,EAAA,CAAAC,cAAA,cAA+D;IAC5BD,EAAA,CAAAU,MAAA,uBAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAqCtDX,EAAA,CAAAC,cAAA,eACiC;IAC/BD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IADLX,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAA0B,kBAAA,MACF;;;;;IAOAjD,EAAA,CAAAC,cAAA,SAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAA2B,kBAAA,MACF;;;;;IAOAlD,EAAA,CAAAC,cAAA,SAA+D;IAC7DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAA4B,cAAA,MACF;;;;;IALJnD,EAAA,CAAAC,cAAA,cAAmE;IAC7DD,EAAA,CAAAU,MAAA,sCAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC9BX,EAAA,CAAAC,cAAA,aAA6B;IAC3BD,EAAA,CAAAa,UAAA,IAAAuC,0DAAA,iBAEK;IACPpD,EAAA,CAAAW,YAAA,EAAK;;;;IAHwBX,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA4B,UAAA,YAAAyB,OAAA,CAAAC,mBAAA,CAAAC,WAAA,CAAkC;;;;;IA9CnEvD,EAAA,CAAAC,cAAA,cAA4D;IACtDD,EAAA,CAAAU,MAAA,yCAA6B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAEtCX,EAAA,CAAAC,cAAA,cAA8G;IAGpGD,EAAA,CAAAU,MAAA,GAAuC;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAChDX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAU,MAAA,GAA8C;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAEvDX,EAAA,CAAAC,cAAA,eAAiC;IAEHD,EAAA,CAAAU,MAAA,IAA6C;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC9EX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,0BAAa;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAKpDX,EAAA,CAAAC,cAAA,eAAkC;IAE9BD,EAAA,CAAAU,MAAA,IACF;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAIXX,EAAA,CAAAC,cAAA,eAAkC;IAC5BD,EAAA,CAAAU,MAAA,2CAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACvCX,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAa,UAAA,KAAA2C,sDAAA,mBAGO;IACTxD,EAAA,CAAAW,YAAA,EAAM;IAGRX,EAAA,CAAAC,cAAA,eAA6B;IACvBD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACxBX,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAAa,UAAA,KAAA4C,oDAAA,iBAEK;IACPzD,EAAA,CAAAW,YAAA,EAAK;IAGPX,EAAA,CAAAa,UAAA,KAAA6C,qDAAA,kBAOM;IACR1D,EAAA,CAAAW,YAAA,EAAM;;;;IAhD+BX,EAAA,CAAAsB,SAAA,GAA0E;IAA1EtB,EAAA,CAAA2D,UAAA,CAAAC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,iCAA0E;IAGnG7D,EAAA,CAAAsB,SAAA,GAAuC;IAAvCtB,EAAA,CAAAoC,iBAAA,CAAAwB,OAAA,CAAAN,mBAAA,CAAAQ,QAAA,CAAApC,IAAA,CAAuC;IACxC1B,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAoC,iBAAA,CAAAwB,OAAA,CAAAN,mBAAA,CAAAQ,QAAA,CAAAC,WAAA,CAA8C;IAGvB/D,EAAA,CAAAsB,SAAA,GAAuE;IAAvEtB,EAAA,CAAA2D,UAAA,CAAAC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,8BAAuE;IACrE7D,EAAA,CAAAsB,SAAA,GAA6C;IAA7CtB,EAAA,CAAAuB,kBAAA,KAAAqC,OAAA,CAAAN,mBAAA,CAAAU,kBAAA,MAA6C;IAOhDhE,EAAA,CAAAsB,SAAA,GAA0E;IAA1EtB,EAAA,CAAA2D,UAAA,CAAAC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,iCAA0E;IACnG7D,EAAA,CAAAsB,SAAA,GACF;IADEtB,EAAA,CAAAuB,kBAAA,MAAAqC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,oDACF;IAOiC7D,EAAA,CAAAsB,SAAA,GAA+C;IAA/CtB,EAAA,CAAA4B,UAAA,YAAAgC,OAAA,CAAAN,mBAAA,CAAAQ,QAAA,CAAAG,eAAA,CAA+C;IAUjDjE,EAAA,CAAAsB,SAAA,GAAsC;IAAtCtB,EAAA,CAAA4B,UAAA,YAAAgC,OAAA,CAAAN,mBAAA,CAAAY,eAAA,CAAsC;IAM/ClE,EAAA,CAAAsB,SAAA,GAAuC;IAAvCtB,EAAA,CAAA4B,UAAA,UAAAgC,OAAA,CAAAN,mBAAA,CAAAO,YAAA,CAAuC;;;;;IAUnE7D,EAAA,CAAAC,cAAA,cAAiD;IAC3CD,EAAA,CAAAU,MAAA,iCAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACzBX,EAAA,CAAAC,cAAA,cAAyB;IAEKD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACvCX,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAY,SAAA,cAA6F;IAC/FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAiC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGpEX,EAAA,CAAAC,cAAA,eAAwB;IACID,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACtCX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA2F;IAC7FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAgC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGnEX,EAAA,CAAAC,cAAA,eAAwB;IACID,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACvCX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA6F;IAC/FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAiC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAGpEX,EAAA,CAAAC,cAAA,eAAwB;IACID,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACvCX,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAY,SAAA,eAA6F;IAC/FZ,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAiC;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IA1BjCX,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAgD,WAAA,UAAAmB,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAC,MAAA,gBAAuD;IAE9DtE,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAuB,kBAAA,KAAA4C,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAC,MAAA,OAAiC;IAM3BtE,EAAA,CAAAsB,SAAA,GAAsD;IAAtDtB,EAAA,CAAAgD,WAAA,UAAAmB,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAE,KAAA,gBAAsD;IAE5DvE,EAAA,CAAAsB,SAAA,GAAgC;IAAhCtB,EAAA,CAAAuB,kBAAA,KAAA4C,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAE,KAAA,OAAgC;IAMzBvE,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAgD,WAAA,UAAAmB,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAG,MAAA,gBAAuD;IAE9DxE,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAuB,kBAAA,KAAA4C,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAG,MAAA,OAAiC;IAM1BxE,EAAA,CAAAsB,SAAA,GAAuD;IAAvDtB,EAAA,CAAAgD,WAAA,UAAAmB,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAI,MAAA,gBAAuD;IAE9DzE,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAAuB,kBAAA,KAAA4C,OAAA,CAAAC,WAAA,CAAAC,MAAA,CAAAI,MAAA,OAAiC;;;;;IAU/DzE,EAAA,CAAAC,cAAA,cAA8C;IACxBD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACzCX,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAU,MAAA,GAAqB;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;IAA5BX,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAAoC,iBAAA,CAAAsC,OAAA,CAAAN,WAAA,kBAAAM,OAAA,CAAAN,WAAA,CAAAO,EAAA,CAAqB;;;;;;IAY3C3E,EAAA,CAAAC,cAAA,iBAAsE;IAAvCD,EAAA,CAAAE,UAAA,mBAAA0E,0EAAA;MAAA5E,EAAA,CAAAI,aAAA,CAAAyE,IAAA;MAAA,MAAAC,OAAA,GAAA9E,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAsE,OAAA,CAAArE,MAAA,EAAQ;IAAA,EAAC;IAC/CT,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAU,MAAA,0BAAc;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC3BX,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAU,MAAA,mBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IA7IpCX,EAAA,CAAAC,cAAA,cAA8D;IAGtCD,EAAA,CAAAU,MAAA,wCAA4B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACnDX,EAAA,CAAAY,SAAA,aAA2B;IAC7BZ,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,cAA6B;IAEAD,EAAA,CAAAU,MAAA,GAA+B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IAC7DX,EAAA,CAAAC,cAAA,eAA8B;IACFD,EAAA,CAAAU,MAAA,0BAAkB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IACnDX,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAU,MAAA,IAAmC;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAIxEX,EAAA,CAAAa,UAAA,KAAAkE,8CAAA,kBAEM;IACN/E,EAAA,CAAAa,UAAA,KAAAmE,8CAAA,kBAEM;IACRhF,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,eAAiC;IAC3BD,EAAA,CAAAU,MAAA,mCAA2B;IAAAV,EAAA,CAAAW,YAAA,EAAK;IACpCX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAU,MAAA,IAA8B;IAAAV,EAAA,CAAAW,YAAA,EAAI;IAIvCX,EAAA,CAAAa,UAAA,KAAAoE,8CAAA,oBAmDM;IAENjF,EAAA,CAAAa,UAAA,KAAAqE,8CAAA,oBAmCM;IAENlF,EAAA,CAAAC,cAAA,eAA+B;IAEPD,EAAA,CAAAU,MAAA,mCAAiB;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5CX,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAU,MAAA,IAA0D;;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAEvFX,EAAA,CAAAa,UAAA,KAAAsE,8CAAA,kBAGM;IACRnF,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAC,cAAA,eAA6B;IACKD,EAAA,CAAAE,UAAA,mBAAAkF,iEAAA;MAAApF,EAAA,CAAAI,aAAA,CAAAiF,IAAA;MAAA,MAAAC,OAAA,GAAAtF,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA8E,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACrDvF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAC5BX,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAU,MAAA,oBAAE;IAAAV,EAAA,CAAAW,YAAA,EAAO;IAE9BX,EAAA,CAAAC,cAAA,kBAAuD;IAArBD,EAAA,CAAAE,UAAA,mBAAAsF,iEAAA;MAAAxF,EAAA,CAAAI,aAAA,CAAAiF,IAAA;MAAA,MAAAI,OAAA,GAAAzF,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAiF,OAAA,CAAApE,QAAA,EAAU;IAAA,EAAC;IACpDrB,EAAA,CAAAU,MAAA,iCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;IACTX,EAAA,CAAAa,UAAA,KAAA6E,iDAAA,qBAGS;IACX1F,EAAA,CAAAW,YAAA,EAAM;;;;IAvIuBX,EAAA,CAAAsB,SAAA,GAA+E;IAA/EtB,EAAA,CAAA2D,UAAA,cAAAgC,MAAA,CAAAC,YAAA,CAAAC,YAAA,CAAAC,WAAA,GAAAC,OAAA,UAA+E;IAC/E/F,EAAA,CAAAsB,SAAA,GAA+B;IAA/BtB,EAAA,CAAAoC,iBAAA,CAAAuD,MAAA,CAAAC,YAAA,CAAAC,YAAA,CAA+B;IAG5B7F,EAAA,CAAAsB,SAAA,GAAmC;IAAnCtB,EAAA,CAAAuB,kBAAA,KAAAoE,MAAA,CAAAC,YAAA,CAAAI,eAAA,MAAmC;IAItChG,EAAA,CAAAsB,SAAA,GAAiC;IAAjCtB,EAAA,CAAA4B,UAAA,SAAA+D,MAAA,CAAAC,YAAA,CAAAK,cAAA,CAAiC;IAGjCjG,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA4B,UAAA,UAAA+D,MAAA,CAAAC,YAAA,CAAAK,cAAA,CAAkC;IAO1DjG,EAAA,CAAAsB,SAAA,GAA8B;IAA9BtB,EAAA,CAAAoC,iBAAA,CAAAuD,MAAA,CAAAC,YAAA,CAAA7B,WAAA,CAA8B;IAIF/D,EAAA,CAAAsB,SAAA,GAAyB;IAAzBtB,EAAA,CAAA4B,UAAA,SAAA+D,MAAA,CAAArC,mBAAA,CAAyB;IAqD5BtD,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA4B,UAAA,SAAA+D,MAAA,CAAAvB,WAAA,CAAiB;IAwCvBpE,EAAA,CAAAsB,SAAA,GAA0D;IAA1DtB,EAAA,CAAAoC,iBAAA,CAAApC,EAAA,CAAA8C,WAAA,SAAA6C,MAAA,CAAAvB,WAAA,kBAAAuB,MAAA,CAAAvB,WAAA,CAAA8B,WAAA,6BAA0D;IAEzDlG,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAA4B,UAAA,SAAA+D,MAAA,CAAAvB,WAAA,kBAAAuB,MAAA,CAAAvB,WAAA,CAAAO,EAAA,CAAqB;IAcO3E,EAAA,CAAAsB,SAAA,GAAiB;IAAjBtB,EAAA,CAAA4B,UAAA,SAAA+D,MAAA,CAAA9D,WAAA,CAAiB;;;AD7O5E,OAAM,MAAOsE,wBAAwB;EAuDnCC,YACUC,sBAA8C,EAC9CC,wBAAkD,EAClDC,MAAc;IAFd,KAAAF,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,MAAM,GAANA,MAAM;IAzDhB;IACA,KAAAC,SAAS,GAAezG,qBAAqB;IAC7C,KAAA0G,oBAAoB,GAAW,CAAC;IAChC,KAAAC,SAAS,GAAmB,EAAE;IAC9B,KAAAtC,WAAW,GAAuB,IAAI;IAEtC;IACA,KAAAuC,aAAa,GAAY,KAAK;IAC9B,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAApE,SAAS,GAAY,KAAK;IAC1B,KAAAqE,WAAW,GAAY,KAAK;IAC5B,KAAAC,gBAAgB,GAAW,EAAE;IAE7B;IACA,KAAAC,WAAW,GAAG,CACZ;MACEpC,EAAE,EAAE,CAAC;MACLjD,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BoC,WAAW,EAAE;KACd,EACD;MACEY,EAAE,EAAE,CAAC;MACLjD,IAAI,EAAE,aAAa;MACnBC,KAAK,EAAE,sBAAsB;MAC7BoC,WAAW,EAAE;KACd,EACD;MACEY,EAAE,EAAE,CAAC;MACLjD,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BoC,WAAW,EAAE;KACd,EACD;MACEY,EAAE,EAAE,CAAC;MACLjD,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,wBAAwB;MAC/BoC,WAAW,EAAE;KACd,EACD;MACEY,EAAE,EAAE,CAAC;MACLjD,IAAI,EAAE,cAAc;MACpBC,KAAK,EAAE,uBAAuB;MAC9BoC,WAAW,EAAE;KACd,CACF;IAED,KAAAtC,YAAY,GAAG,IAAI,CAACsF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,KAAAlF,WAAW,GAAQ,IAAI,CAAC,CAAC;IAEzB;IACA,KAAA+D,YAAY,GAA8B,IAAI;IAC9C,KAAAtC,mBAAmB,GAA+B,IAAI;EAMnD;EAEH0D,QAAQA,CAAA;IACN;IACA,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAC3D,IAAIF,eAAe,EAAE;MACnB,IAAI,CAACpF,WAAW,GAAGuF,IAAI,CAACC,KAAK,CAACJ,eAAe,CAAC;MAC9C;MACA,IAAI,CAACxF,YAAY,GAAG;QAClBkD,EAAE,EAAE,CAAC;QACLjD,IAAI,EAAE,IAAI,CAACG,WAAW,CAACH,IAAI;QAC3BC,KAAK,EAAE,IAAI,CAACE,WAAW,CAACF,KAAK;QAC7BoC,WAAW,EAAE;OACd;;IAEH,IAAI,CAACuD,cAAc,EAAE;EACvB;EAEA;;;EAGAA,cAAcA,CAAA;IACZ,IAAI,CAAClD,WAAW,GAAG,IAAI,CAACiC,sBAAsB,CAACkB,iBAAiB,CAC9D,IAAI,CAAC9F,YAAY,CAACC,IAAI,EACtB,IAAI,CAACD,YAAY,CAACE,KAAK,CACxB;EACH;EAEA;;;EAGA6F,UAAUA,CAACC,IAAS;IAClB,IAAI,CAAChG,YAAY,GAAGgG,IAAI;IACxB,IAAI,CAACH,cAAc,EAAE;EACvB;EAEA;;;EAGApG,SAASA,CAAA;IACP,IAAI,CAACyF,aAAa,GAAG,IAAI;IACzB,IAAI,CAACF,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACI,gBAAgB,GAAG,UAAU,GAAGY,IAAI,CAACC,GAAG,EAAE,GAAG,GAAG,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IAE/F,IAAI,IAAI,CAAC3D,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC4D,SAAS,GAAG,IAAIN,IAAI,EAAE;MACvC,IAAI,CAACtD,WAAW,CAACO,EAAE,GAAG,IAAI,CAACmC,gBAAgB;;IAG7CmB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACpB,gBAAgB,CAAC;EACrE;EAEA;;;EAGA7E,cAAcA,CAACkG,MAAe;IAC5B,IAAI,CAAC,IAAI,CAAC/D,WAAW,EAAE;IAEvB,MAAM9B,eAAe,GAAG,IAAI,CAACkE,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC;IACjE,MAAM2B,QAAQ,GAAiB;MAC7BC,UAAU,EAAE/F,eAAe,CAACqC,EAAE;MAC9BwD,MAAM,EAAEA,MAAM;MACdG,SAAS,EAAE,IAAIZ,IAAI;KACpB;IAED,IAAI,CAAChB,SAAS,CAAC6B,IAAI,CAACH,QAAQ,CAAC;IAC7B,IAAI,CAAChE,WAAW,CAACsC,SAAS,GAAG,IAAI,CAACA,SAAS;IAE3C;IACA,IAAI,IAAI,CAAC7E,WAAW,IAAI,IAAI,CAACiF,gBAAgB,EAAE;MAC7C,IAAI,CAACT,sBAAsB,CAACmC,sBAAsB,CAChD,IAAI,CAAC3G,WAAW,CAACF,KAAK,EACtB,IAAI,CAACmF,gBAAgB,EACrBsB,QAAQ,CACT,CAACK,SAAS,CAAC;QACVC,IAAI,EAAGC,OAAO,IAAI;UAChB,IAAIA,OAAO,EAAE;YACXV,OAAO,CAACC,GAAG,CAAC,aAAa5F,eAAe,CAACqC,EAAE,cAAc,CAAC;WAC3D,MAAM;YACLsD,OAAO,CAACW,IAAI,CAAC,gCAAgCtG,eAAe,CAACqC,EAAE,EAAE,CAAC;;QAEtE,CAAC;QACDkE,KAAK,EAAGA,KAAK,IAAI;UACfZ,OAAO,CAACY,KAAK,CAAC,oBAAoBvG,eAAe,CAACqC,EAAE,GAAG,EAAEkE,KAAK,CAAC;QACjE;OACD,CAAC;;IAGJ;IACA,IAAI,IAAI,CAACpC,oBAAoB,GAAG,IAAI,CAACD,SAAS,CAACsC,MAAM,GAAG,CAAC,EAAE;MACzD,IAAI,CAACrC,oBAAoB,EAAE;KAC5B,MAAM;MACL,IAAI,CAACsC,YAAY,EAAE;;EAEvB;EAEA;;;EAGAA,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC3E,WAAW,EAAE;IAEvB,IAAI,CAAC5B,SAAS,GAAG,IAAI;IACrByF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IAEtD;IACAc,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQA,kBAAkBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAC7E,WAAW,EAAE;IAEvB,IAAI;MACF6D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACxB,SAAS,CAACoC,MAAM,EAAE,UAAU,CAAC;MAE3E;MACA,MAAMI,OAAO,GAAG,IAAI,CAAC7C,sBAAsB,CAAC4C,kBAAkB,CAAC,IAAI,CAACvC,SAAS,CAAC;MAC9EuB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEgB,OAAO,CAAC;MAE7C;MACA,IAAI,CAAC9E,WAAW,CAACC,MAAM,GAAG6E,OAAO,CAAC7E,MAAM;MACxC,IAAI,CAACD,WAAW,CAACwB,YAAY,GAAGsD,OAAO,CAACC,OAAO;MAC/C,IAAI,CAAC/E,WAAW,CAAC8B,WAAW,GAAG,IAAIwB,IAAI,EAAE;MAEzC,IAAI,CAAC9B,YAAY,GAAGsD,OAAO,CAACC,OAAO;MAEnC;MACA,IAAI,CAAC7F,mBAAmB,GAAG,IAAI,CAACgD,wBAAwB,CAAC8C,kBAAkB,CACzEF,OAAO,CAACC,OAAO,EACf,IAAI,CAAC1H,YAAY,CAACE,KAAK,CACxB;MACDsG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC5E,mBAAmB,CAAC;MAEnE;MACA,MAAM+F,YAAY,GAAG;QACnBxG,cAAc,EAAE,IAAI,CAAC2D,SAAS,CAACsC,MAAM;QACrCQ,cAAc,EAAE,IAAI,CAAC5C,SAAS,CAACoC,MAAM;QACrCS,cAAc,EAAG,IAAI,CAAC7C,SAAS,CAACoC,MAAM,GAAG,IAAI,CAACtC,SAAS,CAACsC,MAAM,GAAI,GAAG;QACrEU,mBAAmB,EAAE,IAAI,CAACC,4BAA4B,EAAE;QACxDpF,MAAM,EAAE6E,OAAO,CAAC7E,MAAM;QACtB8E,OAAO,EAAED,OAAO,CAACC,OAAO;QACxBO,MAAM,EAAE,IAAI,CAAC7H,WAAW,EAAEF,KAAK;QAC/BuE,WAAW,EAAE,IAAIwB,IAAI,EAAE,CAACiC,WAAW;OACpC;MAED;MACA,IAAI,IAAI,CAAC7C,gBAAgB,EAAE;QACzB,IAAI,CAACT,sBAAsB,CAACuD,gBAAgB,CAAC,IAAI,CAAC9C,gBAAgB,EAAEuC,YAAY,CAAC,CAACZ,SAAS,CAAC;UAC1FC,IAAI,EAAGC,OAAO,IAAI;YAChBV,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAES,OAAO,CAAC;UAClE,CAAC;UACDE,KAAK,EAAGA,KAAK,IAAI;YACfZ,OAAO,CAACY,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UACpD;SACD,CAAC;;MAGJ;MACA,IAAI,CAACxC,sBAAsB,CAACwD,eAAe,CAAC,IAAI,CAACzF,WAAW,CAAC,CAACqE,SAAS,CAAC;QACtEC,IAAI,EAAGoB,SAAS,IAAI;UAClB7B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE4B,SAAS,CAAC;UACvD,IAAI,CAAC1F,WAAY,CAACO,EAAE,GAAGmF,SAAS;UAChC,IAAI,CAACC,oBAAoB,EAAE;QAC7B,CAAC;QACDlB,KAAK,EAAGA,KAAK,IAAI;UACfZ,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,IAAI,CAACkB,oBAAoB,EAAE;QAC7B;OACD,CAAC;KAEH,CAAC,OAAOlB,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MAClE,IAAI,CAACkB,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGQA,oBAAoBA,CAAA;IAC1Bf,UAAU,CAAC,MAAK;MACd,IAAI,CAACxG,SAAS,GAAG,KAAK;MACtB,IAAI,CAACoE,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACC,WAAW,GAAG,IAAI;MACvBoB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACxC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQuB,4BAA4BA,CAAA;IAClC,IAAI,IAAI,CAAC/C,SAAS,CAACoC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAEzC,IAAIkB,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvD,SAAS,CAACoC,MAAM,EAAEmB,CAAC,EAAE,EAAE;MAC9C,MAAMC,QAAQ,GAAG,IAAI,CAACxD,SAAS,CAACuD,CAAC,CAAC,CAAC3B,SAAS,CAAC6B,OAAO,EAAE,GAAG,IAAI,CAACzD,SAAS,CAACuD,CAAC,GAAC,CAAC,CAAC,CAAC3B,SAAS,CAAC6B,OAAO,EAAE;MAChGH,SAAS,IAAIE,QAAQ;;IAGvB,OAAOF,SAAS,IAAI,IAAI,CAACtD,SAAS,CAACoC,MAAM,GAAG,CAAC,CAAC;EAChD;EAEA;;;EAGAvD,WAAWA,CAAA;IACT,IAAI,CAACoB,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACJ,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACd,YAAY,GAAG,IAAI;IACxB,IAAI,CAACtC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACwD,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACQ,cAAc,EAAE;EACvB;EAEA;;;EAGAjG,QAAQA,CAAA;IACN,IAAI,CAACkF,MAAM,CAAC6D,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEA;;;EAGA3J,MAAMA,CAAA;IACJyG,YAAY,CAACmD,UAAU,CAAC,aAAa,CAAC;IACtC,IAAI,CAAC9D,MAAM,CAAC6D,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEA;;;EAGA,IAAI9H,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACkE,SAAS,CAAC,IAAI,CAACC,oBAAoB,CAAC,IAAI,IAAI;EAC1D;EAEA;;;EAGA,IAAI1D,kBAAkBA,CAAA;IACpB,OAAQ,CAAC,IAAI,CAAC0D,oBAAoB,GAAG,CAAC,IAAI,IAAI,CAACD,SAAS,CAACsC,MAAM,GAAI,GAAG;EACxE;EAEA;;;EAGA,IAAIlG,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAAC6D,oBAAoB,GAAG,CAAC;EACtC;EAEA;;;EAGA,IAAI5D,cAAcA,CAAA;IAChB,OAAO,IAAI,CAAC2D,SAAS,CAACsC,MAAM;EAC9B;;;uBApUW3C,wBAAwB,EAAAnG,EAAA,CAAAsK,iBAAA,CAAAC,EAAA,CAAAC,sBAAA,GAAAxK,EAAA,CAAAsK,iBAAA,CAAAG,EAAA,CAAAC,wBAAA,GAAA1K,EAAA,CAAAsK,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAxBzE,wBAAwB;MAAA0E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjBrCnL,EAAA,CAAAC,cAAA,aAAwC;UAEtCD,EAAA,CAAAa,UAAA,IAAAwK,uCAAA,kBA4DM;UAGNrL,EAAA,CAAAa,UAAA,IAAAyK,uCAAA,kBAsCM;UAGNtL,EAAA,CAAAa,UAAA,IAAA0K,uCAAA,iBAMM;UAGNvL,EAAA,CAAAa,UAAA,IAAA2K,uCAAA,mBAiJM;UACRxL,EAAA,CAAAW,YAAA,EAAM;;;UAnQqBX,EAAA,CAAAsB,SAAA,GAAoC;UAApCtB,EAAA,CAAA4B,UAAA,UAAAwJ,GAAA,CAAAzE,aAAA,KAAAyE,GAAA,CAAAvE,WAAA,CAAoC;UA+DhC7G,EAAA,CAAAsB,SAAA,GAAmC;UAAnCtB,EAAA,CAAA4B,UAAA,SAAAwJ,GAAA,CAAAzE,aAAA,KAAAyE,GAAA,CAAAvE,WAAA,CAAmC;UAyCnC7G,EAAA,CAAAsB,SAAA,GAAe;UAAftB,EAAA,CAAA4B,UAAA,SAAAwJ,GAAA,CAAA5I,SAAA,CAAe;UASjBxC,EAAA,CAAAsB,SAAA,GAAiC;UAAjCtB,EAAA,CAAA4B,UAAA,SAAAwJ,GAAA,CAAAvE,WAAA,IAAAuE,GAAA,CAAAxF,YAAA,CAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}