{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n// Firebase imports\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\nimport { provideFirestore, getFirestore } from '@angular/fire/firestore';\nimport { provideAuth, getAuth } from '@angular/fire/auth';\nimport { firebaseConfig } from '../environments/firebase.config';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { IrisFormComponent } from './iris-form/iris-form.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { FooterComponent } from './shared/footer/footer.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\nimport { AdminComponent } from './admin/admin.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/app\";\nimport * as i2 from \"@angular/fire/firestore\";\nimport * as i3 from \"@angular/fire/auth\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, provideFirebaseApp(() => initializeApp(firebaseConfig)), provideFirestore(() => getFirestore()), provideAuth(() => getAuth())]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, AccueilComponent, SuivantaccComponent, TypeirisComponent, Iris2Component, FleurComponent, BijouComponent, FluxComponent, ShakerComponent, IrisFormComponent, LoginComponent, SignupComponent, IrisDiversityComponent, DashboardComponent, FooterComponent, PersonalityTestComponent, AdminComponent],\n    imports: [BrowserModule, AppRoutingModule, FormsModule, ReactiveFormsModule, i1.FirebaseAppModule, i2.FirestoreModule, i3.AuthModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "ReactiveFormsModule", "AppRoutingModule", "AppComponent", "initializeApp", "provideFirebaseApp", "provideFirestore", "getFirestore", "provideAuth", "getAuth", "firebaseConfig", "AccueilComponent", "SuivantaccComponent", "TypeirisComponent", "Iris2Component", "FleurComponent", "BijouComponent", "FluxComponent", "ShakerComponent", "IrisFormComponent", "LoginComponent", "SignupComponent", "IrisDiversityComponent", "DashboardComponent", "FooterComponent", "PersonalityTestComponent", "AdminComponent", "AppModule", "bootstrap", "declarations", "imports", "i1", "FirebaseAppModule", "i2", "FirestoreModule", "i3", "AuthModule"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n\n// Firebase imports\nimport { initializeApp, provideFirebaseApp } from '@angular/fire/app';\nimport { provideFirestore, getFirestore } from '@angular/fire/firestore';\nimport { provideAuth, getAuth } from '@angular/fire/auth';\nimport { firebaseConfig } from '../environments/firebase.config';\n\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { IrisFormComponent } from './iris-form/iris-form.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { FooterComponent } from './shared/footer/footer.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\nimport { AdminComponent } from './admin/admin.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    AccueilComponent,\n    SuivantaccComponent,\n    TypeirisComponent,\n    Iris2Component,\n    FleurComponent,\n    BijouComponent,\n    FluxComponent,\n    ShakerComponent,\n    IrisFormComponent,\n    LoginComponent,\n    SignupComponent,\n    IrisDiversityComponent,\n    DashboardComponent,\n    FooterComponent,\n    PersonalityTestComponent,\n    AdminComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    ReactiveFormsModule,\n    provideFirebaseApp(() => initializeApp(firebaseConfig)),\n    provideFirestore(() => getFirestore()),\n    provideAuth(() => getAuth())\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB,CAAC,CAAC;AACnE,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,mBAAmB;AACrE,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,yBAAyB;AACxE,SAASC,WAAW,EAAEC,OAAO,QAAQ,oBAAoB;AACzD,SAASC,cAAc,QAAQ,iCAAiC;AAEhE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,cAAc,QAAQ,yBAAyB;;;;;AAkCxD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRzB,YAAY;IAAA;EAAA;;;gBATtBJ,aAAa,EACbG,gBAAgB,EAChBF,WAAW,EACXC,mBAAmB,EACnBI,kBAAkB,CAAC,MAAMD,aAAa,CAACM,cAAc,CAAC,CAAC,EACvDJ,gBAAgB,CAAC,MAAMC,YAAY,EAAE,CAAC,EACtCC,WAAW,CAAC,MAAMC,OAAO,EAAE,CAAC;IAAA;EAAA;;;2EAKnBkB,SAAS;IAAAE,YAAA,GA9BlB1B,YAAY,EACZQ,gBAAgB,EAChBC,mBAAmB,EACnBC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,eAAe,EACfC,sBAAsB,EACtBC,kBAAkB,EAClBC,eAAe,EACfC,wBAAwB,EACxBC,cAAc;IAAAI,OAAA,GAGd/B,aAAa,EACbG,gBAAgB,EAChBF,WAAW,EACXC,mBAAmB,EAAA8B,EAAA,CAAAC,iBAAA,EAAAC,EAAA,CAAAC,eAAA,EAAAC,EAAA,CAAAC,UAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}