{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AccueilComponent {\n  static {\n    this.ɵfac = function AccueilComponent_Factory(t) {\n      return new (t || AccueilComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccueilComponent,\n      selectors: [[\"app-accueil\"]],\n      decls: 29,\n      vars: 0,\n      consts: [[1, \"hero-section\"], [1, \"header\"], [1, \"logo\"], [1, \"nav\"], [\"href\", \"#\", 1, \"active\"], [\"href\", \"#\"], [1, \"register-button\"], [1, \"hero-content\"], [1, \"text-zone\"], [1, \"start-button\"], [1, \"image-zone\"], [\"src\", \"assets/iris.png\", \"alt\", \"Iris image\"]],\n      template: function AccueilComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"nav\", 3)(5, \"a\", 4);\n          i0.ɵɵtext(6, \"accueil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"a\", 5);\n          i0.ɵɵtext(8, \"\\u00C0 propos de nous\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"a\", 5);\n          i0.ɵɵtext(10, \"Contact\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"button\", 6);\n          i0.ɵɵtext(12, \"Register\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8)(15, \"h1\");\n          i0.ɵɵtext(16, \"Iris & Identit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\");\n          i0.ɵɵtext(18, \" Chaque iris est une signature.\");\n          i0.ɵɵelement(19, \"br\");\n          i0.ɵɵtext(20, \" Notre syst\\u00E8me de profilage biom\\u00E9trique\");\n          i0.ɵɵelement(21, \"br\");\n          i0.ɵɵtext(22, \" offre une s\\u00E9curit\\u00E9 in\\u00E9gal\\u00E9e, inspir\\u00E9e\");\n          i0.ɵɵelement(23, \"br\");\n          i0.ɵɵtext(24, \" directement par la nature humaine. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 9);\n          i0.ɵɵtext(26, \"Commencer\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 10);\n          i0.ɵɵelement(28, \"img\", 11);\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      styles: [\".accueil-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #e1d8f1, #fbdde5);\\n  min-height: 100vh;\\n  font-family: \\\"Poppins\\\", sans-serif;\\n  padding: 20px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  font-weight: 500;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-family: \\\"Pacifico\\\", cursive;\\n  font-size: 24px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%] {\\n  list-style: none;\\n  display: flex;\\n  gap: 30px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: #000;\\n  font-size: 16px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  border: 2px solid #000;\\n  background: transparent;\\n  border-radius: 20px;\\n  font-size: 14px;\\n  cursor: pointer;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-top: 60px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%] {\\n  max-width: 50%;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  font-weight: bold;\\n  margin-bottom: 20px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-style: italic;\\n  margin-bottom: 30px;\\n  line-height: 1.6;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   .start-button[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  border: 2px solid #000;\\n  border-radius: 30px;\\n  background-color: transparent;\\n  cursor: pointer;\\n  font-size: 16px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-zone[_ngcontent-%COMP%] {\\n  position: relative;\\n  max-width: 40%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-zone[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  position: relative;\\n  z-index: 2;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-zone[_ngcontent-%COMP%]   .rectangles[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 10%;\\n  left: 0;\\n  z-index: 1;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-zone[_ngcontent-%COMP%]   .rectangles[_ngcontent-%COMP%]   .rectangle-vertical[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 80px;\\n  background-color: white;\\n  margin-bottom: 10px;\\n}\\n.accueil-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-zone[_ngcontent-%COMP%]   .rectangles[_ngcontent-%COMP%]   .rectangle-horizontal[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 6px;\\n  background-color: white;\\n  margin-top: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWNjdWVpbC9hY2N1ZWlsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksdURBQUE7RUFDQSxpQkFBQTtFQUNBLGtDQUFBO0VBQ0EsYUFBQTtBQUNKO0FBQ0k7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0FBQ047QUFDTTtFQUNFLGdDQUFBO0VBQ0EsZUFBQTtBQUNSO0FBRU07RUFDRSxnQkFBQTtFQUNBLGFBQUE7RUFDQSxTQUFBO0FBQVI7QUFFUTtFQUNFLHFCQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7QUFBVjtBQUVVO0VBQ0UsaUJBQUE7QUFBWjtBQUtNO0VBQ0UsaUJBQUE7RUFDQSxzQkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtBQUhSO0FBT0k7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLGlCQUFBO0FBTE47QUFPTTtFQUNFLGNBQUE7QUFMUjtBQU9RO0VBQ0UsZUFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7QUFMVjtBQVFRO0VBQ0UsZUFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxnQkFBQTtBQU5WO0FBU1E7RUFDRSxrQkFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSw2QkFBQTtFQUNBLGVBQUE7RUFDQSxlQUFBO0FBUFY7QUFXTTtFQUNFLGtCQUFBO0VBQ0EsY0FBQTtFQUNBLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0FBVFI7QUFXUTtFQUNFLGVBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0FBVFY7QUFZUTtFQUNFLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLE9BQUE7RUFDQSxVQUFBO0FBVlY7QUFZVTtFQUNFLFdBQUE7RUFDQSxZQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBQVZaO0FBYVU7RUFDRSxZQUFBO0VBQ0EsV0FBQTtFQUNBLHVCQUFBO0VBQ0EsZ0JBQUE7QUFYWiIsInNvdXJjZXNDb250ZW50IjpbIi5hY2N1ZWlsLWNvbnRhaW5lciB7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gcmlnaHQsICNlMWQ4ZjEsICNmYmRkZTUpO1xyXG4gICAgbWluLWhlaWdodDogMTAwdmg7XHJcbiAgICBmb250LWZhbWlseTogJ1BvcHBpbnMnLCBzYW5zLXNlcmlmO1xyXG4gICAgcGFkZGluZzogMjBweDtcclxuICBcclxuICAgIC5uYXZiYXIge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgXHJcbiAgICAgIC5sb2dvIHtcclxuICAgICAgICBmb250LWZhbWlseTogJ1BhY2lmaWNvJywgY3Vyc2l2ZTtcclxuICAgICAgICBmb250LXNpemU6IDI0cHg7XHJcbiAgICAgIH1cclxuICBcclxuICAgICAgLm5hdi1saW5rcyB7XHJcbiAgICAgICAgbGlzdC1zdHlsZTogbm9uZTtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGdhcDogMzBweDtcclxuICBcclxuICAgICAgICBsaSBhIHtcclxuICAgICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICAgICAgICAgIGNvbG9yOiAjMDAwO1xyXG4gICAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gIFxyXG4gICAgICAgICAgJi5hY3RpdmUge1xyXG4gICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDtcclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICBcclxuICAgICAgLnJlZ2lzdGVyLWJ1dHRvbiB7XHJcbiAgICAgICAgcGFkZGluZzogOHB4IDIwcHg7XHJcbiAgICAgICAgYm9yZGVyOiAycHggc29saWQgIzAwMDtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICBcclxuICAgIC5jb250ZW50IHtcclxuICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgICBwYWRkaW5nLXRvcDogNjBweDtcclxuICBcclxuICAgICAgLnRleHQtem9uZSB7XHJcbiAgICAgICAgbWF4LXdpZHRoOiA1MCU7XHJcbiAgXHJcbiAgICAgICAgaDEge1xyXG4gICAgICAgICAgZm9udC1zaXplOiA0OHB4O1xyXG4gICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XHJcbiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAyMHB4O1xyXG4gICAgICAgIH1cclxuICBcclxuICAgICAgICBwIHtcclxuICAgICAgICAgIGZvbnQtc2l6ZTogMThweDtcclxuICAgICAgICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcclxuICAgICAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7XHJcbiAgICAgICAgICBsaW5lLWhlaWdodDogMS42O1xyXG4gICAgICAgIH1cclxuICBcclxuICAgICAgICAuc3RhcnQtYnV0dG9uIHtcclxuICAgICAgICAgIHBhZGRpbmc6IDEwcHggMjBweDtcclxuICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICMwMDA7XHJcbiAgICAgICAgICBib3JkZXItcmFkaXVzOiAzMHB4O1xyXG4gICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgICAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgXHJcbiAgICAgIC5pbWFnZS16b25lIHtcclxuICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgICAgbWF4LXdpZHRoOiA0MCU7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIFxyXG4gICAgICAgIGltZyB7XHJcbiAgICAgICAgICBtYXgtd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICBoZWlnaHQ6IGF1dG87XHJcbiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICAgICAgICB6LWluZGV4OiAyO1xyXG4gICAgICAgIH1cclxuICBcclxuICAgICAgICAucmVjdGFuZ2xlcyB7XHJcbiAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICBib3R0b206IDEwJTtcclxuICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICB6LWluZGV4OiAxO1xyXG4gIFxyXG4gICAgICAgICAgLnJlY3RhbmdsZS12ZXJ0aWNhbCB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxMHB4O1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDgwcHg7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xyXG4gICAgICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xyXG4gICAgICAgICAgfVxyXG4gIFxyXG4gICAgICAgICAgLnJlY3RhbmdsZS1ob3Jpem9udGFsIHtcclxuICAgICAgICAgICAgd2lkdGg6IDE1MHB4O1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDZweDtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICAgIG1hcmdpbi10b3A6IDEwcHg7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AccueilComponent", "selectors", "decls", "vars", "consts", "template", "AccueilComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-accueil',\n  templateUrl: './accueil.component.html',\n  styleUrls: ['./accueil.component.scss']\n})\nexport class AccueilComponent {\n\n}\n", "<div class=\"hero-section\">\n    <header class=\"header\">\n      <div class=\"logo\">IrisLock</div>\n      <nav class=\"nav\">\n        <a class=\"active\" href=\"#\">accueil</a>\n        <a href=\"#\">À propos de nous</a>\n        <a href=\"#\">Contact</a>\n      </nav>\n      <button class=\"register-button\">Register</button>\n    </header>\n  \n    <div class=\"hero-content\">\n      <div class=\"text-zone\">\n        <h1>Iris & Identité</h1>\n        <p>\n          Chaque iris est une signature.<br />\n          Notre système de profilage biométrique<br />\n          offre une sécurité inégalée, inspirée<br />\n          directement par la nature humaine.\n        </p>\n        <button class=\"start-button\">Commencer</button>\n      </div>\n  \n      <div class=\"image-zone\">\n        <img src=\"assets/iris.png\" alt=\"Iris image\" />\n      </div>\n    </div>\n  </div>\n  "], "mappings": ";AAOA,OAAM,MAAOA,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BE,EAAA,CAAAC,cAAA,aAA0B;UAEFD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,aAAiB;UACYD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtCH,EAAA,CAAAC,cAAA,WAAY;UAAAD,EAAA,CAAAE,MAAA,4BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChCH,EAAA,CAAAC,cAAA,WAAY;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEzBH,EAAA,CAAAC,cAAA,iBAAgC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGnDH,EAAA,CAAAC,cAAA,cAA0B;UAElBD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,uCAA8B;UAAAF,EAAA,CAAAI,SAAA,UAAM;UACpCJ,EAAA,CAAAE,MAAA,yDAAsC;UAAAF,EAAA,CAAAI,SAAA,UAAM;UAC5CJ,EAAA,CAAAE,MAAA,uEAAqC;UAAAF,EAAA,CAAAI,SAAA,UAAM;UAC3CJ,EAAA,CAAAE,MAAA,4CACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGjDH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAI,SAAA,eAA8C;UAChDJ,EAAA,CAAAG,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}