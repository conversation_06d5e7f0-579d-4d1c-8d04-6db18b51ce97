{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class IrisDiversityComponent {\n  static {\n    this.ɵfac = function IrisDiversityComponent_Factory(t) {\n      return new (t || IrisDiversityComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IrisDiversityComponent,\n      selectors: [[\"app-iris-diversity\"]],\n      decls: 52,\n      vars: 0,\n      consts: [[1, \"page-container\", \"iris-diversity\"], [1, \"container\"], [1, \"navbar\"], [1, \"logo\"], [1, \"nav-links\"], [\"routerLink\", \"/accueil\", \"routerLinkActive\", \"active\"], [\"href\", \"#\"], [1, \"auth-buttons\"], [\"routerLink\", \"/login\", 1, \"login-button\"], [\"routerLink\", \"/signup\", 1, \"register-button\"], [1, \"content\"], [1, \"card\", \"main-card\"], [1, \"card-header\"], [1, \"divider\"], [1, \"iris-diversity-content\"], [1, \"iris-circle-container\"], [1, \"iris-main-image\"], [\"src\", \"assets/Repere2.png\", \"alt\", \"Diversit\\u00E9 des iris\", 1, \"diversity-image\"], [1, \"text-sections\"], [1, \"text-section\", \"left\"], [1, \"text-section\", \"right\"], [1, \"navigation-buttons\"], [\"routerLink\", \"/typeiris\", 1, \"nav-button\", \"prev\"], [1, \"icon\"], [\"routerLink\", \"/fleur\", 1, \"nav-button\", \"next\"]],\n      template: function IrisDiversityComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"nav\", 2)(3, \"div\", 3);\n          i0.ɵɵtext(4, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"ul\", 4)(6, \"li\")(7, \"a\", 5);\n          i0.ɵɵtext(8, \"Accueil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"li\")(10, \"a\", 6);\n          i0.ɵɵtext(11, \"\\u00C0 propos\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"li\")(13, \"a\", 6);\n          i0.ɵɵtext(14, \"Contact\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"a\", 8);\n          i0.ɵɵtext(17, \"Connexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"a\", 9);\n          i0.ɵɵtext(19, \"Inscription\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(20, \"div\", 10)(21, \"div\", 11)(22, \"div\", 12)(23, \"h1\");\n          i0.ɵɵtext(24, \"La Diversit\\u00E9 des Iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 14)(27, \"div\", 15)(28, \"div\", 16);\n          i0.ɵɵelement(29, \"img\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 18)(31, \"div\", 19)(32, \"h3\");\n          i0.ɵɵtext(33, \"Le Rep\\u00E8re des Types d'Iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"p\");\n          i0.ɵɵtext(35, \" L'image ci-dessus pr\\u00E9sente le rep\\u00E8re des diff\\u00E9rents types d'iris et leurs relations. Cette repr\\u00E9sentation permet de visualiser comment les caract\\u00E9ristiques des quatre types fondamentaux (Fleur, Bijou, Shaker et Flux) s'organisent et s'influencent mutuellement. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 20)(37, \"h3\");\n          i0.ɵɵtext(38, \"La Diversit\\u00E9 des Profils\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"p\");\n          i0.ɵɵtext(40, \" Bien que ces quatre types repr\\u00E9sentent les cat\\u00E9gories principales, il est rare qu'un individu corresponde parfaitement \\u00E0 un seul profil. En r\\u00E9alit\\u00E9, la majorit\\u00E9 des personnes pr\\u00E9sentent des formes interm\\u00E9diaires, m\\u00EAlant des caract\\u00E9ristiques issues de plusieurs types. Cette diversit\\u00E9 refl\\u00E8te la richesse et la complexit\\u00E9 unique de chaque \\u00EAtre humain. \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 21)(42, \"a\", 22)(43, \"span\", 23);\n          i0.ɵɵtext(44, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"span\");\n          i0.ɵɵtext(46, \"Pr\\u00E9c\\u00E9dent\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"a\", 24)(48, \"span\");\n          i0.ɵɵtext(49, \"Suivant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"span\", 23);\n          i0.ɵɵtext(51, \"\\u2192\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink, i1.RouterLinkActive],\n      styles: [\".page-container.iris-diversity[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #e6f0ff 0%, #e0e6ff 100%);\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 20px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 20px 0;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  color: #333;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  gap: 30px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: #555;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover, .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  color: var(--fleur-primary);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 15px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%], .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%] {\\n  padding: 10px 25px;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  text-decoration: none;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  color: var(--fleur-primary);\\n  border: 1px solid var(--fleur-primary);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .login-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(138, 79, 255, 0.1);\\n  transform: translateY(-3px);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%] {\\n  background-color: var(--fleur-primary);\\n  color: white;\\n  border: 1px solid var(--fleur-primary);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .auth-buttons[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%]:hover {\\n  background-color: #681cff;\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  padding: 20px 0 60px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 20px;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  padding: 40px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 30px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2.5rem;\\n  color: #333;\\n  margin-bottom: 15px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--bijou-primary), #2a6dff);\\n  margin: 0 auto;\\n  border-radius: 3px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-bottom: 40px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .iris-circle-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  max-width: 800px;\\n  margin: 20px auto 40px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .iris-circle-container[_ngcontent-%COMP%]   .iris-main-image[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .iris-circle-container[_ngcontent-%COMP%]   .iris-main-image[_ngcontent-%COMP%]   .diversity-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 600px;\\n  height: auto;\\n  border-radius: 10px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .iris-circle-container[_ngcontent-%COMP%]   .iris-main-image[_ngcontent-%COMP%]   .diversity-image[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .text-sections[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 30px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .text-sections[_ngcontent-%COMP%]   .text-section[_ngcontent-%COMP%] {\\n  flex: 1;\\n  min-width: 300px;\\n  padding: 20px;\\n  background-color: rgba(245, 247, 250, 0.7);\\n  border-radius: 15px;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .text-sections[_ngcontent-%COMP%]   .text-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  color: #333;\\n  margin-bottom: 15px;\\n  font-weight: 600;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .text-sections[_ngcontent-%COMP%]   .text-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  line-height: 1.6;\\n  color: #555;\\n  text-align: justify;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .text-sections[_ngcontent-%COMP%]   .text-section.left[_ngcontent-%COMP%] {\\n  border-left: 3px solid var(--bijou-primary);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .text-sections[_ngcontent-%COMP%]   .text-section.right[_ngcontent-%COMP%] {\\n  border-left: 3px solid #2a6dff;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-top: 30px;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .nav-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 12px 25px;\\n  background-color: white;\\n  color: #555;\\n  border-radius: 50px;\\n  font-weight: 500;\\n  text-decoration: none;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n  transition: all 0.3s ease;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .nav-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .nav-button.prev[_ngcontent-%COMP%]:hover {\\n  color: var(--fleur-primary);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .nav-button.next[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, var(--bijou-primary), #2a6dff);\\n  color: white;\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .nav-button.next[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 8px 25px rgba(79, 138, 255, 0.3);\\n}\\n.page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .nav-button[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n  }\\n  .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .navbar[_ngcontent-%COMP%]   .nav-links[_ngcontent-%COMP%] {\\n    gap: 15px;\\n  }\\n  .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%] {\\n    padding: 25px;\\n  }\\n  .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .iris-circle-container[_ngcontent-%COMP%] {\\n    width: 300px;\\n    height: 300px;\\n  }\\n  .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .iris-circle-container[_ngcontent-%COMP%]   .iris-circle[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    margin: -25px;\\n  }\\n  .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .iris-diversity-content[_ngcontent-%COMP%]   .text-sections[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 15px;\\n  }\\n  .page-container.iris-diversity[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .navigation-buttons[_ngcontent-%COMP%]   .nav-button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["IrisDiversityComponent", "selectors", "decls", "vars", "consts", "template", "IrisDiversityComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris-diversity\\iris-diversity.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\iris-diversity\\iris-diversity.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-iris-diversity',\n  templateUrl: './iris-diversity.component.html',\n  styleUrls: ['./iris-diversity.component.scss']\n})\nexport class IrisDiversityComponent {\n\n}\n", "<div class=\"page-container iris-diversity\">\n  <div class=\"container\">\n    <nav class=\"navbar\">\n      <div class=\"logo\">IrisLock</div>\n      <ul class=\"nav-links\">\n        <li><a routerLink=\"/accueil\" routerLinkActive=\"active\">Accueil</a></li>\n        <li><a href=\"#\">À propos</a></li>\n        <li><a href=\"#\">Contact</a></li>\n      </ul>\n      <div class=\"auth-buttons\">\n        <a routerLink=\"/login\" class=\"login-button\">Connexion</a>\n        <a routerLink=\"/signup\" class=\"register-button\">Inscription</a>\n      </div>\n    </nav>\n\n    <div class=\"content\">\n      <div class=\"card main-card\">\n        <div class=\"card-header\">\n          <h1>La Diversité des Iris</h1>\n          <div class=\"divider\"></div>\n        </div>\n\n        <div class=\"iris-diversity-content\">\n          <div class=\"iris-circle-container\">\n            <div class=\"iris-main-image\">\n              <img src=\"assets/Repere2.png\" alt=\"Diversité des iris\" class=\"diversity-image\">\n            </div>\n          </div>\n\n          <div class=\"text-sections\">\n            <div class=\"text-section left\">\n              <h3>Le Repère des Types d'Iris</h3>\n              <p>\n                L'image ci-dessus présente le repère des différents types d'iris et leurs relations.\n                Cette représentation permet de visualiser comment les caractéristiques des quatre types fondamentaux\n                (Fleur, Bijou, Shaker et Flux) s'organisent et s'influencent mutuellement.\n              </p>\n            </div>\n\n            <div class=\"text-section right\">\n              <h3>La Diversité des Profils</h3>\n              <p>\n                Bien que ces quatre types représentent les catégories principales, il est rare qu'un individu\n                corresponde parfaitement à un seul profil. En réalité, la majorité des personnes présentent des formes\n                intermédiaires, mêlant des caractéristiques issues de plusieurs types. Cette diversité reflète la richesse et la\n                complexité unique de chaque être humain.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"navigation-buttons\">\n          <a routerLink=\"/typeiris\" class=\"nav-button prev\">\n            <span class=\"icon\">←</span>\n            <span>Précédent</span>\n          </a>\n          <a routerLink=\"/fleur\" class=\"nav-button next\">\n            <span>Suivant</span>\n            <span class=\"icon\">→</span>\n          </a>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA,sBAAsB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPnCE,EAAA,CAAAC,cAAA,aAA2C;UAGnBD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,YAAsB;UACmCD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClEH,EAAA,CAAAC,cAAA,SAAI;UAAYD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5BH,EAAA,CAAAC,cAAA,UAAI;UAAYD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE7BH,EAAA,CAAAC,cAAA,cAA0B;UACoBD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACzDH,EAAA,CAAAC,cAAA,YAAgD;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAInEH,EAAA,CAAAC,cAAA,eAAqB;UAGXD,EAAA,CAAAE,MAAA,kCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9BH,EAAA,CAAAI,SAAA,eAA2B;UAC7BJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAoC;UAG9BD,EAAA,CAAAI,SAAA,eAA+E;UACjFJ,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eAA2B;UAEnBD,EAAA,CAAAE,MAAA,uCAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnCH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,uSAGF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGNH,EAAA,CAAAC,cAAA,eAAgC;UAC1BD,EAAA,CAAAE,MAAA,qCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjCH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,8aAIF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKVH,EAAA,CAAAC,cAAA,eAAgC;UAETD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAExBH,EAAA,CAAAC,cAAA,aAA+C;UACvCD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpBH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}