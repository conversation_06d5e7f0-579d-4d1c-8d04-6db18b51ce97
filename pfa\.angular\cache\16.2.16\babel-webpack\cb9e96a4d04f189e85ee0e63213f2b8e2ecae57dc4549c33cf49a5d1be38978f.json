{"ast": null, "code": "import { Observable } from '../../Observable';\nimport { performanceTimestampProvider } from '../../scheduler/performanceTimestampProvider';\nimport { animationFrameProvider } from '../../scheduler/animationFrameProvider';\nexport function animationFrames(timestampProvider) {\n  return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\nfunction animationFramesFactory(timestampProvider) {\n  return new Observable(subscriber => {\n    const provider = timestampProvider || performanceTimestampProvider;\n    const start = provider.now();\n    let id = 0;\n    const run = () => {\n      if (!subscriber.closed) {\n        id = animationFrameProvider.requestAnimationFrame(timestamp => {\n          id = 0;\n          const now = provider.now();\n          subscriber.next({\n            timestamp: timestampProvider ? now : timestamp,\n            elapsed: now - start\n          });\n          run();\n        });\n      }\n    };\n    run();\n    return () => {\n      if (id) {\n        animationFrameProvider.cancelAnimationFrame(id);\n      }\n    };\n  });\n}\nconst DEFAULT_ANIMATION_FRAMES = animationFramesFactory();", "map": {"version": 3, "names": ["Observable", "performanceTimestampProvider", "animationFrameProvider", "animationFrames", "timestampProvider", "animationFramesFactory", "DEFAULT_ANIMATION_FRAMES", "subscriber", "provider", "start", "now", "id", "run", "closed", "requestAnimationFrame", "timestamp", "next", "elapsed", "cancelAnimationFrame"], "sources": ["C:/Users/<USER>/Desktop/pfa/pfa/node_modules/rxjs/dist/esm/internal/observable/dom/animationFrames.js"], "sourcesContent": ["import { Observable } from '../../Observable';\nimport { performanceTimestampProvider } from '../../scheduler/performanceTimestampProvider';\nimport { animationFrameProvider } from '../../scheduler/animationFrameProvider';\nexport function animationFrames(timestampProvider) {\n    return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\nfunction animationFramesFactory(timestampProvider) {\n    return new Observable((subscriber) => {\n        const provider = timestampProvider || performanceTimestampProvider;\n        const start = provider.now();\n        let id = 0;\n        const run = () => {\n            if (!subscriber.closed) {\n                id = animationFrameProvider.requestAnimationFrame((timestamp) => {\n                    id = 0;\n                    const now = provider.now();\n                    subscriber.next({\n                        timestamp: timestampProvider ? now : timestamp,\n                        elapsed: now - start,\n                    });\n                    run();\n                });\n            }\n        };\n        run();\n        return () => {\n            if (id) {\n                animationFrameProvider.cancelAnimationFrame(id);\n            }\n        };\n    });\n}\nconst DEFAULT_ANIMATION_FRAMES = animationFramesFactory();\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAC7C,SAASC,4BAA4B,QAAQ,8CAA8C;AAC3F,SAASC,sBAAsB,QAAQ,wCAAwC;AAC/E,OAAO,SAASC,eAAeA,CAACC,iBAAiB,EAAE;EAC/C,OAAOA,iBAAiB,GAAGC,sBAAsB,CAACD,iBAAiB,CAAC,GAAGE,wBAAwB;AACnG;AACA,SAASD,sBAAsBA,CAACD,iBAAiB,EAAE;EAC/C,OAAO,IAAIJ,UAAU,CAAEO,UAAU,IAAK;IAClC,MAAMC,QAAQ,GAAGJ,iBAAiB,IAAIH,4BAA4B;IAClE,MAAMQ,KAAK,GAAGD,QAAQ,CAACE,GAAG,CAAC,CAAC;IAC5B,IAAIC,EAAE,GAAG,CAAC;IACV,MAAMC,GAAG,GAAGA,CAAA,KAAM;MACd,IAAI,CAACL,UAAU,CAACM,MAAM,EAAE;QACpBF,EAAE,GAAGT,sBAAsB,CAACY,qBAAqB,CAAEC,SAAS,IAAK;UAC7DJ,EAAE,GAAG,CAAC;UACN,MAAMD,GAAG,GAAGF,QAAQ,CAACE,GAAG,CAAC,CAAC;UAC1BH,UAAU,CAACS,IAAI,CAAC;YACZD,SAAS,EAAEX,iBAAiB,GAAGM,GAAG,GAAGK,SAAS;YAC9CE,OAAO,EAAEP,GAAG,GAAGD;UACnB,CAAC,CAAC;UACFG,GAAG,CAAC,CAAC;QACT,CAAC,CAAC;MACN;IACJ,CAAC;IACDA,GAAG,CAAC,CAAC;IACL,OAAO,MAAM;MACT,IAAID,EAAE,EAAE;QACJT,sBAAsB,CAACgB,oBAAoB,CAACP,EAAE,CAAC;MACnD;IACJ,CAAC;EACL,CAAC,CAAC;AACN;AACA,MAAML,wBAAwB,GAAGD,sBAAsB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}