{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\nimport { AdminComponent } from './admin/admin.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'accueil',\n  component: AccueilComponent\n}, {\n  path: 'suivantacc',\n  component: SuivantaccComponent\n}, {\n  path: 'typeiris',\n  component: TypeirisComponent\n}, {\n  path: 'iris2',\n  component: Iris2Component\n}, {\n  path: 'iris-diversity',\n  component: IrisDiversityComponent\n}, {\n  path: 'fleur',\n  component: FleurComponent\n}, {\n  path: 'bijou',\n  component: BijouComponent\n}, {\n  path: 'flux',\n  component: FluxComponent\n}, {\n  path: 'shaker',\n  component: ShakerComponent\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'signup',\n  component: SignupComponent\n}, {\n  path: 'dashboard',\n  component: DashboardComponent\n}, {\n  path: 'personality-test',\n  component: PersonalityTestComponent\n}, {\n  path: 'admin',\n  component: AdminComponent\n}, {\n  path: '',\n  redirectTo: '/accueil',\n  pathMatch: 'full'\n} // Rediriger vers la page d'accueil par défaut\n];\n\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AccueilComponent", "SuivantaccComponent", "TypeirisComponent", "Iris2Component", "FleurComponent", "BijouComponent", "FluxComponent", "ShakerComponent", "LoginComponent", "SignupComponent", "IrisDiversityComponent", "DashboardComponent", "PersonalityTestComponent", "AdminComponent", "routes", "path", "component", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\nimport { AdminComponent } from './admin/admin.component';\n\nconst routes: Routes = [\n  { path: 'accueil', component: AccueilComponent },  // Page d'accueil\n  { path: 'suivantacc', component: SuivantaccComponent },  // Page après avoir cliqué sur \"Commencer\"\n  { path: 'typeiris', component: TypeirisComponent },  // Page après avoir cliqué sur \"Suivant\"\n  { path: 'iris2' , component: Iris2Component},\n  { path: 'iris-diversity', component: IrisDiversityComponent },  // Page de diversité des iris\n  { path: 'fleur', component: FleurComponent },\n  { path: 'bijou', component: BijouComponent},\n  { path: 'flux', component: FluxComponent },\n  { path: 'shaker', component: ShakerComponent },\n  { path: 'login', component: LoginComponent },  // Page de connexion\n  { path: 'signup', component: SignupComponent },  // Page d'inscription\n  { path: 'dashboard', component: DashboardComponent },  // Tableau de bord utilisateur\n  { path: 'personality-test', component: PersonalityTestComponent },  // Test de personnalité\n  { path: 'admin', component: AdminComponent },  // Administration Firebase\n  { path: '', redirectTo: '/accueil', pathMatch: 'full' },  // Rediriger vers la page d'accueil par défaut\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,cAAc,QAAQ,yBAAyB;;;AAExD,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEhB;AAAgB,CAAE,EAChD;EAAEe,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAEf;AAAmB,CAAE,EACtD;EAAEc,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEd;AAAiB,CAAE,EAClD;EAAEa,IAAI,EAAE,OAAO;EAAGC,SAAS,EAAEb;AAAc,CAAC,EAC5C;EAAEY,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAEN;AAAsB,CAAE,EAC7D;EAAEK,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEZ;AAAc,CAAE,EAC5C;EAAEW,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEX;AAAc,CAAC,EAC3C;EAAEU,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEV;AAAa,CAAE,EAC1C;EAAES,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAET;AAAe,CAAE,EAC9C;EAAEQ,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAER;AAAc,CAAE,EAC5C;EAAEO,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEP;AAAe,CAAE,EAC9C;EAAEM,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAEL;AAAkB,CAAE,EACpD;EAAEI,IAAI,EAAE,kBAAkB;EAAEC,SAAS,EAAEJ;AAAwB,CAAE,EACjE;EAAEG,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEH;AAAc,CAAE,EAC5C;EAAEE,IAAI,EAAE,EAAE;EAAEE,UAAU,EAAE,UAAU;EAAEC,SAAS,EAAE;AAAM,CAAE,CAAG;AAAA,CAC3D;;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBpB,YAAY,CAACqB,OAAO,CAACN,MAAM,CAAC,EAC5Bf,YAAY;IAAA;EAAA;;;2EAEXoB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAvB,YAAA;IAAAwB,OAAA,GAFjBxB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}