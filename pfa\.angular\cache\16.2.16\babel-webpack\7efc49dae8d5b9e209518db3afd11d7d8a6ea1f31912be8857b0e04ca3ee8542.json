{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class SuivantaccComponent {\n  static {\n    this.ɵfac = function SuivantaccComponent_Factory(t) {\n      return new (t || SuivantaccComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SuivantaccComponent,\n      selectors: [[\"app-suivantacc\"]],\n      decls: 45,\n      vars: 0,\n      consts: [[1, \"page-container\", \"suivantacc\"], [1, \"container\"], [1, \"content\"], [1, \"card\", \"main-card\"], [1, \"card-inner\"], [1, \"card-front\"], [1, \"image-container\"], [\"src\", \"assets/iris3.png\", \"alt\", \"Iris\", 1, \"main-image\"], [1, \"image-decoration\"], [1, \"text-content\"], [1, \"title\"], [1, \"divider\"], [1, \"intro\"], [1, \"description\"], [1, \"action-container\"], [\"routerLink\", \"/typeiris\", 1, \"btn\", \"next-btn\"], [1, \"icon\"], [1, \"features\"], [1, \"feature-card\"]],\n      template: function SuivantaccComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵelement(7, \"img\", 7)(8, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"h2\", 10);\n          i0.ɵɵtext(11, \"D\\u00E9couvrir l'unicit\\u00E9 de chacun\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"div\", 11);\n          i0.ɵɵelementStart(13, \"p\", 12);\n          i0.ɵɵtext(14, \" L'iris est une structure biom\\u00E9trique complexe et unique \\u00E0 chaque individu. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\", 13);\n          i0.ɵɵtext(16, \" Ses motifs, distincts et inimitables, peuvent fournir des informations pr\\u00E9cieuses sur les caract\\u00E9ristiques physiologiques, psychologiques et comportementales d'une personne. L'analyse de la structure irienne permet d'identifier des traits de personnalit\\u00E9, des pr\\u00E9dispositions h\\u00E9r\\u00E9ditaires, ainsi que d'\\u00E9ventuelles implications sur la sant\\u00E9 et les relations interpersonnelles. \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(17, \"div\", 14)(18, \"a\", 15)(19, \"span\");\n          i0.ɵɵtext(20, \"Suivant\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"span\", 16);\n          i0.ɵɵtext(22, \"\\u2192\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(23, \"div\", 17)(24, \"div\", 18)(25, \"div\", 16);\n          i0.ɵɵtext(26, \"\\uD83D\\uDC41\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"h3\");\n          i0.ɵɵtext(28, \"Structure Unique\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"p\");\n          i0.ɵɵtext(30, \"Chaque iris poss\\u00E8de une structure aussi unique qu'une empreinte digitale\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 18)(32, \"div\", 16);\n          i0.ɵɵtext(33, \"\\uD83E\\uDDE0\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"h3\");\n          i0.ɵɵtext(35, \"Reflet de la Personnalit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"p\");\n          i0.ɵɵtext(37, \"Les motifs de l'iris r\\u00E9v\\u00E8lent des aspects profonds de notre personnalit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 18)(39, \"div\", 16);\n          i0.ɵɵtext(40, \"\\uD83D\\uDD04\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"h3\");\n          i0.ɵɵtext(42, \"\\u00C9volution Continue\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"Les caract\\u00E9ristiques \\u00E9voluent selon notre parcours de vie et nos habitudes\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".page-container.suivantacc[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\\n  padding: 20px 0;\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  margin-bottom: 60px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 1000px;\\n  margin-bottom: 40px;\\n  perspective: 1000px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  transform-style: preserve-3d;\\n  transition: transform 0.6s;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%] {\\n  width: 100%;\\n  backface-visibility: hidden;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  padding: 40px;\\n  gap: 40px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  position: relative;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n  width: 300px;\\n  height: 300px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\\n  z-index: 2;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 330px;\\n  height: 330px;\\n  border-radius: 50%;\\n  border: 2px dashed var(--bijou-secondary);\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_rotate 20s linear infinite;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-family: \\\"Playfair Display\\\", serif;\\n  font-size: 2.2rem;\\n  color: #333;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  margin-bottom: 20px;\\n  border-radius: 3px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .intro[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 500;\\n  color: #555;\\n  margin-bottom: 15px;\\n  font-style: italic;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  line-height: 1.7;\\n  color: #666;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 15px 35px;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  color: white;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n  box-shadow: 0 10px 25px rgba(138, 79, 255, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(138, 79, 255, 0.4);\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .action-container[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 30px;\\n  margin-top: 60px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px;\\n  border-radius: 15px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);\\n  text-align: center;\\n  transition: all 0.3s ease;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  margin-bottom: 20px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  color: #333;\\n  margin-bottom: 15px;\\n}\\n.page-container.suivantacc[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  color: #666;\\n  line-height: 1.6;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n    padding: 30px;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%] {\\n    margin-bottom: 30px;\\n    text-align: center;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%] {\\n    margin: 0 auto 20px;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n    margin-bottom: 30px;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    width: 250px;\\n    height: 250px;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n    width: 280px;\\n    height: 280px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n    font-size: 1.8rem;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .intro[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n    font-size: 0.95rem;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%] {\\n    width: 200px;\\n    height: 200px;\\n  }\\n  .page-container.suivantacc[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .card-inner[_ngcontent-%COMP%]   .card-front[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n    width: 230px;\\n    height: 230px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SuivantaccComponent", "selectors", "decls", "vars", "consts", "template", "SuivantaccComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext"], "sources": ["E:\\aymen\\pfa\\pfa\\src\\app\\suivantacc\\suivantacc.component.ts", "E:\\aymen\\pfa\\pfa\\src\\app\\suivantacc\\suivantacc.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-suivantacc',\n  templateUrl: './suivantacc.component.html',\n  styleUrls: ['./suivantacc.component.scss']\n})\nexport class SuivantaccComponent {\n\n}\n", "<div class=\"page-container suivantacc\">\n  <div class=\"container\">\n    <div class=\"content\">\n      <div class=\"card main-card\">\n        <div class=\"card-inner\">\n          <div class=\"card-front\">\n            <div class=\"image-container\">\n              <img src=\"assets/iris3.png\" alt=\"Iris\" class=\"main-image\" />\n              <div class=\"image-decoration\"></div>\n            </div>\n            <div class=\"text-content\">\n              <h2 class=\"title\">Découvrir l'unicité de chacun</h2>\n              <div class=\"divider\"></div>\n              <p class=\"intro\">\n                L'iris est une structure biométrique complexe et unique à chaque individu.\n              </p>\n              <p class=\"description\">\n                Ses motifs, distincts et inimitables, peuvent fournir des informations précieuses\n                sur les caractéristiques physiologiques, psychologiques et comportementales d'une personne.\n                L'analyse de la structure irienne permet d'identifier des traits de personnalité,\n                des prédispositions héréditaires, ainsi que d'éventuelles implications sur la santé\n                et les relations interpersonnelles.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"action-container\">\n        <a routerLink=\"/typeiris\" class=\"btn next-btn\">\n          <span>Suivant</span>\n          <span class=\"icon\">→</span>\n        </a>\n      </div>\n    </div>\n\n    <div class=\"features\">\n      <div class=\"feature-card\">\n        <div class=\"icon\">👁️</div>\n        <h3>Structure Unique</h3>\n        <p>Chaque iris possède une structure aussi unique qu'une empreinte digitale</p>\n      </div>\n      <div class=\"feature-card\">\n        <div class=\"icon\">🧠</div>\n        <h3>Reflet de la Personnalité</h3>\n        <p>Les motifs de l'iris révèlent des aspects profonds de notre personnalité</p>\n      </div>\n      <div class=\"feature-card\">\n        <div class=\"icon\">🔄</div>\n        <h3>Évolution Continue</h3>\n        <p>Les caractéristiques évoluent selon notre parcours de vie et nos habitudes</p>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,mBAAmB;;;uBAAnBA,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPhCE,EAAA,CAAAC,cAAA,aAAuC;UAOzBD,EAAA,CAAAE,SAAA,aAA4D;UAE9DF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA0B;UACND,EAAA,CAAAI,MAAA,+CAA6B;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACpDH,EAAA,CAAAE,SAAA,eAA2B;UAC3BF,EAAA,CAAAC,cAAA,aAAiB;UACfD,EAAA,CAAAI,MAAA,8FACF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,aAAuB;UACrBD,EAAA,CAAAI,MAAA,yaAKF;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAMZH,EAAA,CAAAC,cAAA,eAA8B;UAEpBD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UACpBH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAI,MAAA,cAAC;UAAAJ,EAAA,CAAAG,YAAA,EAAO;UAKjCH,EAAA,CAAAC,cAAA,eAAsB;UAEAD,EAAA,CAAAI,MAAA,0BAAG;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAC3BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,wBAAgB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,qFAAwE;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAEjFH,EAAA,CAAAC,cAAA,eAA0B;UACND,EAAA,CAAAI,MAAA,oBAAE;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,sCAAyB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,+FAAwE;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAEjFH,EAAA,CAAAC,cAAA,eAA0B;UACND,EAAA,CAAAI,MAAA,oBAAE;UAAAJ,EAAA,CAAAG,YAAA,EAAM;UAC1BH,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAI,MAAA,+BAAkB;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,4FAA0E;UAAAJ,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}