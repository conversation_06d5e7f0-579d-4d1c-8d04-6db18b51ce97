{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AccueilComponent {\n  static {\n    this.ɵfac = function AccueilComponent_Factory(t) {\n      return new (t || AccueilComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccueilComponent,\n      selectors: [[\"app-accueil\"]],\n      decls: 29,\n      vars: 0,\n      consts: [[1, \"hero-section\"], [1, \"header\"], [1, \"logo\"], [1, \"nav\"], [\"href\", \"#\", 1, \"active\"], [\"href\", \"#\"], [1, \"register-button\"], [1, \"hero-content\"], [1, \"text-zone\"], [1, \"start-button\"], [1, \"image-zone\"], [\"src\", \"assets/iris.png\", \"alt\", \"Iris image\"]],\n      template: function AccueilComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"div\", 2);\n          i0.ɵɵtext(3, \"IrisLock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"nav\", 3)(5, \"a\", 4);\n          i0.ɵɵtext(6, \"accueil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"a\", 5);\n          i0.ɵɵtext(8, \"\\u00C0 propos de nous\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"a\", 5);\n          i0.ɵɵtext(10, \"Contact\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"button\", 6);\n          i0.ɵɵtext(12, \"Register\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 7)(14, \"div\", 8)(15, \"h1\");\n          i0.ɵɵtext(16, \"Iris & Identit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\");\n          i0.ɵɵtext(18, \" Chaque iris est une signature.\");\n          i0.ɵɵelement(19, \"br\");\n          i0.ɵɵtext(20, \" Notre syst\\u00E8me de profilage biom\\u00E9trique\");\n          i0.ɵɵelement(21, \"br\");\n          i0.ɵɵtext(22, \" offre une s\\u00E9curit\\u00E9 in\\u00E9gal\\u00E9e, inspir\\u00E9e\");\n          i0.ɵɵelement(23, \"br\");\n          i0.ɵɵtext(24, \" directement par la nature humaine. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"button\", 9);\n          i0.ɵɵtext(26, \"Commencer\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 10);\n          i0.ɵɵelement(28, \"img\", 11);\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      styles: [\".hero-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #e6defa, #fddde6);\\n  min-height: 100vh;\\n  padding: 2rem;\\n  font-family: \\\"Segoe UI\\\", sans-serif;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  font-size: 1.2rem;\\n}\\n.header[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  font-family: \\\"Brush Script MT\\\", cursive;\\n  font-size: 1.8rem;\\n}\\n.header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 2rem;\\n}\\n.header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n  color: black;\\n  font-weight: 500;\\n}\\n.header[_ngcontent-%COMP%]   .nav[_ngcontent-%COMP%]   a.active[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n.header[_ngcontent-%COMP%]   .register-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  border: 2px solid black;\\n  background: transparent;\\n  border-radius: 20px;\\n  font-size: 1rem;\\n  cursor: pointer;\\n}\\n\\n.hero-content[_ngcontent-%COMP%] {\\n  margin-top: 5rem;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.hero-content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%] {\\n  max-width: 50%;\\n}\\n.hero-content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-bottom: 1rem;\\n}\\n.hero-content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  margin-bottom: 2rem;\\n  color: #333;\\n}\\n.hero-content[_ngcontent-%COMP%]   .text-zone[_ngcontent-%COMP%]   .start-button[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1.2rem;\\n  border: 2px solid black;\\n  background: transparent;\\n  border-radius: 20px;\\n  font-size: 1rem;\\n  cursor: pointer;\\n}\\n.hero-content[_ngcontent-%COMP%]   .image-zone[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWNjdWVpbC9hY2N1ZWlsLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksdURBQUE7RUFDQSxpQkFBQTtFQUNBLGFBQUE7RUFDQSxtQ0FBQTtBQUNKOztBQUVFO0VBQ0UsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxpQkFBQTtBQUNKO0FBQ0k7RUFDRSx1Q0FBQTtFQUNBLGlCQUFBO0FBQ047QUFFSTtFQUNFLGFBQUE7RUFDQSxTQUFBO0FBQU47QUFFTTtFQUNFLHFCQUFBO0VBQ0EsWUFBQTtFQUNBLGdCQUFBO0FBQVI7QUFFUTtFQUNFLGlCQUFBO0FBQVY7QUFLSTtFQUNFLG9CQUFBO0VBQ0EsdUJBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtFQUNBLGVBQUE7QUFITjs7QUFPRTtFQUNFLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7QUFKSjtBQU1JO0VBQ0UsY0FBQTtBQUpOO0FBTU07RUFDRSxlQUFBO0VBQ0EsbUJBQUE7QUFKUjtBQU9NO0VBQ0UsaUJBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7QUFMUjtBQVFNO0VBQ0Usc0JBQUE7RUFDQSx1QkFBQTtFQUNBLHVCQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtBQU5SO0FBVUk7RUFDRSxlQUFBO0VBQ0EsWUFBQTtBQVJOIiwic291cmNlc0NvbnRlbnQiOlsiLmhlcm8tc2VjdGlvbiB7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gcmlnaHQsICNlNmRlZmEsICNmZGRkZTYpO1xyXG4gICAgbWluLWhlaWdodDogMTAwdmg7XHJcbiAgICBwYWRkaW5nOiAycmVtO1xyXG4gICAgZm9udC1mYW1pbHk6ICdTZWdvZSBVSScsIHNhbnMtc2VyaWY7XHJcbiAgfVxyXG4gIFxyXG4gIC5oZWFkZXIge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBmb250LXNpemU6IDEuMnJlbTtcclxuICBcclxuICAgIC5sb2dvIHtcclxuICAgICAgZm9udC1mYW1pbHk6ICdCcnVzaCBTY3JpcHQgTVQnLCBjdXJzaXZlO1xyXG4gICAgICBmb250LXNpemU6IDEuOHJlbTtcclxuICAgIH1cclxuICBcclxuICAgIC5uYXYge1xyXG4gICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICBnYXA6IDJyZW07XHJcbiAgXHJcbiAgICAgIGEge1xyXG4gICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcclxuICAgICAgICBjb2xvcjogYmxhY2s7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICBcclxuICAgICAgICAmLmFjdGl2ZSB7XHJcbiAgICAgICAgICBmb250LXdlaWdodDogYm9sZDtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICBcclxuICAgIC5yZWdpc3Rlci1idXR0b24ge1xyXG4gICAgICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcclxuICAgICAgYm9yZGVyOiAycHggc29saWQgYmxhY2s7XHJcbiAgICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xyXG4gICAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xyXG4gICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgIH1cclxuICB9XHJcbiAgXHJcbiAgLmhlcm8tY29udGVudCB7XHJcbiAgICBtYXJnaW4tdG9wOiA1cmVtO1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgXHJcbiAgICAudGV4dC16b25lIHtcclxuICAgICAgbWF4LXdpZHRoOiA1MCU7XHJcbiAgXHJcbiAgICAgIGgxIHtcclxuICAgICAgICBmb250LXNpemU6IDNyZW07XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMXJlbTtcclxuICAgICAgfVxyXG4gIFxyXG4gICAgICBwIHtcclxuICAgICAgICBmb250LXNpemU6IDEuMnJlbTtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAycmVtO1xyXG4gICAgICAgIGNvbG9yOiAjMzMzO1xyXG4gICAgICB9XHJcbiAgXHJcbiAgICAgIC5zdGFydC1idXR0b24ge1xyXG4gICAgICAgIHBhZGRpbmc6IDAuNXJlbSAxLjJyZW07XHJcbiAgICAgICAgYm9yZGVyOiAycHggc29saWQgYmxhY2s7XHJcbiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMjBweDtcclxuICAgICAgICBmb250LXNpemU6IDFyZW07XHJcbiAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgXHJcbiAgICAuaW1hZ2Utem9uZSBpbWcge1xyXG4gICAgICBtYXgtd2lkdGg6IDEwMCU7XHJcbiAgICAgIGhlaWdodDogYXV0bztcclxuICAgIH1cclxuICB9XHJcbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AccueilComponent", "selectors", "decls", "vars", "consts", "template", "AccueilComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\accueil\\accueil.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-accueil',\n  templateUrl: './accueil.component.html',\n  styleUrls: ['./accueil.component.scss']\n})\nexport class AccueilComponent {\n\n}\n", "<div class=\"hero-section\">\n    <header class=\"header\">\n      <div class=\"logo\">IrisLock</div>\n      <nav class=\"nav\">\n        <a class=\"active\" href=\"#\">accueil</a>\n        <a href=\"#\">À propos de nous</a>\n        <a href=\"#\">Contact</a>\n      </nav>\n      <button class=\"register-button\">Register</button>\n    </header>\n  \n    <div class=\"hero-content\">\n      <div class=\"text-zone\">\n        <h1>Iris & Identité</h1>\n        <p>\n          Chaque iris est une signature.<br />\n          Notre système de profilage biométrique<br />\n          offre une sécurité inégalée, inspirée<br />\n          directement par la nature humaine.\n        </p>\n        <button class=\"start-button\">Commencer</button>\n      </div>\n  \n      <div class=\"image-zone\">\n        <img src=\"assets/iris.png\" alt=\"Iris image\" />\n      </div>\n    </div>\n  </div>\n  "], "mappings": ";AAOA,OAAM,MAAOA,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP7BE,EAAA,CAAAC,cAAA,aAA0B;UAEFD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAChCH,EAAA,CAAAC,cAAA,aAAiB;UACYD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtCH,EAAA,CAAAC,cAAA,WAAY;UAAAD,EAAA,CAAAE,MAAA,4BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChCH,EAAA,CAAAC,cAAA,WAAY;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEzBH,EAAA,CAAAC,cAAA,iBAAgC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGnDH,EAAA,CAAAC,cAAA,cAA0B;UAElBD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,SAAG;UACDD,EAAA,CAAAE,MAAA,uCAA8B;UAAAF,EAAA,CAAAI,SAAA,UAAM;UACpCJ,EAAA,CAAAE,MAAA,yDAAsC;UAAAF,EAAA,CAAAI,SAAA,UAAM;UAC5CJ,EAAA,CAAAE,MAAA,uEAAqC;UAAAF,EAAA,CAAAI,SAAA,UAAM;UAC3CJ,EAAA,CAAAE,MAAA,4CACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,iBAA6B;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGjDH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAI,SAAA,eAA8C;UAChDJ,EAAA,CAAAG,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}