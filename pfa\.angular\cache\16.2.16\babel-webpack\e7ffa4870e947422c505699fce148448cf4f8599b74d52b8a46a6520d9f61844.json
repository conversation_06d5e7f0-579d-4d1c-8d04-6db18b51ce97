{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../iris-form/iris-form.component\";\nexport class ShakerComponent {\n  static {\n    this.ɵfac = function ShakerComponent_Factory(t) {\n      return new (t || ShakerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShakerComponent,\n      selectors: [[\"app-shaker\"]],\n      decls: 68,\n      vars: 0,\n      consts: [[1, \"iris-profile\", \"shaker\"], [1, \"container\"], [1, \"header\"], [1, \"title\"], [1, \"subtitle\"], [1, \"content\"], [1, \"image-container\"], [\"src\", \"assets/4.png\", \"alt\", \"Shaker\", 1, \"iris-image\"], [1, \"image-decoration\"], [1, \"description\"], [1, \"card\", \"traits-card\"], [1, \"traits-list\"], [1, \"icon\"], [1, \"text\"], [1, \"navigation\"], [\"routerLink\", \"/iris2\", 1, \"btn\", \"back-btn\"]],\n      template: function ShakerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \"Shaker - Le Visionnaire\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \"Type motiv\\u00E9, expressif et orient\\u00E9 action\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5)(8, \"div\", 6);\n          i0.ɵɵelement(9, \"img\", 7)(10, \"div\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"h2\");\n          i0.ɵɵtext(14, \"Caract\\u00E9ristiques\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"ul\", 11)(16, \"li\")(17, \"span\", 12);\n          i0.ɵɵtext(18, \"\\uD83D\\uDE80\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"span\", 13);\n          i0.ɵɵtext(20, \"Type motiv\\u00E9, expressif, pionnier, orient\\u00E9 action et innovation\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"li\")(22, \"span\", 12);\n          i0.ɵɵtext(23, \"\\uD83D\\uDCA1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 13);\n          i0.ɵɵtext(25, \"D\\u00E9passe la pens\\u00E9e conventionnelle, \\u00E9nergique et inspirant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"li\")(27, \"span\", 12);\n          i0.ɵɵtext(28, \"\\uD83D\\uDD04\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"span\", 13);\n          i0.ɵɵtext(30, \"Combine les forces de Bijou (visuel) et Fleur (auditif)\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"li\")(32, \"span\", 12);\n          i0.ɵɵtext(33, \"\\u26A1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"span\", 13);\n          i0.ɵɵtext(35, \"D\\u00E9cide vite et tient ses choix\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"li\")(37, \"span\", 12);\n          i0.ɵɵtext(38, \"\\uD83C\\uDFC3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"span\", 13);\n          i0.ɵɵtext(40, \"Apprentissage par le mouvement, le toucher et l'intuition\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\")(42, \"span\", 12);\n          i0.ɵɵtext(43, \"\\uD83D\\uDC4B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 13);\n          i0.ɵɵtext(45, \"Communique avec gestes dynamiques et passion\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"li\")(47, \"span\", 12);\n          i0.ɵɵtext(48, \"\\uD83D\\uDD04\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 13);\n          i0.ɵɵtext(50, \"Agent de changement, inventif, motivateur, leader n\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"li\")(52, \"span\", 12);\n          i0.ɵɵtext(53, \"\\u26A0\\uFE0F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"span\", 13);\n          i0.ɵɵtext(55, \"Peut devenir instable, autoritaire, autocritique\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"li\")(57, \"span\", 12);\n          i0.ɵɵtext(58, \"\\uD83C\\uDF31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"span\", 13);\n          i0.ɵɵtext(60, \"Le\\u00E7on de vie : cultiver la coh\\u00E9rence, la confiance et l'ancrage\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(61, \"div\", 14)(62, \"a\", 15)(63, \"span\", 12);\n          i0.ɵɵtext(64, \"\\u2190\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"span\");\n          i0.ɵɵtext(66, \"Retour aux types d'iris\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(67, \"app-iris-form\");\n        }\n      },\n      dependencies: [i1.RouterLink, i2.IrisFormComponent],\n      styles: [\".iris-profile.shaker[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background: var(--shaker-gradient);\\n  padding: 40px 0;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  color: var(--shaker-primary);\\n  font-size: 2.5rem;\\n  margin-bottom: 10px;\\n  position: relative;\\n  display: inline-block;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 3px;\\n  background-color: var(--shaker-secondary);\\n  border-radius: 3px;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.2rem;\\n  font-weight: 300;\\n  max-width: 600px;\\n  margin: 0 auto;\\n  margin-top: 20px;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 30px;\\n  margin-bottom: 40px;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  object-fit: cover;\\n  border-radius: 50%;\\n  box-shadow: 0 10px 30px rgba(255, 138, 79, 0.3);\\n  border: 5px solid white;\\n  z-index: 2;\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 15px 40px rgba(255, 138, 79, 0.4);\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   .image-decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 220px;\\n  height: 220px;\\n  border-radius: 50%;\\n  border: 2px dashed var(--shaker-secondary);\\n  top: -10px;\\n  left: -10px;\\n  z-index: 1;\\n  animation: _ngcontent-%COMP%_rotate 20s linear infinite;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 800px;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: var(--shaker-primary);\\n  font-size: 1.8rem;\\n  margin-bottom: 25px;\\n  text-align: center;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  display: grid;\\n  grid-template-columns: 1fr;\\n  gap: 15px;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 10px 15px;\\n  border-radius: 8px;\\n  background-color: rgba(255, 255, 255, 0.7);\\n  transition: all 0.3s ease;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover {\\n  background-color: white;\\n  transform: translateX(5px);\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  margin-right: 15px;\\n  min-width: 30px;\\n  text-align: center;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #444;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 30px;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%] {\\n  background-color: var(--shaker-primary);\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  padding: 12px 25px;\\n  border-radius: 50px;\\n  font-weight: 500;\\n  box-shadow: 0 5px 15px rgba(255, 138, 79, 0.3);\\n  transition: all 0.3s ease;\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #ff681c;\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 20px rgba(255, 138, 79, 0.4);\\n}\\n.iris-profile.shaker[_ngcontent-%COMP%]   .navigation[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n@media (min-width: 768px) {\\n  .iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    align-items: flex-start;\\n  }\\n  .iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n    flex: 0 0 auto;\\n  }\\n  .iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n  .iris-profile.shaker[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]   .traits-card[_ngcontent-%COMP%]   .traits-list[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ShakerComponent", "selectors", "decls", "vars", "consts", "template", "ShakerComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shaker\\shaker.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shaker\\shaker.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-shaker',\n  templateUrl: './shaker.component.html',\n  styleUrls: ['./shaker.component.scss']\n})\nexport class ShakerComponent {\n\n}\n", "<div class=\"iris-profile shaker\">\n  <div class=\"container\">\n    <div class=\"header\">\n      <h1 class=\"title\">Shaker - Le Visionnaire</h1>\n      <p class=\"subtitle\">Type motivé, expressif et orienté action</p>\n    </div>\n\n    <div class=\"content\">\n      <div class=\"image-container\">\n        <img src=\"assets/4.png\" alt=\"Shaker\" class=\"iris-image\" />\n        <div class=\"image-decoration\"></div>\n      </div>\n\n      <div class=\"description\">\n        <div class=\"card traits-card\">\n          <h2>Caractéristiques</h2>\n          <ul class=\"traits-list\">\n            <li>\n              <span class=\"icon\">🚀</span>\n              <span class=\"text\">Type motivé, expressif, pionnier, orienté action et innovation</span>\n            </li>\n            <li>\n              <span class=\"icon\">💡</span>\n              <span class=\"text\">Dépasse la pensée conventionnelle, énergique et inspirant</span>\n            </li>\n            <li>\n              <span class=\"icon\">🔄</span>\n              <span class=\"text\">Combine les forces de Bijou (visuel) et Fleur (auditif)</span>\n            </li>\n            <li>\n              <span class=\"icon\">⚡</span>\n              <span class=\"text\">Décide vite et tient ses choix</span>\n            </li>\n            <li>\n              <span class=\"icon\">🏃</span>\n              <span class=\"text\">Apprentissage par le mouvement, le toucher et l'intuition</span>\n            </li>\n            <li>\n              <span class=\"icon\">👋</span>\n              <span class=\"text\">Communique avec gestes dynamiques et passion</span>\n            </li>\n            <li>\n              <span class=\"icon\">🔄</span>\n              <span class=\"text\">Agent de changement, inventif, motivateur, leader né</span>\n            </li>\n            <li>\n              <span class=\"icon\">⚠️</span>\n              <span class=\"text\">Peut devenir instable, autoritaire, autocritique</span>\n            </li>\n            <li>\n              <span class=\"icon\">🌱</span>\n              <span class=\"text\">Leçon de vie : cultiver la cohérence, la confiance et l'ancrage</span>\n            </li>\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"navigation\">\n      <a routerLink=\"/iris2\" class=\"btn back-btn\">\n        <span class=\"icon\">←</span>\n        <span>Retour aux types d'iris</span>\n      </a>\n    </div>\n  </div>\n</div>\n\n<!-- Formulaire d'analyse d'iris -->\n<app-iris-form></app-iris-form>\n"], "mappings": ";;;AAOA,OAAM,MAAOA,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,aAAiC;UAGTD,EAAA,CAAAE,MAAA,8BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC9CH,EAAA,CAAAC,cAAA,WAAoB;UAAAD,EAAA,CAAAE,MAAA,yDAAwC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGlEH,EAAA,CAAAC,cAAA,aAAqB;UAEjBD,EAAA,CAAAI,SAAA,aAA0D;UAE5DJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAyB;UAEjBD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzBH,EAAA,CAAAC,cAAA,cAAwB;UAEDD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,gFAA8D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1FH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,gFAAyD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAErFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,+DAAuD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEnFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,2CAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE1DH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,iEAAyD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAErFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,oDAA4C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAExEH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,iEAAoD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAEhFH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,wDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAE5EH,EAAA,CAAAC,cAAA,UAAI;UACiBD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5BH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,iFAA+D;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAOnGH,EAAA,CAAAC,cAAA,eAAwB;UAEDD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAO5CH,EAAA,CAAAI,SAAA,qBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}