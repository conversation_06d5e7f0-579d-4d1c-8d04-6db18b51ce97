{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DashboardComponent {\n  constructor(router) {\n    this.router = router;\n    // Données utilisateur\n    this.userName = '<PERSON>';\n    this.lastScanTime = '10:30';\n    this.securityLevel = 'Élevé';\n    this.profileStatus = 'Vérifié';\n    // Données de l'iris\n    this.irisType = 'Crypte Dominant';\n    this.irisColor = 'Marron';\n    this.uniqueFeatures = 42;\n    this.confidenceScore = '98.7%';\n    // Activités récentes\n    this.recentActivities = [{\n      type: 'scan',\n      title: 'Scan d\\'iris complété',\n      time: 'Aujourd\\'hui à 10:30'\n    }, {\n      type: 'profile',\n      title: 'Profil mis à jour',\n      time: 'Hier à 14:15'\n    }, {\n      type: 'report',\n      title: 'Rapport généré',\n      time: 'Il y a 3 jours à 11:45'\n    }];\n    // Statuts de sécurité\n    this.securityStatuses = [{\n      type: 'data',\n      title: 'Protection des données',\n      description: 'Vos données biométriques sont cryptées'\n    }, {\n      type: 'biometric',\n      title: 'Authentification biométrique',\n      description: 'Activée pour une sécurité renforcée'\n    }, {\n      type: 'compliance',\n      title: 'Conformité RGPD',\n      description: 'Conforme aux réglementations de protection des données'\n    }];\n    // Onglet actif\n    this.activeTab = 'overview';\n  }\n  ngOnInit() {\n    // Vérifier si l'utilisateur est connecté (à implémenter avec un service d'authentification)\n    // Si non connecté, rediriger vers la page de connexion\n    // this.checkAuthentication();\n  }\n  // Méthode pour naviguer vers la page de scan d'iris\n  startNewScan() {\n    alert('Lancement d\\'un nouveau scan d\\'iris...');\n    // Redirection vers la page de scan (à implémenter)\n    // this.router.navigate(['/scan-iris']);\n  }\n  // Méthode pour changer d'onglet dans le tableau de bord\n  changeTab(tab) {\n    this.activeTab = tab;\n    alert(`Navigation vers l'onglet: ${tab}`);\n    // Implémenter la logique de changement d'onglet\n  }\n  // Méthodes pour les cartes d'information\n  showSecurityDetails() {\n    alert('Détails du niveau de sécurité');\n  }\n  showProfileStatus() {\n    alert('Détails du statut du profil');\n  }\n  showScanHistory() {\n    alert('Historique des scans');\n  }\n  // Méthodes pour l'aperçu du profil d'iris\n  viewFullIrisImage() {\n    alert('Affichage de l\\'image d\\'iris en plein écran');\n  }\n  showPatternDetails() {\n    alert(`Détails du type de motif: ${this.irisType}`);\n  }\n  showColorDetails() {\n    alert(`Détails de la couleur d'iris: ${this.irisColor}`);\n  }\n  showFeaturesDetails() {\n    alert(`Détails des caractéristiques uniques: ${this.uniqueFeatures} points identifiés`);\n  }\n  showConfidenceDetails() {\n    alert(`Détails du score de confiance: ${this.confidenceScore}`);\n  }\n  showVerificationDetails() {\n    alert(`Détails de la vérification effectuée à ${this.lastScanTime}`);\n  }\n  // Méthodes pour l'activité récente\n  viewScanDetails() {\n    alert('Détails du scan d\\'iris complété');\n  }\n  viewProfileUpdateDetails() {\n    alert('Détails de la mise à jour du profil');\n  }\n  viewReportDetails() {\n    alert('Détails du rapport généré');\n  }\n  // Méthodes pour le statut de sécurité\n  viewDataProtectionDetails() {\n    alert('Détails de la protection des données');\n  }\n  viewBiometricAuthDetails() {\n    alert('Détails de l\\'authentification biométrique');\n  }\n  viewGDPRComplianceDetails() {\n    alert('Détails de la conformité RGPD');\n  }\n  // Méthode pour vérifier l'authentification (à implémenter)\n  checkAuthentication() {\n    const isAuthenticated = false; // À remplacer par la vérification réelle\n    if (!isAuthenticated) {\n      this.router.navigate(['/login']);\n    }\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 161,\n      vars: 1,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"welcome-card\"], [1, \"new-scan-btn\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [1, \"info-cards\"], [1, \"info-card\", 3, \"click\"], [1, \"card-icon\", \"security\"], [1, \"fas\", \"fa-shield-alt\"], [1, \"card-content\"], [1, \"status\"], [1, \"card-icon\", \"verified\"], [1, \"fas\", \"fa-check-circle\"], [1, \"card-icon\", \"time\"], [1, \"fas\", \"fa-clock\"], [1, \"dashboard-nav\"], [1, \"nav-btn\", \"active\", 3, \"click\"], [1, \"fas\", \"fa-th-large\"], [1, \"nav-btn\", 3, \"click\"], [1, \"fas\", \"fa-user\"], [1, \"fas\", \"fa-history\"], [1, \"profile-overview\"], [1, \"section-header\"], [1, \"profile-content\"], [1, \"iris-image-container\", 3, \"click\"], [\"src\", \"\", \"alt\", \"Iris Scan\", 1, \"iris-image\"], [1, \"verification-badge\"], [1, \"iris-id\"], [1, \"iris-details\"], [1, \"detail-item\", 3, \"click\"], [1, \"detail-label\"], [1, \"detail-bar\"], [1, \"progress-bar\"], [1, \"detail-value\"], [1, \"verification-info\", 3, \"click\"], [1, \"verification-label\"], [1, \"verification-time\"], [1, \"verification-status\", \"success\"], [1, \"bottom-sections\"], [1, \"activity-section\"], [1, \"activity-list\"], [1, \"activity-item\", 3, \"click\"], [1, \"activity-icon\", \"scan\"], [1, \"activity-details\"], [1, \"activity-icon\", \"profile\"], [1, \"fas\", \"fa-user-edit\"], [1, \"activity-icon\", \"report\"], [1, \"fas\", \"fa-file-alt\"], [1, \"security-section\"], [1, \"security-list\"], [1, \"security-item\", 3, \"click\"], [1, \"security-icon\", \"data\"], [1, \"fas\", \"fa-database\"], [1, \"security-details\"], [1, \"security-icon\", \"biometric\"], [1, \"fas\", \"fa-fingerprint\"], [1, \"security-icon\", \"compliance\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Votre espace personnel de d\\u00E9tection et profilage d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_7_listener() {\n            return ctx.startNewScan();\n          });\n          i0.ɵɵelement(8, \"i\", 4);\n          i0.ɵɵtext(9, \" Nouveau scan d'iris \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 5)(11, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_11_listener() {\n            return ctx.showSecurityDetails();\n          });\n          i0.ɵɵelementStart(12, \"div\", 7);\n          i0.ɵɵelement(13, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"h3\");\n          i0.ɵɵtext(16, \"Niveau de s\\u00E9curit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\", 10);\n          i0.ɵɵtext(18, \"\\u00C9lev\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_19_listener() {\n            return ctx.showProfileStatus();\n          });\n          i0.ɵɵelementStart(20, \"div\", 11);\n          i0.ɵɵelement(21, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"div\", 9)(23, \"h3\");\n          i0.ɵɵtext(24, \"Statut du profil\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p\", 10);\n          i0.ɵɵtext(26, \"V\\u00E9rifi\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(27, \"div\", 6);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_27_listener() {\n            return ctx.showScanHistory();\n          });\n          i0.ɵɵelementStart(28, \"div\", 13);\n          i0.ɵɵelement(29, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"div\", 9)(31, \"h3\");\n          i0.ɵɵtext(32, \"Dernier scan\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"p\", 10);\n          i0.ɵɵtext(34, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(35, \"div\", 15)(36, \"button\", 16);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_36_listener() {\n            return ctx.changeTab(\"overview\");\n          });\n          i0.ɵɵelement(37, \"i\", 17);\n          i0.ɵɵtext(38, \" Vue d'ensemble \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_39_listener() {\n            return ctx.changeTab(\"scan\");\n          });\n          i0.ɵɵelement(40, \"i\", 4);\n          i0.ɵɵtext(41, \" Scan d'iris \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_42_listener() {\n            return ctx.changeTab(\"profile\");\n          });\n          i0.ɵɵelement(43, \"i\", 19);\n          i0.ɵɵtext(44, \" Mon profil \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_45_listener() {\n            return ctx.changeTab(\"history\");\n          });\n          i0.ɵɵelement(46, \"i\", 20);\n          i0.ɵɵtext(47, \" Historique \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\", 21)(49, \"div\", 22)(50, \"h2\");\n          i0.ɵɵtext(51, \"Aper\\u00E7u de votre profil d'iris\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"p\");\n          i0.ɵɵtext(53, \"Caract\\u00E9ristiques principales de votre iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 23)(55, \"div\", 24);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_55_listener() {\n            return ctx.viewFullIrisImage();\n          });\n          i0.ɵɵelement(56, \"img\", 25);\n          i0.ɵɵelementStart(57, \"div\", 26);\n          i0.ɵɵtext(58, \"V\\u00E9rifi\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"div\", 27);\n          i0.ɵɵtext(60, \"ID: #A12345678\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(61, \"div\", 28)(62, \"div\", 29);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_62_listener() {\n            return ctx.showPatternDetails();\n          });\n          i0.ɵɵelementStart(63, \"div\", 30);\n          i0.ɵɵtext(64, \"Type de motif:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"div\", 31);\n          i0.ɵɵelement(66, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"div\", 33);\n          i0.ɵɵtext(68, \"Crypte Dominant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"div\", 29);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_69_listener() {\n            return ctx.showColorDetails();\n          });\n          i0.ɵɵelementStart(70, \"div\", 30);\n          i0.ɵɵtext(71, \"Couleur d'iris:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"div\", 31);\n          i0.ɵɵelement(73, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"div\", 33);\n          i0.ɵɵtext(75, \"Marron\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 29);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_76_listener() {\n            return ctx.showFeaturesDetails();\n          });\n          i0.ɵɵelementStart(77, \"div\", 30);\n          i0.ɵɵtext(78, \"Caract\\u00E9ristiques uniques:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 31);\n          i0.ɵɵelement(80, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"div\", 33);\n          i0.ɵɵtext(82, \"42\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(83, \"div\", 29);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_83_listener() {\n            return ctx.showConfidenceDetails();\n          });\n          i0.ɵɵelementStart(84, \"div\", 30);\n          i0.ɵɵtext(85, \"Score de confiance:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"div\", 31);\n          i0.ɵɵelement(87, \"div\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"div\", 33);\n          i0.ɵɵtext(89, \"98.7%\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(90, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_90_listener() {\n            return ctx.showVerificationDetails();\n          });\n          i0.ɵɵelementStart(91, \"div\", 35);\n          i0.ɵɵtext(92, \"Derni\\u00E8re v\\u00E9rification\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"div\", 36);\n          i0.ɵɵtext(94, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"div\", 37);\n          i0.ɵɵelement(96, \"i\", 12);\n          i0.ɵɵtext(97, \" R\\u00E9ussi \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(98, \"div\", 38)(99, \"div\", 39)(100, \"div\", 22)(101, \"h2\");\n          i0.ɵɵtext(102, \"Activit\\u00E9 r\\u00E9cente\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"p\");\n          i0.ɵɵtext(104, \"Votre activit\\u00E9 r\\u00E9cente de scan d'iris\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(105, \"div\", 40)(106, \"div\", 41);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_106_listener() {\n            return ctx.viewScanDetails();\n          });\n          i0.ɵɵelementStart(107, \"div\", 42);\n          i0.ɵɵelement(108, \"i\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"div\", 43)(110, \"h4\");\n          i0.ɵɵtext(111, \"Scan d'iris compl\\u00E9t\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"p\");\n          i0.ɵɵtext(113, \"Aujourd'hui \\u00E0 10:30\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(114, \"div\", 41);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_114_listener() {\n            return ctx.viewProfileUpdateDetails();\n          });\n          i0.ɵɵelementStart(115, \"div\", 44);\n          i0.ɵɵelement(116, \"i\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"div\", 43)(118, \"h4\");\n          i0.ɵɵtext(119, \"Profil mis \\u00E0 jour\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"p\");\n          i0.ɵɵtext(121, \"Hier \\u00E0 14:15\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(122, \"div\", 41);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_122_listener() {\n            return ctx.viewReportDetails();\n          });\n          i0.ɵɵelementStart(123, \"div\", 46);\n          i0.ɵɵelement(124, \"i\", 47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(125, \"div\", 43)(126, \"h4\");\n          i0.ɵɵtext(127, \"Rapport g\\u00E9n\\u00E9r\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"p\");\n          i0.ɵɵtext(129, \"Il y a 3 jours \\u00E0 11:45\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(130, \"div\", 48)(131, \"div\", 22)(132, \"h2\");\n          i0.ɵɵtext(133, \"Statut de s\\u00E9curit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"p\");\n          i0.ɵɵtext(135, \"Aper\\u00E7u de la s\\u00E9curit\\u00E9 de votre compte\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(136, \"div\", 49)(137, \"div\", 50);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_137_listener() {\n            return ctx.viewDataProtectionDetails();\n          });\n          i0.ɵɵelementStart(138, \"div\", 51);\n          i0.ɵɵelement(139, \"i\", 52);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(140, \"div\", 53)(141, \"h4\");\n          i0.ɵɵtext(142, \"Protection des donn\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(143, \"p\");\n          i0.ɵɵtext(144, \"Vos donn\\u00E9es biom\\u00E9triques sont crypt\\u00E9es\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(145, \"div\", 50);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_145_listener() {\n            return ctx.viewBiometricAuthDetails();\n          });\n          i0.ɵɵelementStart(146, \"div\", 54);\n          i0.ɵɵelement(147, \"i\", 55);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(148, \"div\", 53)(149, \"h4\");\n          i0.ɵɵtext(150, \"Authentification biom\\u00E9trique\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(151, \"p\");\n          i0.ɵɵtext(152, \"Activ\\u00E9e pour une s\\u00E9curit\\u00E9 renforc\\u00E9e\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(153, \"div\", 50);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_153_listener() {\n            return ctx.viewGDPRComplianceDetails();\n          });\n          i0.ɵɵelementStart(154, \"div\", 56);\n          i0.ɵɵelement(155, \"i\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(156, \"div\", 53)(157, \"h4\");\n          i0.ɵɵtext(158, \"Conformit\\u00E9 RGPD\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(159, \"p\");\n          i0.ɵɵtext(160, \"Conforme aux r\\u00E9glementations de protection des donn\\u00E9es\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\"Bienvenue, \", ctx.userName, \"\");\n        }\n      },\n      styles: [\"@charset \\\"UTF-8\\\";\\n.dashboard-container[_ngcontent-%COMP%] {\\n  font-family: \\\"Montserrat\\\", sans-serif;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);\\n  padding: 20px;\\n  min-height: 100vh;\\n  color: #333;\\n}\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  margin-bottom: 30px;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%] {\\n  background: #ffffff;\\n  color: #333;\\n  padding: 30px;\\n  border-radius: 12px;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  border-left: 4px solid var(--fleur-primary);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  width: 30%;\\n  height: 100%;\\n  background: linear-gradient(to right, rgba(44, 123, 229, 0), rgba(44, 123, 229, 0.1));\\n  z-index: 0;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 700;\\n  color: #333;\\n  position: relative;\\n  z-index: 1;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #666;\\n  position: relative;\\n  z-index: 1;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  color: white;\\n  border: none;\\n  padding: 12px 24px;\\n  border-radius: 50px;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  z-index: 1;\\n  box-shadow: 0 10px 25px rgba(138, 79, 255, 0.3);\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  font-size: 1.1rem;\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 15px 35px rgba(138, 79, 255, 0.4);\\n}\\n.dashboard-header[_ngcontent-%COMP%]   .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(-2px);\\n}\\n\\n.info-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 20px;\\n  margin-bottom: 30px;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  padding: 20px;\\n  display: flex;\\n  align-items: center;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n  cursor: pointer;\\n  position: relative;\\n  overflow: hidden;\\n  border-bottom: 3px solid transparent;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 1rem 2rem rgba(18, 38, 63, 0.075);\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:nth-child(1) {\\n  border-bottom-color: var(--fleur-primary);\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:nth-child(2) {\\n  border-bottom-color: var(--flux-primary);\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:nth-child(3) {\\n  border-bottom-color: var(--shaker-primary);\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1.2rem;\\n  transition: transform 0.2s ease;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.security[_ngcontent-%COMP%] {\\n  background-color: var(--fleur-primary);\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.verified[_ngcontent-%COMP%] {\\n  background-color: var(--flux-primary);\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-icon.time[_ngcontent-%COMP%] {\\n  background-color: var(--shaker-primary);\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]:hover   .card-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  margin: 0 0 5px 0;\\n  color: #666;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.info-cards[_ngcontent-%COMP%]   .info-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  margin: 0;\\n  color: #333;\\n}\\n\\n.dashboard-nav[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-bottom: 30px;\\n  background-color: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  padding: 16px 25px;\\n  font-size: 0.95rem;\\n  font-weight: 600;\\n  color: #666;\\n  cursor: pointer;\\n  position: relative;\\n  transition: all 0.2s ease;\\n  flex: 1;\\n  text-align: center;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 1rem;\\n  opacity: 0.7;\\n  transition: opacity 0.2s ease;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 3px;\\n  background-color: var(--fleur-primary);\\n  transform: scaleX(0);\\n  transition: transform 0.2s ease;\\n  transform-origin: center;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover {\\n  color: #333;\\n  background-color: rgba(44, 123, 229, 0.05);\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn.active[_ngcontent-%COMP%] {\\n  color: var(--fleur-primary);\\n  background-color: rgba(44, 123, 229, 0.1);\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn.active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.dashboard-nav[_ngcontent-%COMP%]   .nav-btn.active[_ngcontent-%COMP%]:after {\\n  transform: scaleX(1);\\n}\\n\\n.profile-overview[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  padding: 25px;\\n  margin-bottom: 30px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.profile-overview[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  width: 120px;\\n  height: 120px;\\n  background: radial-gradient(circle, rgba(44, 123, 229, 0.1) 0%, rgba(44, 123, 229, 0) 70%);\\n  border-radius: 0 0 0 100%;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n  position: relative;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 700;\\n  color: #333;\\n  display: flex;\\n  align-items: center;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 4px;\\n  height: 20px;\\n  background-color: var(--fleur-primary);\\n  margin-right: 10px;\\n  border-radius: 2px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0 0 0 14px;\\n  font-size: 0.9rem;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 30px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 220px;\\n  height: 220px;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  background-color: #f0f0f0;\\n  box-shadow: 0 0.5rem 1rem rgba(18, 38, 63, 0.1);\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n  cursor: pointer;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.15);\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .iris-image[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.5s ease;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]:hover   .iris-image[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .verification-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 10px;\\n  background-color: var(--flux-primary);\\n  color: white;\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n  font-size: 0.75rem;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  box-shadow: 0 2px 5px rgba(0, 217, 126, 0.3);\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .verification-badge[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u2713\\\";\\n  margin-right: 5px;\\n  font-weight: bold;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%]   .iris-id[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 10px;\\n  left: 10px;\\n  color: white;\\n  font-size: 0.8rem;\\n  background-color: rgba(18, 38, 63, 0.8);\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  cursor: pointer;\\n  padding: 5px;\\n  border-radius: 6px;\\n  transition: background-color 0.2s ease;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(44, 123, 229, 0.05);\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n  width: 180px;\\n  font-size: 0.9rem;\\n  color: #666;\\n  font-weight: 500;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 8px;\\n  background-color: #edf2f9;\\n  border-radius: 4px;\\n  margin: 0 20px;\\n  overflow: hidden;\\n  position: relative;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(to right, var(--fleur-primary), var(--bijou-primary));\\n  width: 100%;\\n  border-radius: 4px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 100%);\\n  animation: _ngcontent-%COMP%_shimmer 2s infinite;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n  width: 120px;\\n  text-align: right;\\n  font-weight: 700;\\n  color: #333;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  padding: 15px;\\n  border-radius: 6px;\\n  background-color: #edf2f9;\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n  transition: background-color 0.2s ease;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]:hover {\\n  background-color: #e5ecf6;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-label[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 0.9rem;\\n  margin-right: 15px;\\n  font-weight: 500;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-time[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-right: 20px;\\n  color: #333;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: flex;\\n  align-items: center;\\n  font-weight: 600;\\n  padding: 5px 10px;\\n  border-radius: 4px;\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status.success[_ngcontent-%COMP%] {\\n  color: white;\\n  background-color: var(--flux-primary);\\n}\\n.profile-overview[_ngcontent-%COMP%]   .profile-content[_ngcontent-%COMP%]   .iris-details[_ngcontent-%COMP%]   .verification-info[_ngcontent-%COMP%]   .verification-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shimmer {\\n  0% {\\n    transform: translateX(-100%);\\n  }\\n  100% {\\n    transform: translateX(100%);\\n  }\\n}\\n.bottom-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 30px;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 12px;\\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\\n  padding: 25px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n  position: relative;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  margin: 0 0 5px 0;\\n  font-weight: 700;\\n  color: #333;\\n  display: flex;\\n  align-items: center;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before, .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  display: inline-block;\\n  width: 4px;\\n  height: 20px;\\n  background-color: var(--fleur-primary);\\n  margin-right: 10px;\\n  border-radius: 2px;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .bottom-sections[_ngcontent-%COMP%]   .security-section[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin: 0 0 0 14px;\\n  font-size: 0.9rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 15px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  margin-bottom: 10px;\\n  border-left: 3px solid transparent;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f9fbfd;\\n  transform: translateX(5px);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(1) {\\n  border-left-color: var(--bijou-primary);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(1):hover {\\n  background-color: rgba(110, 0, 255, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(2) {\\n  border-left-color: #ff5c8d;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(2):hover {\\n  background-color: rgba(255, 92, 141, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(3) {\\n  border-left-color: var(--shaker-primary);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:nth-child(3):hover {\\n  background-color: rgba(246, 195, 67, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon[_ngcontent-%COMP%] {\\n  width: 42px;\\n  height: 42px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1rem;\\n  transition: transform 0.2s ease;\\n  box-shadow: 0 0.25rem 0.5rem rgba(18, 38, 63, 0.1);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.scan[_ngcontent-%COMP%] {\\n  background-color: var(--bijou-primary);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.profile[_ngcontent-%COMP%] {\\n  background-color: #ff5c8d;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-icon.report[_ngcontent-%COMP%] {\\n  background-color: var(--shaker-primary);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]:hover   .activity-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.85rem;\\n  display: flex;\\n  align-items: center;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .activity-list[_ngcontent-%COMP%]   .activity-item[_ngcontent-%COMP%]   .activity-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDD52\\\";\\n  margin-right: 5px;\\n  font-size: 0.8rem;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 15px;\\n  border-radius: 8px;\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  margin-bottom: 10px;\\n  border-left: 3px solid transparent;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f9fbfd;\\n  transform: translateX(5px);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(1) {\\n  border-left-color: var(--flux-primary);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(1):hover {\\n  background-color: rgba(0, 217, 126, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(2) {\\n  border-left-color: var(--bijou-primary);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(2):hover {\\n  background-color: rgba(110, 0, 255, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(3) {\\n  border-left-color: var(--fleur-primary);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:nth-child(3):hover {\\n  background-color: rgba(44, 123, 229, 0.05);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon[_ngcontent-%COMP%] {\\n  width: 42px;\\n  height: 42px;\\n  border-radius: 8px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 15px;\\n  color: white;\\n  font-size: 1rem;\\n  transition: transform 0.2s ease;\\n  box-shadow: 0 0.25rem 0.5rem rgba(18, 38, 63, 0.1);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.data[_ngcontent-%COMP%] {\\n  background-color: var(--flux-primary);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.biometric[_ngcontent-%COMP%] {\\n  background-color: var(--bijou-primary);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-icon.compliance[_ngcontent-%COMP%] {\\n  background-color: var(--fleur-primary);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]:hover   .security-icon[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-details[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 5px 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n.bottom-sections[_ngcontent-%COMP%]   .security-list[_ngcontent-%COMP%]   .security-item[_ngcontent-%COMP%]   .security-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.85rem;\\n}\\n\\n@media (max-width: 1200px) {\\n  .profile-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .profile-content[_ngcontent-%COMP%]   .iris-image-container[_ngcontent-%COMP%] {\\n    margin: 0 auto 30px;\\n    width: 280px;\\n    height: 280px;\\n  }\\n}\\n@media (max-width: 1024px) {\\n  .bottom-sections[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 20px;\\n  }\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 15px;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .info-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 15px;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n    white-space: nowrap;\\n    padding: 5px;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    padding: 12px 15px;\\n    font-size: 0.85rem;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 5px;\\n  }\\n  .profile-overview[_ngcontent-%COMP%], .activity-section[_ngcontent-%COMP%], .security-section[_ngcontent-%COMP%] {\\n    padding: 20px 15px;\\n  }\\n  .detail-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-label[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    margin-bottom: 8px;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-bar[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    margin: 8px 0 !important;\\n  }\\n  .detail-item[_ngcontent-%COMP%]   .detail-value[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    text-align: left !important;\\n    margin-top: 8px;\\n  }\\n  .verification-info[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .verification-info[_ngcontent-%COMP%]   .verification-label[_ngcontent-%COMP%], .verification-info[_ngcontent-%COMP%]   .verification-time[_ngcontent-%COMP%] {\\n    margin-bottom: 10px;\\n    margin-right: 0 !important;\\n  }\\n  .verification-info[_ngcontent-%COMP%]   .verification-status[_ngcontent-%COMP%] {\\n    margin-left: 0 !important;\\n    align-self: flex-start;\\n  }\\n  .section-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n    font-size: 1.2rem !important;\\n  }\\n  .section-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.85rem !important;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .welcome-card[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem !important;\\n  }\\n  .welcome-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 0.9rem !important;\\n  }\\n  .welcome-card[_ngcontent-%COMP%]   .new-scan-btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n    padding: 12px 15px !important;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%] {\\n    padding: 10px !important;\\n    font-size: 0.8rem !important;\\n  }\\n  .dashboard-nav[_ngcontent-%COMP%]   .nav-btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    margin-right: 3px !important;\\n  }\\n  .iris-image-container[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n    height: 220px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DashboardComponent", "constructor", "router", "userName", "lastScanTime", "securityLevel", "profileStatus", "irisType", "irisColor", "uniqueFeatures", "confidenceScore", "recentActivities", "type", "title", "time", "securityStatuses", "description", "activeTab", "ngOnInit", "startNewScan", "alert", "changeTab", "tab", "showSecurityDetails", "showProfileStatus", "showScanHistory", "viewFullIrisImage", "showPatternDetails", "showColorDetails", "showFeaturesDetails", "showConfidenceDetails", "showVerificationDetails", "viewScanDetails", "viewProfileUpdateDetails", "viewReportDetails", "viewDataProtectionDetails", "viewBiometricAuthDetails", "viewGDPRComplianceDetails", "checkAuthentication", "isAuthenticated", "navigate", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "DashboardComponent_Template_button_click_7_listener", "ɵɵelement", "DashboardComponent_Template_div_click_11_listener", "DashboardComponent_Template_div_click_19_listener", "DashboardComponent_Template_div_click_27_listener", "DashboardComponent_Template_button_click_36_listener", "DashboardComponent_Template_button_click_39_listener", "DashboardComponent_Template_button_click_42_listener", "DashboardComponent_Template_button_click_45_listener", "DashboardComponent_Template_div_click_55_listener", "DashboardComponent_Template_div_click_62_listener", "DashboardComponent_Template_div_click_69_listener", "DashboardComponent_Template_div_click_76_listener", "DashboardComponent_Template_div_click_83_listener", "DashboardComponent_Template_div_click_90_listener", "DashboardComponent_Template_div_click_106_listener", "DashboardComponent_Template_div_click_114_listener", "DashboardComponent_Template_div_click_122_listener", "DashboardComponent_Template_div_click_137_listener", "DashboardComponent_Template_div_click_145_listener", "DashboardComponent_Template_div_click_153_listener", "ɵɵadvance", "ɵɵtextInterpolate1"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit {\n  // Données utilisateur\n  userName: string = '<PERSON>';\n  lastScanTime: string = '10:30';\n  securityLevel: string = 'Élevé';\n  profileStatus: string = 'Vérifié';\n\n  // Données de l'iris\n  irisType: string = 'Crypte Dominant';\n  irisColor: string = 'Marron';\n  uniqueFeatures: number = 42;\n  confidenceScore: string = '98.7%';\n\n  // Activités récentes\n  recentActivities = [\n    { type: 'scan', title: 'Scan d\\'iris complété', time: 'Aujourd\\'hui à 10:30' },\n    { type: 'profile', title: 'Profil mis à jour', time: 'Hier à 14:15' },\n    { type: 'report', title: 'Rapport généré', time: 'Il y a 3 jours à 11:45' }\n  ];\n\n  // Statuts de sécurité\n  securityStatuses = [\n    { type: 'data', title: 'Protection des données', description: 'Vos données biométriques sont cryptées' },\n    { type: 'biometric', title: 'Authentification biométrique', description: 'Activée pour une sécurité renforcée' },\n    { type: 'compliance', title: 'Conformité RGPD', description: 'Conforme aux réglementations de protection des données' }\n  ];\n\n  // Onglet actif\n  activeTab: string = 'overview';\n\n  constructor(private router: Router) { }\n\n  ngOnInit(): void {\n    // Vérifier si l'utilisateur est connecté (à implémenter avec un service d'authentification)\n    // Si non connecté, rediriger vers la page de connexion\n    // this.checkAuthentication();\n  }\n\n  // Méthode pour naviguer vers la page de scan d'iris\n  startNewScan(): void {\n    alert('Lancement d\\'un nouveau scan d\\'iris...');\n    // Redirection vers la page de scan (à implémenter)\n    // this.router.navigate(['/scan-iris']);\n  }\n\n  // Méthode pour changer d'onglet dans le tableau de bord\n  changeTab(tab: string): void {\n    this.activeTab = tab;\n    alert(`Navigation vers l'onglet: ${tab}`);\n    // Implémenter la logique de changement d'onglet\n  }\n\n  // Méthodes pour les cartes d'information\n  showSecurityDetails(): void {\n    alert('Détails du niveau de sécurité');\n  }\n\n  showProfileStatus(): void {\n    alert('Détails du statut du profil');\n  }\n\n  showScanHistory(): void {\n    alert('Historique des scans');\n  }\n\n  // Méthodes pour l'aperçu du profil d'iris\n  viewFullIrisImage(): void {\n    alert('Affichage de l\\'image d\\'iris en plein écran');\n  }\n\n  showPatternDetails(): void {\n    alert(`Détails du type de motif: ${this.irisType}`);\n  }\n\n  showColorDetails(): void {\n    alert(`Détails de la couleur d'iris: ${this.irisColor}`);\n  }\n\n  showFeaturesDetails(): void {\n    alert(`Détails des caractéristiques uniques: ${this.uniqueFeatures} points identifiés`);\n  }\n\n  showConfidenceDetails(): void {\n    alert(`Détails du score de confiance: ${this.confidenceScore}`);\n  }\n\n  showVerificationDetails(): void {\n    alert(`Détails de la vérification effectuée à ${this.lastScanTime}`);\n  }\n\n  // Méthodes pour l'activité récente\n  viewScanDetails(): void {\n    alert('Détails du scan d\\'iris complété');\n  }\n\n  viewProfileUpdateDetails(): void {\n    alert('Détails de la mise à jour du profil');\n  }\n\n  viewReportDetails(): void {\n    alert('Détails du rapport généré');\n  }\n\n  // Méthodes pour le statut de sécurité\n  viewDataProtectionDetails(): void {\n    alert('Détails de la protection des données');\n  }\n\n  viewBiometricAuthDetails(): void {\n    alert('Détails de l\\'authentification biométrique');\n  }\n\n  viewGDPRComplianceDetails(): void {\n    alert('Détails de la conformité RGPD');\n  }\n\n  // Méthode pour vérifier l'authentification (à implémenter)\n  private checkAuthentication(): void {\n    const isAuthenticated = false; // À remplacer par la vérification réelle\n\n    if (!isAuthenticated) {\n      this.router.navigate(['/login']);\n    }\n  }\n}\n", "<div class=\"dashboard-container\">\n  <!-- En-tête du tableau de bord -->\n  <div class=\"dashboard-header\">\n    <div class=\"welcome-card\">\n      <h1>Bienvenue, {{ userName }}</h1>\n      <p>Votre espace personnel de détection et profilage d'iris</p>\n      <button class=\"new-scan-btn\" (click)=\"startNewScan()\">\n        <i class=\"fas fa-eye\"></i>\n        Nouveau scan d'iris\n      </button>\n    </div>\n  </div>\n\n  <!-- Cartes d'information -->\n  <div class=\"info-cards\">\n    <div class=\"info-card\" (click)=\"showSecurityDetails()\">\n      <div class=\"card-icon security\">\n        <i class=\"fas fa-shield-alt\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Niveau de sécurité</h3>\n        <p class=\"status\">Élevé</p>\n      </div>\n    </div>\n\n    <div class=\"info-card\" (click)=\"showProfileStatus()\">\n      <div class=\"card-icon verified\">\n        <i class=\"fas fa-check-circle\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Statut du profil</h3>\n        <p class=\"status\">Vérifié</p>\n      </div>\n    </div>\n\n    <div class=\"info-card\" (click)=\"showScanHistory()\">\n      <div class=\"card-icon time\">\n        <i class=\"fas fa-clock\"></i>\n      </div>\n      <div class=\"card-content\">\n        <h3>Dernier scan</h3>\n        <p class=\"status\">Aujourd'hui à 10:30</p>\n      </div>\n    </div>\n  </div>\n\n  <!-- Navigation du tableau de bord -->\n  <div class=\"dashboard-nav\">\n    <button class=\"nav-btn active\" (click)=\"changeTab('overview')\">\n      <i class=\"fas fa-th-large\"></i> Vue d'ensemble\n    </button>\n    <button class=\"nav-btn\" (click)=\"changeTab('scan')\">\n      <i class=\"fas fa-eye\"></i> Scan d'iris\n    </button>\n    <button class=\"nav-btn\" (click)=\"changeTab('profile')\">\n      <i class=\"fas fa-user\"></i> Mon profil\n    </button>\n    <button class=\"nav-btn\" (click)=\"changeTab('history')\">\n      <i class=\"fas fa-history\"></i> Historique\n    </button>\n  </div>\n\n  <!-- Aperçu du profil d'iris -->\n  <div class=\"profile-overview\">\n    <div class=\"section-header\">\n      <h2>Aperçu de votre profil d'iris</h2>\n      <p>Caractéristiques principales de votre iris</p>\n    </div>\n\n    <div class=\"profile-content\">\n      <div class=\"iris-image-container\" (click)=\"viewFullIrisImage()\">\n        <img src=\"\" alt=\"Iris Scan\" class=\"iris-image\">\n        <div class=\"verification-badge\">Vérifié</div>\n        <div class=\"iris-id\">ID: #A12345678</div>\n      </div>\n\n      <div class=\"iris-details\">\n        <div class=\"detail-item\" (click)=\"showPatternDetails()\">\n          <div class=\"detail-label\">Type de motif:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">Crypte Dominant</div>\n        </div>\n\n        <div class=\"detail-item\" (click)=\"showColorDetails()\">\n          <div class=\"detail-label\">Couleur d'iris:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">Marron</div>\n        </div>\n\n        <div class=\"detail-item\" (click)=\"showFeaturesDetails()\">\n          <div class=\"detail-label\">Caractéristiques uniques:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">42</div>\n        </div>\n\n        <div class=\"detail-item\" (click)=\"showConfidenceDetails()\">\n          <div class=\"detail-label\">Score de confiance:</div>\n          <div class=\"detail-bar\">\n            <div class=\"progress-bar\"></div>\n          </div>\n          <div class=\"detail-value\">98.7%</div>\n        </div>\n\n        <div class=\"verification-info\" (click)=\"showVerificationDetails()\">\n          <div class=\"verification-label\">Dernière vérification</div>\n          <div class=\"verification-time\">Aujourd'hui à 10:30</div>\n          <div class=\"verification-status success\">\n            <i class=\"fas fa-check-circle\"></i> Réussi\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Section inférieure avec deux colonnes -->\n  <div class=\"bottom-sections\">\n    <!-- Activité récente -->\n    <div class=\"activity-section\">\n      <div class=\"section-header\">\n        <h2>Activité récente</h2>\n        <p>Votre activité récente de scan d'iris</p>\n      </div>\n\n      <div class=\"activity-list\">\n        <div class=\"activity-item\" (click)=\"viewScanDetails()\">\n          <div class=\"activity-icon scan\">\n            <i class=\"fas fa-eye\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Scan d'iris complété</h4>\n            <p>Aujourd'hui à 10:30</p>\n          </div>\n        </div>\n\n        <div class=\"activity-item\" (click)=\"viewProfileUpdateDetails()\">\n          <div class=\"activity-icon profile\">\n            <i class=\"fas fa-user-edit\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Profil mis à jour</h4>\n            <p>Hier à 14:15</p>\n          </div>\n        </div>\n\n        <div class=\"activity-item\" (click)=\"viewReportDetails()\">\n          <div class=\"activity-icon report\">\n            <i class=\"fas fa-file-alt\"></i>\n          </div>\n          <div class=\"activity-details\">\n            <h4>Rapport généré</h4>\n            <p>Il y a 3 jours à 11:45</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statut de sécurité -->\n    <div class=\"security-section\">\n      <div class=\"section-header\">\n        <h2>Statut de sécurité</h2>\n        <p>Aperçu de la sécurité de votre compte</p>\n      </div>\n\n      <div class=\"security-list\">\n        <div class=\"security-item\" (click)=\"viewDataProtectionDetails()\">\n          <div class=\"security-icon data\">\n            <i class=\"fas fa-database\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Protection des données</h4>\n            <p>Vos données biométriques sont cryptées</p>\n          </div>\n        </div>\n\n        <div class=\"security-item\" (click)=\"viewBiometricAuthDetails()\">\n          <div class=\"security-icon biometric\">\n            <i class=\"fas fa-fingerprint\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Authentification biométrique</h4>\n            <p>Activée pour une sécurité renforcée</p>\n          </div>\n        </div>\n\n        <div class=\"security-item\" (click)=\"viewGDPRComplianceDetails()\">\n          <div class=\"security-icon compliance\">\n            <i class=\"fas fa-shield-alt\"></i>\n          </div>\n          <div class=\"security-details\">\n            <h4>Conformité RGPD</h4>\n            <p>Conforme aux réglementations de protection des données</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;AAQA,OAAM,MAAOA,kBAAkB;EA8B7BC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IA7B1B;IACA,KAAAC,QAAQ,GAAW,aAAa;IAChC,KAAAC,YAAY,GAAW,OAAO;IAC9B,KAAAC,aAAa,GAAW,OAAO;IAC/B,KAAAC,aAAa,GAAW,SAAS;IAEjC;IACA,KAAAC,QAAQ,GAAW,iBAAiB;IACpC,KAAAC,SAAS,GAAW,QAAQ;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,eAAe,GAAW,OAAO;IAEjC;IACA,KAAAC,gBAAgB,GAAG,CACjB;MAAEC,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,uBAAuB;MAAEC,IAAI,EAAE;IAAsB,CAAE,EAC9E;MAAEF,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,IAAI,EAAE;IAAc,CAAE,EACrE;MAAEF,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,gBAAgB;MAAEC,IAAI,EAAE;IAAwB,CAAE,CAC5E;IAED;IACA,KAAAC,gBAAgB,GAAG,CACjB;MAAEH,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE,wBAAwB;MAAEG,WAAW,EAAE;IAAwC,CAAE,EACxG;MAAEJ,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,8BAA8B;MAAEG,WAAW,EAAE;IAAqC,CAAE,EAChH;MAAEJ,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,iBAAiB;MAAEG,WAAW,EAAE;IAAwD,CAAE,CACxH;IAED;IACA,KAAAC,SAAS,GAAW,UAAU;EAEQ;EAEtCC,QAAQA,CAAA;IACN;IACA;IACA;EAAA;EAGF;EACAC,YAAYA,CAAA;IACVC,KAAK,CAAC,yCAAyC,CAAC;IAChD;IACA;EACF;EAEA;EACAC,SAASA,CAACC,GAAW;IACnB,IAAI,CAACL,SAAS,GAAGK,GAAG;IACpBF,KAAK,CAAC,6BAA6BE,GAAG,EAAE,CAAC;IACzC;EACF;EAEA;EACAC,mBAAmBA,CAAA;IACjBH,KAAK,CAAC,+BAA+B,CAAC;EACxC;EAEAI,iBAAiBA,CAAA;IACfJ,KAAK,CAAC,6BAA6B,CAAC;EACtC;EAEAK,eAAeA,CAAA;IACbL,KAAK,CAAC,sBAAsB,CAAC;EAC/B;EAEA;EACAM,iBAAiBA,CAAA;IACfN,KAAK,CAAC,8CAA8C,CAAC;EACvD;EAEAO,kBAAkBA,CAAA;IAChBP,KAAK,CAAC,6BAA6B,IAAI,CAACb,QAAQ,EAAE,CAAC;EACrD;EAEAqB,gBAAgBA,CAAA;IACdR,KAAK,CAAC,iCAAiC,IAAI,CAACZ,SAAS,EAAE,CAAC;EAC1D;EAEAqB,mBAAmBA,CAAA;IACjBT,KAAK,CAAC,yCAAyC,IAAI,CAACX,cAAc,oBAAoB,CAAC;EACzF;EAEAqB,qBAAqBA,CAAA;IACnBV,KAAK,CAAC,kCAAkC,IAAI,CAACV,eAAe,EAAE,CAAC;EACjE;EAEAqB,uBAAuBA,CAAA;IACrBX,KAAK,CAAC,0CAA0C,IAAI,CAAChB,YAAY,EAAE,CAAC;EACtE;EAEA;EACA4B,eAAeA,CAAA;IACbZ,KAAK,CAAC,kCAAkC,CAAC;EAC3C;EAEAa,wBAAwBA,CAAA;IACtBb,KAAK,CAAC,qCAAqC,CAAC;EAC9C;EAEAc,iBAAiBA,CAAA;IACfd,KAAK,CAAC,2BAA2B,CAAC;EACpC;EAEA;EACAe,yBAAyBA,CAAA;IACvBf,KAAK,CAAC,sCAAsC,CAAC;EAC/C;EAEAgB,wBAAwBA,CAAA;IACtBhB,KAAK,CAAC,4CAA4C,CAAC;EACrD;EAEAiB,yBAAyBA,CAAA;IACvBjB,KAAK,CAAC,+BAA+B,CAAC;EACxC;EAEA;EACQkB,mBAAmBA,CAAA;IACzB,MAAMC,eAAe,GAAG,KAAK,CAAC,CAAC;IAE/B,IAAI,CAACA,eAAe,EAAE;MACpB,IAAI,CAACrC,MAAM,CAACsC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;EAEpC;;;uBA3HWxC,kBAAkB,EAAAyC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlB5C,kBAAkB;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR/BV,EAAA,CAAAY,cAAA,aAAiC;UAIvBZ,EAAA,CAAAa,MAAA,GAAyB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAClCd,EAAA,CAAAY,cAAA,QAAG;UAAAZ,EAAA,CAAAa,MAAA,mEAAuD;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAC9Dd,EAAA,CAAAY,cAAA,gBAAsD;UAAzBZ,EAAA,CAAAe,UAAA,mBAAAC,oDAAA;YAAA,OAASL,GAAA,CAAAjC,YAAA,EAAc;UAAA,EAAC;UACnDsB,EAAA,CAAAiB,SAAA,WAA0B;UAC1BjB,EAAA,CAAAa,MAAA,4BACF;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAKbd,EAAA,CAAAY,cAAA,cAAwB;UACCZ,EAAA,CAAAe,UAAA,mBAAAG,kDAAA;YAAA,OAASP,GAAA,CAAA7B,mBAAA,EAAqB;UAAA,EAAC;UACpDkB,EAAA,CAAAY,cAAA,cAAgC;UAC9BZ,EAAA,CAAAiB,SAAA,YAAiC;UACnCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,cAA0B;UACpBZ,EAAA,CAAAa,MAAA,oCAAkB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC3Bd,EAAA,CAAAY,cAAA,aAAkB;UAAAZ,EAAA,CAAAa,MAAA,uBAAK;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI/Bd,EAAA,CAAAY,cAAA,cAAqD;UAA9BZ,EAAA,CAAAe,UAAA,mBAAAI,kDAAA;YAAA,OAASR,GAAA,CAAA5B,iBAAA,EAAmB;UAAA,EAAC;UAClDiB,EAAA,CAAAY,cAAA,eAAgC;UAC9BZ,EAAA,CAAAiB,SAAA,aAAmC;UACrCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,cAA0B;UACpBZ,EAAA,CAAAa,MAAA,wBAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACzBd,EAAA,CAAAY,cAAA,aAAkB;UAAAZ,EAAA,CAAAa,MAAA,yBAAO;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAIjCd,EAAA,CAAAY,cAAA,cAAmD;UAA5BZ,EAAA,CAAAe,UAAA,mBAAAK,kDAAA;YAAA,OAAST,GAAA,CAAA3B,eAAA,EAAiB;UAAA,EAAC;UAChDgB,EAAA,CAAAY,cAAA,eAA4B;UAC1BZ,EAAA,CAAAiB,SAAA,aAA4B;UAC9BjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,cAA0B;UACpBZ,EAAA,CAAAa,MAAA,oBAAY;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrBd,EAAA,CAAAY,cAAA,aAAkB;UAAAZ,EAAA,CAAAa,MAAA,gCAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAM/Cd,EAAA,CAAAY,cAAA,eAA2B;UACMZ,EAAA,CAAAe,UAAA,mBAAAM,qDAAA;YAAA,OAASV,GAAA,CAAA/B,SAAA,CAAU,UAAU,CAAC;UAAA,EAAC;UAC5DoB,EAAA,CAAAiB,SAAA,aAA+B;UAACjB,EAAA,CAAAa,MAAA,wBAClC;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAY,cAAA,kBAAoD;UAA5BZ,EAAA,CAAAe,UAAA,mBAAAO,qDAAA;YAAA,OAASX,GAAA,CAAA/B,SAAA,CAAU,MAAM,CAAC;UAAA,EAAC;UACjDoB,EAAA,CAAAiB,SAAA,YAA0B;UAACjB,EAAA,CAAAa,MAAA,qBAC7B;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAY,cAAA,kBAAuD;UAA/BZ,EAAA,CAAAe,UAAA,mBAAAQ,qDAAA;YAAA,OAASZ,GAAA,CAAA/B,SAAA,CAAU,SAAS,CAAC;UAAA,EAAC;UACpDoB,EAAA,CAAAiB,SAAA,aAA2B;UAACjB,EAAA,CAAAa,MAAA,oBAC9B;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAY,cAAA,kBAAuD;UAA/BZ,EAAA,CAAAe,UAAA,mBAAAS,qDAAA;YAAA,OAASb,GAAA,CAAA/B,SAAA,CAAU,SAAS,CAAC;UAAA,EAAC;UACpDoB,EAAA,CAAAiB,SAAA,aAA8B;UAACjB,EAAA,CAAAa,MAAA,oBACjC;UAAAb,EAAA,CAAAc,YAAA,EAAS;UAIXd,EAAA,CAAAY,cAAA,eAA8B;UAEtBZ,EAAA,CAAAa,MAAA,0CAA6B;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtCd,EAAA,CAAAY,cAAA,SAAG;UAAAZ,EAAA,CAAAa,MAAA,uDAA0C;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAGnDd,EAAA,CAAAY,cAAA,eAA6B;UACOZ,EAAA,CAAAe,UAAA,mBAAAU,kDAAA;YAAA,OAASd,GAAA,CAAA1B,iBAAA,EAAmB;UAAA,EAAC;UAC7De,EAAA,CAAAiB,SAAA,eAA+C;UAC/CjB,EAAA,CAAAY,cAAA,eAAgC;UAAAZ,EAAA,CAAAa,MAAA,yBAAO;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC7Cd,EAAA,CAAAY,cAAA,eAAqB;UAAAZ,EAAA,CAAAa,MAAA,sBAAc;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAG3Cd,EAAA,CAAAY,cAAA,eAA0B;UACCZ,EAAA,CAAAe,UAAA,mBAAAW,kDAAA;YAAA,OAASf,GAAA,CAAAzB,kBAAA,EAAoB;UAAA,EAAC;UACrDc,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,sBAAc;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC9Cd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,uBAAe;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGjDd,EAAA,CAAAY,cAAA,eAAsD;UAA7BZ,EAAA,CAAAe,UAAA,mBAAAY,kDAAA;YAAA,OAAShB,GAAA,CAAAxB,gBAAA,EAAkB;UAAA,EAAC;UACnDa,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,uBAAe;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC/Cd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,cAAM;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGxCd,EAAA,CAAAY,cAAA,eAAyD;UAAhCZ,EAAA,CAAAe,UAAA,mBAAAa,kDAAA;YAAA,OAASjB,GAAA,CAAAvB,mBAAA,EAAqB;UAAA,EAAC;UACtDY,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,sCAAyB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACzDd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,UAAE;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGpCd,EAAA,CAAAY,cAAA,eAA2D;UAAlCZ,EAAA,CAAAe,UAAA,mBAAAc,kDAAA;YAAA,OAASlB,GAAA,CAAAtB,qBAAA,EAAuB;UAAA,EAAC;UACxDW,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,2BAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACnDd,EAAA,CAAAY,cAAA,eAAwB;UACtBZ,EAAA,CAAAiB,SAAA,eAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAA0B;UAAAZ,EAAA,CAAAa,MAAA,aAAK;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAGvCd,EAAA,CAAAY,cAAA,eAAmE;UAApCZ,EAAA,CAAAe,UAAA,mBAAAe,kDAAA;YAAA,OAASnB,GAAA,CAAArB,uBAAA,EAAyB;UAAA,EAAC;UAChEU,EAAA,CAAAY,cAAA,eAAgC;UAAAZ,EAAA,CAAAa,MAAA,uCAAqB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAC3Dd,EAAA,CAAAY,cAAA,eAA+B;UAAAZ,EAAA,CAAAa,MAAA,gCAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAM;UACxDd,EAAA,CAAAY,cAAA,eAAyC;UACvCZ,EAAA,CAAAiB,SAAA,aAAmC;UAACjB,EAAA,CAAAa,MAAA,qBACtC;UAAAb,EAAA,CAAAc,YAAA,EAAM;UAOdd,EAAA,CAAAY,cAAA,eAA6B;UAInBZ,EAAA,CAAAa,MAAA,mCAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACzBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,wDAAqC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAG9Cd,EAAA,CAAAY,cAAA,gBAA2B;UACEZ,EAAA,CAAAe,UAAA,mBAAAgB,mDAAA;YAAA,OAASpB,GAAA,CAAApB,eAAA,EAAiB;UAAA,EAAC;UACpDS,EAAA,CAAAY,cAAA,gBAAgC;UAC9BZ,EAAA,CAAAiB,SAAA,aAA0B;UAC5BjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,uCAAoB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC7Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,iCAAmB;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI9Bd,EAAA,CAAAY,cAAA,gBAAgE;UAArCZ,EAAA,CAAAe,UAAA,mBAAAiB,mDAAA;YAAA,OAASrB,GAAA,CAAAnB,wBAAA,EAA0B;UAAA,EAAC;UAC7DQ,EAAA,CAAAY,cAAA,gBAAmC;UACjCZ,EAAA,CAAAiB,SAAA,cAAgC;UAClCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,+BAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC1Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,0BAAY;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAIvBd,EAAA,CAAAY,cAAA,gBAAyD;UAA9BZ,EAAA,CAAAe,UAAA,mBAAAkB,mDAAA;YAAA,OAAStB,GAAA,CAAAlB,iBAAA,EAAmB;UAAA,EAAC;UACtDO,EAAA,CAAAY,cAAA,gBAAkC;UAChCZ,EAAA,CAAAiB,SAAA,cAA+B;UACjCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,sCAAc;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACvBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,oCAAsB;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAOrCd,EAAA,CAAAY,cAAA,gBAA8B;UAEtBZ,EAAA,CAAAa,MAAA,qCAAkB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC3Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,6DAAqC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAG9Cd,EAAA,CAAAY,cAAA,gBAA2B;UACEZ,EAAA,CAAAe,UAAA,mBAAAmB,mDAAA;YAAA,OAASvB,GAAA,CAAAjB,yBAAA,EAA2B;UAAA,EAAC;UAC9DM,EAAA,CAAAY,cAAA,gBAAgC;UAC9BZ,EAAA,CAAAiB,SAAA,cAA+B;UACjCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,oCAAsB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Bd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,8DAAsC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAIjDd,EAAA,CAAAY,cAAA,gBAAgE;UAArCZ,EAAA,CAAAe,UAAA,mBAAAoB,mDAAA;YAAA,OAASxB,GAAA,CAAAhB,wBAAA,EAA0B;UAAA,EAAC;UAC7DK,EAAA,CAAAY,cAAA,gBAAqC;UACnCZ,EAAA,CAAAiB,SAAA,cAAkC;UACpCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,0CAA4B;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACrCd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,gEAAmC;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI9Cd,EAAA,CAAAY,cAAA,gBAAiE;UAAtCZ,EAAA,CAAAe,UAAA,mBAAAqB,mDAAA;YAAA,OAASzB,GAAA,CAAAf,yBAAA,EAA2B;UAAA,EAAC;UAC9DI,EAAA,CAAAY,cAAA,gBAAsC;UACpCZ,EAAA,CAAAiB,SAAA,aAAiC;UACnCjB,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAA8B;UACxBZ,EAAA,CAAAa,MAAA,6BAAe;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACxBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,yEAAsD;UAAAb,EAAA,CAAAc,YAAA,EAAI;;;UAhM/Dd,EAAA,CAAAqC,SAAA,GAAyB;UAAzBrC,EAAA,CAAAsC,kBAAA,gBAAA3B,GAAA,CAAAjD,QAAA,KAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}