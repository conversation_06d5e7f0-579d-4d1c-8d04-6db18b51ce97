{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'accueil',\n  component: AccueilComponent\n}, {\n  path: 'suivantacc',\n  component: SuivantaccComponent\n}, {\n  path: 'typeiris',\n  component: TypeirisComponent\n}, {\n  path: '',\n  redirectTo: '/accueil',\n  pathMatch: 'full'\n} // Rediriger vers la page d'accueil par défaut\n];\n\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AccueilComponent", "SuivantaccComponent", "TypeirisComponent", "routes", "path", "component", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\n\nconst routes: Routes = [\n  { path: 'accueil', component: AccueilComponent },  // Page d'accueil\n  { path: 'suivantacc', component: SuivantaccComponent },  // Page après avoir cliqué sur \"Commencer\"\n  { path: 'typeiris', component: TypeirisComponent },  // Page après avoir cliqué sur \"Suivant\"\n  { path: '', redirectTo: '/accueil', pathMatch: 'full' },  // Rediriger vers la page d'accueil par défaut\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;;;AAEjE,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEL;AAAgB,CAAE,EAChD;EAAEI,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAEJ;AAAmB,CAAE,EACtD;EAAEG,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEH;AAAiB,CAAE,EAClD;EAAEE,IAAI,EAAE,EAAE;EAAEE,UAAU,EAAE,UAAU;EAAEC,SAAS,EAAE;AAAM,CAAE,CAAG;AAAA,CAC3D;;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBT,YAAY,CAACU,OAAO,CAACN,MAAM,CAAC,EAC5BJ,YAAY;IAAA;EAAA;;;2EAEXS,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAZ,YAAA;IAAAa,OAAA,GAFjBb,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}