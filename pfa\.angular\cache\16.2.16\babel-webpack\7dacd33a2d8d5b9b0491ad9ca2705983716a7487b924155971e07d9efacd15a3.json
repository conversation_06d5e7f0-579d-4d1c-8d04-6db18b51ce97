{"ast": null, "code": "export const config = {\n  onUnhandledError: null,\n  onStoppedNotification: null,\n  Promise: undefined,\n  useDeprecatedSynchronousErrorHandling: false,\n  useDeprecatedNextContext: false\n};", "map": {"version": 3, "names": ["config", "onUnhandledError", "onStoppedNotification", "Promise", "undefined", "useDeprecatedSynchronousErrorHandling", "useDeprecatedNextContext"], "sources": ["D:/ProjetPfa/pfa/pfa/node_modules/rxjs/dist/esm/internal/config.js"], "sourcesContent": ["export const config = {\n    onUnhandledError: null,\n    onStoppedNotification: null,\n    Promise: undefined,\n    useDeprecatedSynchronousErrorHandling: false,\n    useDeprecatedNextContext: false,\n};\n"], "mappings": "AAAA,OAAO,MAAMA,MAAM,GAAG;EAClBC,gBAAgB,EAAE,IAAI;EACtBC,qBAAqB,EAAE,IAAI;EAC3BC,OAAO,EAAEC,SAAS;EAClBC,qCAAqC,EAAE,KAAK;EAC5CC,wBAAwB,EAAE;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}