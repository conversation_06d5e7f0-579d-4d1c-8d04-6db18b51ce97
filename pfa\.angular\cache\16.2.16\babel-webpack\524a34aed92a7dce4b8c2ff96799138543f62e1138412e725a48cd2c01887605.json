{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\nimport { AdminComponent } from './admin/admin.component';\n// Guards\nimport { AuthGuard, AdminGuard, GuestGuard } from './guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'accueil',\n  component: AccueilComponent\n}, {\n  path: 'suivantacc',\n  component: SuivantaccComponent\n}, {\n  path: 'typeiris',\n  component: TypeirisComponent\n}, {\n  path: 'iris2',\n  component: Iris2Component\n}, {\n  path: 'iris-diversity',\n  component: IrisDiversityComponent\n}, {\n  path: 'fleur',\n  component: FleurComponent\n}, {\n  path: 'bijou',\n  component: BijouComponent\n}, {\n  path: 'flux',\n  component: FluxComponent\n}, {\n  path: 'shaker',\n  component: ShakerComponent\n},\n// Routes d'authentification (accessibles uniquement aux non-connectés)\n{\n  path: 'login',\n  component: LoginComponent,\n  canActivate: [GuestGuard]\n}, {\n  path: 'signup',\n  component: SignupComponent,\n  canActivate: [GuestGuard]\n},\n// Routes protégées (nécessitent une authentification)\n{\n  path: 'dashboard',\n  component: DashboardComponent,\n  canActivate: [AuthGuard, AdminGuard]\n}, {\n  path: 'personality-test',\n  component: PersonalityTestComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'admin',\n  component: AdminComponent,\n  canActivate: [AuthGuard, AdminGuard]\n}, {\n  path: '',\n  redirectTo: '/accueil',\n  pathMatch: 'full'\n} // Rediriger vers la page d'accueil par défaut\n];\n\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AccueilComponent", "SuivantaccComponent", "TypeirisComponent", "Iris2Component", "FleurComponent", "BijouComponent", "FluxComponent", "ShakerComponent", "LoginComponent", "SignupComponent", "IrisDiversityComponent", "DashboardComponent", "PersonalityTestComponent", "AdminComponent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "routes", "path", "component", "canActivate", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["D:\\ProjetPfa\\pfa\\pfa\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport { Iris2Component } from './iris2/iris2.component';\nimport { FleurComponent } from './fleur/fleur.component';\nimport { BijouComponent } from './bijou/bijou.component';\nimport { FluxComponent } from './flux/flux.component';\nimport { ShakerComponent } from './shaker/shaker.component';\nimport { LoginComponent } from './login/login.component';\nimport { SignupComponent } from './signup/signup.component';\nimport { IrisDiversityComponent } from './iris-diversity/iris-diversity.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { PersonalityTestComponent } from './personality-test/personality-test.component';\nimport { AdminComponent } from './admin/admin.component';\n\n// Guards\nimport { AuthGuard, AdminGuard, GuestGuard } from './guards/auth.guard';\n\nconst routes: Routes = [\n  { path: 'accueil', component: AccueilComponent },  // Page d'accueil\n  { path: 'suivantacc', component: SuivantaccComponent },  // Page après avoir cliqué sur \"Commencer\"\n  { path: 'typeiris', component: TypeirisComponent },  // Page après avoir cliqué sur \"Suivant\"\n  { path: 'iris2' , component: Iris2Component},\n  { path: 'iris-diversity', component: IrisDiversityComponent },  // Page de diversité des iris\n  { path: 'fleur', component: FleurComponent },\n  { path: 'bijou', component: BijouComponent},\n  { path: 'flux', component: FluxComponent },\n  { path: 'shaker', component: ShakerComponent },\n\n  // Routes d'authentification (accessibles uniquement aux non-connectés)\n  { path: 'login', component: LoginComponent, canActivate: [GuestGuard] },\n  { path: 'signup', component: SignupComponent, canActivate: [GuestGuard] },\n\n  // Routes protégées (nécessitent une authentification)\n  { path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard, AdminGuard] },\n  { path: 'personality-test', component: PersonalityTestComponent, canActivate: [AuthGuard] },\n  { path: 'admin', component: AdminComponent, canActivate: [AuthGuard, AdminGuard] },\n\n  { path: '', redirectTo: '/accueil', pathMatch: 'full' },  // Rediriger vers la page d'accueil par défaut\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,cAAc,QAAQ,yBAAyB;AAExD;AACA,SAASC,SAAS,EAAEC,UAAU,EAAEC,UAAU,QAAQ,qBAAqB;;;AAEvE,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEnB;AAAgB,CAAE,EAChD;EAAEkB,IAAI,EAAE,YAAY;EAAEC,SAAS,EAAElB;AAAmB,CAAE,EACtD;EAAEiB,IAAI,EAAE,UAAU;EAAEC,SAAS,EAAEjB;AAAiB,CAAE,EAClD;EAAEgB,IAAI,EAAE,OAAO;EAAGC,SAAS,EAAEhB;AAAc,CAAC,EAC5C;EAAEe,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAAET;AAAsB,CAAE,EAC7D;EAAEQ,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEf;AAAc,CAAE,EAC5C;EAAEc,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEd;AAAc,CAAC,EAC3C;EAAEa,IAAI,EAAE,MAAM;EAAEC,SAAS,EAAEb;AAAa,CAAE,EAC1C;EAAEY,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEZ;AAAe,CAAE;AAE9C;AACA;EAAEW,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEX,cAAc;EAAEY,WAAW,EAAE,CAACJ,UAAU;AAAC,CAAE,EACvE;EAAEE,IAAI,EAAE,QAAQ;EAAEC,SAAS,EAAEV,eAAe;EAAEW,WAAW,EAAE,CAACJ,UAAU;AAAC,CAAE;AAEzE;AACA;EAAEE,IAAI,EAAE,WAAW;EAAEC,SAAS,EAAER,kBAAkB;EAAES,WAAW,EAAE,CAACN,SAAS,EAAEC,UAAU;AAAC,CAAE,EAC1F;EAAEG,IAAI,EAAE,kBAAkB;EAAEC,SAAS,EAAEP,wBAAwB;EAAEQ,WAAW,EAAE,CAACN,SAAS;AAAC,CAAE,EAC3F;EAAEI,IAAI,EAAE,OAAO;EAAEC,SAAS,EAAEN,cAAc;EAAEO,WAAW,EAAE,CAACN,SAAS,EAAEC,UAAU;AAAC,CAAE,EAElF;EAAEG,IAAI,EAAE,EAAE;EAAEG,UAAU,EAAE,UAAU;EAAEC,SAAS,EAAE;AAAM,CAAE,CAAG;AAAA,CAC3D;;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBxB,YAAY,CAACyB,OAAO,CAACP,MAAM,CAAC,EAC5BlB,YAAY;IAAA;EAAA;;;2EAEXwB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAA3B,YAAA;IAAA4B,OAAA,GAFjB5B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}