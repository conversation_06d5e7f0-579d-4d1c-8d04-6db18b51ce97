{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FluxComponent {\n  static {\n    this.ɵfac = function FluxComponent_Factory(t) {\n      return new (t || FluxComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FluxComponent,\n      selectors: [[\"app-flux\"]],\n      decls: 26,\n      vars: 0,\n      consts: [[1, \"iris-details\"], [\"src\", \"assets/3.png\", \"alt\", \"Flux\", 1, \"iris-img\"], [1, \"iris-description\"]],\n      template: function FluxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4, \"Flux\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"ul\")(6, \"li\");\n          i0.ɵɵtext(7, \"Le Sensitif\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"li\");\n          i0.ɵɵtext(9, \"Type intuitif, physique et empathique par nature\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"li\");\n          i0.ɵɵtext(11, \"Int\\u00E8gre la vie via l\\u2019exp\\u00E9rience sensorielle et corporelle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"li\");\n          i0.ɵɵtext(13, \"Apprentissage kinesth\\u00E9sique : bouger, pratiquer, ressentir\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"li\");\n          i0.ɵɵtext(15, \"Calme, pos\\u00E9, attentionn\\u00E9, \\u00E9quilibre les autres\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"li\");\n          i0.ɵɵtext(17, \"Adapt\\u00E9 aux soins, sport, et services humains\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"li\");\n          i0.ɵɵtext(19, \"Communication physique : posture, gestes, toucher contr\\u00F4l\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"li\");\n          i0.ɵɵtext(21, \"Apporte stabilit\\u00E9, empathie et soutien\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"li\");\n          i0.ɵɵtext(23, \"Peut se sentir impuissant ou d\\u00E9bord\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"li\");\n          i0.ɵɵtext(25, \"Le\\u00E7on de vie : faire confiance, l\\u00E2cher prise et trouver sa mission\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FluxComponent", "selectors", "decls", "vars", "consts", "template", "FluxComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\flux\\flux.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\flux\\flux.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-flux',\n  templateUrl: './flux.component.html',\n  styleUrls: ['./flux.component.scss']\n})\nexport class FluxComponent {\n\n}\n", "<div class=\"iris-details\">\n    <img src=\"assets/3.png\" alt=\"Flux\" class=\"iris-img\" />\n    <div class=\"iris-description\">\n      <h2>Flux</h2>\n      <ul>\n        <li>Le Sensitif</li>\n        <li>Type intuitif, physique et empathique par nature</li>\n        <li>Intègre la vie via l’expérience sensorielle et corporelle</li>\n        <li>Apprentissage kinesthésique : bouger, pratiquer, ressentir</li>\n        <li><PERSON><PERSON>, posé, attentionn<PERSON>, équilibre les autres</li>\n        <li>Adapté aux soins, sport, et services humains</li>\n        <li>Communication physique : posture, gestes, toucher contrôlé</li>\n        <li>Apporte stabilité, empathie et soutien</li>\n        <li>Peut se sentir impuissant ou débordé</li>\n        <li>Leçon de vie : faire confiance, lâcher prise et trouver sa mission</li>\n      </ul>\n    </div>\n  </div>"], "mappings": ";AAOA,OAAM,MAAOA,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BE,EAAA,CAAAC,cAAA,aAA0B;UACtBD,EAAA,CAAAE,SAAA,aAAsD;UACtDF,EAAA,CAAAC,cAAA,aAA8B;UACxBD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACbJ,EAAA,CAAAC,cAAA,SAAI;UACED,EAAA,CAAAG,MAAA,kBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpBJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,uDAAgD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,gFAAyD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,uEAA0D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,qEAA8C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,yDAA4C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACrDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,4EAA0D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,mDAAsC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/CJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,sDAAoC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC7CJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,oFAAkE;UAAAH,EAAA,CAAAI,YAAA,EAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}