{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nfunction AppComponent_pre_115_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"pre\");\n    i0.ɵɵtext(1, \"ng generate component xyz\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_pre_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"pre\");\n    i0.ɵɵtext(1, \"ng add @angular/material\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_pre_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"pre\");\n    i0.ɵɵtext(1, \"ng add @angular/pwa\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_pre_118_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"pre\");\n    i0.ɵɵtext(1, \"ng add _____\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_pre_119_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"pre\");\n    i0.ɵɵtext(1, \"ng test\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_pre_120_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"pre\");\n    i0.ɵɵtext(1, \"ng build\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class AppComponent {\n  constructor() {\n    this.title = 'pfa';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 151,\n      vars: 7,\n      consts: [[\"role\", \"banner\", 1, \"toolbar\"], [\"width\", \"40\", \"alt\", \"Angular Logo\", \"src\", \"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTAgMjUwIj4KICAgIDxwYXRoIGZpbGw9IiNERDAwMzEiIGQ9Ik0xMjUgMzBMMzEuOSA2My4ybDE0LjIgMTIzLjFMMTI1IDIzMGw3OC45LTQzLjcgMTQuMi0xMjMuMXoiIC8+CiAgICA8cGF0aCBmaWxsPSIjQzMwMDJGIiBkPSJNMTI1IDMwdjIyLjItLjFWMjMwbDc4LjktNDMuNyAxNC4yLTEyMy4xTDEyNSAzMHoiIC8+CiAgICA8cGF0aCAgZmlsbD0iI0ZGRkZGRiIgZD0iTTEyNSA1Mi4xTDY2LjggMTgyLjZoMjEuN2wxMS43LTI5LjJoNDkuNGwxMS43IDI5LjJIMTgzTDEyNSA1Mi4xem0xNyA4My4zaC0zNGwxNy00MC45IDE3IDQwLjl6IiAvPgogIDwvc3ZnPg==\"], [1, \"spacer\"], [\"aria-label\", \"Angular on X\", \"target\", \"_blank\", \"rel\", \"noopener\", \"href\", \"https://twitter.com/angular\", \"title\", \"X\"], [\"id\", \"twitter-logo\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"height\", \"24\", \"width\", \"24\", \"data-name\", \"Logo\", \"viewBox\", \"0 0 512 512\", \"fill\", \"#fff\"], [\"d\", \"M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z\"], [\"aria-label\", \"Angular on YouTube\", \"target\", \"_blank\", \"rel\", \"noopener\", \"href\", \"https://youtube.com/angular\", \"title\", \"YouTube\"], [\"id\", \"youtube-logo\", \"height\", \"24\", \"width\", \"24\", \"data-name\", \"Logo\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 0 24 24\", \"fill\", \"#fff\"], [\"d\", \"M0 0h24v24H0V0z\", \"fill\", \"none\"], [\"d\", \"M21.58 7.19c-.23-.86-.91-1.54-1.77-1.77C18.25 5 12 5 12 5s-6.25 0-7.81.42c-.86.23-1.54.91-1.77 1.77C2 8.75 2 12 2 12s0 3.25.42 4.81c.23.86.91 1.54 1.77 1.77C5.75 19 12 19 12 19s6.25 0 7.81-.42c.86-.23 1.54-.91 1.77-1.77C22 15.25 22 12 22 12s0-3.25-.42-4.81zM10 15V9l5.2 3-5.2 3z\"], [\"role\", \"main\", 1, \"content\"], [1, \"card\", \"highlight-card\", \"card-small\"], [\"id\", \"rocket\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"101.678\", \"height\", \"101.678\", \"viewBox\", \"0 0 101.678 101.678\"], [\"id\", \"Group_83\", \"data-name\", \"Group 83\", \"transform\", \"translate(-141 -696)\"], [\"id\", \"Ellipse_8\", \"data-name\", \"Ellipse 8\", \"cx\", \"50.839\", \"cy\", \"50.839\", \"r\", \"50.839\", \"transform\", \"translate(141 696)\", \"fill\", \"#dd0031\"], [\"id\", \"Group_47\", \"data-name\", \"Group 47\", \"transform\", \"translate(165.185 720.185)\"], [\"id\", \"Path_33\", \"data-name\", \"Path 33\", \"d\", \"M3.4,42.615a3.084,3.084,0,0,0,3.553,3.553,21.419,21.419,0,0,0,12.215-6.107L9.511,30.4A21.419,21.419,0,0,0,3.4,42.615Z\", \"transform\", \"translate(0.371 3.363)\", \"fill\", \"#fff\"], [\"id\", \"Path_34\", \"data-name\", \"Path 34\", \"d\", \"M53.3,3.221A3.09,3.09,0,0,0,50.081,0,48.227,48.227,0,0,0,18.322,13.437c-6-1.666-14.991-1.221-18.322,7.218A33.892,33.892,0,0,1,9.439,25.1l-.333.666a3.013,3.013,0,0,0,.555,3.553L23.985,43.641a2.9,2.9,0,0,0,3.553.555l.666-.333A33.892,33.892,0,0,1,32.647,53.3c8.55-3.664,8.884-12.326,7.218-18.322A48.227,48.227,0,0,0,53.3,3.221ZM34.424,9.772a6.439,6.439,0,1,1,9.106,9.106,6.368,6.368,0,0,1-9.106,0A6.467,6.467,0,0,1,34.424,9.772Z\", \"transform\", \"translate(0 0.005)\", \"fill\", \"#fff\"], [\"id\", \"rocket-smoke\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"516.119\", \"height\", \"1083.632\", \"viewBox\", \"0 0 516.119 1083.632\"], [\"id\", \"Path_40\", \"data-name\", \"Path 40\", \"d\", \"M644.6,141S143.02,215.537,147.049,870.207s342.774,201.755,342.774,201.755S404.659,847.213,388.815,762.2c-27.116-145.51-11.551-384.124,271.9-609.1C671.15,139.365,644.6,141,644.6,141Z\", \"transform\", \"translate(-147.025 -140.939)\", \"fill\", \"#f5f5f5\"], [1, \"card-container\"], [\"target\", \"_blank\", \"rel\", \"noopener\", \"href\", \"https://angular.io/tutorial\", 1, \"card\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", 1, \"material-icons\"], [\"d\", \"M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"], [\"target\", \"_blank\", \"rel\", \"noopener\", \"href\", \"https://angular.io/cli\", 1, \"card\"], [\"d\", \"M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z\"], [\"target\", \"_blank\", \"rel\", \"noopener\", \"href\", \"https://material.angular.io\", 1, \"card\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"21.813\", \"height\", \"23.453\", \"viewBox\", \"0 0 179.2 192.7\", 2, \"margin-right\", \"8px\"], [\"fill\", \"#ffa726\", \"d\", \"M89.4 0 0 32l13.5 118.4 75.9 42.3 76-42.3L179.2 32 89.4 0z\"], [\"fill\", \"#fb8c00\", \"d\", \"M89.4 0v192.7l76-42.3L179.2 32 89.4 0z\"], [\"fill\", \"#ffe0b2\", \"d\", \"m102.9 146.3-63.3-30.5 36.3-22.4 63.7 30.6-36.7 22.3z\"], [\"fill\", \"#fff3e0\", \"d\", \"M102.9 122.8 39.6 92.2l36.3-22.3 63.7 30.6-36.7 22.3z\"], [\"fill\", \"#fff\", \"d\", \"M102.9 99.3 39.6 68.7l36.3-22.4 63.7 30.6-36.7 22.4z\"], [\"target\", \"_blank\", \"rel\", \"noopener\", \"href\", \"https://blog.angular.io/\", 1, \"card\"], [\"d\", \"M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z\"], [\"target\", \"_blank\", \"rel\", \"noopener\", \"href\", \"https://angular.io/devtools/\", 1, \"card\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"enable-background\", \"new 0 0 24 24\", \"height\", \"24px\", \"viewBox\", \"0 0 24 24\", \"width\", \"24px\", \"fill\", \"#000000\", 1, \"material-icons\"], [\"fill\", \"none\", \"height\", \"24\", \"width\", \"24\"], [\"d\", \"M14.73,13.31C15.52,12.24,16,10.93,16,9.5C16,5.91,13.09,3,9.5,3S3,5.91,3,9.5C3,13.09,5.91,16,9.5,16 c1.43,0,2.74-0.48,3.81-1.27L19.59,21L21,19.59L14.73,13.31z M9.5,14C7.01,14,5,11.99,5,9.5S7.01,5,9.5,5S14,7.01,14,9.5 S11.99,14,9.5,14z\"], [\"points\", \"10.29,8.44 9.5,6 8.71,8.44 6.25,8.44 8.26,10.03 7.49,12.5 9.5,10.97 11.51,12.5 10.74,10.03 12.75,8.44\"], [\"type\", \"hidden\"], [\"selection\", \"\"], [\"tabindex\", \"0\", 1, \"card\", \"card-small\", 3, \"click\"], [\"d\", \"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"], [1, \"terminal\", 3, \"ngSwitch\"], [4, \"ngSwitchDefault\"], [4, \"ngSwitchCase\"], [\"title\", \"Find a Local Meetup\", \"href\", \"https://www.meetup.com/find/?keywords=angular\", \"target\", \"_blank\", \"rel\", \"noopener\", 1, \"circle-link\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"24.607\", \"height\", \"23.447\", \"viewBox\", \"0 0 24.607 23.447\"], [\"id\", \"logo--mSwarm\", \"d\", \"M21.221,14.95A4.393,4.393,0,0,1,17.6,19.281a4.452,4.452,0,0,1-.8.069c-.09,0-.125.035-.154.117a2.939,2.939,0,0,1-2.506,2.091,2.868,2.868,0,0,1-2.248-.624.168.168,0,0,0-.245-.005,3.926,3.926,0,0,1-2.589.741,4.015,4.015,0,0,1-3.7-3.347,2.7,2.7,0,0,1-.043-.38c0-.106-.042-.146-.143-.166a3.524,3.524,0,0,1-1.516-.69A3.623,3.623,0,0,1,2.23,14.557a3.66,3.66,0,0,1,1.077-3.085.138.138,0,0,0,.026-.2,3.348,3.348,0,0,1-.451-1.821,3.46,3.46,0,0,1,2.749-3.28.44.44,0,0,0,.355-.281,5.072,5.072,0,0,1,3.863-3,5.028,5.028,0,0,1,3.555.666.31.31,0,0,0,.271.03A4.5,4.5,0,0,1,18.3,4.7a4.4,4.4,0,0,1,1.334,2.751,3.658,3.658,0,0,1,.022.706.131.131,0,0,0,.1.157,2.432,2.432,0,0,1,1.574,1.645,2.464,2.464,0,0,1-.7,2.616c-.065.064-.051.1-.014.166A4.321,4.321,0,0,1,21.221,14.95ZM13.4,14.607a2.09,2.09,0,0,0,1.409,1.982,4.7,4.7,0,0,0,1.275.221,1.807,1.807,0,0,0,.9-.151.542.542,0,0,0,.321-.545.558.558,0,0,0-.359-.534,1.2,1.2,0,0,0-.254-.078c-.262-.047-.526-.086-.787-.138a.674.674,0,0,1-.617-.75,3.394,3.394,0,0,1,.218-1.109c.217-.658.509-1.286.79-1.918a15.609,15.609,0,0,0,.745-1.86,1.95,1.95,0,0,0,.06-1.073,1.286,1.286,0,0,0-1.051-1.033,1.977,1.977,0,0,0-1.521.2.339.339,0,0,1-.446-.042c-.1-.092-.2-.189-.307-.284a1.214,1.214,0,0,0-1.643-.061,7.563,7.563,0,0,1-.614.512A.588.588,0,0,1,10.883,8c-.215-.115-.437-.215-.659-.316a2.153,2.153,0,0,0-.695-.248A2.091,2.091,0,0,0,7.541,8.562a9.915,9.915,0,0,0-.405.986c-.559,1.545-1.015,3.123-1.487,4.7a1.528,1.528,0,0,0,.634,1.777,1.755,1.755,0,0,0,1.5.211,1.35,1.35,0,0,0,.824-.858c.543-1.281,1.032-2.584,1.55-3.875.142-.355.28-.712.432-1.064a.548.548,0,0,1,.851-.24.622.622,0,0,1,.185.539,2.161,2.161,0,0,1-.181.621c-.337.852-.68,1.7-1.018,2.552a2.564,2.564,0,0,0-.173.528.624.624,0,0,0,.333.71,1.073,1.073,0,0,0,.814.034,1.22,1.22,0,0,0,.657-.655q.758-1.488,1.511-2.978.35-.687.709-1.37a1.073,1.073,0,0,1,.357-.434.43.43,0,0,1,.463-.016.373.373,0,0,1,.153.387.7.7,0,0,1-.057.236c-.065.157-.127.316-.2.469-.42.883-.846,1.763-1.262,2.648A2.463,2.463,0,0,0,13.4,14.607Zm5.888,6.508a1.09,1.09,0,0,0-2.179.006,1.09,1.09,0,0,0,2.179-.006ZM1.028,12.139a1.038,1.038,0,1,0,.01-2.075,1.038,1.038,0,0,0-.01,2.075ZM13.782.528a1.027,1.027,0,1,0-.011,2.055A1.027,1.027,0,0,0,13.782.528ZM22.21,6.95a.882.882,0,0,0-1.763.011A.882.882,0,0,0,22.21,6.95ZM4.153,4.439a.785.785,0,1,0,.787-.78A.766.766,0,0,0,4.153,4.439Zm8.221,18.22a.676.676,0,1,0-.677.666A.671.671,0,0,0,12.374,22.658ZM22.872,12.2a.674.674,0,0,0-.665.665.656.656,0,0,0,.655.643.634.634,0,0,0,.655-.644A.654.654,0,0,0,22.872,12.2ZM7.171-.123A.546.546,0,0,0,6.613.43a.553.553,0,1,0,1.106,0A.539.539,0,0,0,7.171-.123ZM24.119,9.234a.507.507,0,0,0-.493.488.494.494,0,0,0,.494.494.48.48,0,0,0,.487-.483A.491.491,0,0,0,24.119,9.234Zm-19.454,9.7a.5.5,0,0,0-.488-.488.491.491,0,0,0-.487.5.483.483,0,0,0,.491.479A.49.49,0,0,0,4.665,18.936Z\", \"transform\", \"translate(0 0.123)\", \"fill\", \"#f64060\"], [\"title\", \"Join the Conversation on Discord\", \"href\", \"https://discord.gg/angular\", \"target\", \"_blank\", \"rel\", \"noopener\", 1, \"circle-link\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"26\", \"height\", \"26\", \"viewBox\", \"0 0 245 240\"], [\"d\", \"M104.4 103.9c-5.7 0-10.2 5-10.2 11.1s4.6 11.1 10.2 11.1c5.7 0 10.2-5 10.2-11.1.1-6.1-4.5-11.1-10.2-11.1zM140.9 103.9c-5.7 0-10.2 5-10.2 11.1s4.6 11.1 10.2 11.1c5.7 0 10.2-5 10.2-11.1s-4.5-11.1-10.2-11.1z\"], [\"d\", \"M189.5 20h-134C44.2 20 35 29.2 35 40.6v135.2c0 11.4 9.2 20.6 20.5 20.6h113.4l-5.3-18.5 12.8 11.9 12.1 11.2 21.5 19V40.6c0-11.4-9.2-20.6-20.5-20.6zm-38.6 130.6s-3.6-4.3-6.6-8.1c13.1-3.7 18.1-11.9 18.1-11.9-4.1 2.7-8 4.6-11.5 5.9-5 2.1-9.8 3.5-14.5 4.3-9.6 1.8-18.4 1.3-25.9-.1-5.7-1.1-10.6-2.7-14.7-4.3-2.3-.9-4.8-2-7.3-3.4-.3-.2-.6-.3-.9-.5-.2-.1-.3-.2-.4-.3-1.8-1-2.8-1.7-2.8-1.7s4.8 8 17.5 11.8c-3 3.8-6.7 8.3-6.7 8.3-22.1-.7-30.5-15.2-30.5-15.2 0-32.2 14.4-58.3 14.4-58.3 14.4-10.8 28.1-10.5 28.1-10.5l1 1.2c-18 5.2-26.3 13.1-26.3 13.1s2.2-1.2 5.9-2.9c10.7-4.7 19.2-6 22.7-6.3.6-.1 1.1-.2 1.7-.2 6.1-.8 13-1 20.2-.2 9.5 1.1 19.7 3.9 30.1 9.6 0 0-7.9-7.5-24.9-12.7l1.4-1.6s13.7-.3 28.1 10.5c0 0 14.4 26.1 14.4 58.3 0 0-8.5 14.5-30.6 15.2z\"], [\"href\", \"https://github.com/angular/angular\", \"target\", \"_blank\", \"rel\", \"noopener\"], [1, \"github-star-badge\"], [\"d\", \"M0 0h24v24H0z\", \"fill\", \"none\"], [\"d\", \"M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z\"], [\"d\", \"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\", \"fill\", \"#1976d2\"], [\"id\", \"clouds\", \"xmlns\", \"http://www.w3.org/2000/svg\", \"width\", \"2611.084\", \"height\", \"485.677\", \"viewBox\", \"0 0 2611.084 485.677\"], [\"id\", \"Path_39\", \"data-name\", \"Path 39\", \"d\", \"M2379.709,863.793c10-93-77-171-168-149-52-114-225-105-264,15-75,3-140,59-152,133-30,2.83-66.725,9.829-93.5,26.25-26.771-16.421-63.5-23.42-93.5-26.25-12-74-77-130-152-133-39-120-212-129-264-15-54.084-13.075-106.753,9.173-138.488,48.9-31.734-39.726-84.4-61.974-138.487-48.9-52-114-225-105-264,15a162.027,162.027,0,0,0-103.147,43.044c-30.633-45.365-87.1-72.091-145.206-58.044-52-114-225-105-264,15-75,3-140,59-152,133-53,5-127,23-130,83-2,42,35,72,70,86,49,20,106,18,157,5a165.625,165.625,0,0,0,120,0c47,94,178,113,251,33,61.112,8.015,113.854-5.72,150.492-29.764a165.62,165.62,0,0,0,110.861-3.236c47,94,178,113,251,33,31.385,4.116,60.563,2.495,86.487-3.311,25.924,5.806,55.1,7.427,86.488,3.311,73,80,204,61,251-33a165.625,165.625,0,0,0,120,0c51,13,108,15,157-5a147.188,147.188,0,0,0,33.5-18.694,147.217,147.217,0,0,0,33.5,18.694c49,20,106,18,157,5a165.625,165.625,0,0,0,120,0c47,94,178,113,251,33C2446.709,1093.793,2554.709,922.793,2379.709,863.793Z\", \"transform\", \"translate(142.69 -634.312)\", \"fill\", \"#eee\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r7 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementStart(2, \"span\");\n          i0.ɵɵtext(3, \"Welcome\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"div\", 2);\n          i0.ɵɵelementStart(5, \"a\", 3);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(6, \"svg\", 4);\n          i0.ɵɵelement(7, \"path\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(8, \"a\", 6);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(9, \"svg\", 7);\n          i0.ɵɵelement(10, \"path\", 8)(11, \"path\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(14, \"svg\", 12)(15, \"title\");\n          i0.ɵɵtext(16, \"Rocket Ship\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"g\", 13);\n          i0.ɵɵelement(18, \"circle\", 14);\n          i0.ɵɵelementStart(19, \"g\", 15);\n          i0.ɵɵelement(20, \"path\", 16)(21, \"path\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(22, \"span\");\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(24, \"svg\", 18)(25, \"title\");\n          i0.ɵɵtext(26, \"Rocket Ship Smoke\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"path\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(28, \"h2\");\n          i0.ɵɵtext(29, \"Resources\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"p\");\n          i0.ɵɵtext(31, \"Here are some links to help you get started:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 20)(33, \"a\", 21);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(34, \"svg\", 22);\n          i0.ɵɵelement(35, \"path\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(36, \"span\");\n          i0.ɵɵtext(37, \"Learn Angular\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(38, \"svg\", 22);\n          i0.ɵɵelement(39, \"path\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(40, \"a\", 25);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(41, \"svg\", 22);\n          i0.ɵɵelement(42, \"path\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(43, \"span\");\n          i0.ɵɵtext(44, \"CLI Documentation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(45, \"svg\", 22);\n          i0.ɵɵelement(46, \"path\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(47, \"a\", 27);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(48, \"svg\", 28);\n          i0.ɵɵelement(49, \"path\", 29)(50, \"path\", 30)(51, \"path\", 31)(52, \"path\", 32)(53, \"path\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(54, \"span\");\n          i0.ɵɵtext(55, \"Angular Material\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(56, \"svg\", 22);\n          i0.ɵɵelement(57, \"path\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(58, \"a\", 34);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(59, \"svg\", 22);\n          i0.ɵɵelement(60, \"path\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(61, \"span\");\n          i0.ɵɵtext(62, \"Angular Blog\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(63, \"svg\", 22);\n          i0.ɵɵelement(64, \"path\", 24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(65, \"a\", 36);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(66, \"svg\", 37)(67, \"g\");\n          i0.ɵɵelement(68, \"rect\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"g\")(70, \"g\");\n          i0.ɵɵelement(71, \"path\", 39)(72, \"polygon\", 40);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(73, \"span\");\n          i0.ɵɵtext(74, \"Angular DevTools\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(75, \"svg\", 22);\n          i0.ɵɵelement(76, \"path\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(77, \"h2\");\n          i0.ɵɵtext(78, \"Next Steps\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"p\");\n          i0.ɵɵtext(80, \"What do you want to do next with your app?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(81, \"input\", 41, 42);\n          i0.ɵɵelementStart(83, \"div\", 20)(84, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function AppComponent_Template_button_click_84_listener() {\n            i0.ɵɵrestoreView(_r7);\n            const _r0 = i0.ɵɵreference(82);\n            return i0.ɵɵresetView(_r0.value = \"component\");\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(85, \"svg\", 22);\n          i0.ɵɵelement(86, \"path\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(87, \"span\");\n          i0.ɵɵtext(88, \"New Component\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function AppComponent_Template_button_click_89_listener() {\n            i0.ɵɵrestoreView(_r7);\n            const _r0 = i0.ɵɵreference(82);\n            return i0.ɵɵresetView(_r0.value = \"material\");\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(90, \"svg\", 22);\n          i0.ɵɵelement(91, \"path\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(92, \"span\");\n          i0.ɵɵtext(93, \"Angular Material\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(94, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function AppComponent_Template_button_click_94_listener() {\n            i0.ɵɵrestoreView(_r7);\n            const _r0 = i0.ɵɵreference(82);\n            return i0.ɵɵresetView(_r0.value = \"pwa\");\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(95, \"svg\", 22);\n          i0.ɵɵelement(96, \"path\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(97, \"span\");\n          i0.ɵɵtext(98, \"Add PWA Support\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(99, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function AppComponent_Template_button_click_99_listener() {\n            i0.ɵɵrestoreView(_r7);\n            const _r0 = i0.ɵɵreference(82);\n            return i0.ɵɵresetView(_r0.value = \"dependency\");\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(100, \"svg\", 22);\n          i0.ɵɵelement(101, \"path\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(102, \"span\");\n          i0.ɵɵtext(103, \"Add Dependency\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(104, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function AppComponent_Template_button_click_104_listener() {\n            i0.ɵɵrestoreView(_r7);\n            const _r0 = i0.ɵɵreference(82);\n            return i0.ɵɵresetView(_r0.value = \"test\");\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(105, \"svg\", 22);\n          i0.ɵɵelement(106, \"path\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(107, \"span\");\n          i0.ɵɵtext(108, \"Run and Watch Tests\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(109, \"button\", 43);\n          i0.ɵɵlistener(\"click\", function AppComponent_Template_button_click_109_listener() {\n            i0.ɵɵrestoreView(_r7);\n            const _r0 = i0.ɵɵreference(82);\n            return i0.ɵɵresetView(_r0.value = \"build\");\n          });\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(110, \"svg\", 22);\n          i0.ɵɵelement(111, \"path\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(112, \"span\");\n          i0.ɵɵtext(113, \"Build for Production\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(114, \"div\", 45);\n          i0.ɵɵtemplate(115, AppComponent_pre_115_Template, 2, 0, \"pre\", 46);\n          i0.ɵɵtemplate(116, AppComponent_pre_116_Template, 2, 0, \"pre\", 47);\n          i0.ɵɵtemplate(117, AppComponent_pre_117_Template, 2, 0, \"pre\", 47);\n          i0.ɵɵtemplate(118, AppComponent_pre_118_Template, 2, 0, \"pre\", 47);\n          i0.ɵɵtemplate(119, AppComponent_pre_119_Template, 2, 0, \"pre\", 47);\n          i0.ɵɵtemplate(120, AppComponent_pre_120_Template, 2, 0, \"pre\", 47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"div\", 20)(122, \"a\", 48);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(123, \"svg\", 49)(124, \"title\");\n          i0.ɵɵtext(125, \"Meetup Logo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(126, \"path\", 50);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(127, \"a\", 51);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(128, \"svg\", 52)(129, \"title\");\n          i0.ɵɵtext(130, \"Discord Logo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(131, \"path\", 53)(132, \"path\", 54);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(133, \"footer\");\n          i0.ɵɵtext(134, \" Love Angular?\\u00A0 \");\n          i0.ɵɵelementStart(135, \"a\", 55);\n          i0.ɵɵtext(136, \" Give our repo a star. \");\n          i0.ɵɵelementStart(137, \"div\", 56);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(138, \"svg\", 22);\n          i0.ɵɵelement(139, \"path\", 57)(140, \"path\", 58);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(141, \" Star \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(142, \"a\", 55);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(143, \"svg\", 22);\n          i0.ɵɵelement(144, \"path\", 59)(145, \"path\", 57);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(146, \"svg\", 60)(147, \"title\");\n          i0.ɵɵtext(148, \"Gray Clouds Background\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(149, \"path\", 61);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(150, \"router-outlet\");\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(82);\n          i0.ɵɵadvance(23);\n          i0.ɵɵtextInterpolate1(\"\", ctx.title, \" app is running!\");\n          i0.ɵɵadvance(91);\n          i0.ɵɵproperty(\"ngSwitch\", _r0.value);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngSwitchCase\", \"material\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", \"pwa\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", \"dependency\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", \"test\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngSwitchCase\", \"build\");\n        }\n      },\n      dependencies: [i1.NgSwitch, i1.NgSwitchCase, i1.NgSwitchDefault, i2.RouterOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\", \"[_nghost-%COMP%] {\\n    font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", Roboto, Helvetica, Arial, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\";\\n    font-size: 14px;\\n    color: #333;\\n    box-sizing: border-box;\\n    -webkit-font-smoothing: antialiased;\\n    -moz-osx-font-smoothing: grayscale;\\n  }\\n\\n  h1[_ngcontent-%COMP%], h2[_ngcontent-%COMP%], h3[_ngcontent-%COMP%], h4[_ngcontent-%COMP%], h5[_ngcontent-%COMP%], h6[_ngcontent-%COMP%] {\\n    margin: 8px 0;\\n  }\\n\\n  p[_ngcontent-%COMP%] {\\n    margin: 0;\\n  }\\n\\n  .spacer[_ngcontent-%COMP%] {\\n    flex: 1;\\n  }\\n\\n  .toolbar[_ngcontent-%COMP%] {\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    right: 0;\\n    height: 60px;\\n    display: flex;\\n    align-items: center;\\n    background-color: #1976d2;\\n    color: white;\\n    font-weight: 600;\\n  }\\n\\n  .toolbar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    margin: 0 16px;\\n  }\\n\\n  .toolbar[_ngcontent-%COMP%]   #twitter-logo[_ngcontent-%COMP%] {\\n    height: 40px;\\n    margin: 0 8px;\\n  }\\n\\n  .toolbar[_ngcontent-%COMP%]   #youtube-logo[_ngcontent-%COMP%] {\\n    height: 40px;\\n    margin: 0 16px;\\n  }\\n\\n  .toolbar[_ngcontent-%COMP%]   #twitter-logo[_ngcontent-%COMP%]:hover, .toolbar[_ngcontent-%COMP%]   #youtube-logo[_ngcontent-%COMP%]:hover {\\n    opacity: 0.8;\\n  }\\n\\n  .content[_ngcontent-%COMP%] {\\n    display: flex;\\n    margin: 82px auto 32px;\\n    padding: 0 16px;\\n    max-width: 960px;\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n\\n  svg.material-icons[_ngcontent-%COMP%] {\\n    height: 24px;\\n    width: auto;\\n  }\\n\\n  svg.material-icons[_ngcontent-%COMP%]:not(:last-child) {\\n    margin-right: 8px;\\n  }\\n\\n  .card[_ngcontent-%COMP%]   svg.material-icons[_ngcontent-%COMP%]   path[_ngcontent-%COMP%] {\\n    fill: #888;\\n  }\\n\\n  .card-container[_ngcontent-%COMP%] {\\n    display: flex;\\n    flex-wrap: wrap;\\n    justify-content: center;\\n    margin-top: 16px;\\n  }\\n\\n  .card[_ngcontent-%COMP%] {\\n    all: unset;\\n    border-radius: 4px;\\n    border: 1px solid #eee;\\n    background-color: #fafafa;\\n    height: 40px;\\n    width: 200px;\\n    margin: 0 8px 16px;\\n    padding: 16px;\\n    display: flex;\\n    flex-direction: row;\\n    justify-content: center;\\n    align-items: center;\\n    transition: all 0.2s ease-in-out;\\n    line-height: 24px;\\n  }\\n\\n  .card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:not(:last-child) {\\n    margin-right: 0;\\n  }\\n\\n  .card.card-small[_ngcontent-%COMP%] {\\n    height: 16px;\\n    width: 168px;\\n  }\\n\\n  .card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:not(.highlight-card) {\\n    cursor: pointer;\\n  }\\n\\n  .card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:not(.highlight-card):hover {\\n    transform: translateY(-3px);\\n    box-shadow: 0 4px 17px rgba(0, 0, 0, 0.35);\\n  }\\n\\n  .card-container[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:not(.highlight-card):hover   .material-icons[_ngcontent-%COMP%]   path[_ngcontent-%COMP%] {\\n    fill: rgb(105, 103, 103);\\n  }\\n\\n  .card.highlight-card[_ngcontent-%COMP%] {\\n    background-color: #1976d2;\\n    color: white;\\n    font-weight: 600;\\n    border: none;\\n    width: auto;\\n    min-width: 30%;\\n    position: relative;\\n  }\\n\\n  .card.card.highlight-card[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    margin-left: 60px;\\n  }\\n\\n  svg#rocket[_ngcontent-%COMP%] {\\n    width: 80px;\\n    position: absolute;\\n    left: -10px;\\n    top: -24px;\\n  }\\n\\n  svg#rocket-smoke[_ngcontent-%COMP%] {\\n    height: calc(100vh - 95px);\\n    position: absolute;\\n    top: 10px;\\n    right: 180px;\\n    z-index: -10;\\n  }\\n\\n  a[_ngcontent-%COMP%], a[_ngcontent-%COMP%]:visited, a[_ngcontent-%COMP%]:hover {\\n    color: #1976d2;\\n    text-decoration: none;\\n  }\\n\\n  a[_ngcontent-%COMP%]:hover {\\n    color: #125699;\\n  }\\n\\n  .terminal[_ngcontent-%COMP%] {\\n    position: relative;\\n    width: 80%;\\n    max-width: 600px;\\n    border-radius: 6px;\\n    padding-top: 45px;\\n    margin-top: 8px;\\n    overflow: hidden;\\n    background-color: rgb(15, 15, 16);\\n  }\\n\\n  .terminal[_ngcontent-%COMP%]::before {\\n    content: \\\"\\\\2022 \\\\2022 \\\\2022\\\";\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    height: 4px;\\n    background: rgb(58, 58, 58);\\n    color: #c2c3c4;\\n    width: 100%;\\n    font-size: 2rem;\\n    line-height: 0;\\n    padding: 14px 0;\\n    text-indent: 4px;\\n  }\\n\\n  .terminal[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n    font-family: SFMono-Regular,Consolas,Liberation Mono,Menlo,monospace;\\n    color: white;\\n    padding: 0 1rem 1rem;\\n    margin: 0;\\n  }\\n\\n  .circle-link[_ngcontent-%COMP%] {\\n    height: 40px;\\n    width: 40px;\\n    border-radius: 40px;\\n    margin: 8px;\\n    background-color: white;\\n    border: 1px solid #eeeeee;\\n    display: flex;\\n    justify-content: center;\\n    align-items: center;\\n    cursor: pointer;\\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\\n    transition: 1s ease-out;\\n  }\\n\\n  .circle-link[_ngcontent-%COMP%]:hover {\\n    transform: translateY(-0.25rem);\\n    box-shadow: 0px 3px 15px rgba(0, 0, 0, 0.2);\\n  }\\n\\n  footer[_ngcontent-%COMP%] {\\n    margin-top: 8px;\\n    display: flex;\\n    align-items: center;\\n    line-height: 20px;\\n  }\\n\\n  footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n  }\\n\\n  .github-star-badge[_ngcontent-%COMP%] {\\n    color: #24292e;\\n    display: flex;\\n    align-items: center;\\n    font-size: 12px;\\n    padding: 3px 10px;\\n    border: 1px solid rgba(27,31,35,.2);\\n    border-radius: 3px;\\n    background-image: linear-gradient(-180deg,#fafbfc,#eff3f6 90%);\\n    margin-left: 4px;\\n    font-weight: 600;\\n  }\\n\\n  .github-star-badge[_ngcontent-%COMP%]:hover {\\n    background-image: linear-gradient(-180deg,#f0f3f6,#e6ebf1 90%);\\n    border-color: rgba(27,31,35,.35);\\n    background-position: -.5em;\\n  }\\n\\n  .github-star-badge[_ngcontent-%COMP%]   .material-icons[_ngcontent-%COMP%] {\\n    height: 16px;\\n    width: 16px;\\n    margin-right: 4px;\\n  }\\n\\n  svg#clouds[_ngcontent-%COMP%] {\\n    position: fixed;\\n    bottom: -160px;\\n    left: -230px;\\n    z-index: -10;\\n    width: 1920px;\\n  }\\n\\n  \\n\\n  @media screen and (max-width: 767px) {\\n    .card-container[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:not(.circle-link), .terminal[_ngcontent-%COMP%] {\\n      width: 100%;\\n    }\\n\\n    .card[_ngcontent-%COMP%]:not(.highlight-card) {\\n      height: 16px;\\n      margin: 8px 0;\\n    }\\n\\n    .card.highlight-card[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n      margin-left: 72px;\\n    }\\n\\n    svg#rocket-smoke[_ngcontent-%COMP%] {\\n      right: 120px;\\n      transform: rotate(-5deg);\\n    }\\n  }\\n\\n  @media screen and (max-width: 575px) {\\n    svg#rocket-smoke[_ngcontent-%COMP%] {\\n      display: none;\\n      visibility: hidden;\\n    }\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵlistener", "AppComponent_Template_button_click_84_listener", "ɵɵrestoreView", "_r7", "_r0", "ɵɵreference", "ɵɵresetView", "value", "AppComponent_Template_button_click_89_listener", "AppComponent_Template_button_click_94_listener", "AppComponent_Template_button_click_99_listener", "AppComponent_Template_button_click_104_listener", "AppComponent_Template_button_click_109_listener", "ɵɵtemplate", "AppComponent_pre_115_Template", "AppComponent_pre_116_Template", "AppComponent_pre_117_Template", "AppComponent_pre_118_Template", "AppComponent_pre_119_Template", "AppComponent_pre_120_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "ɵɵproperty"], "sources": ["C:\\pfa\\src\\app\\app.component.ts", "C:\\pfa\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.scss']\n})\nexport class AppComponent {\n  title = 'pfa';\n}\n", "<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->\n<!-- * * * * * * * * * * * The content below * * * * * * * * * * * -->\n<!-- * * * * * * * * * * is only a placeholder * * * * * * * * * * -->\n<!-- * * * * * * * * * * and can be replaced. * * * * * * * * * * * -->\n<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->\n<!-- * * * * * * * * * Delete the template below * * * * * * * * * * -->\n<!-- * * * * * * * to get started with your project! * * * * * * * * -->\n<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->\n\n<style>\n  :host {\n    font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\";\n    font-size: 14px;\n    color: #333;\n    box-sizing: border-box;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6 {\n    margin: 8px 0;\n  }\n\n  p {\n    margin: 0;\n  }\n\n  .spacer {\n    flex: 1;\n  }\n\n  .toolbar {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 60px;\n    display: flex;\n    align-items: center;\n    background-color: #1976d2;\n    color: white;\n    font-weight: 600;\n  }\n\n  .toolbar img {\n    margin: 0 16px;\n  }\n\n  .toolbar #twitter-logo {\n    height: 40px;\n    margin: 0 8px;\n  }\n\n  .toolbar #youtube-logo {\n    height: 40px;\n    margin: 0 16px;\n  }\n\n  .toolbar #twitter-logo:hover,\n  .toolbar #youtube-logo:hover {\n    opacity: 0.8;\n  }\n\n  .content {\n    display: flex;\n    margin: 82px auto 32px;\n    padding: 0 16px;\n    max-width: 960px;\n    flex-direction: column;\n    align-items: center;\n  }\n\n  svg.material-icons {\n    height: 24px;\n    width: auto;\n  }\n\n  svg.material-icons:not(:last-child) {\n    margin-right: 8px;\n  }\n\n  .card svg.material-icons path {\n    fill: #888;\n  }\n\n  .card-container {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: center;\n    margin-top: 16px;\n  }\n\n  .card {\n    all: unset;\n    border-radius: 4px;\n    border: 1px solid #eee;\n    background-color: #fafafa;\n    height: 40px;\n    width: 200px;\n    margin: 0 8px 16px;\n    padding: 16px;\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    align-items: center;\n    transition: all 0.2s ease-in-out;\n    line-height: 24px;\n  }\n\n  .card-container .card:not(:last-child) {\n    margin-right: 0;\n  }\n\n  .card.card-small {\n    height: 16px;\n    width: 168px;\n  }\n\n  .card-container .card:not(.highlight-card) {\n    cursor: pointer;\n  }\n\n  .card-container .card:not(.highlight-card):hover {\n    transform: translateY(-3px);\n    box-shadow: 0 4px 17px rgba(0, 0, 0, 0.35);\n  }\n\n  .card-container .card:not(.highlight-card):hover .material-icons path {\n    fill: rgb(105, 103, 103);\n  }\n\n  .card.highlight-card {\n    background-color: #1976d2;\n    color: white;\n    font-weight: 600;\n    border: none;\n    width: auto;\n    min-width: 30%;\n    position: relative;\n  }\n\n  .card.card.highlight-card span {\n    margin-left: 60px;\n  }\n\n  svg#rocket {\n    width: 80px;\n    position: absolute;\n    left: -10px;\n    top: -24px;\n  }\n\n  svg#rocket-smoke {\n    height: calc(100vh - 95px);\n    position: absolute;\n    top: 10px;\n    right: 180px;\n    z-index: -10;\n  }\n\n  a,\n  a:visited,\n  a:hover {\n    color: #1976d2;\n    text-decoration: none;\n  }\n\n  a:hover {\n    color: #125699;\n  }\n\n  .terminal {\n    position: relative;\n    width: 80%;\n    max-width: 600px;\n    border-radius: 6px;\n    padding-top: 45px;\n    margin-top: 8px;\n    overflow: hidden;\n    background-color: rgb(15, 15, 16);\n  }\n\n  .terminal::before {\n    content: \"\\2022 \\2022 \\2022\";\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 4px;\n    background: rgb(58, 58, 58);\n    color: #c2c3c4;\n    width: 100%;\n    font-size: 2rem;\n    line-height: 0;\n    padding: 14px 0;\n    text-indent: 4px;\n  }\n\n  .terminal pre {\n    font-family: SFMono-Regular,Consolas,Liberation Mono,Menlo,monospace;\n    color: white;\n    padding: 0 1rem 1rem;\n    margin: 0;\n  }\n\n  .circle-link {\n    height: 40px;\n    width: 40px;\n    border-radius: 40px;\n    margin: 8px;\n    background-color: white;\n    border: 1px solid #eeeeee;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    cursor: pointer;\n    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);\n    transition: 1s ease-out;\n  }\n\n  .circle-link:hover {\n    transform: translateY(-0.25rem);\n    box-shadow: 0px 3px 15px rgba(0, 0, 0, 0.2);\n  }\n\n  footer {\n    margin-top: 8px;\n    display: flex;\n    align-items: center;\n    line-height: 20px;\n  }\n\n  footer a {\n    display: flex;\n    align-items: center;\n  }\n\n  .github-star-badge {\n    color: #24292e;\n    display: flex;\n    align-items: center;\n    font-size: 12px;\n    padding: 3px 10px;\n    border: 1px solid rgba(27,31,35,.2);\n    border-radius: 3px;\n    background-image: linear-gradient(-180deg,#fafbfc,#eff3f6 90%);\n    margin-left: 4px;\n    font-weight: 600;\n  }\n\n  .github-star-badge:hover {\n    background-image: linear-gradient(-180deg,#f0f3f6,#e6ebf1 90%);\n    border-color: rgba(27,31,35,.35);\n    background-position: -.5em;\n  }\n\n  .github-star-badge .material-icons {\n    height: 16px;\n    width: 16px;\n    margin-right: 4px;\n  }\n\n  svg#clouds {\n    position: fixed;\n    bottom: -160px;\n    left: -230px;\n    z-index: -10;\n    width: 1920px;\n  }\n\n  /* Responsive Styles */\n  @media screen and (max-width: 767px) {\n    .card-container > *:not(.circle-link) ,\n    .terminal {\n      width: 100%;\n    }\n\n    .card:not(.highlight-card) {\n      height: 16px;\n      margin: 8px 0;\n    }\n\n    .card.highlight-card span {\n      margin-left: 72px;\n    }\n\n    svg#rocket-smoke {\n      right: 120px;\n      transform: rotate(-5deg);\n    }\n  }\n\n  @media screen and (max-width: 575px) {\n    svg#rocket-smoke {\n      display: none;\n      visibility: hidden;\n    }\n  }\n</style>\n\n<!-- Toolbar -->\n<div class=\"toolbar\" role=\"banner\">\n  <img\n    width=\"40\"\n    alt=\"Angular Logo\"\n    src=\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTAgMjUwIj4KICAgIDxwYXRoIGZpbGw9IiNERDAwMzEiIGQ9Ik0xMjUgMzBMMzEuOSA2My4ybDE0LjIgMTIzLjFMMTI1IDIzMGw3OC45LTQzLjcgMTQuMi0xMjMuMXoiIC8+CiAgICA8cGF0aCBmaWxsPSIjQzMwMDJGIiBkPSJNMTI1IDMwdjIyLjItLjFWMjMwbDc4LjktNDMuNyAxNC4yLTEyMy4xTDEyNSAzMHoiIC8+CiAgICA8cGF0aCAgZmlsbD0iI0ZGRkZGRiIgZD0iTTEyNSA1Mi4xTDY2LjggMTgyLjZoMjEuN2wxMS43LTI5LjJoNDkuNGwxMS43IDI5LjJIMTgzTDEyNSA1Mi4xem0xNyA4My4zaC0zNGwxNy00MC45IDE3IDQwLjl6IiAvPgogIDwvc3ZnPg==\"\n  />\n  <span>Welcome</span>\n    <div class=\"spacer\"></div>\n    <a aria-label=\"Angular on X\" target=\"_blank\" rel=\"noopener\" href=\"https://twitter.com/angular\" title=\"X\">\n      <svg id=\"twitter-logo\" xmlns=\"http://www.w3.org/2000/svg\" height=\"24\" width=\"24\" data-name=\"Logo\" viewBox=\"0 0 512 512\" fill=\"#fff\">\n        <path d=\"M389.2 48h70.6L305.6 224.2 487 464H345L233.7 318.6 106.5 464H35.8L200.7 275.5 26.8 48H172.4L272.9 180.9 389.2 48zM364.4 421.8h39.1L151.1 88h-42L364.4 421.8z\"/>\n      </svg>\n    </a>\n    <a aria-label=\"Angular on YouTube\" target=\"_blank\" rel=\"noopener\" href=\"https://youtube.com/angular\" title=\"YouTube\">\n      <svg id=\"youtube-logo\" height=\"24\" width=\"24\" data-name=\"Logo\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"#fff\">\n        <path d=\"M0 0h24v24H0V0z\" fill=\"none\"/>\n        <path d=\"M21.58 7.19c-.23-.86-.91-1.54-1.77-1.77C18.25 5 12 5 12 5s-6.25 0-7.81.42c-.86.23-1.54.91-1.77 1.77C2 8.75 2 12 2 12s0 3.25.42 4.81c.23.86.91 1.54 1.77 1.77C5.75 19 12 19 12 19s6.25 0 7.81-.42c.86-.23 1.54-.91 1.77-1.77C22 15.25 22 12 22 12s0-3.25-.42-4.81zM10 15V9l5.2 3-5.2 3z\"/>\n      </svg>\n    </a>\n</div>\n\n<div class=\"content\" role=\"main\">\n\n  <!-- Highlight Card -->\n  <div class=\"card highlight-card card-small\">\n\n    <svg id=\"rocket\" xmlns=\"http://www.w3.org/2000/svg\" width=\"101.678\" height=\"101.678\" viewBox=\"0 0 101.678 101.678\">\n      <title>Rocket Ship</title>\n      <g id=\"Group_83\" data-name=\"Group 83\" transform=\"translate(-141 -696)\">\n        <circle id=\"Ellipse_8\" data-name=\"Ellipse 8\" cx=\"50.839\" cy=\"50.839\" r=\"50.839\" transform=\"translate(141 696)\" fill=\"#dd0031\"/>\n        <g id=\"Group_47\" data-name=\"Group 47\" transform=\"translate(165.185 720.185)\">\n          <path id=\"Path_33\" data-name=\"Path 33\" d=\"M3.4,42.615a3.084,3.084,0,0,0,3.553,3.553,21.419,21.419,0,0,0,12.215-6.107L9.511,30.4A21.419,21.419,0,0,0,3.4,42.615Z\" transform=\"translate(0.371 3.363)\" fill=\"#fff\"/>\n          <path id=\"Path_34\" data-name=\"Path 34\" d=\"M53.3,3.221A3.09,3.09,0,0,0,50.081,0,48.227,48.227,0,0,0,18.322,13.437c-6-1.666-14.991-1.221-18.322,7.218A33.892,33.892,0,0,1,9.439,25.1l-.333.666a3.013,3.013,0,0,0,.555,3.553L23.985,43.641a2.9,2.9,0,0,0,3.553.555l.666-.333A33.892,33.892,0,0,1,32.647,53.3c8.55-3.664,8.884-12.326,7.218-18.322A48.227,48.227,0,0,0,53.3,3.221ZM34.424,9.772a6.439,6.439,0,1,1,9.106,9.106,6.368,6.368,0,0,1-9.106,0A6.467,6.467,0,0,1,34.424,9.772Z\" transform=\"translate(0 0.005)\" fill=\"#fff\"/>\n        </g>\n      </g>\n    </svg>\n\n    <span>{{ title }} app is running!</span>\n\n    <svg id=\"rocket-smoke\" xmlns=\"http://www.w3.org/2000/svg\" width=\"516.119\" height=\"1083.632\" viewBox=\"0 0 516.119 1083.632\">\n      <title>Rocket Ship Smoke</title>\n      <path id=\"Path_40\" data-name=\"Path 40\" d=\"M644.6,141S143.02,215.537,147.049,870.207s342.774,201.755,342.774,201.755S404.659,847.213,388.815,762.2c-27.116-145.51-11.551-384.124,271.9-609.1C671.15,139.365,644.6,141,644.6,141Z\" transform=\"translate(-147.025 -140.939)\" fill=\"#f5f5f5\"/>\n    </svg>\n\n  </div>\n\n  <!-- Resources -->\n  <h2>Resources</h2>\n  <p>Here are some links to help you get started:</p>\n\n  <div class=\"card-container\">\n    <a class=\"card\" target=\"_blank\" rel=\"noopener\" href=\"https://angular.io/tutorial\">\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z\"/></svg>\n      <span>Learn Angular</span>\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/></svg>    </a>\n\n    <a class=\"card\" target=\"_blank\" rel=\"noopener\" href=\"https://angular.io/cli\">\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0l4.6-4.6-4.6-4.6L16 6l6 6-6 6-1.4-1.4z\"/></svg>\n      <span>CLI Documentation</span>\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/></svg>\n    </a>\n\n    <a class=\"card\" target=\"_blank\" rel=\"noopener\" href=\"https://material.angular.io\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" style=\"margin-right: 8px\" width=\"21.813\" height=\"23.453\" viewBox=\"0 0 179.2 192.7\"><path fill=\"#ffa726\" d=\"M89.4 0 0 32l13.5 118.4 75.9 42.3 76-42.3L179.2 32 89.4 0z\"/><path fill=\"#fb8c00\" d=\"M89.4 0v192.7l76-42.3L179.2 32 89.4 0z\"/><path fill=\"#ffe0b2\" d=\"m102.9 146.3-63.3-30.5 36.3-22.4 63.7 30.6-36.7 22.3z\"/><path fill=\"#fff3e0\" d=\"M102.9 122.8 39.6 92.2l36.3-22.3 63.7 30.6-36.7 22.3z\"/><path fill=\"#fff\" d=\"M102.9 99.3 39.6 68.7l36.3-22.4 63.7 30.6-36.7 22.4z\"/></svg>\n      <span>Angular Material</span>\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/></svg>\n    </a>\n\n    <a class=\"card\" target=\"_blank\" rel=\"noopener\" href=\"https://blog.angular.io/\">\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z\"/></svg>\n      <span>Angular Blog</span>\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/></svg>\n    </a>\n\n    <a class=\"card\" target=\"_blank\" rel=\"noopener\" href=\"https://angular.io/devtools/\">\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" enable-background=\"new 0 0 24 24\" height=\"24px\" viewBox=\"0 0 24 24\" width=\"24px\" fill=\"#000000\"><g><rect fill=\"none\" height=\"24\" width=\"24\"/></g><g><g><path d=\"M14.73,13.31C15.52,12.24,16,10.93,16,9.5C16,5.91,13.09,3,9.5,3S3,5.91,3,9.5C3,13.09,5.91,16,9.5,16 c1.43,0,2.74-0.48,3.81-1.27L19.59,21L21,19.59L14.73,13.31z M9.5,14C7.01,14,5,11.99,5,9.5S7.01,5,9.5,5S14,7.01,14,9.5 S11.99,14,9.5,14z\"/><polygon points=\"10.29,8.44 9.5,6 8.71,8.44 6.25,8.44 8.26,10.03 7.49,12.5 9.5,10.97 11.51,12.5 10.74,10.03 12.75,8.44\"/></g></g></svg>\n      <span>Angular DevTools</span>\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/></svg>\n    </a>\n\n  </div>\n\n  <!-- Next Steps -->\n  <h2>Next Steps</h2>\n  <p>What do you want to do next with your app?</p>\n\n  <input type=\"hidden\" #selection>\n\n  <div class=\"card-container\">\n    <button class=\"card card-small\" (click)=\"selection.value = 'component'\" tabindex=\"0\">\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/></svg>\n      <span>New Component</span>\n    </button>\n\n    <button class=\"card card-small\" (click)=\"selection.value = 'material'\" tabindex=\"0\">\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/></svg>\n      <span>Angular Material</span>\n    </button>\n\n    <button class=\"card card-small\" (click)=\"selection.value = 'pwa'\" tabindex=\"0\">\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/></svg>\n      <span>Add PWA Support</span>\n    </button>\n\n    <button class=\"card card-small\" (click)=\"selection.value = 'dependency'\" tabindex=\"0\">\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/></svg>\n      <span>Add Dependency</span>\n    </button>\n\n    <button class=\"card card-small\" (click)=\"selection.value = 'test'\" tabindex=\"0\">\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/></svg>\n      <span>Run and Watch Tests</span>\n    </button>\n\n    <button class=\"card card-small\" (click)=\"selection.value = 'build'\" tabindex=\"0\">\n      <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"/></svg>\n      <span>Build for Production</span>\n    </button>\n  </div>\n\n  <!-- Terminal -->\n  <div class=\"terminal\" [ngSwitch]=\"selection.value\">\n      <pre *ngSwitchDefault>ng generate component xyz</pre>\n      <pre *ngSwitchCase=\"'material'\">ng add @angular/material</pre>\n      <pre *ngSwitchCase=\"'pwa'\">ng add @angular/pwa</pre>\n      <pre *ngSwitchCase=\"'dependency'\">ng add _____</pre>\n      <pre *ngSwitchCase=\"'test'\">ng test</pre>\n      <pre *ngSwitchCase=\"'build'\">ng build</pre>\n  </div>\n\n  <!-- Links -->\n  <div class=\"card-container\">\n    <a class=\"circle-link\" title=\"Find a Local Meetup\" href=\"https://www.meetup.com/find/?keywords=angular\" target=\"_blank\" rel=\"noopener\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24.607\" height=\"23.447\" viewBox=\"0 0 24.607 23.447\">\n        <title>Meetup Logo</title>\n        <path id=\"logo--mSwarm\" d=\"M21.221,14.95A4.393,4.393,0,0,1,17.6,19.281a4.452,4.452,0,0,1-.8.069c-.09,0-.125.035-.154.117a2.939,2.939,0,0,1-2.506,2.091,2.868,2.868,0,0,1-2.248-.624.168.168,0,0,0-.245-.005,3.926,3.926,0,0,1-2.589.741,4.015,4.015,0,0,1-3.7-3.347,2.7,2.7,0,0,1-.043-.38c0-.106-.042-.146-.143-.166a3.524,3.524,0,0,1-1.516-.69A3.623,3.623,0,0,1,2.23,14.557a3.66,3.66,0,0,1,1.077-3.085.138.138,0,0,0,.026-.2,3.348,3.348,0,0,1-.451-1.821,3.46,3.46,0,0,1,2.749-3.28.44.44,0,0,0,.355-.281,5.072,5.072,0,0,1,3.863-3,5.028,5.028,0,0,1,3.555.666.31.31,0,0,0,.271.03A4.5,4.5,0,0,1,18.3,4.7a4.4,4.4,0,0,1,1.334,2.751,3.658,3.658,0,0,1,.022.706.131.131,0,0,0,.1.157,2.432,2.432,0,0,1,1.574,1.645,2.464,2.464,0,0,1-.7,2.616c-.065.064-.051.1-.014.166A4.321,4.321,0,0,1,21.221,14.95ZM13.4,14.607a2.09,2.09,0,0,0,1.409,1.982,4.7,4.7,0,0,0,1.275.221,1.807,1.807,0,0,0,.9-.151.542.542,0,0,0,.321-.545.558.558,0,0,0-.359-.534,1.2,1.2,0,0,0-.254-.078c-.262-.047-.526-.086-.787-.138a.674.674,0,0,1-.617-.75,3.394,3.394,0,0,1,.218-1.109c.217-.658.509-1.286.79-1.918a15.609,15.609,0,0,0,.745-1.86,1.95,1.95,0,0,0,.06-1.073,1.286,1.286,0,0,0-1.051-1.033,1.977,1.977,0,0,0-1.521.2.339.339,0,0,1-.446-.042c-.1-.092-.2-.189-.307-.284a1.214,1.214,0,0,0-1.643-.061,7.563,7.563,0,0,1-.614.512A.588.588,0,0,1,10.883,8c-.215-.115-.437-.215-.659-.316a2.153,2.153,0,0,0-.695-.248A2.091,2.091,0,0,0,7.541,8.562a9.915,9.915,0,0,0-.405.986c-.559,1.545-1.015,3.123-1.487,4.7a1.528,1.528,0,0,0,.634,1.777,1.755,1.755,0,0,0,1.5.211,1.35,1.35,0,0,0,.824-.858c.543-1.281,1.032-2.584,1.55-3.875.142-.355.28-.712.432-1.064a.548.548,0,0,1,.851-.24.622.622,0,0,1,.185.539,2.161,2.161,0,0,1-.181.621c-.337.852-.68,1.7-1.018,2.552a2.564,2.564,0,0,0-.173.528.624.624,0,0,0,.333.71,1.073,1.073,0,0,0,.814.034,1.22,1.22,0,0,0,.657-.655q.758-1.488,1.511-2.978.35-.687.709-1.37a1.073,1.073,0,0,1,.357-.434.43.43,0,0,1,.463-.016.373.373,0,0,1,.153.387.7.7,0,0,1-.057.236c-.065.157-.127.316-.2.469-.42.883-.846,1.763-1.262,2.648A2.463,2.463,0,0,0,13.4,14.607Zm5.888,6.508a1.09,1.09,0,0,0-2.179.006,1.09,1.09,0,0,0,2.179-.006ZM1.028,12.139a1.038,1.038,0,1,0,.01-2.075,1.038,1.038,0,0,0-.01,2.075ZM13.782.528a1.027,1.027,0,1,0-.011,2.055A1.027,1.027,0,0,0,13.782.528ZM22.21,6.95a.882.882,0,0,0-1.763.011A.882.882,0,0,0,22.21,6.95ZM4.153,4.439a.785.785,0,1,0,.787-.78A.766.766,0,0,0,4.153,4.439Zm8.221,18.22a.676.676,0,1,0-.677.666A.671.671,0,0,0,12.374,22.658ZM22.872,12.2a.674.674,0,0,0-.665.665.656.656,0,0,0,.655.643.634.634,0,0,0,.655-.644A.654.654,0,0,0,22.872,12.2ZM7.171-.123A.546.546,0,0,0,6.613.43a.553.553,0,1,0,1.106,0A.539.539,0,0,0,7.171-.123ZM24.119,9.234a.507.507,0,0,0-.493.488.494.494,0,0,0,.494.494.48.48,0,0,0,.487-.483A.491.491,0,0,0,24.119,9.234Zm-19.454,9.7a.5.5,0,0,0-.488-.488.491.491,0,0,0-.487.5.483.483,0,0,0,.491.479A.49.49,0,0,0,4.665,18.936Z\" transform=\"translate(0 0.123)\" fill=\"#f64060\"/>\n      </svg>\n    </a>\n\n    <a class=\"circle-link\" title=\"Join the Conversation on Discord\" href=\"https://discord.gg/angular\" target=\"_blank\" rel=\"noopener\">\n      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"26\" height=\"26\" viewBox=\"0 0 245 240\">\n        <title>Discord Logo</title>\n        <path d=\"M104.4 103.9c-5.7 0-10.2 5-10.2 11.1s4.6 11.1 10.2 11.1c5.7 0 10.2-5 10.2-11.1.1-6.1-4.5-11.1-10.2-11.1zM140.9 103.9c-5.7 0-10.2 5-10.2 11.1s4.6 11.1 10.2 11.1c5.7 0 10.2-5 10.2-11.1s-4.5-11.1-10.2-11.1z\"/>\n        <path d=\"M189.5 20h-134C44.2 20 35 29.2 35 40.6v135.2c0 11.4 9.2 20.6 20.5 20.6h113.4l-5.3-18.5 12.8 11.9 12.1 11.2 21.5 19V40.6c0-11.4-9.2-20.6-20.5-20.6zm-38.6 130.6s-3.6-4.3-6.6-8.1c13.1-3.7 18.1-11.9 18.1-11.9-4.1 2.7-8 4.6-11.5 5.9-5 2.1-9.8 3.5-14.5 4.3-9.6 1.8-18.4 1.3-25.9-.1-5.7-1.1-10.6-2.7-14.7-4.3-2.3-.9-4.8-2-7.3-3.4-.3-.2-.6-.3-.9-.5-.2-.1-.3-.2-.4-.3-1.8-1-2.8-1.7-2.8-1.7s4.8 8 17.5 11.8c-3 3.8-6.7 8.3-6.7 8.3-22.1-.7-30.5-15.2-30.5-15.2 0-32.2 14.4-58.3 14.4-58.3 14.4-10.8 28.1-10.5 28.1-10.5l1 1.2c-18 5.2-26.3 13.1-26.3 13.1s2.2-1.2 5.9-2.9c10.7-4.7 19.2-6 22.7-6.3.6-.1 1.1-.2 1.7-.2 6.1-.8 13-1 20.2-.2 9.5 1.1 19.7 3.9 30.1 9.6 0 0-7.9-7.5-24.9-12.7l1.4-1.6s13.7-.3 28.1 10.5c0 0 14.4 26.1 14.4 58.3 0 0-8.5 14.5-30.6 15.2z\"/>\n      </svg>\n    </a>\n  </div>\n\n  <!-- Footer -->\n  <footer>\n      Love Angular?&nbsp;\n      <a href=\"https://github.com/angular/angular\" target=\"_blank\" rel=\"noopener\"> Give our repo a star.\n        <div class=\"github-star-badge\">\n            <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M0 0h24v24H0z\" fill=\"none\"/><path d=\"M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z\"/></svg>\n          Star\n        </div>\n      </a>\n      <a href=\"https://github.com/angular/angular\" target=\"_blank\" rel=\"noopener\">\n        <svg class=\"material-icons\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\" fill=\"#1976d2\"/><path d=\"M0 0h24v24H0z\" fill=\"none\"/></svg>\n      </a>\n  </footer>\n\n  <svg id=\"clouds\" xmlns=\"http://www.w3.org/2000/svg\" width=\"2611.084\" height=\"485.677\" viewBox=\"0 0 2611.084 485.677\">\n    <title>Gray Clouds Background</title>\n    <path id=\"Path_39\" data-name=\"Path 39\" d=\"M2379.709,863.793c10-93-77-171-168-149-52-114-225-105-264,15-75,3-140,59-152,133-30,2.83-66.725,9.829-93.5,26.25-26.771-16.421-63.5-23.42-93.5-26.25-12-74-77-130-152-133-39-120-212-129-264-15-54.084-13.075-106.753,9.173-138.488,48.9-31.734-39.726-84.4-61.974-138.487-48.9-52-114-225-105-264,15a162.027,162.027,0,0,0-103.147,43.044c-30.633-45.365-87.1-72.091-145.206-58.044-52-114-225-105-264,15-75,3-140,59-152,133-53,5-127,23-130,83-2,42,35,72,70,86,49,20,106,18,157,5a165.625,165.625,0,0,0,120,0c47,94,178,113,251,33,61.112,8.015,113.854-5.72,150.492-29.764a165.62,165.62,0,0,0,110.861-3.236c47,94,178,113,251,33,31.385,4.116,60.563,2.495,86.487-3.311,25.924,5.806,55.1,7.427,86.488,3.311,73,80,204,61,251-33a165.625,165.625,0,0,0,120,0c51,13,108,15,157-5a147.188,147.188,0,0,0,33.5-18.694,147.217,147.217,0,0,0,33.5,18.694c49,20,106,18,157,5a165.625,165.625,0,0,0,120,0c47,94,178,113,251,33C2446.709,1093.793,2554.709,922.793,2379.709,863.793Z\" transform=\"translate(142.69 -634.312)\" fill=\"#eee\"/>\n  </svg>\n\n</div>\n\n<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->\n<!-- * * * * * * * * * * * The content above * * * * * * * * * * * -->\n<!-- * * * * * * * * * * is only a placeholder * * * * * * * * * * -->\n<!-- * * * * * * * * * * and can be replaced. * * * * * * * * * * * -->\n<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->\n<!-- * * * * * * * * * * End of Placeholder * * * * * * * * * * * -->\n<!-- * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * -->\n\n<router-outlet></router-outlet>\n"], "mappings": ";;;;;IC2aMA,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACrDH,EAAA,CAAAC,cAAA,UAAgC;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC9DH,EAAA,CAAAC,cAAA,UAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACpDH,EAAA,CAAAC,cAAA,UAAkC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACpDH,EAAA,CAAAC,cAAA,UAA4B;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;<PERSON><PERSON>z<PERSON>,EAAA,CAAAC,cAAA,UAA6B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;ADzajD,OAAM,MAAOC,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,KAAK;;;;uBADFF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UC0SzBb,EAAA,CAAAC,cAAA,aAAmC;UACjCD,EAAA,CAAAe,SAAA,aAIE;UACFf,EAAA,CAAAC,cAAA,WAAM;UAAAD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAClBH,EAAA,CAAAe,SAAA,aAA0B;UAC1Bf,EAAA,CAAAC,cAAA,WAAyG;UACvGD,EAAA,CAAAgB,cAAA,EAAoI;UAApIhB,EAAA,CAAAC,cAAA,aAAoI;UAClID,EAAA,CAAAe,SAAA,cAAwK;UAC1Kf,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAiB,eAAA,EAAqH;UAArHjB,EAAA,CAAAC,cAAA,WAAqH;UACnHD,EAAA,CAAAgB,cAAA,EAAkI;UAAlIhB,EAAA,CAAAC,cAAA,aAAkI;UAChID,EAAA,CAAAe,SAAA,eAAuC;UAEzCf,EAAA,CAAAG,YAAA,EAAM;UAIZH,EAAA,CAAAiB,eAAA,EAAiC;UAAjCjB,EAAA,CAAAC,cAAA,eAAiC;UAK7BD,EAAA,CAAAgB,cAAA,EAAmH;UAAnHhB,EAAA,CAAAC,cAAA,eAAmH;UAC1GD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1BH,EAAA,CAAAC,cAAA,aAAuE;UACrED,EAAA,CAAAe,SAAA,kBAA+H;UAC/Hf,EAAA,CAAAC,cAAA,aAA6E;UAC3ED,EAAA,CAAAe,SAAA,gBAAiN;UAEnNf,EAAA,CAAAG,YAAA,EAAI;UAIRH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAExCH,EAAA,CAAAgB,cAAA,EAA2H;UAA3HhB,EAAA,CAAAC,cAAA,eAA2H;UAClHD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChCH,EAAA,CAAAe,SAAA,gBAA0R;UAC5Rf,EAAA,CAAAG,YAAA,EAAM;UAKRH,EAAA,CAAAiB,eAAA,EAAI;UAAJjB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,oDAA4C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEnDH,EAAA,CAAAC,cAAA,eAA4B;UAExBD,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAAuF;UAAAf,EAAA,CAAAG,YAAA,EAAM;UACvMH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC1BH,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAA0D;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAE5KH,EAAA,CAAAiB,eAAA,EAA6E;UAA7EjB,EAAA,CAAAC,cAAA,aAA6E;UAC3ED,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAAsG;UAAAf,EAAA,CAAAG,YAAA,EAAM;UACtNH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9BH,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAA0D;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAG5KH,EAAA,CAAAiB,eAAA,EAAkF;UAAlFjB,EAAA,CAAAC,cAAA,aAAkF;UAChFD,EAAA,CAAAgB,cAAA,EAA2H;UAA3HhB,EAAA,CAAAC,cAAA,eAA2H;UAAAD,EAAA,CAAAe,SAAA,gBAAqF;UAA6Sf,EAAA,CAAAG,YAAA,EAAM;UACngBH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7BH,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAA0D;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAG5KH,EAAA,CAAAiB,eAAA,EAA+E;UAA/EjB,EAAA,CAAAC,cAAA,aAA+E;UAC7ED,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAA6T;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAC7aH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzBH,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAA0D;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAG5KH,EAAA,CAAAiB,eAAA,EAAmF;UAAnFjB,EAAA,CAAAC,cAAA,aAAmF;UACjFD,EAAA,CAAAgB,cAAA,EAA+J;UAA/JhB,EAAA,CAAAC,cAAA,eAA+J;UAAGD,EAAA,CAAAe,SAAA,gBAA0C;UAAAf,EAAA,CAAAG,YAAA,EAAI;UAAAH,EAAA,CAAAC,cAAA,SAAG;UAAGD,EAAA,CAAAe,SAAA,gBAAqP;UAAyHf,EAAA,CAAAG,YAAA,EAAI;UACxkBH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7BH,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAA0D;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAM9KH,EAAA,CAAAiB,eAAA,EAAI;UAAJjB,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,kDAA0C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjDH,EAAA,CAAAe,SAAA,qBAAgC;UAEhCf,EAAA,CAAAC,cAAA,eAA4B;UACMD,EAAA,CAAAkB,UAAA,mBAAAC,+CAAA;YAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;YAAA,MAAAC,GAAA,GAAAtB,EAAA,CAAAuB,WAAA;YAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAF,GAAA,CAAAG,KAAA,GAAkB,WAAW;UAAA,EAAC;UACrEzB,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAA+C;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAC/JH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG5BH,EAAA,CAAAC,cAAA,kBAAoF;UAApDD,EAAA,CAAAkB,UAAA,mBAAAQ,+CAAA;YAAA1B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;YAAA,MAAAC,GAAA,GAAAtB,EAAA,CAAAuB,WAAA;YAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAF,GAAA,CAAAG,KAAA,GAAkB,UAAU;UAAA,EAAC;UACpEzB,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAA+C;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAC/JH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG/BH,EAAA,CAAAC,cAAA,kBAA+E;UAA/CD,EAAA,CAAAkB,UAAA,mBAAAS,+CAAA;YAAA3B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;YAAA,MAAAC,GAAA,GAAAtB,EAAA,CAAAuB,WAAA;YAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAF,GAAA,CAAAG,KAAA,GAAkB,KAAK;UAAA,EAAC;UAC/DzB,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,eAA0G;UAAAD,EAAA,CAAAe,SAAA,gBAA+C;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAC/JH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG9BH,EAAA,CAAAC,cAAA,kBAAsF;UAAtDD,EAAA,CAAAkB,UAAA,mBAAAU,+CAAA;YAAA5B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;YAAA,MAAAC,GAAA,GAAAtB,EAAA,CAAAuB,WAAA;YAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAF,GAAA,CAAAG,KAAA,GAAkB,YAAY;UAAA,EAAC;UACtEzB,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,gBAA0G;UAAAD,EAAA,CAAAe,SAAA,iBAA+C;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAC/JH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAG7BH,EAAA,CAAAC,cAAA,mBAAgF;UAAhDD,EAAA,CAAAkB,UAAA,mBAAAW,gDAAA;YAAA7B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;YAAA,MAAAC,GAAA,GAAAtB,EAAA,CAAAuB,WAAA;YAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAF,GAAA,CAAAG,KAAA,GAAkB,MAAM;UAAA,EAAC;UAChEzB,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,gBAA0G;UAAAD,EAAA,CAAAe,SAAA,iBAA+C;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAC/JH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAGlCH,EAAA,CAAAC,cAAA,mBAAiF;UAAjDD,EAAA,CAAAkB,UAAA,mBAAAY,gDAAA;YAAA9B,EAAA,CAAAoB,aAAA,CAAAC,GAAA;YAAA,MAAAC,GAAA,GAAAtB,EAAA,CAAAuB,WAAA;YAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAF,GAAA,CAAAG,KAAA,GAAkB,OAAO;UAAA,EAAC;UACjEzB,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,gBAA0G;UAAAD,EAAA,CAAAe,SAAA,iBAA+C;UAAAf,EAAA,CAAAG,YAAA,EAAM;UAC/JH,EAAA,CAAAiB,eAAA,EAAM;UAANjB,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,6BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKrCH,EAAA,CAAAC,cAAA,gBAAmD;UAC/CD,EAAA,CAAA+B,UAAA,MAAAC,6BAAA,kBAAqD;UACrDhC,EAAA,CAAA+B,UAAA,MAAAE,6BAAA,kBAA8D;UAC9DjC,EAAA,CAAA+B,UAAA,MAAAG,6BAAA,kBAAoD;UACpDlC,EAAA,CAAA+B,UAAA,MAAAI,6BAAA,kBAAoD;UACpDnC,EAAA,CAAA+B,UAAA,MAAAK,6BAAA,kBAAyC;UACzCpC,EAAA,CAAA+B,UAAA,MAAAM,6BAAA,kBAA2C;UAC/CrC,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,gBAA4B;UAExBD,EAAA,CAAAgB,cAAA,EAAmG;UAAnGhB,EAAA,CAAAC,cAAA,gBAAmG;UAC1FD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1BH,EAAA,CAAAe,SAAA,iBAA4zF;UAC9zFf,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAiB,eAAA,EAAiI;UAAjIjB,EAAA,CAAAC,cAAA,cAAiI;UAC/HD,EAAA,CAAAgB,cAAA,EAAqF;UAArFhB,EAAA,CAAAC,cAAA,gBAAqF;UAC5ED,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3BH,EAAA,CAAAe,SAAA,iBAAuN;UAEzNf,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAiB,eAAA,EAAQ;UAARjB,EAAA,CAAAC,cAAA,eAAQ;UACJD,EAAA,CAAAE,MAAA,8BACA;UAAAF,EAAA,CAAAC,cAAA,cAA4E;UAACD,EAAA,CAAAE,MAAA,gCAC3E;UAAAF,EAAA,CAAAC,cAAA,gBAA+B;UAC3BD,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,gBAA0G;UAAAD,EAAA,CAAAe,SAAA,iBAAqC;UAAoGf,EAAA,CAAAG,YAAA,EAAM;UAC3PH,EAAA,CAAAE,MAAA,eACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAiB,eAAA,EAA4E;UAA5EjB,EAAA,CAAAC,cAAA,cAA4E;UAC1ED,EAAA,CAAAgB,cAAA,EAA0G;UAA1GhB,EAAA,CAAAC,cAAA,gBAA0G;UAAAD,EAAA,CAAAe,SAAA,iBAAyE;UAAqCf,EAAA,CAAAG,YAAA,EAAM;UAIpOH,EAAA,CAAAC,cAAA,gBAAqH;UAC5GD,EAAA,CAAAE,MAAA,+BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrCH,EAAA,CAAAe,SAAA,iBAAkhC;UACphCf,EAAA,CAAAG,YAAA,EAAM;UAYRH,EAAA,CAAAiB,eAAA,EAAe;UAAfjB,EAAA,CAAAe,SAAA,sBAA+B;;;;UA5IrBf,EAAA,CAAAsC,SAAA,IAA2B;UAA3BtC,EAAA,CAAAuC,kBAAA,KAAAzB,GAAA,CAAAR,KAAA,qBAA2B;UAoFbN,EAAA,CAAAsC,SAAA,IAA4B;UAA5BtC,EAAA,CAAAwC,UAAA,aAAAlB,GAAA,CAAAG,KAAA,CAA4B;UAExCzB,EAAA,CAAAsC,SAAA,GAAwB;UAAxBtC,EAAA,CAAAwC,UAAA,4BAAwB;UACxBxC,EAAA,CAAAsC,SAAA,GAAmB;UAAnBtC,EAAA,CAAAwC,UAAA,uBAAmB;UACnBxC,EAAA,CAAAsC,SAAA,GAA0B;UAA1BtC,EAAA,CAAAwC,UAAA,8BAA0B;UAC1BxC,EAAA,CAAAsC,SAAA,GAAoB;UAApBtC,EAAA,CAAAwC,UAAA,wBAAoB;UACpBxC,EAAA,CAAAsC,SAAA,GAAqB;UAArBtC,EAAA,CAAAwC,UAAA,yBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}