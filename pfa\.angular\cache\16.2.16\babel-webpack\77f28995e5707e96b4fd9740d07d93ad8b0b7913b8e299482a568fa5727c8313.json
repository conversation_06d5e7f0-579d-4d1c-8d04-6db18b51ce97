{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class BijouComponent {\n  static {\n    this.ɵfac = function BijouComponent_Factory(t) {\n      return new (t || BijouComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BijouComponent,\n      selectors: [[\"app-bijou\"]],\n      decls: 24,\n      vars: 0,\n      consts: [[1, \"iris-details\"], [\"src\", \"assets/2.png\", \"alt\", \"Bijou\", 1, \"iris-img\"], [1, \"iris-description\"]],\n      template: function BijouComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"h2\");\n          i0.ɵɵtext(4, \"Bijou\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"ul\")(6, \"li\");\n          i0.ɵɵtext(7, \"Le R\\u00E9fl\\u00E9chi\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"li\");\n          i0.ɵɵtext(9, \"Type analytique, mental et tourn\\u00E9 vers la r\\u00E9flexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"li\");\n          i0.ɵɵtext(11, \"Ressent et per\\u00E7oit par l\\u2019analyse interne, peu d\\u2019expression \\u00E9motionnelle ext\\u00E9rieure\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"li\");\n          i0.ɵɵtext(13, \"Apprentissage visuel : observe, lit, cat\\u00E9gorise, puis verbalise\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"li\");\n          i0.ɵɵtext(15, \"Communicateur pr\\u00E9cis, souvent enseignant, critique, scientifique ou leader\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"li\");\n          i0.ɵɵtext(17, \"Orient\\u00E9 vers l\\u2019avenir, porteur de sagesse\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"li\");\n          i0.ɵɵtext(19, \"N\\u2019aime pas \\u00EAtre critiqu\\u00E9 ni contr\\u00F4l\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"li\");\n          i0.ɵɵtext(21, \"Proximit\\u00E9 avec la m\\u00E8re, parfois distante \\u00E9motionnellement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"li\");\n          i0.ɵɵtext(23, \"Le\\u00E7on de vie : apprendre \\u00E0 l\\u00E2cher prise, faire confiance et exprimer ses \\u00E9motions\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BijouComponent", "selectors", "decls", "vars", "consts", "template", "BijouComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\bijou\\bijou.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\bijou\\bijou.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-bijou',\n  templateUrl: './bijou.component.html',\n  styleUrls: ['./bijou.component.scss']\n})\nexport class BijouComponent {\n\n}\n", "<div class=\"iris-details\">\n    <img src=\"assets/2.png\" alt=\"Bijou\" class=\"iris-img\" />\n    <div class=\"iris-description\">\n      <h2>Bijou</h2>\n      <ul>\n        <li><PERSON> Réfléchi</li>\n        <li>Type analytique, mental et tourné vers la réflexion</li>\n        <li>Ressent et perçoit par l’analyse interne, peu d’expression émotionnelle extérieure</li>\n        <li>Apprentissage visuel : observe, lit, catégorise, puis verbalise</li>\n        <li>Communicateur pré<PERSON>, souvent enseignant, critique, scientifique ou leader</li>\n        <li><PERSON><PERSON> vers l’avenir, porteur de sagesse</li>\n        <li>N’aime pas être critiqué ni contrôlé</li>\n        <li>Proximité avec la mère, parfois distante émotionnellement</li>\n        <li>Leçon de vie : apprendre à lâcher prise, faire confiance et exprimer ses émotions</li>\n      </ul>\n    </div>\n  </div>\n  "], "mappings": ";AAOA,OAAM,MAAOA,cAAc;;;uBAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3BE,EAAA,CAAAC,cAAA,aAA0B;UACtBD,EAAA,CAAAE,SAAA,aAAuD;UACvDF,EAAA,CAAAC,cAAA,aAA8B;UACxBD,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,SAAI;UACED,EAAA,CAAAG,MAAA,4BAAW;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpBJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,oEAAmD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC5DJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,mHAAkF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC3FJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,4EAA+D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACxEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,uFAA0E;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnFJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,2DAAyC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClDJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,qEAAoC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC7CJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,gFAAyD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClEJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,6GAAiF;UAAAH,EAAA,CAAAI,YAAA,EAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}