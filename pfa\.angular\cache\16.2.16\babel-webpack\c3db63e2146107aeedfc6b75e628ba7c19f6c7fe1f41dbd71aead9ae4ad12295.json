{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, FormsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, AccueilComponent, SuivantaccComponent, TypeirisComponent],\n    imports: [BrowserModule, AppRoutingModule, FormsModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "FormsModule", "AppRoutingModule", "AppComponent", "AccueilComponent", "SuivantaccComponent", "TypeirisComponent", "AppModule", "bootstrap", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { FormsModule } from '@angular/forms'; // Importez FormsModule\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\n\nimport { AccueilComponent } from './accueil/accueil.component';\nimport { SuivantaccComponent } from './suivantacc/suivantacc.component';\nimport { TypeirisComponent } from './typeiris/typeiris.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    AccueilComponent,\n    SuivantaccComponent,\n    TypeirisComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,FormsModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,WAAW,QAAQ,gBAAgB,CAAC,CAAC;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;;AAgBjE,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRL,YAAY;IAAA;EAAA;;;gBAJtBH,aAAa,EACbE,gBAAgB,EAACD,WAAW;IAAA;EAAA;;;2EAKnBM,SAAS;IAAAE,YAAA,GAZlBN,YAAY,EACZC,gBAAgB,EAChBC,mBAAmB,EACnBC,iBAAiB;IAAAI,OAAA,GAGjBV,aAAa,EACbE,gBAAgB,EAACD,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}