{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ShakerComponent {\n  static {\n    this.ɵfac = function ShakerComponent_Factory(t) {\n      return new (t || ShakerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShakerComponent,\n      selectors: [[\"app-shaker\"]],\n      decls: 2,\n      vars: 0,\n      template: function ShakerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"shaker works!\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ShakerComponent", "selectors", "decls", "vars", "template", "ShakerComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shaker\\shaker.component.ts", "C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\shaker\\shaker.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-shaker',\n  templateUrl: './shaker.component.html',\n  styleUrls: ['./shaker.component.scss']\n})\nexport class ShakerComponent {\n\n}\n", "<p>shaker works!</p>\n"], "mappings": ";AAOA,OAAM,MAAOA,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}