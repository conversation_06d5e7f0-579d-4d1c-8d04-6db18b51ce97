{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'accueil',\n  component: AccueilComponent\n}, {\n  path: '',\n  redirectTo: '/accueil',\n  pathMatch: 'full'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AccueilComponent", "routes", "path", "component", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\pfa\\pfa\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { AccueilComponent } from './accueil/accueil.component';\n\nconst routes: Routes = [\n  { path: 'accueil', component: AccueilComponent }, // 👈 nouvelle route\n  { path: '', redirectTo: '/accueil', pathMatch: 'full' }\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;;;AAE9D,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,SAAS;EAAEC,SAAS,EAAEH;AAAgB,CAAE,EAChD;EAAEE,IAAI,EAAE,EAAE;EAAEE,UAAU,EAAE,UAAU;EAAEC,SAAS,EAAE;AAAM,CAAE,CACxD;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBP,YAAY,CAACQ,OAAO,CAACN,MAAM,CAAC,EAC5BF,YAAY;IAAA;EAAA;;;2EAEXO,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAV,YAAA;IAAAW,OAAA,GAFjBX,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}