{"ast": null, "code": "export const dateTimestampProvider = {\n  now() {\n    return (dateTimestampProvider.delegate || Date).now();\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["dateTimestampProvider", "now", "delegate", "Date", "undefined"], "sources": ["D:/ProjetPfa/pfa/pfa/node_modules/rxjs/dist/esm/internal/scheduler/dateTimestampProvider.js"], "sourcesContent": ["export const dateTimestampProvider = {\n    now() {\n        return (dateTimestampProvider.delegate || Date).now();\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,GAAG;EACjCC,GAAGA,CAAA,EAAG;IACF,OAAO,CAACD,qBAAqB,CAACE,QAAQ,IAAIC,IAAI,EAAEF,GAAG,CAAC,CAAC;EACzD,CAAC;EACDC,QAAQ,EAAEE;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}